apply plugin: 'com.android.library'
apply plugin: 'kotlin-android'
//apply plugin: 'kotlin-android-extensions'

android {
    compileSdkVersion 34
    namespace = "com.uniq.materialanimation"

    defaultConfig {
        minSdkVersion 16
        targetSdkVersion 30

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"

    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }

    kotlinOptions {
        jvmTarget = '17'
   }

}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    // implementation 'androidx.appcompat:appcompat:1.4.2'
    implementation "androidx.appcompat:appcompat:1.6.1"

    //testing
    testImplementation 'junit:junit:4.13'
    androidTestImplementation 'androidx.test.ext:junit:1.1.2'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.3.0'
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$kotlin_version"
}

repositories {
    mavenCentral()
}
