package com.uniq.materialanimation

import android.content.Context
import android.content.res.ColorStateList
import android.graphics.Color
import androidx.core.content.ContextCompat
import android.util.AttributeSet
import android.util.TypedValue
import android.view.LayoutInflater
import android.view.View
import android.widget.Button
import android.widget.FrameLayout
import android.widget.ProgressBar

/**
 * Created by annasblackhat on 2019-08-07
 */
class ButtonLoading @JvmOverloads constructor(
        context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {


    private var button: Button
    private var progressBar: ProgressBar
    private var text: String? = null
    private var buttonColor: Int = 0
    private var textColor: Int = 0
    private var isLoading = false
    private lateinit var colorStateList: ColorStateList

    companion object {

        @JvmStatic
        fun setButtonColor(view: ButtonLoading, color: Int) {
            view.buttonColor = color
            view.drawButton()
        }

        @JvmStatic
        fun setTextColor(view: ButtonLoading, color: Int) {
            view.textColor = color
            view.drawButton()
        }

        @JvmStatic
        fun setButtonText(view: ButtonLoading, text: String) {
            view.text = text
            view.button.text = text
        }
    }

    init {
        LayoutInflater.from(context).inflate(R.layout.button_loading, this, true)
        button = findViewById(R.id.btn)
        progressBar = findViewById(R.id.pb)
        val a = context.theme.obtainStyledAttributes(attrs, R.styleable.LoadingButton, 0, 0)
        text = a.getString(R.styleable.LoadingButton_buttonText)
        buttonColor = a.getColor(R.styleable.LoadingButton_buttonColor, 0)
        textColor = a.getColor(R.styleable.LoadingButton_textColor, 0)
        drawButton()
    }

    private fun drawButton() {
        colorStateList = ColorStateList (
                arrayOf(
                        intArrayOf(android.R.attr.state_enabled),
                        intArrayOf(-android.R.attr.state_enabled)
                ),
                intArrayOf(
                        if (buttonColor == 0) fetchAccentColor() else buttonColor,
                        ContextCompat.getColor(context, R.color.disabledButtonBackground)
                )
        )

        if (android.os.Build.VERSION.SDK_INT >= 21) {
            button.backgroundTintList = colorStateList
        }else{
            button.setBackgroundColor(buttonColor)
        }
        button.text = text


        if (button.isEnabled) {
            if (textColor != 0) {
                button.setTextColor(textColor)
                if (android.os.Build.VERSION.SDK_INT >= 21){
                    progressBar.indeterminateTintList = ColorStateList.valueOf(textColor)
                }
            } else {
                button.setTextColor(if (isColorDark(buttonColor)) Color.WHITE else Color.BLACK)
                if (android.os.Build.VERSION.SDK_INT >= 21){
                    progressBar.indeterminateTintList = ColorStateList.valueOf(if (isColorDark(buttonColor)) Color.WHITE else Color.BLACK)
                }
            }
        } else {
            button.setTextColor(Color.GRAY)
        }
    }

//    fun setButtonOnClickListener(onClick: View.OnClickListener?) {
//        button.setOnClickListener(onClick)
//    }

    fun setOnButtonClickListener(func: () -> Unit){
        button.setOnClickListener { if(!isLoading) func() }
    }

    fun setButtonColor(color: Int) {
        buttonColor = color
        drawButton()
    }

    fun setTextColor(color: Int) {
        textColor = color
        drawButton()
    }

    fun setButtonText(text: String) {
        this.text = text
        button.text = text
    }

    fun setButtonEnabled(isEnabled: Boolean) {
        button.isEnabled = isEnabled
        drawButton()
    }

    fun startLoading() {
        button.text = ""
        isLoading = true
        button.isClickable = false
        progressBar.visibility = View.VISIBLE
    }

    fun stopLoading() {
        isLoading = false
        button.isClickable = true
        button.text = text
        progressBar.visibility = View.GONE
    }

    fun setLoading(isLoading: Boolean){
        if(isLoading) startLoading()
        else stopLoading()
    }

    fun isInProgress(): Boolean {
        return progressBar.visibility == View.VISIBLE
    }

    fun setButtonOnClick(onClick: OnClickListener?) {
        button.setOnClickListener(onClick)
    }

    private fun fetchAccentColor(): Int {
        val typedValue = TypedValue()
        val a = context.obtainStyledAttributes(typedValue.data, intArrayOf(androidx.appcompat.R.attr.colorAccent))
        
        val color = a.getColor(0, 0)
        a.recycle()
        return color
    }


    fun isColorDark(color: Int): Boolean {
        val darkness = 1 - (0.299 * Color.red(color) + 0.587 * Color.green(color) + 0.114 * Color.blue(color)) / 255
        return darkness >= 0.5
    }
}