**File to edit:** `app/src/main/java/com/uniq/uniqpos/view/transaction/dialog/SaveOrderDialog.kt`

### Primary Objective:
Modernize all input fields to match the styling of the "No. HP" field while preserving all existing functionality.

### Specific Field Updates Required:

1. **Customer Field:**
   - Convert to modern input field style (like No. HP)
   - Keep the barcode icon (📊) on the right side and maintain its click functionality
   - Add placeholder text: "Enter customer name..."
   - Ensure the barcode click handler remains functional

2. **Customer Qty Field:**
   - Convert to modern input field with placeholder: "Enter quantity..."
   - Add quantity icon (🔢) on the left side
   - Maintain all existing input validation and functionality

3. **PIN Field:**
   - Style as modern input field but keep it non-editable (read-only appearance)
   - Add lock icon (🔒) on the left side
   - Add placeholder/hint text: "Tap to enter PIN..."
   - Preserve the click functionality that opens the PIN dialog
   - Make it visually clear it's clickable (maybe subtle highlight or different background)

4. **Table Field:**
   - Convert to modern input field with placeholder: "Enter table number..."
   - Add table icon (🍽️) on the left side
   - Maintain existing functionality

5. **Note Field:**
   - Convert to modern input field with placeholder: "Add notes (for cart only)..."
   - Add note icon (📝) on the left side
   - Maintain existing functionality

### Styling Requirements:
- **Consistent Background:** Dark theme with subtle borders matching "No. HP" field
- **Typography:** Same font size, weight, and color as "No. HP" field
- **Spacing:** Consistent padding and margins between all fields
- **Border Radius:** Same rounded corners as "No. HP" field
- **Focus States:** Add proper focus indicators for interactive fields
- **Height:** All fields should have the same height as "No. HP" field

### Functional Requirements (CRITICAL):
- **Preserve all existing click handlers** (Customer barcode, PIN dialog)
- **Maintain all data binding** and variable assignments
- **Keep all validation logic** intact
- **Preserve all existing business logic** and callbacks
- **Ensure all fields still populate data correctly** when saving

### Layout Considerations:
- Maintain the current field order and grouping
- Keep the "Additional Information" section structure
- Preserve the button layout (PAY, CLEAR BILL)
- Ensure proper spacing between form sections

### Implementation Notes:
- Use EditText with appropriate InputType for each field
- For PIN field: set `android:focusable="false"` and `android:clickable="true"`
- Add appropriate `android:drawableStart` for left icons
- Add appropriate `android:drawableEnd` for right icons (Customer barcode)
- Use consistent styling attributes across all fields
- Consider using styles.xml for reusable field styling

### Testing Requirements:
After implementation, ensure:
- Customer barcode scanner still works
- PIN dialog still opens when PIN field is tapped
- All form data is properly saved when PAY button is pressed
- Field validation still functions correctly
- No existing functionality is broken

**Priority:** High - UI modernization while maintaining 100% functional compatibility