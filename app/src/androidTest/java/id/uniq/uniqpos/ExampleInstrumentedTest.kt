package id.uniq.uniqpos

import android.content.Context
import androidx.test.platform.app.InstrumentationRegistry
import androidx.test.ext.junit.runners.AndroidJUnit4
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.async
import kotlinx.coroutines.runBlocking

import org.junit.Test
import org.junit.runner.RunWith

import org.junit.Assert.*

/**
 * Instrumentation test, which will execute on an Android device.
 *
 * @see [Testing documentation](http://d.android.com/tools/testing)
 */
@RunWith(AndroidJUnit4::class)
class ExampleInstrumentedTest {
    @Test
    @Throws(Exception::class)
    fun useAppContext() {
        // Context of the app under test.
//        val appContext = InstrumentationRegistry.getTargetContext()

//        assertEquals("id.co.uniq.uniqpos", appContext.packageName)

        test()
        println("okkee...")
    }

    fun test() = runBlocking {
        GlobalScope.async {
            for (i in 0..10000){ }
            println("loop finish...")
        }.await()
        println("after await!!")
    }
}
