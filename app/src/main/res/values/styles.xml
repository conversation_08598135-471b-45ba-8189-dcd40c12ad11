<resources>

    <!-- Base application theme. -->
    <!--    Theme.AppCompat.Light.DarkActionBar-->
    <!--    Theme.MaterialComponents.Light.DarkActionBar-->
    <style name="AppTheme" parent="Theme.AppCompat.Light.DarkActionBar">
        <!-- Customize your theme here. -->
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>
        <item name="bottomSheetDialogTheme">@style/AppBottomSheetDialogTheme</item>
    </style>

    <style name="AppThemeMaterial" parent="Theme.MaterialComponents.Light.DarkActionBar">
        <!-- Customize your theme here. -->
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>
    </style>

    <style name="AppTheme.NoActionBarAndTitleBar">
        <item name="android:windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>

    <style name="InputStyle">
        <item name="colorAccent">@color/text_grey_light</item>
        <item name="colorControlNormal">@color/text_grey_light</item>
        <item name="colorControlActivated">@color/text_grey_light</item>
        <item name="colorControlHighlight">@color/text_grey_light</item>
    </style>

    <style name="InputStyleWhiteBack">
        <item name="colorAccent">@color/text_grey_light</item>
        <item name="colorControlNormal">@color/text_grey_light</item>
        <item name="colorControlActivated">@color/text_grey_light</item>
        <item name="colorControlHighlight">@color/text_grey_light</item>
    </style>

    <style name="RoundedEditTextTheme" parent="Theme.AppCompat">
        <item name="colorControlNormal">#808080</item> <!-- Set desired background color (grey) -->
        <item name="android:background">@drawable/rounded_edittext_background</item> <!-- Set the background drawable for rounded shape -->
    </style>

    <style name="AppTheme.NoActionBar">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
        <item name="bottomSheetDialogTheme">@style/AppBottomSheetDialogTheme</item>
    </style>

    <style name="AppTheme.AppBarOverlay" parent="ThemeOverlay.AppCompat.Dark.ActionBar" />

    <style name="AppTheme.PopupOverlay" parent="ThemeOverlay.AppCompat.Light" />

    <style name="DarkDialog" parent="Theme.AppCompat.Light.Dialog.Alert">
        <item name="android:background">@color/colorPrimary</item>
    </style>

    <!-- Splash Screen theme. -->
    <style name="SplashTheme" parent="Theme.AppCompat.NoActionBar">
        <item name="android:windowBackground">@drawable/splash_background</item>
    </style>

    <style name="CustomCheckBox">
        <item name="colorAccent">#fff</item>
        <item name="colorControlNormal">#fff</item>
        <item name="android:textColorSecondary">#fff</item>
    </style>

    <style name="custom_dialog_dark" parent="Theme.AppCompat.Light.Dialog.Alert">
        <item name="android:windowNoTitle">true</item>
        <item name="colorAccent">@color/text_grey_light</item>
        <item name="colorPrimary">@color/text_grey_light</item>
        <item name="android:textColorPrimary">@color/grey_light</item>
        <item name="android:layout_width">match_parent</item>
        <item name="android:windowBackground">@color/background</item>
    </style>

    <style name="DialogStyle" parent="Theme.Design.Light.BottomSheetDialog">
        <item name="android:windowIsFloating">false</item>
        <!--        <item name="android:statusBarColor">@android:color/transparent</item>-->
        <item name="android:windowSoftInputMode">adjustResize</item>
    </style>

    <style name="AppBottomSheetDialogTheme" parent="Theme.Design.Light.BottomSheetDialog">
        <item name="bottomSheetStyle">@style/AppModalStyle</item>
        <item name="android:windowSoftInputMode">adjustResize</item>
        <item name="android:windowIsFloating">false</item>
    </style>

    <style name="AppModalStyle" parent="Widget.Design.BottomSheet.Modal">
        <item name="android:background">@drawable/rounded_dialog</item>
    </style>

    <style name="receipt_number_orientation">
        <item name="android:orientation">vertical</item>
    </style>

    <style name="checkboxStyle">

    </style>

    <style name="FullScreenDialog" parent="Theme.AppCompat.Dialog">
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowIsFloating">false</item>
        <item name="android:windowBackground">@color/background</item>
    </style>

</resources>
