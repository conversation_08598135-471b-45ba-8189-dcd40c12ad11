<?xml version="1.0" encoding="utf-8"?>
<menu xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <group android:checkableBehavior="single">
        <!--<item-->
        <!--android:id="@+id/nav_sync"-->
        <!--android:icon="@drawable/ic_sync_black_24dp"-->
        <!--android:title="Sync" />-->
        <item
            android:id="@+id/nav_transaction"
            android:icon="@drawable/ic_monetization_on_black_24dp"
            android:title="@string/transaksi" />
        <item
            android:id="@+id/nav_transc_history"
            android:icon="@drawable/ic_history_black_24dp"
            android:title="@string/transaction_history" />
        <item
            android:id="@+id/nav_purchase"
            android:icon="@drawable/ic_shopping_cart_white_24dp"
            android:title="@string/purchase"
            android:visible="false" />
        <item
            android:id="@+id/nav_cash_in"
            android:icon="@drawable/ic_copyright_black_24dp"
            android:title="@string/cash_in"
            android:visible="false" />
        <item
            android:id="@+id/nav_cash_management"
            android:icon="@drawable/ic_baseline_how_to_vote_24px"
            android:title="@string/cash_management"
            app:actionViewClass="android.widget.TextView" />
        <item
            android:id="@+id/nav_close_cashier"
            android:icon="@drawable/ic_archive_black_24dp"
            android:title="@string/close_cashier" />
        <item
            android:id="@+id/nav_stock"
            android:icon="@drawable/ic_timeline_black_24dp"
            android:title="@string/stock"
            android:visible="false" />
        <item
            android:id="@+id/nav_setting"
            android:icon="@drawable/ic_menu_manage"
            android:title="@string/setting" />
        <item
            android:id="@+id/nav_help"
            android:icon="@drawable/ic_baseline_help_24"
            android:title="@string/help" />
    </group>

    <item android:title="@string/account">
        <menu>
            <item
                android:id="@+id/nav_change_operator"
                android:icon="@drawable/ic_swap_horiz_black_24dp"
                android:title="Logout" />
        </menu>
    </item>

</menu>
