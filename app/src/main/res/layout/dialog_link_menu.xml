<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/dialog_round"
        android:padding="9dp">

        <TextView
            android:id="@+id/txt_menu"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="8dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="#ffff"
            android:textSize="19sp"
            app:layout_constraintEnd_toStartOf="@id/img_minus"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="Yamie Panda " />

        <View
            android:id="@+id/view1"
            android:layout_width="0dp"
            android:layout_height="27dp"
            android:background="@drawable/btn_round_dark"
            app:layout_constraintBottom_toBottomOf="@id/view2"
            app:layout_constraintEnd_toEndOf="@id/img_add"
            app:layout_constraintStart_toStartOf="@id/img_minus"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/img_add"
            android:layout_width="30dp"
            android:layout_height="27dp"
            android:layout_marginRight="8dp"
            android:layout_marginBottom="5dp"
            android:padding="3dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/view1"
            app:srcCompat="@drawable/ic_add_white_24dp"
            app:tint="@color/grey_light" />

        <EditText
            android:id="@+id/edt_qty"
            android:layout_width="40dp"
            android:layout_height="0dp"
            android:background="@android:color/transparent"
            android:ems="10"
            android:focusable="false"
            android:gravity="center"
            android:inputType="number"
            android:text="1"
            android:textColor="@color/grey_light"
            android:textSize="13sp"
            app:layout_constraintBottom_toBottomOf="@+id/img_add"
            app:layout_constraintEnd_toStartOf="@+id/img_add"
            app:layout_constraintTop_toTopOf="@+id/img_add"
            tools:text="10" />

        <ImageView
            android:id="@+id/img_minus"
            android:layout_width="30dp"
            android:layout_height="27dp"
            android:layout_marginBottom="5dp"
            android:padding="1dp"
            app:layout_constraintEnd_toStartOf="@+id/edt_qty"
            app:layout_constraintTop_toTopOf="@id/view1"
            app:srcCompat="@drawable/ic_remove_white_24dp"
            app:tint="@color/grey_light" />

        <TextView
            android:id="@+id/txt_link_count"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="8dp"
            android:text="(1/2)"
            android:textColor="@color/text_orange"
            android:textSize="13sp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/txt_menu" />

        <TextView
            android:id="@+id/txt_link_info"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="8dp"
            android:text="Link Menu :"
            android:textColor="@color/text_grey_light"
            android:textSize="13sp"
            app:layout_constraintStart_toEndOf="@id/txt_link_count"
            app:layout_constraintTop_toBottomOf="@id/txt_menu" />

        <TextView
            android:id="@+id/txt_linkname"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="8dp"
            android:text="Topping"
            android:textColor="@color/text_grey_light"
            android:textSize="13sp"
            app:layout_constraintStart_toEndOf="@id/txt_link_info"
            app:layout_constraintTop_toBottomOf="@id/txt_menu" />

        <View
            android:id="@+id/view2"
            android:layout_width="0dp"
            android:layout_height="1dp"
            android:layout_marginTop="3dp"
            android:background="@color/background_selected"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/txt_linkname" />

        <TextView
            android:id="@+id/txt_info_select"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="9dp"
            android:layout_marginTop="9dp"
            android:ellipsize="end"
            android:fontFamily="@font/poppins"
            android:maxLines="1"
            android:text="@string/select_one_or_more"
            android:textColor="@color/grey_light"
            android:textSize="12sp"
            app:layout_constraintEnd_toStartOf="@id/txt_select_all"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/view2" />

        <TextView
            android:id="@+id/txt_select_all"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingLeft="9dp"
            android:paddingRight="9dp"
            android:text="+pilih semua"
            android:textColor="@color/text_orange"
            android:textSize="13sp"
            app:layout_constraintBottom_toBottomOf="@id/txt_info_select"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/txt_info_select" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recview_linkmenu"
            android:layout_width="0dp"
            android:layout_height="210dp"
            android:layout_marginStart="8dp"
            android:layout_marginTop="8dp"
            android:layout_marginEnd="8dp"
            android:layoutAnimation="@anim/layout_animation"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/txt_info_select"
            tools:listitem="@layout/list_item_link_menu" />

        <TextView
            android:id="@+id/txt_qty"
            style="?android:listSeparatorTextViewStyle"
            android:layout_width="0dp"
            android:layout_height="1dp"
            android:layout_marginStart="8dp"
            android:layout_marginEnd="8dp"
            android:background="@color/text_grey"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/recview_linkmenu" />

        <Button
            android:id="@+id/btn_previous"
            android:layout_width="110dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:background="@android:color/transparent"
            android:text="PREVIOUS"
            android:textColor="@color/grey_light"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@+id/txt_qty"
            tools:text="Previous" />

        <Button
            android:id="@+id/btn_save_new"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:background="@android:color/transparent"
            android:text="Save And New"
            android:textColor="@color/greeen_background"
            android:visibility="gone"
            app:layout_constraintEnd_toStartOf="@id/btn_next"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toEndOf="@+id/btn_previous"
            app:layout_constraintTop_toTopOf="@+id/txt_qty" />

        <View
            android:layout_width="1dp"
            android:layout_height="25dp"
            android:background="@color/text_grey_light"
            app:layout_constraintBottom_toBottomOf="@id/btn_previous"
            app:layout_constraintEnd_toStartOf="@id/btn_next"
            app:layout_constraintStart_toEndOf="@id/btn_previous"
            app:layout_constraintTop_toTopOf="@id/btn_previous" />

        <Button
            android:id="@+id/btn_next"
            android:layout_width="85dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:background="@android:color/transparent"
            android:text="NEXT"
            android:textColor="#FFFFFF"
            app:layout_constraintStart_toEndOf="@+id/btn_save_new"
            app:layout_constraintTop_toTopOf="@+id/txt_qty"
            tools:text="Next" />

        <Button
            android:id="@+id/btn_add_to_bill"
            android:layout_width="wrap_content"
            android:layout_height="35dp"
            android:layout_marginTop="8dp"
            android:layout_marginRight="9dp"
            android:background="@drawable/btn_round_green"
            android:paddingLeft="19dp"
            android:paddingRight="19dp"
            android:text="Add To Bill"
            android:textColor="@color/text_green_disable"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/txt_qty"
            app:layout_constraintTop_toTopOf="@id/btn_next" />

        <Button
            android:id="@+id/btn_add_to_bill_new"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@android:color/transparent"
            android:text="ADD TO BILL and NEW"
            android:textAllCaps="false"
            android:textColor="@color/grey_light"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="@id/recview_linkmenu"
            app:layout_constraintTop_toBottomOf="@id/btn_add_to_bill" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
