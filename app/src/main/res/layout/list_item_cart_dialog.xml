<?xml version="1.0" encoding="utf-8"?>
<layout>

    <data>

        <import type="android.view.View" />

        <variable
            name="model"
            type="com.uniq.uniqpos.model.Order" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingTop="11dp"
        android:paddingBottom="11dp"
        tools:background="@color/background">

        <TextView
            android:id="@+id/textView1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="VOID:"
            android:textColor="@color/red_background"
            android:visibility="@{model.itemVoid ? View.VISIBLE : View.GONE}"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/txt_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:textColor="@color/grey_light"
            app:layout_constraintEnd_toStartOf="@id/txt_subtotal"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/textView1"
            tools:text="[09:11:10] 1X Dinsum" />

        <TextView
            android:id="@+id/txt_subtotal"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/grey_light"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/txt_title"
            app:textCurrency="@{model.subTotal}"
            tools:text="50,000" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@{`note: ` + model.note}"
            android:textColor="@color/text_grey_light"
            android:visibility="@{model.note == null ? View.GONE : View.VISIBLE}"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/txt_title"
            tools:text="note: jagan pedes-pedes ya" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
