<?xml version="1.0" encoding="utf-8"?>
<layout>

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        android:id="@+id/main"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/background"
        tools:context=".view.productcatalogue.AddProductMainActivity">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/layout_warning"
            android:visibility="gone"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:padding="9dp"
            android:background="@color/text_orange"
            android:layout_width="0dp"
            android:layout_height="wrap_content">

            <TextView
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                android:fontFamily="@font/poppins_medium"
                android:textColor="@color/white"
                app:layout_constraintEnd_toStartOf="@id/btn_try_ocr"
                app:layout_constraintBottom_toBottomOf="@id/btn_try_ocr"
                android:text="@string/try_ocr"
                android:layout_width="0dp"
                android:layout_height="wrap_content"/>

            <Button
                android:id="@+id/btn_try_ocr"
                android:text="@string/try_ai"
                android:textAllCaps="false"
                android:textColor="@color/white"
                android:background="@drawable/btn_round_orange"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                android:layout_width="wrap_content"
                android:layout_height="29dp"/>

        </androidx.constraintlayout.widget.ConstraintLayout>

        <ScrollView
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/layout_warning"
            app:layout_constraintBottom_toTopOf="@id/btn_save"
            android:layout_width="0dp"
            android:layout_height="0dp">

            <FrameLayout
                android:id="@+id/container_form"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"/>
        </ScrollView>

        <Button
            android:id="@+id/btn_save"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/btn_round"
            android:text="@string/save"
            android:textColor="#ffff"
            app:layout_constraintBottom_toBottomOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
