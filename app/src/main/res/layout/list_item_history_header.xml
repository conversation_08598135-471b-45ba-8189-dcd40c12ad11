<?xml version="1.0" encoding="utf-8"?>
<layout>

    <data>

        <variable
            name="model"
            type="com.uniq.uniqpos.model.ViewHeader" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingStart="9dp"
        android:paddingTop="9dp"
        android:paddingEnd="16dp"
        android:paddingBottom="9dp"
        tools:background="@color/background">

        <TextView
            android:id="@+id/txt_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@{model.title}"
            android:textColor="@color/grey_light"
            android:textSize="12sp"
            app:fontFamily="@font/poppins"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="@string/invoice_no" />

        <TextView
            android:id="@+id/txt_value"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:maxLines="2"
            android:text="@{model.content}"
            android:textColor="@color/text_orange"
            android:textSize="13sp"
            app:fontFamily="@font/poppins_medium"
            app:layout_constraintStart_toStartOf="@+id/txt_title"
            app:layout_constraintTop_toBottomOf="@+id/txt_title"
            tools:text="KA67SFBHSD7" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
