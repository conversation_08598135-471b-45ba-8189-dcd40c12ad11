<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="horizontal"
    tools:context="com.uniq.uniqpos.view.transaction.MenuExtraActivity">

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rec_view_category"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        tools:listitem="@layout/list_item_category"
        android:background="@color/grey_background"
        android:layout_width="168dp"
        android:layout_height="match_parent"/>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rec_view_extra"
            android:layout_marginLeft="7dp"
            app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
            tools:listitem="@layout/list_item_menu_extra"
            app:spanCount="5"
            android:layout_weight="1"
            android:layout_width="match_parent"
            android:layout_height="0dp"/>
        <View
            android:background="@color/grey_background"
            android:layout_width="match_parent"
            android:layout_height="1dp"/>
        <RelativeLayout
            android:background="#F2F2F2"
            android:padding="5dp"
            android:layout_width="match_parent"
            android:layout_height="43dp">
            <Button
                android:id="@+id/btn_save"
                android:background="@color/orange_background"
                android:text="@string/save"
                android:layout_alignParentRight="true"
                android:textColor="@android:color/white"
                android:layout_width="wrap_content"
                android:layout_height="35dp" />
            <TextView
                android:id="@+id/txt_ket_subtotal"
                android:text="Subtotal"
                android:textSize="10sp"
                android:layout_marginLeft="9dp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content" />
            <TextView
                android:layout_below="@+id/txt_ket_subtotal"
                android:text="23,000"
                android:layout_marginLeft="9dp"
                android:textSize="15sp"
                android:textStyle="bold"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content" />
        </RelativeLayout>
    </LinearLayout>
</LinearLayout>
