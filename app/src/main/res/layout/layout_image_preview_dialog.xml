<?xml version="1.0" encoding="utf-8"?>
<layout>

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/background">

        <ImageView
            android:id="@+id/btnClose"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_margin="16dp"
            android:padding="8dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:srcCompat="@drawable/met_ic_close"
            app:tint="@color/text_grey_light" />

        <com.github.chrisbanes.photoview.PhotoView
            android:id="@+id/imagePreview"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_margin="16dp"
            app:layout_constraintBottom_toTopOf="@id/sourceOptions"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/btnClose" />

        <TextView
            android:text="@string/select_image_edit_product"
            app:layout_constraintTop_toBottomOf="@id/imagePreview"

            app:layout_constraintStart_toStartOf="parent"
            android:fontFamily="@font/poppins_medium"
            app:layout_constraintEnd_toEndOf="parent"
            android:textColor="@color/text_grey_light"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>

        <LinearLayout
            android:id="@+id/sourceOptions"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="16dp"
            android:orientation="horizontal"
            android:gravity="center"
            app:layout_constraintBottom_toBottomOf="parent">

            <LinearLayout
                android:id="@+id/btnCamera"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:orientation="vertical"
                android:padding="16dp">

                <ImageView
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:src="@drawable/camera_24"
                    app:tint="@color/text_grey_light" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:text="Camera"
                    android:textColor="@color/text_grey_light" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/btnGallery"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:orientation="vertical"
                android:padding="16dp">

                <ImageView
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:src="@drawable/baseline_photo_library_24"
                    app:tint="@color/text_grey_light" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:text="Gallery"
                    android:textColor="@color/text_grey_light" />
            </LinearLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/btnAiGenerate"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:orientation="vertical"
                android:padding="16dp">

                <ImageView
                    android:id="@+id/imgAiGen"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:layout_centerHorizontal="true"
                    android:src="@drawable/baseline_auto_fix_high_24"
                    app:tint="@color/text_grey_light" />

                <TextView
                    android:layout_below="@id/imgAiGen"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    app:layout_constraintTop_toBottomOf="@id/imgAiGen"
                    app:layout_constraintStart_toStartOf="parent"
                    android:text="AI Generate"
                    android:textColor="@color/text_grey_light" />

                <TextView
                    android:id="@+id/txtBeta"
                    android:gravity="right"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="4dp"
                    android:background="@drawable/bg_beta_tag"
                    android:paddingHorizontal="4dp"
                    android:paddingVertical="2dp"
                    android:layout_marginTop="3dp"
                    app:layout_constraintTop_toTopOf="@id/imgAiGen"
                    app:layout_constraintStart_toEndOf="@id/imgAiGen"
                    android:text="BETA"
                    android:textColor="#FFFFFF"
                    android:textSize="7sp"
                    android:textStyle="bold" />

            </androidx.constraintlayout.widget.ConstraintLayout>

        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
