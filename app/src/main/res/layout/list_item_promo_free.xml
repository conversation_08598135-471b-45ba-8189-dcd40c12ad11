<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>
        <variable
            name="model"
            type="com.uniq.uniqpos.model.PromotionFree" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingTop="5dp"
        android:paddingBottom="1dp"
        tools:background="@color/background">

        <TextView
            android:id="@+id/textView1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:maxLines="1"
            android:text="@{model.productName}"
            android:textColor="@color/grey_light"
            app:layout_constraintEnd_toStartOf="@id/img_min"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="HD Ice Tea" />

        <TextView
            android:id="@+id/textView2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/text_grey_light"
            android:textSize="12sp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/textView1"
            app:textCurrency="@{model.price}"
            tools:text="Rp9,000" />

        <View
            android:id="@+id/view1"
            android:layout_width="0dp"
            android:layout_height="27dp"
            android:layout_marginRight="9dp"
            android:background="@drawable/btn_round_dark"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/txt_add_all"
            app:layout_constraintStart_toStartOf="@id/img_min"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/img_add"
            android:layout_width="wrap_content"
            android:layout_height="19dp"
            android:paddingLeft="9dp"
            android:paddingRight="9dp"
            android:layout_marginRight="9dp"
            app:layout_constraintBottom_toBottomOf="@id/txt_qty"
            app:layout_constraintEnd_toStartOf="@id/txt_add_all"
            app:layout_constraintTop_toTopOf="@id/txt_qty"
            app:srcCompat="@drawable/ic_add_white_24dp"
            app:tint="@color/grey_light" />

        <TextView
            android:id="@+id/txt_qty"
            android:layout_width="23dp"
            android:layout_height="wrap_content"
            android:fontFamily="@font/poppins_medium"
            android:gravity="center_horizontal"
            android:text="@{String.valueOf(model.qtySelected)}"
            android:textColor="@color/grey_light"
            app:layout_constraintBottom_toBottomOf="@id/view1"
            app:layout_constraintEnd_toStartOf="@id/img_add"
            app:layout_constraintTop_toTopOf="@id/view1"
            tools:text="3" />

        <ImageView
            android:id="@+id/img_min"
            android:layout_width="wrap_content"
            android:layout_height="19dp"
            android:paddingLeft="9dp"
            android:paddingRight="9dp"
            app:layout_constraintBottom_toBottomOf="@id/txt_qty"
            app:layout_constraintEnd_toStartOf="@id/txt_qty"
            app:layout_constraintTop_toTopOf="@id/txt_qty"
            app:srcCompat="@drawable/ic_remove_white_24dp"
            app:tint="@color/grey_light" />

        <View
            android:layout_width="0dp"
            android:layout_height="1dp"
            android:layout_marginTop="9dp"
            android:background="#2f3a4a"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/textView2" />

        <View
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="@id/txt_add_all"
            android:background="@drawable/btn_round_dark"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:layout_width="0dp"
            android:layout_height="27dp"/>

        <TextView
            android:id="@+id/txt_add_all"
            android:text="Add All"
            android:textSize="11sp"
            android:paddingLeft="9dp"
            android:paddingRight="9dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:textColor="@color/text_grey_light"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
