<?xml version="1.0" encoding="utf-8"?>
<layout>

    <androidx.drawerlayout.widget.DrawerLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        android:id="@+id/drawer_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fitsSystemWindows="true"
        tools:openDrawer="start">

        <include
            android:id="@+id/inc_app_bar"
            layout="@layout/app_bar_main"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

        <com.google.android.material.navigation.NavigationView
            android:id="@+id/nav_view"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_gravity="start"
            android:background="@color/drawer_background"
            android:fitsSystemWindows="true"
            android:theme="@style/ThemeOverlay.AppCompat.Light"
            app:headerLayout="@layout/nav_header_main"
            app:itemIconTint="@color/drawer_color"
            app:itemTextColor="@color/drawer_color"
            app:menu="@menu/activity_main_drawer">

            <TextView
                android:id="@+id/txt_nav"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="bottom"
                android:gravity="center_horizontal"
                android:paddingBottom="7dp"
                android:text="powered by UNIQ"
                android:textColor="#586065"
                android:textSize="11sp" />
        </com.google.android.material.navigation.NavigationView>

    </androidx.drawerlayout.widget.DrawerLayout>
</layout>
