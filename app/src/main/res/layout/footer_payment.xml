<?xml version="1.0" encoding="utf-8"?>
<layout>

    <data>
        <variable
            name="viewmodel"
            type="com.uniq.uniqpos.view.payment.PaymentViewModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:tools="http://schemas.android.com/tools"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        android:padding="9dp"
        tools:background="@color/background"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <TextView
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:text="@string/change_money"
            android:fontFamily="@font/poppins"
            android:textColor="@color/text_grey_light"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>

        <TextView
            android:id="@+id/txt_change"
            tools:text="Rp5,000"
            android:text="@{viewmodel.changeTotal}"
            android:fontFamily="@font/poppins_medium"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:textSize="16sp"
            android:textColor="@color/text_grey_light"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>

        <Button
            android:id="@+id/btn_finish_payment"
            android:text="@string/finish_payment"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/txt_change"
            android:layout_marginTop="9dp"
            android:textColor="@color/white"
            android:background="@drawable/btn_round_orange"
            android:layout_width="0dp"
            android:layout_height="wrap_content" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
