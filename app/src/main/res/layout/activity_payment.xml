<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/background">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="9dp"
            android:baselineAligned="false"
            android:orientation="horizontal"
            android:paddingTop="16dp">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/poppins_medium"
                    android:paddingLeft="16dp"
                    android:text="@string/choose_pay_method"
                    android:textColor="@color/text_grey_light" />

                <ScrollView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <androidx.recyclerview.widget.RecyclerView
                            android:id="@+id/rec_view_payment"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:nestedScrollingEnabled="false"
                            tools:itemCount="2"
                            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                            tools:listitem="@layout/list_item_payment" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="16dp"
                            android:fontFamily="@font/poppins_medium"
                            android:paddingLeft="16dp"
                            android:text="@string/send_receipt"
                            android:textColor="@color/grey_light" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:paddingLeft="16dp"
                            android:paddingRight="16dp"
                            android:text="@string/send_receipt_info"
                            android:textColor="@color/text_grey_light"
                            android:textSize="13sp" />

                        <EditText
                            android:id="@+id/edt_receipt_send"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:hint="@string/email_or_wa"
                            android:paddingLeft="16dp"
                            android:textColor="@color/text_grey_light"
                            android:textColorHint="@color/text_grey_light"
                            android:textSize="14sp"
                            android:theme="@style/InputStyle"
                            android:visibility="gone" />

                        <RelativeLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content">
                            <AutoCompleteTextView
                                android:id="@+id/edt_receipt_address"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:completionThreshold="4"
                                android:hint="@string/email_or_wa"
                                android:paddingLeft="16dp"
                                android:paddingRight="29dp"
                                android:textColor="@color/text_grey_light"
                                android:textColorHint="@color/text_grey_light"
                                android:textSize="14sp"
                                android:theme="@style/InputStyle" />

                            <ImageView
                                android:paddingTop="7dp"
                                android:id="@+id/img_clear_receipt"
                                app:srcCompat="@drawable/ic_close"
                                android:layout_alignParentEnd="true"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"/>
                        </RelativeLayout>


                        <LinearLayout
                            android:id="@+id/layout_paste"
                            style="@style/receipt_number_orientation"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="center_vertical"
                            android:paddingLeft="16dp"
                            android:paddingTop="9dp"
                            android:visibility="gone"
                            tools:visibility="visible">

                            <RelativeLayout
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content">

                                <ImageView
                                    android:id="@+id/imageView1"
                                    android:layout_width="15dp"
                                    android:layout_height="15dp"
                                    android:layout_marginTop="3dp"
                                    app:srcCompat="@drawable/ic_content_paste_grey_24dp"
                                    app:tint="@color/greeen_background" />

                                <TextView
                                    android:id="@+id/txt_paste_info"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_toRightOf="@id/imageView1"
                                    android:paddingLeft="5dp"
                                    android:textColor="@color/greeen_background"
                                    tools:text="paste the number    " />
                            </RelativeLayout>

                            <TextView
                                android:id="@+id/txt_clip_number"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:textColor="@color/text_grey_light"
                                android:textSize="13sp"
                                tools:text="6285752257881" />
                        </LinearLayout>
                    </LinearLayout>
                </ScrollView>
            </LinearLayout>

            <ScrollView
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_horizontal"
                    android:orientation="vertical"
                    android:paddingLeft="13dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:text="Grand Total"
                        android:textColor="@color/text_grey_light"
                        android:textSize="19sp"
                        app:fontFamily="@font/poppins" />

                    <TextView
                        android:id="@+id/txtGrandTotal"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="26dp"
                        android:layout_marginRight="26dp"
                        android:gravity="center"
                        android:maxLines="1"
                        android:textColor="@color/text_grey_light"
                        android:textSize="27sp"
                        app:autoSizeTextType="uniform"
                        app:fontFamily="@font/poppins_semibold"
                        tools:text="230.000" />

                    <Button
                        android:id="@+id/btn_split"
                        android:layout_width="130dp"
                        android:layout_height="37dp"
                        android:background="@drawable/btn_round_green"
                        android:text="Split Bill"
                        android:textColor="#ffff" />

                    <TextView
                        android:id="@+id/txt_change_ket"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="13dp"
                        android:text="Kembalian"
                        android:textColor="@color/text_grey_light"
                        android:textColorHint="@color/text_grey_light"
                        android:textSize="17sp" />

                    <TextView
                        android:id="@+id/txt_change"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="0"
                        android:textColor="@color/text_grey_light"
                        android:textSize="19sp" />

                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="@dimen/input_payment_widht"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="17dp"
                        android:textColorHint="@color/text_grey_light"
                        android:theme="@style/InputStyle">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/edt_pay"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="center_horizontal"
                            android:hint="@string/jumlah"
                            android:inputType="number"
                            android:textColor="@color/text_grey_light" />
                    </com.google.android.material.textfield.TextInputLayout>

                    <TextView
                        android:id="@+id/txt_card_detail"
                        android:layout_width="@dimen/input_payment_widht"
                        android:layout_height="wrap_content"
                        android:text="Card Detail"
                        android:textColor="@color/text_grey_light"
                        android:textSize="11sp"
                        android:visibility="gone"
                        tools:visibility="visible" />

                    <RelativeLayout
                        android:id="@+id/layout_card_info"
                        android:layout_width="@dimen/input_payment_widht"
                        android:layout_height="wrap_content"
                        android:paddingBottom="9dp"
                        android:visibility="gone"
                        tools:visibility="visible">

                        <TextView
                            android:id="@+id/txt_card_info"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_toLeftOf="@id/txt_change_card"
                            android:ellipsize="end"
                            android:maxLines="1"
                            android:text="information not provided"
                            android:textColor="@color/grey_light"
                            android:textSize="13sp"
                            tools:text="Gopay - 34672" />

                        <TextView
                            android:id="@+id/txt_change_card"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentRight="true"
                            android:paddingLeft="9dp"
                            android:text="ubah"
                            android:textColor="@color/greeen_background"
                            android:textSize="13sp" />
                    </RelativeLayout>

                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/layout_due_date"
                        android:layout_width="230dp"
                        android:layout_height="wrap_content"
                        android:textColorHint="@color/text_grey_light"
                        android:theme="@style/InputStyle"
                        android:visibility="gone"
                        tools:visibility="visible">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/edt_due_date"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="center_horizontal"
                            android:hint="Due Date"
                            android:textColor="@color/text_grey_light" />
                    </com.google.android.material.textfield.TextInputLayout>

                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/layout_pay_info"
                        android:layout_width="230dp"
                        android:layout_height="wrap_content"
                        android:textColorHint="@color/text_grey_light"
                        android:theme="@style/InputStyle"
                        android:visibility="gone"
                        tools:visibility="visible">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/edt_pay_info"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="center_horizontal"
                            android:hint="informasi tambahan"
                            android:maxLength="50"
                            android:textColor="@color/text_grey_light" />
                    </com.google.android.material.textfield.TextInputLayout>

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="9dp"
                        android:text="opsi pembayaran"
                        android:textColor="@color/text_grey_light"
                        app:fontFamily="@font/poppins" />

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/recview_payment_nominal"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_horizontal"
                        app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
                        app:spanCount="@integer/payment_span_count"
                        tools:listitem="@layout/item_payment_nominal" />
                </LinearLayout>
            </ScrollView>
        </LinearLayout>

    </RelativeLayout>
</layout>
