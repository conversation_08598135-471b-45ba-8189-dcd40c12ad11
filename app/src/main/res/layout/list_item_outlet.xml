<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <data>
        <variable
            name="model"
            type="com.uniq.uniqpos.data.remote.model.Outlet"/>
    </data>

    <LinearLayout
        android:padding="16dp"
        android:orientation="horizontal"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <ImageView
            android:src="@drawable/icon_shop_outline"
            android:background="@android:color/transparent"
            android:layout_width="70dp"
            android:layout_height="70dp" />
        <LinearLayout
            android:paddingLeft="9dp"
            android:layout_gravity="center_vertical"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">
            <TextView
                android:text="@{model.name}"
                tools:text="Yamie Panda"
                android:textColor="@color/text_grey_light"
                android:textSize="23sp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content" />
            <TextView
                android:text="@{model.address}"
                tools:text="Gejayan"
                android:textSize="16sp"
                android:textColor="@color/text_grey_light"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content" />
        </LinearLayout>
    </LinearLayout>
</layout>
