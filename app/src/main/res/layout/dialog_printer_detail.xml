<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:background="@color/background">

    <View
        android:id="@+id/view1"
        android:layout_width="50dp"
        android:layout_height="1dp"
        android:layout_marginTop="7dp"
        android:background="@color/text_grey_light"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/view2"
        android:layout_width="0dp"
        android:layout_height="1dp"
        android:layout_marginTop="1dp"
        android:background="@color/text_grey_light"
        app:layout_constraintEnd_toEndOf="@id/view1"
        app:layout_constraintStart_toStartOf="@id/view1"
        app:layout_constraintTop_toBottomOf="@id/view1" />

    <ImageView
        android:id="@+id/img_close"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:paddingLeft="9dp"
        android:paddingRight="9dp"
        android:paddingBottom="9dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/textView1"
        app:srcCompat="@drawable/ic_clear_white_24dp"
        app:tint="@color/grey_light" />

    <TextView
        android:id="@+id/textView1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:fontFamily="@font/poppins_medium"
        android:paddingTop="16dp"

        android:text="PRINTER SETTING"
        android:textColor="@color/grey_light"
        android:textSize="16sp"
        app:layout_constraintStart_toEndOf="@id/img_close"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/txt_printer_info"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/text_grey_light"
        app:layout_constraintStart_toStartOf="@id/textView1"
        app:layout_constraintTop_toBottomOf="@id/textView1"
        tools:text="RPP-32001" />

    <Button
        android:id="@+id/btn_save"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@android:color/transparent"
        android:paddingRight="9dp"
        android:text="@string/save_setting"
        android:textColor="@color/greeen_background"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/textView1" />

    <FrameLayout
        android:id="@+id/container"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/txt_printer_info" />

</androidx.constraintlayout.widget.ConstraintLayout>