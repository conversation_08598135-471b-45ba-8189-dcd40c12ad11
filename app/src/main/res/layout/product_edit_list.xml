<?xml version="1.0" encoding="utf-8"?>
<layout>

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        android:id="@+id/main"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/background"
        tools:context=".view.productcatalogue.ocr.ProductEditList">

        <TextView
            android:id="@+id/txt_info"
            android:text="Menu successfully extracted, you can make any changes before saving"
            android:textColor="@color/text_orange"
            android:background="@color/colorPrimaryDark"
            android:padding="9dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            android:layout_width="0dp"
            android:layout_height="wrap_content"/>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rec_view_menu"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            tools:listitem="@layout/list_item_product_edit"
            app:layout_constraintTop_toBottomOf="@id/txt_info"
            app:layout_constraintBottom_toTopOf="@id/btn_save"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            android:layout_width="0dp"
            android:layout_height="0dp"/>

        <com.uniq.materialanimation.ButtonLoading
            android:id="@+id/btn_save"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            app:buttonColor="@color/colorPrimaryDark"
            app:buttonText="@string/save_all"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:textColor="@color/white" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
