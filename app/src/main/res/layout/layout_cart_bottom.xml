<?xml version="1.0" encoding="utf-8"?>
<layout>

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        android:id="@+id/layout_cart"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/round_stroke_semi_dark"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        tools:visibility="visible">

        <TextView
            android:id="@+id/txt_qty_order"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_margin="8dp"
            android:gravity="center_vertical"
            android:text="0"
            android:textColor="@color/grey_light"
            android:textSize="12sp"
            app:layout_constraintBottom_toBottomOf="@id/imgarrow"
            app:layout_constraintEnd_toStartOf="@id/imgarrow"
            app:layout_constraintTop_toTopOf="@id/imgarrow"
            tools:text="3 items" />

        <TextView
            android:id="@+id/txt_grandtotal"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:ellipsize="end"
            android:fontFamily="@font/poppins_medium"
            android:maxLines="1"
            android:text="0"
            android:textColor="@color/text_orange"
            android:textSize="12sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/txt_qty_order"
            app:layout_constraintStart_toStartOf="@id/txt_ket_grandtotal"
            app:layout_constraintTop_toBottomOf="@+id/txt_ket_grandtotal"
            tools:text="Rp 39,500" />

        <TextView
            android:id="@+id/txt_ket_grandtotal"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="16dp"
            android:layout_marginTop="8dp"
            android:text="Grand Total"
            android:textColor="@color/grey_light"
            android:textSize="11sp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/viewtop" />

        <View
            android:id="@+id/viewtop"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="@android:color/black"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <Button
            android:id="@+id/btn_scan"
            android:layout_width="wrap_content"
            android:layout_height="35dp"
            android:layout_marginRight="9dp"
            android:background="@drawable/btn_stroke_grey"
            android:paddingLeft="9dp"
            android:paddingRight="9dp"
            android:text="Scan"
            android:textSize="11sp"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/imgarrow"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/imgarrow"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginRight="9dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:srcCompat="@drawable/ic_baseline_shopping_basket_24"
            app:tint="@color/text_grey_light" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
