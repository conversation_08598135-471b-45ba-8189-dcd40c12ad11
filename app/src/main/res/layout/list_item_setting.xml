<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="model"
            type="com.uniq.uniqpos.model.Setting" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="76dp"
        tools:background="@color/background">

        <ImageView
            android:id="@+id/imageView1"
            android:layout_width="27dp"
            android:layout_height="27dp"
            android:layout_marginLeft="16dp"
            app:imgDrawable="@{model.icon}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:src="@drawable/ic_printer_orange" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="19dp"
            android:paddingRight="16dp"
            android:text="@{model.title}"
            android:textColor="@color/grey_light"
            android:textSize="17sp"
            app:fontFamily="@font/poppins"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@+id/imageView1"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="Mode Layout" />

        <View
            android:layout_width="0dp"
            android:layout_height="1dp"
            android:background="@color/text_grey"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
