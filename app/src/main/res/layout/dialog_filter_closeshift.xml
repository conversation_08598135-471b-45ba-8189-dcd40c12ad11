<?xml version="1.0" encoding="utf-8"?>
<layout>

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/background"
        android:padding="16dp">

        <TextView
            android:id="@+id/textView1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_margin="16dp"
            android:text="Filter Close Shift Report"
            android:textAppearance="@style/TextAppearance.AppCompat.Medium"
            android:textColor="@color/grey_light"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.weiwangcn.betterspinner.library.material.MaterialBetterSpinner
            android:id="@+id/spin_filter"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_margin="16dp"
            android:hint="@string/cashier"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/textView1"
            app:met_baseColor="@color/text_grey_light"
            app:met_floatingLabel="normal"
            app:met_floatingLabelTextColor="@color/text_grey_light"
            app:met_textColor="@color/text_grey_light"
            app:met_textColorHint="@color/text_grey_light"
            app:met_underlineColor="@color/text_grey_light" />

        <TextView
            android:text="@string/filter_close_shift_cashier_info"
            android:textSize="13sp"
            android:textStyle="italic"
            android:textColor="@color/text_grey_light"
            android:layout_width="0dp"
            android:layout_marginLeft="16dp"
            android:layout_height="wrap_content"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/spin_filter" />

        <Button
            android:id="@+id/btn_apply_filter"
            android:layout_width="wrap_content"
            android:layout_height="38dp"
            android:layout_marginTop="27dp"
            android:layout_marginRight="16dp"
            android:background="@drawable/btn_grey_effect"
            android:paddingLeft="9dp"
            android:paddingRight="9dp"
            android:text="Filter"
            android:textColor="@color/grey_light"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/spin_filter" />

        <Button
            android:id="@+id/btn_reset"
            android:layout_width="wrap_content"
            android:layout_height="38dp"
            android:layout_marginRight="27dp"
            android:background="@drawable/btn_grey_effect"
            android:text="Reset Filter"
            android:textColor="@color/grey_original"
            app:layout_constraintEnd_toStartOf="@id/btn_apply_filter"
            app:layout_constraintTop_toTopOf="@id/btn_apply_filter" />

        <Button
            android:id="@+id/btn_close"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginRight="27dp"
            android:background="@drawable/btn_grey_effect"
            android:text="Close"
            android:textColor="@color/grey_original"
            android:visibility="gone"
            app:layout_constraintEnd_toStartOf="@id/btn_reset"
            app:layout_constraintTop_toTopOf="@id/btn_apply_filter" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
