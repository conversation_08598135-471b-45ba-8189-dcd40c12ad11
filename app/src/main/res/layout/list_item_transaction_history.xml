<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>
        <variable
            name="model"
            type="com.uniq.uniqpos.data.local.entity.SalesEntity" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_rounded_dark"
        android:layout_marginBottom="8dp"
        android:padding="13dp"
        android:clickable="true"
        android:focusable="true"
        android:foreground="?android:attr/selectableItemBackground">

        <TextView
            android:id="@+id/txt_transaction_id"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            tools:textColor="@color/red_background"
            android:textSize="14sp"
            android:textStyle="bold"
            android:fontFamily="@font/poppins_semibold"
            android:text="@{model.displayNota}"
            android:textColor="@{model.status.equalsIgnoreCase(`Refund`) ? @color/red_background : @color/text_white}"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="TRX-20240304-001" />

        <TextView
            android:id="@+id/txt_customer_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:textColor="@color/text_grey_light"
            android:textSize="13sp"
            android:text="@{model.customer ?? `Guest`}"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/txt_transaction_id"
            tools:text="John Doe" />

        <TextView
            android:id="@+id/txt_payment_method"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="1dp"
            android:textColor="@color/text_grey_light"
            android:textSize="13sp"
            android:text="@{model.payment}"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/txt_customer_name"
            tools:text="Cash" />

        <TextView
            android:id="@+id/txt_amount"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/orange"
            android:textSize="14sp"
            android:textStyle="bold"
            android:fontFamily="@font/poppins_medium"
            app:textCurrency="@{model.grandTotal}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="$124.99" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
