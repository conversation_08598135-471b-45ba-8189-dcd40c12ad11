<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>
        <variable
            name="model"
            type="com.uniq.uniqpos.data.local.entity.ProductEntity"/>
    </data>

    <RelativeLayout
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="90dp">

        <ImageView
            android:id="@+id/img_product"
            app:imgUrlText="@{model.photo}"
            app:name="@{model.name}"
            tools:src="@drawable/ayam"
            android:layout_margin="2dp"
            android:scaleType="centerCrop"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

        <TextView
            android:text="@{model.name}"
            tools:text="Paket Panik 2"
            android:layout_margin="2dp"
            android:padding="3dp"
            app:fontFamily="@font/poppins"
            android:textColor="@android:color/black"
            android:background="#A6FFFFFF"
            android:layout_alignParentBottom="true"
            android:textSize="13sp"
            android:ellipsize="end"
            android:maxLines="1"
            android:gravity="center_horizontal"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

    </RelativeLayout>
</layout>
