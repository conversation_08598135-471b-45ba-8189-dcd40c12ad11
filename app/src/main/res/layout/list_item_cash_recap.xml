<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>
        <variable
            name="model"
            type="com.uniq.uniqpos.data.local.entity.CashRecapEntity"/>
    </data>
    <LinearLayout
        android:id="@+id/layout_item"
        android:orientation="horizontal"
        android:paddingTop="5dp"
        android:paddingBottom="5dp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <TextView
            android:id="@+id/txt_num"
            tools:text="13"
            android:paddingLeft="5dp"
            android:textColor="@color/grey_light"
            app:fontFamily="@font/poppins"
            android:layout_weight="1"
            android:layout_width="0dp"
            android:layout_height="wrap_content" />
        <TextView
            android:text="@{model.employeeName}"
            tools:text="Annas"
            android:textColor="@color/grey_light"
            app:fontFamily="@font/poppins"
            android:layout_weight="4"
            android:layout_width="0dp"
            android:layout_height="wrap_content" />
        <TextView
            app:timeMillisToDate="@{model.timeCreated}"
            app:format="@{`dd MMM yyyy HH:mm`}"
            tools:text="09 Nov 2018 14:50"
            android:textColor="@color/grey_light"
            app:fontFamily="@font/poppins"
            android:layout_weight="5"
            android:layout_width="0dp"
            android:layout_height="wrap_content" />
    </LinearLayout>
</layout>
