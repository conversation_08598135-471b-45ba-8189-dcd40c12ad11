<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/background"
        tools:context="com.uniq.uniqpos.view.setting.printer.AddPrinterActivity">

        <com.uniq.uniqpos.util.RippleBackground
            android:id="@+id/ripple"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:rb_color="@color/text_grey_light"
            app:rb_duration="5000"
            app:rb_radius="32dp"
            app:rb_rippleAmount="4"
            app:rb_scale="6">

            <ImageView
                android:id="@+id/iconPrinter"
                android:layout_width="50dp"
                android:layout_height="50dp"
                android:layout_centerInParent="true"
                android:padding="9dp"
                app:srcCompat="@drawable/ic_print_black_24dp"
                app:tint="#BDBDBD" />
        </com.uniq.uniqpos.util.RippleBackground>

        <LinearLayout
            android:id="@+id/layout_progress"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="16dp"
            android:layout_marginTop="9dp"
            android:layout_marginRight="16dp"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:visibility="gone"
            tools:visibility="visible">

            <ProgressBar
                android:id="@+id/progress_bar"
                style="?android:attr/progressBarStyleSmall"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content" />

            <TextView
                android:id="@+id/txt_info"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="9dp"
                android:text="Searching..."
                android:textColor="@color/text_grey_light" />
        </LinearLayout>

        <Button
            android:id="@+id/btn_add_manual"
            android:layout_width="wrap_content"
            android:layout_height="30dp"
            android:layout_alignParentRight="true"
            android:layout_marginTop="5dp"
            android:layout_marginRight="16dp"
            android:background="@drawable/btn_round_blue"
            android:text="Add Manual"
            android:textAllCaps="false"
            android:textColor="@color/white"
            android:textSize="12sp"
            android:visibility="gone"
            tools:visibility="visible" />

        <RelativeLayout
            android:id="@+id/layout_warning"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@id/layout_progress"
            android:layout_centerHorizontal="true"
            android:layout_margin="16dp"
            android:background="@color/background_selected"
            android:paddingBottom="9dp">

            <ImageView
                android:id="@+id/img_close"
                android:layout_width="17dp"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_marginTop="3dp"
                android:layout_marginRight="13dp"
                app:srcCompat="@drawable/met_ic_close"
                app:tint="@color/grey_light" />

            <TextView
                android:id="@+id/txt_warning"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="9dp"
                android:layout_toLeftOf="@id/img_close"
                android:gravity="center_horizontal"
                android:paddingLeft="9dp"
                android:paddingRight="9dp"
                android:text="jika printer bluetooth anda tidak terdaftar pada list di bawah, silahkan lakukan pairing terlebih dahulu"
                android:textColor="@color/grey_light" />

            <TextView
                android:id="@+id/txt_pairing"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@id/txt_warning"
                android:layout_centerHorizontal="true"
                android:text="PAIRING PRINTER"
                android:textColor="@color/greeen_background" />

        </RelativeLayout>


        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recviewPrinter"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/layout_warning"
            android:padding="16dp"
            app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
            app:spanCount="1"
            tools:listitem="@layout/list_item_printer" />
    </RelativeLayout>
</layout>
