<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>
        <variable
            name="model"
            type="com.uniq.uniqpos.data.local.entity.PendingPrintEntity"/>
    </data>

    <androidx.cardview.widget.CardView
        android:foreground="?android:attr/selectableItemBackground"
        android:clickable="true"
        app:cardBackgroundColor="@color/background_selected"
        app:cardUseCompatPadding="true"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <RelativeLayout
            android:padding="9dp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">
            <TextView
                android:id="@+id/txt_print"
                android:text="@{model.beautifyDisplay}"
                android:textColor="@color/grey_light"
                tools:text="Print Data"
                android:maxLines="9"
                android:ellipsize="end"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />
            <Button
                android:id="@+id/btn_delete"
                android:text="@string/delete"
                android:textColor="@color/red_background"
                android:layout_below="@+id/txt_print"
                android:layout_marginTop="33dp"
                android:minWidth="0dp"
                android:background="@android:color/transparent"
                android:layout_width="wrap_content"
                android:layout_height="23dp" />
            <Button
                android:id="@+id/btn_reprint"
                android:text="Reprint"
                android:textColor="@color/grey_light"
                android:layout_marginTop="33dp"
                android:layout_below="@+id/txt_print"
                android:layout_toRightOf="@+id/btn_delete"
                android:layout_marginLeft="16dp"
                android:minWidth="0dp"
                android:background="@android:color/transparent"
                android:layout_width="wrap_content"
                android:layout_height="23dp" />
            <TextView
                android:text="@{model.printerName}"
                tools:text="Printer Kitchen"
                android:textSize="12sp"
                android:textColor="@color/grey_light"
                android:layout_marginTop="37dp"
                android:layout_below="@+id/txt_print"
                android:layout_alignParentRight="true"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content" />
        </RelativeLayout>

    </androidx.cardview.widget.CardView>
</layout>
