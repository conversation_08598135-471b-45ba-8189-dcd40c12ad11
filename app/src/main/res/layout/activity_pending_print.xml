<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/background"
        tools:context="com.uniq.uniqpos.view.pendingprint.PendingPrintActivity">

        <include
            android:id="@+id/inc_lay_no_data"
            layout="@layout/layout_no_data" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rec_view_pendingprint"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:spanCount="@integer/pending_print_span"
            tools:listitem="@layout/list_item_pending_print" />


    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
