<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/background"
        tools:context="com.uniq.uniqpos.view.payment.SplitBillActivity">

        <TextView
            android:id="@+id/textView2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_margin="8dp"
            android:text="@string/choose_menu_to_split"
            android:textColor="@color/grey_light"
            app:fontFamily="@font/poppins_medium"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recview_menu"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginLeft="8dp"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/guideline3"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/textView2"
            tools:listitem="@layout/list_item_menu_card" />

        <View
            android:layout_width="1dp"
            android:layout_height="0dp"
            android:background="@color/background_selected"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/guideline3"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/guideline3"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintGuide_end="@dimen/bill_width" />


        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recview_bill"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            app:layout_constraintBottom_toTopOf="@+id/layout_tax"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="@+id/guideline3"
            app:layout_constraintTop_toTopOf="parent"
            tools:listitem="@layout/list_item_bill" />

<!--        <android.support.v7.widget.RecyclerView-->
<!--            android:id="@+id/recview_tax"-->
<!--            android:layout_width="0dp"-->
<!--            android:layout_height="75dp"-->
<!--            android:visibility="gone"-->
<!--            app:layoutManager="android.support.v7.widget.LinearLayoutManager"-->
<!--            app:layout_constraintBottom_toTopOf="@+id/layoutPayment"-->
<!--            app:layout_constraintEnd_toEndOf="parent"-->
<!--            app:layout_constraintStart_toStartOf="@+id/guideline3"-->
<!--            tools:listitem="@layout/list_item_tax" />-->

        <include
            android:id="@+id/layout_tax"
            layout="@layout/layout_tax"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            app:layout_constraintBottom_toTopOf="@+id/layoutPayment"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="@+id/guideline3" />

        <View
            android:id="@+id/layoutPayment"
            android:layout_width="0dp"
            android:layout_height="40dp"
            android:background="@color/greeen_background"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="@+id/guideline3" />

        <TextView
            android:id="@+id/txt_grandtotal"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginRight="5dp"
            android:text="0"
            android:textColor="#ffff"
            app:fontFamily="@font/poppins_semibold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/imgarrow"
            app:layout_constraintTop_toTopOf="@+id/layoutPayment"
            tools:text="225,000" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="8dp"
            android:text="@string/payment"
            android:textColor="#ffff"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="@+id/guideline3"
            app:layout_constraintTop_toTopOf="@+id/layoutPayment" />

        <ImageView
            android:id="@+id/imgarrow"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginRight="3dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/layoutPayment"
            app:srcCompat="@drawable/ic_keyboard_arrow_right_white_24dp" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
