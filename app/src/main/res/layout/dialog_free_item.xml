<?xml version="1.0" encoding="utf-8"?>
<layout>

    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        android:orientation="vertical"
        xmlns:tools="http://schemas.android.com/tools"
        android:padding="16dp"
        android:background="@color/background"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <com.google.android.material.textfield.TextInputLayout
            android:textColorHint="@color/text_grey_light"
            android:theme="@style/InputStyle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/edt_free_info"
                android:hint="@string/additional_information"
                android:inputType="textCapSentences"
                android:textColor="@color/text_grey_light"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />
        </com.google.android.material.textfield.TextInputLayout>

        <TextView
            android:layout_marginTop="11dp"
            android:textColor="@color/grey_light"
            android:text="Quick Options :"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>

        <HorizontalScrollView
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recview_suggestion"
                android:layout_marginTop="9dp"
                app:spanCount="@integer/info_span_count"
                app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
                tools:listitem="@layout/list_item_info"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>
        </HorizontalScrollView>

    </LinearLayout>
</layout>
