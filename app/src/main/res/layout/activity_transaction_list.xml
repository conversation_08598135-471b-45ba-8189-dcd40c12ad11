<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/background">

        <include
            android:id="@+id/inc_lay_no_data"
            layout="@layout/layout_no_data" />

        <TextView
            android:id="@+id/textView1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginRight="11dp"
            android:fontFamily="@font/poppins"
            android:text="view setting: "
            android:textColor="@color/text_grey_light"
            android:textSize="13sp"
            app:layout_constraintBottom_toBottomOf="@id/btn_view_list"
            app:layout_constraintEnd_toStartOf="@id/btn_view_list"
            app:layout_constraintTop_toTopOf="@id/btn_view_list" />

        <Button
            android:id="@+id/btn_view_list"
            android:layout_width="wrap_content"
            android:layout_height="30dp"
            android:background="@drawable/backg_round_left_diselect"
            android:text="List"
            android:textAllCaps="false"
            app:layout_constraintEnd_toStartOf="@id/btn_view_table"
            app:layout_constraintTop_toTopOf="@id/btn_view_table" />

        <Button
            android:id="@+id/btn_view_table"
            android:layout_width="wrap_content"
            android:layout_height="30dp"
            android:layout_marginTop="9dp"
            android:layout_marginRight="9dp"
            android:background="@drawable/backg_round_right"
            android:text="Table"
            android:textAllCaps="false"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
            android:id="@+id/swipe_refresh"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginTop="9dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/btn_view_list">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recview"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                tools:listitem="@layout/item_transaction_list" />
        </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

        <androidx.constraintlayout.widget.Group
            android:id="@+id/group_view_setting"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:constraint_referenced_ids="btn_view_list,textView1,btn_view_table" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
