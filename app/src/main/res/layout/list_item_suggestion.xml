<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:tools="http://schemas.android.com/tools"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>
        <variable
            name="model"
            type="String"/>
    </data>
    <LinearLayout
        android:orientation="horizontal"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">

        <Button
            android:id="@+id/btn_1"
            android:layout_marginTop="9dp"
            tools:text="Buy 1 Get 1 Free"
            app:splitView="@{model}"
            app:index="@{0}"
            android:maxLines="1"
            android:ellipsize="end"
            android:textAllCaps="false"
            android:textColor="@color/grey_light"
            android:background="@drawable/round_stroke_grey_selected"
            android:paddingRight="15dp"
            android:paddingLeft="15dp"
            android:layout_width="wrap_content"
            android:layout_height="45dp" />
        <Button
            android:id="@+id/btn_2"
            android:layout_marginTop="9dp"
            tools:text="Spesial promo"
            app:splitView="@{model}"
            app:index="@{1}"
            android:maxLines="1"
            android:ellipsize="end"
            android:textAllCaps="false"
            android:textColor="@color/grey_light"
            android:background="@drawable/round_stroke_grey_selected"
            android:paddingRight="15dp"
            android:paddingLeft="15dp"
            android:layout_marginRight="5dp"
            android:layout_marginLeft="5dp"
            android:layout_width="wrap_content"
            android:layout_height="45dp" />
        <Button
            android:id="@+id/btn_3"
            android:layout_marginTop="9dp"
            tools:text="Promo"
            app:splitView="@{model}"
            app:index="@{2}"
            android:maxLines="1"
            android:ellipsize="end"
            android:textAllCaps="false"
            android:textColor="@color/grey_light"
            android:background="@drawable/round_stroke_grey_selected"
            android:paddingRight="15dp"
            android:paddingLeft="15dp"
            android:layout_width="wrap_content"
            android:layout_height="45dp" />

    </LinearLayout>
</layout>
