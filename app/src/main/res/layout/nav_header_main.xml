<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="outlet"
            type="com.uniq.uniqpos.data.remote.model.Outlet" />

        <variable
            name="employee"
            type="com.uniq.uniqpos.data.remote.model.Employee" />

        <variable
            name="subscription"
            type="com.uniq.uniqpos.model.SubscriptionStatus" />
        <variable
            name="online"
            type="Boolean" />
        <variable
            name="shiftName"
            type="String" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/nav_header_height"
        android:background="#2B363D"
        android:paddingLeft="16dp"
        android:paddingRight="16dp"
        android:paddingBottom="16dp"
        android:theme="@style/ThemeOverlay.AppCompat.Dark">

        <TextView
            android:id="@+id/textView1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Subscription Status"
            android:textColor="@color/grey_light"
            android:textSize="9sp"
            app:layout_constraintBottom_toTopOf="@id/txt_subscription"
            app:layout_constraintStart_toStartOf="parent" />

        <TextView
            android:id="@+id/txt_subscription"
            android:layout_width="0dp"
            android:paddingRight="9dp"
            android:layout_height="wrap_content"
            tools:text="Expires on 3 Des 2019"
            android:text="@{subscription.subscriptionMessage}"
            android:textSize="10sp"
            app:layout_constraintEnd_toStartOf="@id/txt_action_subscribe"
            android:textColor="@color/text_grey_light"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <TextView
            android:id="@+id/txt_action_subscribe"
            android:text="subscribe"
            android:textSize="12sp"
            android:background="@drawable/btn_round_green"
            android:paddingRight="9dp"
            android:paddingLeft="9dp"
            android:paddingBottom="2dp"
            app:layout_constraintTop_toTopOf="@id/textView1"
            app:layout_constraintBottom_toBottomOf="@id/txt_subscription"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>

        <de.hdodenhof.circleimageview.CircleImageView
            android:id="@+id/img_logo"
            android:layout_width="53dp"
            android:layout_height="53dp"
            android:scaleType="centerCrop"
            app:civ_border_color="@color/text_grey"
            app:civ_border_width="1dp"
            app:imgLocalFile="@{outlet.receiptLogo}"
            app:layout_constraintBottom_toTopOf="@id/textView1"
            app:layout_constraintStart_toStartOf="parent"
            android:layout_marginBottom="5dp"
            app:path="@{`logo/`}"
            tools:src="@drawable/icon_shop_outline" />

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginLeft="16dp"
            app:layout_constraintStart_toEndOf="@id/img_logo"
            app:layout_constraintTop_toTopOf="@id/img_logo"
            android:orientation="vertical">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:maxLines="1"
                android:text="@{outlet.name}"
                android:textAppearance="@style/TextAppearance.AppCompat.Body1"
                tools:text="Alive Fusion" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:maxLines="1"
                android:text="@{outlet.address}"
                android:textSize="11sp"
                android:visibility="gone"
                tools:text="Timoho, Gondokusuman, Yogyakarta" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="bottom"
                android:orientation="horizontal">

                <ImageView
                    android:layout_width="15dp"
                    android:layout_height="15dp"
                    android:layout_gravity="left"
                    android:layout_marginLeft="-3dp"
                    app:srcCompat="@drawable/ic_person_grey_500_24dp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="3dp"
                    android:maxLines="1"
                    android:text="@{employee.name}"
                    android:textSize="11sp"
                    tools:text="Annas" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="2dp"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <LinearLayout
                    android:layout_width="7dp"
                    android:layout_height="7dp"
                    android:background="@drawable/btn_round_blue"
                    android:backgroundTint="@{online ?  @color/blue_background : @color/colorPrimary}" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="7dp"
                    android:text="@{online ? `Online` : `Offline`}"
                    android:textSize="10sp"
                    android:visibility="gone"
                    tools:text="Online" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="7dp"
                    android:text="@{online ? `Online` : `Offline`}"
                    android:textSize="10sp"
                    tools:text="Shift Pagi" />
            </LinearLayout>
        </LinearLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
