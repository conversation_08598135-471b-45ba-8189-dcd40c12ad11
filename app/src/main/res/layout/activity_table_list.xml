<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/background"
        tools:context="com.uniq.uniqpos.view.table.TableListActivity">

        <include
            android:id="@+id/inc_toolbar"
            layout="@layout/uniq_toolbar" />

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layout_behavior="@string/appbar_scrolling_view_behavior">

            <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
                android:id="@+id/swipe_refresh"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recview"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingLeft="16dp"
                    android:paddingTop="16dp"
                    android:paddingRight="16dp"
                    app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
                    app:spanCount="@integer/table_span_count"
                    tools:listitem="@layout/list_item_table" />
            </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>
        </RelativeLayout>
    </androidx.coordinatorlayout.widget.CoordinatorLayout>
</layout>
