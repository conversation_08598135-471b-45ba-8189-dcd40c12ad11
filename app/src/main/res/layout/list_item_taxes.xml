<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="model"
            type="com.uniq.uniqpos.data.local.entity.GratuityEntity" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:clickable="true"
        android:foreground="?android:attr/selectableItemBackground"
        android:paddingTop="8dp"
        tools:background="@color/background">

        <CheckBox
            android:id="@+id/cb_tax"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:maxLines="1"
            android:text="@{model.name}"
            android:textColor="@color/grey_light"
            app:buttonTint="@android:color/white"
            app:fontFamily="@font/poppins"
            app:layout_constraintEnd_toStartOf="@id/textView1"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="Delivery Service" />

        <TextView
            android:id="@+id/textView1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingRight="7dp"
            android:text="@{model.readableTotal}"
            android:textColor="@color/text_grey_light"
            android:textSize="12sp"
            app:layout_constraintBottom_toBottomOf="@id/cb_tax"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/cb_tax"
            tools:text="15%" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
