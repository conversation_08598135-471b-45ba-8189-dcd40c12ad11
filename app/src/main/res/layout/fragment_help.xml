<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/background"
        android:padding="9dp"
        tools:context="com.uniq.uniqpos.view.help.HelpFragment">

        <View
            android:id="@+id/view1"
            android:layout_width="0dp"
            android:layout_height="40dp"
            android:layout_marginTop="9dp"
            android:background="@drawable/round_stroke_grey_search"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/imageView1"
            android:layout_width="wrap_content"
            android:layout_height="0dp"
            android:layout_marginLeft="9dp"
            android:paddingTop="7dp"
            android:paddingBottom="7dp"
            app:layout_constraintBottom_toBottomOf="@id/view1"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/view1"
            app:srcCompat="@drawable/ic_search_white_24dp"
            app:tint="@color/text_grey_light" />

        <EditText
            android:id="@+id/edt_search"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="@null"
            android:hint="search..."
            android:textColor="@color/text_grey_light"
            android:textColorHint="@color/text_grey_light"
            android:textSize="14sp"
            app:layout_constraintBottom_toBottomOf="@id/view1"
            app:layout_constraintEnd_toEndOf="@id/view1"
            app:layout_constraintStart_toEndOf="@id/imageView1"
            app:layout_constraintTop_toTopOf="@id/view1" />

        <ImageView
            android:id="@+id/img_clear"
            android:layout_width="wrap_content"
            android:layout_height="20dp"
            android:layout_marginRight="5dp"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@id/view1"
            app:layout_constraintEnd_toEndOf="@id/view1"
            app:layout_constraintTop_toTopOf="@id/view1"
            app:srcCompat="@drawable/ic_clear_white_24dp"
            tools:visibility="visible"
            app:tint="@color/text_grey_light" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rec_view_help"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginTop="16dp"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/edt_search"
            tools:listitem="@layout/list_item_help" />

        <TextView
            android:id="@+id/txt_not_found"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="19dp"
            android:fontFamily="@font/poppins"
            android:gravity="center"
            android:text="@string/didnt_find_tutorial_info"
            android:textColor="@color/text_grey_light"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/edt_search" />

        <TextView
            android:id="@+id/chat_wa"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginRight="16dp"
            android:layout_marginBottom="16dp"
            android:background="@drawable/round_grey"
            android:paddingLeft="13dp"
            android:paddingTop="5dp"
            android:paddingRight="13dp"
            android:paddingBottom="5dp"
            android:text="Live Chat"
            android:textColor="@color/greeen_background"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

<!--        <com.google.android.material.floatingactionbutton.ExtendedFloatingActionButton-->
<!--            android:layout_width="wrap_content"-->
<!--            android:layout_height="wrap_content"-->
<!--            android:text="Chat"-->
<!--            android:textColor="#FFFFFF"-->
<!--            app:icon="@drawable/ic_add_white_24dp"-->
<!--            app:iconTint="#FFFFFF"-->
<!--            app:layout_constraintBottom_toBottomOf="parent"-->
<!--            app:layout_constraintEnd_toEndOf="parent" />-->

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>
