<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:background="@color/background">

        <View
            android:id="@+id/view1"
            android:layout_width="50dp"
            android:layout_height="1dp"
            android:layout_marginTop="7dp"
            android:background="@color/text_grey_light"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <View
            android:id="@+id/view2"
            android:layout_width="0dp"
            android:layout_height="1dp"
            android:layout_marginTop="1dp"
            android:background="@color/text_grey_light"
            app:layout_constraintEnd_toEndOf="@id/view1"
            app:layout_constraintStart_toStartOf="@id/view1"
            app:layout_constraintTop_toBottomOf="@id/view1" />

        <ImageView
            android:id="@+id/img_close"
            android:layout_width="wrap_content"
            android:layout_height="27dp"
            android:layout_marginTop="16dp"
            android:contentDescription="@string/close"
            android:paddingTop="5dp"
            android:paddingBottom="5dp"
            android:paddingLeft="3dp"
            app:srcCompat="@drawable/met_ic_close"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/view1"
            app:tint="@color/text_grey_light" />

        <TextView
            android:id="@+id/txt_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:fontFamily="@font/poppins_medium"
            android:textColor="#ffff"
            android:textSize="16sp"
            app:layout_constraintBottom_toBottomOf="@id/img_close"
            app:layout_constraintStart_toEndOf="@id/img_close"
            app:layout_constraintTop_toTopOf="@id/img_close"
            tools:text="Cara Melakukan Refund" />

        <WebView
            android:id="@+id/web_view"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_margin="9dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/img_close" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
