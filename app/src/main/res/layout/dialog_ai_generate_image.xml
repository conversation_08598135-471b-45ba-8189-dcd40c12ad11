<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools"
    tools:background="@color/background"
    android:orientation="vertical"
    android:padding="16dp">

    <TextView
        android:id="@+id/txtProductName"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textSize="18sp"
        android:textStyle="bold"
        android:textColor="@color/grey_light"
        android:layout_marginBottom="16dp"/>

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textColor="@color/text_grey_light"
        android:text="AI will generate a product image based on the product name and your description. A more detailed description will help generate better results."
        android:layout_marginBottom="16dp"/>

    <com.google.android.material.textfield.TextInputLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/edtDescription"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textColorHint="@color/grey_light"
            android:textColor="@color/text_grey_light"
            android:hint="Additional description (optional)"
            android:minLines="3"
            android:gravity="top"/>

    </com.google.android.material.textfield.TextInputLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="end">

        <Button
            android:id="@+id/btnCancel"
            android:layout_weight="1"
            android:visibility="gone"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Cancel"/>

        <Button
            android:id="@+id/btnGenerate"
            android:background="@drawable/btn_round_dark"
            android:layout_weight="1"
            android:textColor="@color/grey_light"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Generate"
            android:layout_marginStart="8dp"/>

    </LinearLayout>

</LinearLayout>
