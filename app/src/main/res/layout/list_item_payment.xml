<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="model"
            type="String" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <LinearLayout
            android:id="@+id/layout_payment"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingLeft="16dp">

            <ImageView
                android:id="@+id/img_payment"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:srcCompat="@drawable/ic_check_circle_blue_500_24dp"
                app:tint="#515A61" />

            <TextView
                android:id="@+id/txt_payment"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="5dp"
                android:paddingTop="15dp"
                android:paddingBottom="15dp"
                android:text="@{model}"
                android:textColor="@color/text_grey_light"
                android:textSize="21sp"
                tools:text="Card" />
        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@color/text_grey_light" />

    </LinearLayout>
</layout>
