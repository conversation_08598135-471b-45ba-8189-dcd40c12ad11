<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

    <androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingLeft="9dp"
            android:paddingRight="9dp"
            android:paddingBottom="9dp"
            tools:background="@color/background">

            <View
                android:id="@+id/view1"
                android:layout_width="50dp"
                android:layout_height="2dp"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="7dp"
                android:background="@color/text_grey_light" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <ImageView
                    android:id="@+id/img_close"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:visibility="gone"
                    app:layout_constraintBottom_toBottomOf="@id/txt_title"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="@id/txt_title"
                    app:srcCompat="@drawable/ic_clear_white_24dp"
                    app:tint="@color/grey_light" />

                <TextView
                    android:id="@+id/txt_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:fontFamily="@font/poppins_medium"
                    android:text="Detail Voucher"
                    android:textColor="@color/grey_light"
                    android:textSize="16sp"
                    app:layout_constraintStart_toEndOf="@id/img_close"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/txt_voucher"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="5dp"
                    android:text="XVer78HiK"
                    android:textColor="@color/text_grey_light"
                    app:layout_constraintStart_toStartOf="@id/txt_title"
                    app:layout_constraintTop_toBottomOf="@id/img_close" />

                <Button
                    android:id="@+id/btn_refresh"
                    android:layout_width="99dp"
                    android:layout_height="37dp"
                    android:background="@drawable/btn_grey_effect_reverse"
                    android:text="Refresh"
                    android:textAllCaps="false"
                    android:textColor="#FFFFFF"
                    app:layout_constraintBottom_toBottomOf="@id/txt_voucher"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="@id/txt_title" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <TextView
                android:id="@+id/txt_promo_terms"
                android:visibility="gone"
                tools:visibility="visible"
                android:paddingTop="3dp"
                tools:text="this is sample if promo terms..."
                android:textColor="@color/text_grey_light"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>
            <TextView
                android:id="@+id/txt_detail"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="20dp"
                android:textColor="@color/grey_light"
                tools:text="@string/lorem_ipsum" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rec_view_free"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                app:layout_behavior="@string/appbar_scrolling_view_behavior"
                tools:listitem="@layout/list_item_promo_free" />

            <Button
                android:id="@+id/btn_remove"
                android:layout_width="match_parent"
                android:layout_height="40dp"
                android:layout_marginTop="25dp"
                android:background="@color/red_background"
                android:text="REMOVE VOUCHER"
                android:textColor="#FFFF" />
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>
</layout>
