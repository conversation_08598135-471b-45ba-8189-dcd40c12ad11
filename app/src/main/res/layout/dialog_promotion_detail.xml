<?xml version="1.0" encoding="utf-8"?>
<layout>

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/background"
        android:padding="9dp">

        <TextView
            android:id="@+id/txt_promo_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:fontFamily="@font/poppins"
            tools:text="Promo Kemerdekaan"
            android:textColor="@color/grey_light"
            android:textSize="16sp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/txt_min_order"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/grey_light"
            android:textSize="12sp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/txt_promo_name"
            tools:text="tanpa minimum order" />

        <TextView
            android:id="@+id/txt_promo_terms"
            android:visibility="gone"
            tools:visibility="visible"
            android:paddingTop="9dp"
            android:paddingBottom="9dp"
            tools:text="gunakan promo ini hanya saat hujan saja"
            android:textColor="@color/text_grey_light"
            app:layout_constraintTop_toBottomOf="@id/txt_min_order"
            app:layout_constraintStart_toStartOf="@id/txt_min_order"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>

        <TextView
            android:id="@+id/txt_promotion_info"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="9dp"
            android:visibility="gone"
            android:textColor="@color/grey_light"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/txt_promo_terms"
            tools:text="promo Rp30,000 untuk item berikut:" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recview_promo_product"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="9dp"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/txt_promotion_info"
            tools:listitem="@layout/list_item_product_promo" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
