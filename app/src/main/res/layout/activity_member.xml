<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/background"
        tools:context="com.uniq.uniqpos.view.member.MemberActivity">

        <TextView
            android:id="@+id/txtRemoveInfo"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:paddingLeft="26dp"
            android:paddingTop="16dp"
            android:paddingRight="16dp"
            android:paddingBottom="16dp"
            android:text="@string/remove_current_member"
            android:textColor="@color/grey_light"
            app:layout_constraintEnd_toStartOf="@id/btn_remove_member"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <Button
            android:id="@+id/btn_remove_member"
            android:layout_width="wrap_content"
            android:layout_height="35dp"
            android:layout_marginRight="16dp"
            android:background="@drawable/btn_round_blue"
            android:backgroundTint="@color/red_background"
            android:text="@string/remove"
            android:textAllCaps="false"
            android:textColor="@color/white"
            app:layout_constraintBottom_toBottomOf="@id/txtRemoveInfo"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/txtRemoveInfo" />


        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recview_member"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginStart="8dp"
            android:layout_marginLeft="8dp"
            android:layout_marginTop="8dp"
            android:layout_marginEnd="8dp"
            android:layout_marginRight="8dp"
            android:layout_marginBottom="8dp"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/txtRemoveInfo"
            tools:listitem="@layout/list_item_member" />

        <com.google.android.material.floatingactionbutton.FloatingActionButton
            android:id="@+id/fab_add_member"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="16dp"
            android:layout_marginRight="16dp"
            android:layout_marginBottom="16dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:srcCompat="@drawable/ic_add_white_24dp" />

        <androidx.constraintlayout.widget.Group
            android:id="@+id/group_remove_member"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone"
            app:constraint_referenced_ids="btn_remove_member,txtRemoveInfo"
            tools:visibility="visible" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
