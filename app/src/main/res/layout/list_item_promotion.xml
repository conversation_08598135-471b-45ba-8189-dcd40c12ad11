<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>
        <variable
            name="model"
            type="com.uniq.uniqpos.data.remote.model.Promotion"/>
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:padding="5dp"
        tools:background="@color/background"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            android:id="@+id/txt_promo"
            android:text="@{model.name}"
            android:fontFamily="@font/poppins"
            android:textColor="@color/grey_light"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            tools:text="Promo Akhir Tahun"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" />
        <ImageView
            android:id="@+id/img_remove"
            android:tint="@color/red_background"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:srcCompat="@drawable/ic_remove_circle_outline_black_24dp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" />
        <TextView
            app:strCurrency="@{model.value}"
            tools:text="5,000"
            android:layout_marginRight="9dp"
            android:fontFamily="@font/poppins"
            app:layout_constraintTop_toTopOf="parent"
            android:textColor="@color/grey_light"
            app:layout_constraintEnd_toStartOf="@id/img_remove"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
