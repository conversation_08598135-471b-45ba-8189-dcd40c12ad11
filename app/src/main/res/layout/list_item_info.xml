<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="model"
            type="String" />
    </data>

    <LinearLayout
        android:paddingLeft="9dp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <Button
            android:id="@+id/btn_1"
            android:layout_width="match_parent"
            android:layout_height="45dp"
            android:layout_marginTop="9dp"
            android:background="@drawable/round_stroke_grey_selected"
            android:ellipsize="end"
            android:maxLines="1"
            android:paddingLeft="15dp"
            android:paddingRight="15dp"
            android:text="@{model}"
            android:textAllCaps="false"
            android:textColor="@color/grey_light"
            tools:text="Buy 1 Get 1 Free" />
    </LinearLayout>
</layout>
