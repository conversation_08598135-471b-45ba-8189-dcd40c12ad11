<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>
        <variable
            name="fragment"
            type="com.uniq.uniqpos.view.closeshift.CloseShiftFragment"/>
    </data>

    <LinearLayout
        android:baselineAligned="false"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="horizontal"
        android:background="@android:color/white"
        tools:context="id.uniq.uniqpos.view.closecashier.CashRecapFragment">
        <LinearLayout
            android:orientation="vertical"
            android:layout_weight="1"
            android:layout_width="0dp"
            android:layout_height="match_parent">
            <com.google.android.material.textfield.TextInputLayout
                android:layout_marginTop="16dp"
                android:layout_marginLeft="16dp"
                android:layout_width="300dp"
                android:layout_height="wrap_content">
                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/edt_cash"
                    android:hint="@string/cash"
                    android:onTextChanged="@{fragment::onTextChanged}"
                    android:inputType="number"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content" />
            </com.google.android.material.textfield.TextInputLayout>
            <com.google.android.material.textfield.TextInputLayout
                android:layout_marginLeft="16dp"
                android:layout_width="300dp"
                android:layout_height="wrap_content">
                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/edt_card"
                    android:hint="@string/card"
                    android:onTextChanged="@{fragment::onTextChanged}"
                    android:inputType="number"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content" />
            </com.google.android.material.textfield.TextInputLayout>
            <RelativeLayout
                android:layout_marginLeft="16dp"
                android:layout_marginTop="5dp"
                android:layout_width="300dp"
                android:layout_height="wrap_content">
                <TextView
                    android:id="@+id/title_1"
                    android:text="@string/sales_total"
                    android:textSize="12sp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content" />
                <TextView
                    android:id="@+id/txt_total_sales"
                    android:layout_below="@+id/title_1"
                    tools:text="2.105.000"
                    android:textSize="19sp"
                    app:fontFamily="@font/poppins_medium"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content" />
                <TextView
                    android:id="@+id/title_2"
                    android:text="@{@string/cash + ` + ` + @string/card}"
                    android:layout_alignParentRight="true"
                    android:textSize="12sp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content" />
                <TextView
                    android:id="@+id/txt_cashpluscard"
                    android:layout_below="@+id/title_2"
                    android:layout_alignParentRight="true"
                    tools:text="1.950.000"
                    android:textSize="19sp"
                    app:fontFamily="@font/poppins_medium"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content" />
            </RelativeLayout>
            <RelativeLayout
                android:paddingRight="16dp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">
                <Button
                    android:id="@+id/btn_submit"
                    android:text="Submit"
                    android:layout_marginLeft="16dp"
                    android:textColor="@android:color/white"
                    android:layout_marginTop="9dp"
                    android:background="@color/blue_background"
                    android:layout_width="120dp"
                    android:layout_height="40dp" />
                <TextView
                    android:id="@+id/txt_daily_recap"
                    android:text="@string/daily_recap"
                    android:layout_alignParentRight="true"
                    android:layout_alignBottom="@+id/btn_submit"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content" />
            </RelativeLayout>
            <LinearLayout
                android:background="#9E9E9E"
                android:layout_marginTop="15dp"
                android:padding="10dp"
                android:orientation="horizontal"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
                <TextView
                    android:text="No."
                    android:textColor="@android:color/black"
                    app:fontFamily="@font/poppins_semibold"
                    android:layout_weight="1"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content" />
                <TextView
                    android:text="Operator"
                    android:textColor="@android:color/black"
                    app:fontFamily="@font/poppins_semibold"
                    android:layout_weight="3"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content" />
                <TextView
                    android:text="@string/date_time"
                    android:textColor="@android:color/black"
                    app:fontFamily="@font/poppins_semibold"
                    android:layout_weight="5"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content" />
            </LinearLayout>
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recviewCashRecap"
                tools:listitem="@layout/list_item_cash_recap"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"/>
        </LinearLayout>

        <RelativeLayout
            android:id="@+id/layout_nota"
            android:visibility="gone"
            android:background="@color/grey_background"
            android:layout_width="270dp"
            android:layout_height="match_parent">
            <ScrollView
                android:layout_above="@+id/layout_button"
                android:layout_alignParentTop="true"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
                <TextView
                    android:id="@+id/txt_nota"
                    android:padding="16dp"
                    android:textSize="11sp"
                    app:fontFamily="@font/poppins"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content" />
            </ScrollView>
            <LinearLayout
                android:id="@+id/layout_button"
                android:layout_alignParentBottom="true"
                android:layout_width="match_parent"
                android:layout_height="43dp"
                android:orientation="horizontal">
                <Button
                    android:id="@+id/layout_print"
                    android:text="Print"
                    android:textColor="@android:color/white"
                    android:background="@color/greeen_background"
                    android:layout_weight="1"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content" />
                <Button
                    android:id="@+id/btn_close"
                    android:text="@string/close"
                    android:textColor="@android:color/white"
                    android:background="@color/red_background"
                    android:layout_weight="1"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content" />
            </LinearLayout>
        </RelativeLayout>
    </LinearLayout>
</layout>
