<?xml version="1.0" encoding="utf-8"?>
<layout>

    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/background"
        android:orientation="vertical"
        android:padding="16dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Filter Transaction"
            android:textAppearance="@style/TextAppearance.AppCompat.Medium"
            android:textColor="@color/white" />

        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:textColorHint="@color/text_grey_light"
            android:theme="@style/InputStyle">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/edt_date"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:focusable="false"
                android:hint="@string/date_open_shift"
                android:longClickable="false"
                android:textColor="@color/text_grey_light" />
        </com.google.android.material.textfield.TextInputLayout>

        <com.weiwangcn.betterspinner.library.material.MaterialBetterSpinner
            android:id="@+id/spin_shift"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:hint="Shift"
            app:met_floatingLabel="normal"
            app:met_floatingLabelTextColor="@color/text_grey_light"
            app:met_textColor="@color/text_grey_light"
            app:met_textColorHint="@color/text_grey_light"
            app:met_underlineColor="@color/text_grey_light" />

        <com.weiwangcn.betterspinner.library.material.MaterialBetterSpinner
            android:id="@+id/spin_payment"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="@string/payment_method"
            app:met_floatingLabel="normal"
            app:met_floatingLabelTextColor="@color/text_grey_light"
            app:met_textColor="@color/text_grey_light"
            app:met_textColorHint="@color/text_grey_light"
            app:met_underlineColor="@color/text_grey_light" />

        <androidx.appcompat.widget.SwitchCompat
            android:id="@+id/sw_discount"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="9dp"
            android:fontFamily="@font/poppins"
            android:text="@string/voucher_and_discount_only"
            android:textColor="@color/text_grey_light" />

        <androidx.appcompat.widget.SwitchCompat
            android:id="@+id/sw_promotion"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="9dp"
            android:fontFamily="@font/poppins"
            android:text="Transactions with promotion Only"
            android:textColor="@color/text_grey_light" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="32dp">

            <Button
                android:id="@+id/btn_filter"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@android:color/transparent"
                android:gravity="right|center_vertical"
                android:text="FILTER"
                android:textColor="@color/greeen_background"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <Button
                android:id="@+id/btn_reset_filter"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginRight="9dp"
                android:background="@android:color/transparent"
                android:text="RESET FILTER"
                android:textColor="@color/greeen_background"
                app:layout_constraintEnd_toStartOf="@id/btn_filter"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>
    </LinearLayout>
</layout>
