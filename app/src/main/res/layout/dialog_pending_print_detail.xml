<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:background="@color/background">

        <View
            android:id="@+id/view1"
            android:layout_width="50dp"
            android:layout_height="2dp"
            android:layout_marginTop="7dp"
            android:background="@color/grey_original"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/img_close"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="9dp"
            app:layout_constraintBottom_toBottomOf="@id/textView1"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/textView1"
            app:srcCompat="@drawable/ic_clear_white_24dp"
            app:tint="@color/grey_light" />

        <TextView
            android:id="@+id/textView1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="9dp"
            android:layout_marginTop="16dp"
            android:fontFamily="@font/poppins_medium"
            android:text="@string/pending_print"
            android:textColor="@color/grey_light"
            android:textSize="16sp"
            app:layout_constraintStart_toEndOf="@id/img_close"
            app:layout_constraintTop_toBottomOf="@id/view1" />

        <TextView
            android:id="@+id/txt_print"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="9dp"
            android:text="PRINT"
            android:textColor="@color/text_grey_light"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@id/textView1"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/textView1" />

        <LinearLayout
            android:id="@+id/layout_print"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginRight="16dp"
            android:background="@drawable/round_semi_grey"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingLeft="13dp"
            android:paddingTop="3dp"
            android:paddingRight="13dp"
            android:paddingBottom="3dp"
            app:layout_constraintBottom_toBottomOf="@id/textView1"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/textView1">

            <ImageView
                android:layout_width="17dp"
                android:layout_height="wrap_content"
                app:srcCompat="@drawable/ic_print_black_24dp"
                app:tint="@color/text_grey_light" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="9dp"
                android:text="print"
                android:textColor="@color/text_grey_light"
                android:textSize="13sp" />
        </LinearLayout>

        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/textView1">

            <TextView
                android:id="@+id/txt_receipt"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:fontFamily="monospace"
                android:padding="12dp"
                android:textColor="@color/text_grey_light"
                android:textSize="13sp"
                tools:text="Outlet Cabang Jogja" />
        </ScrollView>

        <!--        <WebView-->
        <!--            android:id="@+id/web_view"-->
        <!--            android:layout_width="0dp"-->
        <!--            android:layout_height="wrap_content"-->
        <!--            app:layout_constraintEnd_toEndOf="parent"-->
        <!--            app:layout_constraintStart_toStartOf="parent"-->
        <!--            app:layout_constraintTop_toBottomOf="@id/textView1" />-->
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
