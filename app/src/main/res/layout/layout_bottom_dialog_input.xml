<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>
        <variable
            name="model"
            type="com.uniq.uniqpos.model.BottomDialogModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingBottom="9dp"
        app:layout_behavior="android.support.design.widget.BottomSheetBehavior"
        tools:background="@color/background">

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/guide_left"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginLeft="16dp"
            android:orientation="vertical"
            app:layout_constraintGuide_begin="16dp"
            app:layout_constraintStart_toStartOf="parent" />

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/guide_right"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="16dp"
            android:layout_marginRight="16dp"
            android:orientation="vertical"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintGuide_end="16dp" />

        <View
            android:id="@+id/view1"
            android:layout_width="50dp"
            android:layout_height="2dp"
            android:layout_marginTop="7dp"
            android:background="@color/grey_original"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />


        <ImageView
            android:id="@+id/img_close"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:tint="@color/grey_light"
            app:layout_constraintBottom_toBottomOf="@id/textView1"
            app:layout_constraintStart_toStartOf="@id/guide_left"
            app:layout_constraintTop_toTopOf="@id/textView1"
            app:srcCompat="@drawable/ic_clear_white_24dp" />


        <TextView
            android:id="@+id/textView1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="9dp"
            android:layout_marginTop="16dp"
            android:text="@{model.title}"
            android:textColor="@color/grey_light"
            android:textSize="16sp"
            app:layout_constraintStart_toEndOf="@id/img_close"
            app:layout_constraintTop_toBottomOf="@id/view1"
            tools:text="@string/have_voucher_code" />

        <TextView
            android:id="@id/textView2"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="@{model.subtitle}"
            android:textColor="@color/text_grey_light"
            app:layout_constraintEnd_toEndOf="@id/guide_right"
            app:layout_constraintStart_toStartOf="@id/textView1"
            app:layout_constraintTop_toBottomOf="@id/textView1"
            tools:text="Input New Voucher" />

        <TextView
            android:id="@+id/txt_message"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="9dp"
            android:text="@{model.message}"
            android:textColor="@color/text_grey_light"
            app:layout_constraintEnd_toEndOf="@id/guide_right"
            app:layout_constraintStart_toStartOf="@id/guide_left"
            app:layout_constraintTop_toBottomOf="@id/textView2"
            tools:text="Masukan kode voucher yang tertera di aplikasi" />

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/edt_input"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:hint="@{model.hint}"
            android:inputType="@{model.inputType}"
            android:textColor="@color/text_grey_light"
            android:textColorHint="@color/text_grey_light"
            android:theme="@style/InputStyle"
            app:layout_constraintEnd_toEndOf="@id/guide_right"
            app:layout_constraintStart_toStartOf="@id/guide_left"
            app:layout_constraintTop_toBottomOf="@id/txt_message"
            tools:hint="@string/enter_voucher_code" />

        <Button
            android:id="@+id/btn_scan"
            android:layout_width="50dp"
            android:layout_height="wrap_content"
            android:background="@android:color/transparent"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@id/edt_input"
            app:layout_constraintEnd_toEndOf="@id/edt_input"
            app:layout_constraintTop_toTopOf="@id/edt_input" />

        <TextView
            android:id="@+id/txt_err_msg"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="3dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="@color/red_background"
            app:layout_constraintEnd_toEndOf="@id/guide_right"
            app:layout_constraintStart_toStartOf="@id/guide_left"
            app:layout_constraintTop_toBottomOf="@id/edt_input"
            tools:text="field can not be empty" />

        <com.uniq.materialanimation.ButtonLoading
            android:id="@+id/btn_next"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="33dp"
            app:buttonColor="@color/greeen_background"
            app:buttonText="@{model.buttonText}"
            app:layout_constraintEnd_toEndOf="@id/guide_right"
            app:layout_constraintStart_toStartOf="@id/guide_left"
            app:layout_constraintTop_toBottomOf="@id/edt_input"
            app:textColor="@color/white" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
