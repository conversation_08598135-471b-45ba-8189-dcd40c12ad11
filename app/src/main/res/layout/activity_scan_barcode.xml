<?xml version="1.0" encoding="utf-8"?>
<layout>

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:keepScreenOn="true"
        tools:context="com.uniq.uniqpos.view.scanbarcode.ScanBarcodeActivity">

        <SurfaceView
            android:id="@+id/camera_view"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintBottom_toTopOf="@+id/inc_cart"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <RelativeLayout
            android:id="@+id/layout_product"
            android:layout_width="300dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="50dp"
            android:background="@drawable/round_white"
            android:paddingLeft="11dp"
            android:paddingTop="5dp"
            android:paddingRight="11dp"
            android:paddingBottom="5dp"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:visibility="visible">

            <ImageView
                android:id="@+id/img_error"
                android:layout_width="30dp"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:adjustViewBounds="true"
                app:srcCompat="@drawable/ic_cloud_off_grey_600_24dp" />

            <TextView
                android:id="@+id/txt_error"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/img_error"
                android:layout_centerHorizontal="true"
                android:fontFamily="@font/poppins"
                android:gravity="center_horizontal"
                android:text="Produk/Menu tidak ditemukan!"
                android:textSize="13sp" />

            <TextView
                android:id="@+id/txt_item"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:fontFamily="@font/poppins"
                android:gravity="center_horizontal"
                android:maxLines="1"
                android:visibility="gone"
                tools:text="Alive Frid Chicken Dada" />

            <TextView
                android:id="@+id/txt_price"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@+id/txt_item"
                android:gravity="center_horizontal"
                android:visibility="gone"
                tools:text="24,000" />
        </RelativeLayout>

        <include
            android:id="@+id/inc_cart"
            layout="@layout/layout_cart_bottom" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
