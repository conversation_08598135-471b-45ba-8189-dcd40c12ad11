<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/background"
        android:fillViewport="true">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <!-- Header Section -->
            <TextView
                android:id="@+id/txt_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginTop="16dp"
                android:text="Transactions"
                android:textColor="@color/text_white"
                android:textSize="24sp"
                android:textStyle="bold"
                android:visibility="gone"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageButton
                android:id="@+id/btn_calendar"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_marginEnd="8dp"
                android:background="@drawable/bg_circle_button"
                android:src="@drawable/ic_calendar"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="@id/txt_title"
                app:layout_constraintEnd_toStartOf="@id/btn_filter"
                app:layout_constraintTop_toTopOf="@id/txt_title"
                app:tint="@color/text_grey_light" />

            <ImageButton
                android:id="@+id/btn_filter"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_marginEnd="16dp"
                android:background="@drawable/bg_circle_button"
                android:src="@drawable/ic_filter_list_white_24dp"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="@id/txt_title"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="@id/txt_title"
                app:tint="@color/text_grey_light" />

            <!-- Shift Info -->
            <TextView
                android:id="@+id/txt_shift_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginTop="16dp"
                android:textColor="@color/shift_text_orange"
                android:textSize="16sp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/txt_title"
                tools:text="Morning Shift" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:text="•"
                android:textColor="@color/text_grey_light"
                app:layout_constraintBottom_toBottomOf="@id/txt_shift_name"
                app:layout_constraintStart_toEndOf="@id/txt_shift_name"
                app:layout_constraintTop_toTopOf="@id/txt_shift_name" />

            <TextView
                android:id="@+id/txt_shift_time"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="24dp"
                android:textColor="@color/text_grey_light"
                app:layout_constraintBottom_toBottomOf="@id/txt_shift_name"
                app:layout_constraintStart_toEndOf="@id/txt_shift_name"
                app:layout_constraintTop_toTopOf="@id/txt_shift_name"
                tools:text="Opened: 2025-03-04 08:00" />

            <!-- Stats Cards -->
            <LinearLayout
                android:id="@+id/stats_container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="16dp"
                android:layout_marginTop="16dp"
                android:orientation="horizontal"
                app:layout_constraintTop_toBottomOf="@id/txt_shift_name">

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="8dp"
                    android:layout_weight="1"
                    android:background="@drawable/bg_rounded_dark"
                    android:orientation="vertical"
                    android:padding="13dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Total Transactions"
                        android:textColor="@color/text_grey_light"
                        android:textSize="13sp" />

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <TextView
                            android:id="@+id/txt_trans_count"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/text_white"
                            android:textSize="21sp"
                            android:textStyle="bold"
                            tools:text="5" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginHorizontal="4dp"
                            android:textColor="@color/text_grey_light"
                            android:text="•"
                            tools:visibility="visible"
                            android:textSize="16sp"
                            android:visibility="gone"
                            android:id="@+id/txt_refund_separator"/>

                        <TextView
                            android:id="@+id/txt_refund_count"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/red_background"
                            android:textSize="11sp"
                            android:paddingTop="3dp"
                            android:visibility="gone"
                            android:fontFamily="@font/poppins"
                            tools:text="2 Refunds"
                            tools:visibility="visible" />

                    </LinearLayout>

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/layout_total_revenue"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="8dp"
                    android:layout_weight="1"
                    android:background="@drawable/bg_rounded_dark"
                    android:orientation="vertical"
                    android:padding="13dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Total Revenue"
                        android:textColor="@color/text_grey_light"
                        android:textSize="13sp" />

                    <TextView
                        android:id="@+id/txt_total_revenue"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:textColor="@color/text_white"
                        android:textSize="21sp"
                        android:textStyle="bold"
                        tools:text="$348.48" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/layout_total_pax"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:layout_weight="1"
                    android:background="@drawable/bg_rounded_dark"
                    android:orientation="vertical"
                    android:padding="13dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Total PAX"
                        android:textColor="@color/text_grey_light"
                        android:textSize="13sp" />

                    <TextView
                        android:id="@+id/txt_total_pax"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:textColor="@color/text_white"
                        android:textSize="21sp"
                        android:textStyle="bold"
                        tools:text="12" />
                </LinearLayout>
            </LinearLayout>

            <!-- Search Bar -->
            <LinearLayout
                android:id="@+id/search_container"
                android:layout_width="match_parent"
                android:layout_height="48dp"
                android:layout_marginHorizontal="16dp"
                android:layout_marginTop="16dp"
                android:background="@drawable/bg_rounded_dark"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:paddingHorizontal="16dp"
                app:layout_constraintTop_toBottomOf="@id/stats_container">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@drawable/ic_search_white_24dp"
                    app:tint="@color/text_grey_light" />

                <EditText
                    android:id="@+id/edit_search"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginStart="8dp"
                    android:background="@null"
                    android:hint="Search transactions..."
                    android:textColor="@color/text_white"
                    android:textColorHint="@color/text_grey_light"
                    android:textSize="16sp" />
            </LinearLayout>

            <!-- Transaction List -->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recview_history_list"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:clipToPadding="false"
                android:nestedScrollingEnabled="false"
                android:paddingHorizontal="16dp"
                android:paddingBottom="16dp"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                app:layout_constraintTop_toBottomOf="@id/search_container"
                tools:itemCount="3"
                tools:listitem="@layout/list_item_transaction_history" />

            <!-- Loading Progress -->
            <ProgressBar
                android:id="@+id/progressBar2"
                style="?android:attr/progressBarStyle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <!-- No Permission View -->
            <LinearLayout
                android:id="@+id/layout_no_permission"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:gravity="center_horizontal"
                android:orientation="vertical"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <ImageView
                    android:layout_width="50dp"
                    android:layout_height="wrap_content"
                    android:adjustViewBounds="true"
                    app:srcCompat="@drawable/ic_visibility_off_grey_600_24dp"
                    app:tint="@color/grey_light" />

                <TextView
                    android:id="@+id/txt_feature_hide"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/poppins_medium"
                    android:gravity="center_horizontal"
                    android:text="@string/have_no_permission_see_item"
                    android:textColor="@color/grey_light" />
            </LinearLayout>

            <include
                android:id="@+id/layout_no_data"
                layout="@layout/layout_no_data"
                android:visibility="gone" />

        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.core.widget.NestedScrollView>
</layout>
