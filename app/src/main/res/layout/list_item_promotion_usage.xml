<?xml version="1.0" encoding="utf-8"?>
<layout>

    <data>
        <variable
            name="model"
            type="com.uniq.uniqpos.data.remote.model.PromotionUsage" />
    </data>
    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:tools="http://schemas.android.com/tools"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        tools:background="@color/background"
        android:paddingTop="9dp"
        android:paddingBottom="9dp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            app:layout_constraintStart_toStartOf="parent"
            android:id="@+id/txt_date"
            tools:text="Sabtu, 13 Sept 2023 14:40"
            android:textSize="14sp"
            app:timeMillisToDate="@{model.timeCreated}"
            app:format="@{`E, dd MMM yyyy HH:mm`}"
            app:layout_constraintBottom_toBottomOf="@id/txt_outlet"
            app:layout_constraintTop_toTopOf="parent"
            android:fontFamily="@font/poppins_medium"
            android:textColor="@color/text_grey_light"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>

        <TextView
            android:id="@+id/txt_display_nota"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="IM2023031356"
            android:text="@{model.displayNota}"
            android:textSize="13sp"
            android:textColor="@color/text_grey_light"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>

        <TextView
            android:id="@+id/txt_outlet"
            app:layout_constraintTop_toBottomOf="@id/txt_display_nota"
            app:layout_constraintEnd_toEndOf="parent"
            tools:text="Chapter Timoho"
            android:text="@{model.outletName}"
            android:textSize="11sp"
            android:textColor="@color/text_default"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
