<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="android.view.View" />

        <variable
            name="sales"
            type="com.uniq.uniqpos.data.local.entity.SalesEntity" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/background">

        <TextView
            android:id="@+id/txt_show_payment_history"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>

        <androidx.core.widget.NestedScrollView
            android:id="@+id/scroll_content"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:fillViewport="true"
            app:layout_constraintBottom_toTopOf="@id/action_container"
            app:layout_constraintTop_toTopOf="parent">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <!-- Transaction Header -->
                <LinearLayout
                    android:id="@+id/header_container"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp"
                    app:layout_constraintTop_toTopOf="parent">

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">

                        <TextView
                            android:id="@+id/txt_transaction_id"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@{sales.displayNota}"
                            android:textColor="@color/grey_light"
                            android:textSize="20sp"
                            app:fontFamily="@font/poppins_semibold"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            tools:text="INV-123456" />

                        <ImageView
                            android:id="@+id/ic_time"
                            android:layout_width="13dp"
                            android:layout_height="13dp"
                            android:src="@drawable/ic_access_time"
                            app:tint="@color/text_grey_light"
                            app:layout_constraintEnd_toStartOf="@id/txt_transaction_date"
                            app:layout_constraintTop_toTopOf="@id/txt_transaction_date"
                            app:layout_constraintBottom_toBottomOf="@id/txt_transaction_date"
                            android:layout_marginEnd="4dp"/>

                        <TextView
                            android:id="@+id/txt_transaction_date"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/text_grey_light"
                            android:textSize="13sp"
                            android:paddingTop="3dp"
                            android:fontFamily="@font/poppins"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintTop_toTopOf="@id/txt_transaction_id"
                            app:layout_constraintBottom_toBottomOf="@id/txt_transaction_id"
                            tools:text="2025-03-04 14:30" />

                    </androidx.constraintlayout.widget.ConstraintLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <ImageView
                            android:layout_width="13dp"
                            android:layout_height="13dp"
                            android:src="@drawable/ic_person"
                            app:tint="@color/text_grey_light" />

                        <TextView
                            android:id="@+id/txt_customer_name"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="8dp"
                            android:text="@{sales.customer}"
                            android:fontFamily="@font/poppins"
                            android:textColor="@color/grey_light"
                            android:textSize="13sp"
                            tools:text="John Doe" />
                    </LinearLayout>

                    <!-- Payment Method Container -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:background="@drawable/bg_rounded_dark"
                        android:padding="16dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Payment Method"
                            android:textColor="@color/text_grey_light"
                            android:textSize="14sp" />

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="16dp"
                            android:orientation="horizontal">

                            <TextView
                                android:id="@+id/txt_payment_method"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:textColor="@color/grey_light"
                                android:textSize="14sp"
                                tools:text="Card" />

                            <TextView
                                android:id="@+id/txt_payment_bank"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="8dp"
                                android:background="@drawable/bg_rounded_grey"
                                android:paddingHorizontal="8dp"
                                android:paddingVertical="4dp"
                                android:textColor="@color/text_grey_light"
                                android:textSize="12sp"
                                tools:text="BCA Debit" />
                        </LinearLayout>
                    </LinearLayout>
                </LinearLayout>

                <!-- Refund Information -->
                <LinearLayout
                    android:id="@+id/refund_container"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="16dp"
                    android:layout_marginTop="8dp"
                    android:background="@drawable/bg_rounded_red"
                    android:orientation="vertical"
                    android:padding="16dp"
                    android:visibility="@{sales.status.equals(`Refund`) ? View.VISIBLE : View.GONE}"
                    app:layout_constraintTop_toBottomOf="@id/header_container">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Refunded Transaction"
                        android:textColor="@color/red_text"
                        android:textSize="16sp"
                        android:textStyle="bold" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:text='@{"Reason: " + sales.refundReason}'
                        android:textColor="@color/text_grey_light"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/txt_refund_processor"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:textColor="@color/text_grey_light"
                        android:textSize="14sp"
                        tools:text="Processed by: Alex Manager" />
                </LinearLayout>

                <!-- Item Details Section -->
                <TextView
                    android:id="@+id/txt_item_details"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="24dp"
                    android:text="Item Details"
                    android:textColor="@color/grey_light"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/refund_container" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recview_history_detail"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:clipToPadding="false"
                    android:nestedScrollingEnabled="false"
                    android:paddingHorizontal="16dp"
                    app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                    app:layout_constraintTop_toBottomOf="@id/txt_item_details"
                    tools:listitem="@layout/list_item_order_history" />

                <!-- Summary Container -->
                <LinearLayout
                    android:id="@+id/summary_container"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp"
                    app:layout_constraintTop_toBottomOf="@id/recview_history_detail">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Taxes &amp; Fees"
                        android:textColor="@color/grey_light"
                        android:textSize="16sp"
                        android:textStyle="bold" />

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/recViewTax"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:nestedScrollingEnabled="false"
                        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                        tools:listitem="@layout/list_item_tax" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_marginVertical="16dp"
                        android:background="@color/divider" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="Subtotal"
                            android:textColor="@color/grey_light"
                            android:textSize="16sp" />

                        <TextView
                            android:id="@+id/txt_subtotal"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/grey_light"
                            android:textSize="16sp"
                            tools:text="125.000" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="Grand Total"
                            android:textColor="@color/grey_light"
                            android:textSize="18sp"
                            android:textStyle="bold" />

                        <TextView
                            android:id="@+id/txt_grandtotal"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/text_orange"
                            app:textCurrency="@{sales.grandTotal}"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            tools:text="140.000" />
                    </LinearLayout>
                </LinearLayout>

            </androidx.constraintlayout.widget.ConstraintLayout>

        </androidx.core.widget.NestedScrollView>

        <!-- Action Buttons -->
        <LinearLayout
            android:id="@+id/action_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            app:layout_constraintBottom_toBottomOf="parent">

            <Button
                android:id="@+id/btn_print"
                android:layout_width="0dp"
                android:layout_height="48dp"
                android:layout_weight="1"
                android:background="@color/orange"
                android:text="Reprint"
                android:textColor="@android:color/white" />

            <Button
                android:id="@+id/btn_refund"
                android:layout_width="0dp"
                android:layout_height="48dp"
                android:layout_weight="1"
                android:background="@drawable/bg_button_dark"
                android:text="Refund"
                android:textColor="@android:color/white"
                android:visibility="@{sales.status.equalsIgnoreCase(`Refund`) ? View.GONE : View.VISIBLE}" />
        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
