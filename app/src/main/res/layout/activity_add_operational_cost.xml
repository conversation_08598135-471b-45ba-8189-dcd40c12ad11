<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/background"
        tools:context="com.uniq.uniqpos.view.purchase.AddOperationalCostActivity">

        <ScrollView
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintBottom_toTopOf="@+id/view1"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textColorHint="@color/text_grey_light"
                    android:theme="@style/InputStyle">

                    <AutoCompleteTextView
                        android:id="@+id/edt_name"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="@string/operational_cost_name"
                        android:inputType="textCapWords"
                        android:maxLength="100"
                        android:textColor="@color/text_grey_light" />
                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="9dp"
                    android:textColorHint="@color/text_grey_light"
                    android:theme="@style/InputStyle">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/edt_qty"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="@string/quantity"
                        android:inputType="number"
                        android:textColor="@color/text_grey_light" />
                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="9dp"
                    android:textColorHint="@color/text_grey_light"
                    android:theme="@style/InputStyle">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/edt_price"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="@string/price"
                        android:inputType="number"
                        android:selectAllOnFocus="true"
                        android:textColor="@color/text_grey_light" />
                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="9dp"
                    android:textColorHint="@color/text_grey_light"
                    android:theme="@style/InputStyle">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/edt_ket"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="@string/information"
                        android:maxLength="100"
                        android:textColor="@color/text_grey_light" />
                </com.google.android.material.textfield.TextInputLayout>

                <com.weiwangcn.betterspinner.library.material.MaterialBetterSpinner
                    android:id="@+id/spin_supplier"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="9dp"
                    android:hint="Supplier"
                    app:met_floatingLabel="normal"
                    app:met_floatingLabelTextColor="@color/text_grey_light"
                    app:met_textColor="@color/text_grey_light"
                    app:met_textColorHint="@color/text_grey_light"
                    app:met_underlineColor="@color/text_grey_light" />

                <com.weiwangcn.betterspinner.library.material.MaterialBetterSpinner
                    android:id="@+id/spin_category"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="9dp"
                    android:hint="Purchase Report Category"
                    app:met_floatingLabel="normal"
                    app:met_floatingLabelTextColor="@color/text_grey_light"
                    app:met_textColor="@color/text_grey_light"
                    app:met_textColorHint="@color/text_grey_light"
                    app:met_underlineColor="@color/text_grey_light" />

                <com.weiwangcn.betterspinner.library.material.MaterialBetterSpinner
                    android:id="@+id/spin_payment"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="9dp"
                    android:hint="@string/payment"
                    app:met_floatingLabel="normal"
                    app:met_floatingLabelTextColor="@color/text_grey_light"
                    app:met_textColor="@color/text_grey_light"
                    app:met_textColorHint="@color/text_grey_light"
                    app:met_underlineColor="@color/text_grey_light" />

                <com.weiwangcn.betterspinner.library.material.MaterialBetterSpinner
                    android:id="@+id/spin_bank"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="9dp"
                    android:hint="Bank Account"
                    app:met_floatingLabel="normal"
                    app:met_floatingLabelTextColor="@color/text_grey_light"
                    app:met_textColor="@color/text_grey_light"
                    app:met_textColorHint="@color/text_grey_light"
                    app:met_underlineColor="@color/text_grey_light" />

                <CheckBox
                    android:id="@+id/cb_print"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="9dp"
                    android:buttonTint="@color/text_grey_light"
                    android:checked="true"
                    android:text="@string/print_receipt"
                    android:textColor="@color/text_grey_light" />
            </LinearLayout>
        </ScrollView>

        <View
            android:id="@+id/view1"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="@color/background_selected"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@+id/textView1" />

        <TextView
            android:id="@+id/textView1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="16dp"
            android:paddingTop="16dp"
            android:text="Grand Total"
            android:textColor="@color/text_grey_light"
            app:layout_constraintBottom_toTopOf="@+id/txt_grandtotal"
            app:layout_constraintStart_toStartOf="parent" />

        <TextView
            android:id="@+id/textView2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="16dp"
            android:layout_marginBottom="16dp"
            android:fontFamily="@font/poppins_semibold"
            android:text="Rp"
            android:textColor="@color/grey_light"
            android:textSize="16sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <TextView
            android:id="@+id/txt_grandtotal"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:fontFamily="@font/poppins_semibold"
            android:text="0"
            android:textColor="@color/grey_light"
            android:textSize="16sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/btn_save"
            app:layout_constraintStart_toEndOf="@id/textView2"
            tools:text="180,000" />

        <Button
            android:id="@+id/btn_save"
            android:layout_width="110dp"
            android:layout_height="wrap_content"
            android:layout_marginRight="16dp"
            android:layout_marginBottom="16dp"
            android:background="@color/blue_background"
            android:text="@string/save"
            android:textColor="#ffffff"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
