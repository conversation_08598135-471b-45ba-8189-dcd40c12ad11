<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>
        <variable
            name="model"
            type="com.uniq.uniqpos.data.remote.model.Help" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:fontFamily="@font/poppins_medium"
            android:paddingLeft="9dp"
            android:paddingTop="11dp"
            android:paddingRight="9dp"
            android:paddingBottom="11dp"
            android:text="@{model.title}"
            android:textColor="@color/grey_light"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="Menambahkan produk catalog" />

        <View
            android:layout_width="0dp"
            android:layout_height="1dp"
            android:background="@color/text_grey"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
