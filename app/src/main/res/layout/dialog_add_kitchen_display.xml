<?xml version="1.0" encoding="utf-8"?>
<layout>

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        xmlns:tools="http://schemas.android.com/tools"
        android:padding="9dp"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        tools:background="@color/background"
        android:layout_height="match_parent">

        <TextView
            android:id="@+id/txt_title"
            android:text="Add Kitchen Display"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:fontFamily="@font/poppins"
            android:textSize="17sp"
            android:textColor="@color/grey_light"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/inputLayout1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:textColorHint="@color/text_grey_light"
            android:theme="@style/InputStyle"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/txt_title">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/edt_ip"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="numberDecimal|number"
                android:digits="0123456789."
                android:hint="IP Adress"
                android:maxLength="25"
                android:textColor="@color/text_grey_light" />
        </com.google.android.material.textfield.TextInputLayout>


        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/inputLayout2"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:textColorHint="@color/text_grey_light"
            android:theme="@style/InputStyle"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/inputLayout1">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/edt_name"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="textPersonName"
                android:hint="@string/name"
                android:maxLength="25"
                android:textColor="@color/text_grey_light" />
        </com.google.android.material.textfield.TextInputLayout>

        <com.weiwangcn.betterspinner.library.material.MaterialBetterSpinner
            android:id="@+id/spin_category"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:hint="@string/category"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/inputLayout2"
            app:met_baseColor="@color/text_grey_light"
            app:met_floatingLabel="normal"
            app:met_floatingLabelTextColor="@color/text_grey_light"
            app:met_textColor="@color/text_grey_light"
            app:met_textColorHint="@color/text_grey_light"
            app:met_underlineColor="@color/text_grey_light" />

        <TextView
            android:id="@+id/txt_warning"
            app:layout_constraintTop_toBottomOf="@id/spin_category"
            app:layout_constraintStart_toStartOf="parent"
            android:layout_marginTop="9dp"
            android:textColor="@color/grey_light"
            android:text="@string/connect_same_wifi"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>

        <ImageView
            android:id="@+id/img_wifi"
            app:layout_constraintTop_toBottomOf="@id/txt_warning"
            app:layout_constraintStart_toStartOf="parent"
            android:src="@drawable/baseline_wifi_24"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>

        <TextView
            android:id="@+id/txt_wifi_name"
            android:text="Boen Brothers |"
            android:textColor="@color/text_grey_light"
            app:layout_constraintTop_toTopOf="@id/img_wifi"
            app:layout_constraintBottom_toBottomOf="@id/img_wifi"
            app:layout_constraintStart_toEndOf="@id/img_wifi"
            android:paddingLeft="9dp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>

        <TextView
            android:id="@+id/txt_local_ip"
            app:layout_constraintTop_toTopOf="@id/txt_wifi_name"
            app:layout_constraintStart_toEndOf="@id/txt_wifi_name"
            android:text="************"
            android:paddingStart="7dp"
            android:fontFamily="@font/poppins_medium"
            android:textColor="@color/grey_light"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>

        <com.uniq.materialanimation.ButtonLoading
            android:id="@+id/btn_save"
            app:layout_constraintTop_toBottomOf="@id/txt_wifi_name"
            android:layout_marginTop="26dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:buttonText="Test and Save"
            android:layout_width="200dp"
            android:layout_height="wrap_content"/>

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
