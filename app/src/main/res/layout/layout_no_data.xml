<?xml version="1.0" encoding="utf-8"?>
<layout>

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        android:id="@+id/layout_no_data"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:background="@color/background">

        <ImageView
            android:id="@+id/img_no_data"
            android:layout_width="50dp"
            android:layout_height="50dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:srcCompat="@drawable/ic_cloud_off_grey_600_24dp" />

        <TextView
            android:id="@+id/txt_no_data_msg"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="@string/no_data_available"
            android:textColor="@color/text_grey_light"
            android:textSize="14sp"
            android:gravity="center_horizontal"
            app:fontFamily="@font/poppins"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/img_no_data" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
