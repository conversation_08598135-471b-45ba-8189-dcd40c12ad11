<layout  xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>
        <variable
            name="model"
            type="com.uniq.uniqpos.data.local.entity.ReservationEntity"/>
    </data>

    <androidx.cardview.widget.CardView
        android:foreground="?android:attr/selectableItemBackground"
        android:clickable="true"
        app:cardUseCompatPadding="true"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
            <TextView
                android:id="@+id/textView18"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="8dp"
                android:layout_marginStart="8dp"
                android:layout_marginTop="8dp"
                android:text="@{model.name}"
                tools:text="Abdurrahim"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/textView19"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="8dp"
                android:layout_marginStart="8dp"
                android:text="@{model.phone}"
                tools:text="085632236881"
                android:textSize="12sp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/textView18" />

            <TextView
                android:id="@+id/textView20"
                app:timeBetween="@{model.timeStart}"
                app:timeEnd="@{model.timeEnd}"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="8dp"
                android:layout_marginRight="8dp"
                android:layout_marginTop="8dp"
                tools:text="10:30 - 12:00"
                android:textStyle="bold"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/textView21"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="8dp"
                android:layout_marginRight="8dp"
                app:timeMillisToDate="@{model.timeStart}"
                app:format="@{`dd MMM yyyy`}"
                tools:text="21 May 2018"
                android:textSize="12sp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/textView20" />

            <TextView
                android:id="@+id/textView22"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="8dp"
                android:layout_marginStart="8dp"
                android:text="Table"
                app:fontFamily="@font/poppins"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/textView19" />

            <TextView
                android:id="@+id/textView23"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="12dp"
                android:layout_marginStart="12dp"
                android:text="@{model.tableName}"
                tools:text="09"
                android:textStyle="bold"
                app:fontFamily="@font/poppins_semibold"
                app:layout_constraintStart_toEndOf="@+id/textView22"
                app:layout_constraintTop_toBottomOf="@+id/textView19" />

            <TextView
                android:id="@+id/txt_transaction"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="8dp"
                android:layout_marginStart="8dp"
                android:layout_marginTop="8dp"
                android:text="TRANSACTION"
                android:textColor="@color/orange_background"
                android:textStyle="bold"
                app:fontFamily="@font/poppins"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/textView22" />

            <TextView
                android:id="@+id/txt_edit"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="24dp"
                android:layout_marginStart="24dp"
                android:layout_marginTop="8dp"
                android:text="EDIT"
                android:textColor="@color/orange_background"
                android:textStyle="bold"
                app:fontFamily="@font/poppins"
                app:layout_constraintStart_toEndOf="@+id/txt_transaction"
                app:layout_constraintTop_toBottomOf="@+id/textView23" />
        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.cardview.widget.CardView>
</layout>
