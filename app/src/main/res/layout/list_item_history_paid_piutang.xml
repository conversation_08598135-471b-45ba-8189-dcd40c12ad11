<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="model"
            type="com.uniq.uniqpos.data.local.entity.PiutangHistoryEntity" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:app="http://schemas.android.com/apk/res-auto"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        tools:background="@color/background">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginRight="9dp"
            android:fontFamily="@font/poppins_semibold"
            android:textColor="@color/grey_light"
            app:layout_constraintBottom_toTopOf="@+id/view1"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:textCurrency="@{model.total}"
            tools:text="25,000" />

        <TextView
            android:id="@+id/txt_pay_method"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="9dp"
            android:layout_marginTop="9dp"
            android:text="@{model.method}"
            android:textColor="@color/grey_light"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="CASH" />

        <TextView
            android:id="@+id/txt_date"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="9dp"
            android:textColor="@color/grey_light"
            android:textSize="13sp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/txt_pay_method"
            app:timeMillisToDate="@{model.dataCreated}"
            tools:text="Wednesday 23-08-2018 13:20" />

        <View
            android:id="@+id/view1"
            android:layout_width="0dp"
            android:layout_height="1dp"
            android:layout_marginTop="9dp"
            android:background="@color/text_grey"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/txt_date" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
