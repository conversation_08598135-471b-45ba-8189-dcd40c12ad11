<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="android.view.View" />

        <variable
            name="hasPermission"
            type="Boolean" />

        <variable
            name="model"
            type="com.uniq.uniqpos.data.local.entity.PrinterEntity" />
    </data>


    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="16dp"
        tools:background="@color/background"
        tools:layout_height="match_parent">

        <ScrollView
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
                <androidx.appcompat.widget.SwitchCompat
                    android:id="@+id/sw_active"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:checked="true"
                    android:fontFamily="@font/poppins_medium"
                    android:text="@string/enable_for_this_device"
                    android:textColor="@color/grey_light"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/textView1"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="32dp"
                    android:text="@string/cannot_change_setting"
                    android:textColor="@color/text_orange"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/sw_active" />

                <com.weiwangcn.betterspinner.library.material.MaterialBetterSpinner
                    android:id="@+id/spin_paper"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:hint="Paper Size"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/textView1"
                    app:met_baseColor="@color/text_grey_light"
                    app:met_floatingLabel="normal"
                    app:met_floatingLabelTextColor="@color/text_grey_light"
                    app:met_textColor="@color/text_grey_light"
                    app:met_textColorHint="@color/text_grey_light"
                    app:met_underlineColor="@color/text_grey_light" />

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/inputLayout1"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="9dp"
                    android:textColorHint="@color/text_grey_light"
                    android:theme="@style/InputStyle"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/spin_paper">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/edt_name"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:enabled="@{hasPermission}"
                        android:focusable="@{hasPermission}"
                        android:hint="@string/printer_name"
                        android:maxLength="25"
                        android:text="@{model.name}"
                        android:textColor="@color/text_grey_light" />
                </com.google.android.material.textfield.TextInputLayout>

                <androidx.appcompat.widget.SwitchCompat
                    android:id="@+id/sw_receipt"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:checked="@{model.settingPrintreceipt.equals(`1`)}"
                    android:clickable="@{hasPermission}"
                    android:fontFamily="@font/poppins_medium"
                    android:text="Print Receipt"
                    android:textColor="@color/grey_light"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/inputLayout1"
                    tools:checked="true" />

                <TextView
                    android:id="@id/txt_qty"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="19dp"
                    android:text="@string/print_receipt_info"
                    android:textColor="@color/text_grey_light"
                    android:textSize="12sp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="@id/sw_receipt" />

                <TextView
                    android:id="@+id/textView6"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:fontFamily="@font/poppins_medium"
                    android:text="Print Receipt Copy"
                    android:textColor="@color/grey_light"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/txt_qty" />

                <View
                    android:id="@+id/view1"
                    android:layout_width="0dp"
                    android:layout_height="27dp"
                    android:background="@drawable/btn_round_dark"
                    app:layout_constraintBottom_toBottomOf="@id/textView6"
                    app:layout_constraintEnd_toEndOf="@id/img_add"
                    app:layout_constraintStart_toStartOf="@id/img_minus" />

                <ImageView
                    android:id="@+id/img_add"
                    android:layout_width="30dp"
                    android:layout_height="27dp"
                    android:clickable="@{hasPermission}"
                    android:padding="3dp"
                    app:layout_constraintBottom_toBottomOf="@+id/textView6"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:srcCompat="@drawable/ic_add_white_24dp"
                    app:tint="@color/grey_light" />

                <EditText
                    android:id="@+id/edt_copy"
                    android:layout_width="40dp"
                    android:layout_height="0dp"
                    android:background="@android:color/transparent"
                    android:ems="10"
                    android:focusable="false"
                    android:gravity="center"
                    android:inputType="number"
                    android:text="@{model.settingPrintreceiptJumlah}"
                    android:textColor="@color/grey_light"
                    android:textSize="13sp"
                    app:layout_constraintBottom_toBottomOf="@+id/img_add"
                    app:layout_constraintEnd_toStartOf="@+id/img_add"
                    app:layout_constraintTop_toTopOf="@+id/img_add"
                    tools:text="2"
                    tools:visibility="visible" />

                <ImageView
                    android:id="@+id/img_minus"
                    android:layout_width="30dp"
                    android:layout_height="27dp"
                    android:clickable="@{hasPermission}"
                    android:padding="1dp"
                    app:layout_constraintBottom_toBottomOf="@+id/textView6"
                    app:layout_constraintEnd_toStartOf="@+id/edt_copy"
                    app:srcCompat="@drawable/ic_remove_white_24dp"
                    app:tint="@color/grey_light"
                    tools:visibility="visible" />

                <View
                    android:id="@+id/view3"
                    android:layout_width="0dp"
                    android:layout_height="1dp"
                    android:layout_marginTop="9dp"
                    android:background="@color/text_grey"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/textView6" />

                <androidx.appcompat.widget.SwitchCompat
                    android:id="@+id/sw_order"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="19dp"
                    android:checked="@{model.settingPrintorder.equals(`1`)}"
                    android:clickable="@{hasPermission}"
                    android:fontFamily="@font/poppins_medium"
                    android:text="Print Order"
                    android:textColor="@color/grey_light"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/textView6"
                    tools:checked="true" />

                <TextView
                    android:id="@id/textView4"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="19dp"
                    android:text="@string/print_order_info"
                    android:textColor="@color/text_grey_light"
                    android:textSize="12sp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="@id/sw_order" />

                <TextView
                    android:id="@+id/txt_ticket_order_count"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="9dp"
                    android:fontFamily="@font/poppins_medium"
                    android:text="0 printer ticket"
                    android:textColor="@color/grey_light"
                    app:layout_constraintEnd_toStartOf="@id/imageView1"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/textView4" />

                <ImageView
                    android:id="@+id/imageView1"
                    android:layout_width="wrap_content"
                    android:layout_height="19dp"
                    app:layout_constraintBottom_toBottomOf="@id/txt_ticket_order_count"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="@id/txt_ticket_order_count"
                    app:srcCompat="@drawable/ic_keyboard_arrow_right_white_24dp" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recview_ticket_order"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:visibility="gone"
                    app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/txt_ticket_order_count"
                    tools:listitem="@layout/list_item_print_ticket_detail"
                    tools:visibility="gone" />

                <View
                    android:id="@+id/view2"
                    android:layout_width="0dp"
                    android:layout_height="1dp"
                    android:layout_marginTop="9dp"
                    android:background="@color/text_grey"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/recview_ticket_order" />

                <androidx.appcompat.widget.SwitchCompat
                    android:id="@+id/sw_close_shift"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="19dp"
                    android:checked="@{model.settingClosingshift.equals(`1`)}"
                    android:clickable="@{hasPermission}"
                    android:fontFamily="@font/poppins_medium"
                    android:text="Print Close Shift"
                    android:textColor="@color/grey_light"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/view2"
                    tools:checked="true" />

                <TextView
                    android:id="@id/textView5"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="19dp"
                    android:text="@string/print_close_shift_info"
                    android:textColor="@color/text_grey_light"
                    android:textSize="12sp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="@id/sw_close_shift" />

                <TextView
                    android:id="@+id/txt_ticket_close_count"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="9dp"
                    android:fontFamily="@font/poppins_medium"
                    android:text="default print ticket"
                    android:textColor="@color/grey_light"
                    app:layout_constraintEnd_toStartOf="@id/imageView2"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/textView5" />

                <ImageView
                    android:id="@+id/imageView2"
                    android:layout_width="wrap_content"
                    android:layout_height="19dp"
                    app:layout_constraintBottom_toBottomOf="@id/txt_ticket_close_count"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="@id/txt_ticket_close_count"
                    app:srcCompat="@drawable/ic_keyboard_arrow_right_white_24dp" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recview_ticket_close"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:visibility="gone"
                    app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/txt_ticket_close_count"
                    tools:listitem="@layout/list_item_print_ticket_detail"
                    tools:visibility="gone" />

                <androidx.constraintlayout.widget.Group
                    android:id="@+id/group_receipt_copy"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:visibility="gone"
                    app:constraint_referenced_ids="img_minus,view1,textView6,img_add,edt_copy"
                    tools:layout_editor_absoluteX="9dp"
                    tools:layout_editor_absoluteY="9dp"
                    tools:visibility="visible" />

                <androidx.constraintlayout.widget.Group
                    android:id="@+id/group_print_order"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:visibility="gone"
                    app:constraint_referenced_ids="txt_ticket_order_count,imageView1" />

                <androidx.constraintlayout.widget.Group
                    android:id="@+id/group_print_close"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:visibility="gone"
                    app:constraint_referenced_ids="txt_ticket_close_count,imageView2" />
            </androidx.constraintlayout.widget.ConstraintLayout>
        </ScrollView>


    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
