<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.constraintlayout.widget.ConstraintLayout
        xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:tools="http://schemas.android.com/tools"
        tools:background="@color/background"
        android:layout_width="wrap_content"
        android:id="@+id/layout_no_privilege"
        android:gravity="center_horizontal"
        android:layout_height="match_parent">

        <ImageView
            android:id="@+id/img_no_permission"
            android:layout_width="50dp"
            android:layout_height="wrap_content"
            android:adjustViewBounds="true"
            app:layout_constraintBottom_toTopOf="@+id/txt_no_permission"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_chainStyle="packed"
            app:srcCompat="@drawable/ic_visibility_off_grey_600_24dp"
            app:tint="@color/grey_light" />

        <TextView
            android:id="@+id/txt_no_permission"
            app:layout_constraintTop_toBottomOf="@+id/img_no_permission"
            android:text="@string/have_no_permission_see_item"
            android:textColor="@color/text_grey_light"
            app:layout_constraintBottom_toBottomOf="parent"
            android:fontFamily="@font/poppins_medium"
            android:gravity="center_horizontal"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
