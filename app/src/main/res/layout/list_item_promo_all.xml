<?xml version="1.0" encoding="utf-8"?>
<layout>

    <data>

        <variable
            name="model"
            type="com.uniq.uniqpos.data.local.entity.PromotionEntity" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="9dp"
        tools:background="@color/background">

        <TextView
            android:id="@+id/textView1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:fontFamily="@font/poppins_medium"
            android:maxLines="2"
            android:text="@{model.name}"
            android:textColor="@color/grey_light"
            android:textSize="15sp"
            app:fontFamily="@font/poppins_medium"
            android:paddingRight="9dp"
            app:layout_constraintEnd_toStartOf="@id/btn_promo_type"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="Promo Ultah 80" />

        <TextView
            android:id="@+id/txt_date"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/text_grey_light"
            android:textSize="12sp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/textView1"
            tools:text="23 okt - 24 des (08:00 - 12:00)" />

        <Button
            android:id="@+id/btn_promo_type"
            android:layout_width="wrap_content"
            android:layout_height="20dp"
            android:background="@drawable/btn_stroke_grey_thin"
            android:paddingLeft="5dp"
            android:paddingRight="5dp"
            android:textAllCaps="false"
            android:textColor="@color/text_grey_light"
            android:textSize="10sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:textPromotionType="@{model.promotionTypeId}"
            tools:text="special promo" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
