<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="viewmodel"
            type="com.uniq.uniqpos.view.payment.PaymentViewModel" />
    </data>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingLeft="16dp"
            android:paddingRight="16dp"
            android:paddingBottom="16dp"
            tools:background="@color/background"
            tools:context=".view.payment.PaymentDetailFragment"
            tools:paddingTop="16dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/poppins"
                android:text="@string/payment_total"
                android:textColor="@color/text_grey_light"
                android:textSize="13sp" />

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/edt_pay"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="9dp"
                    android:background="@drawable/rounded_edittext_background"
                    android:fontFamily="@font/poppins_medium"
                    android:hint="@string/type_nominal"
                    android:inputType="number"
                    android:paddingTop="13dp"
                    android:paddingBottom="13dp"
                    android:paddingStart="13dp"
                    android:paddingEnd="23dp"
                    android:selectAllOnFocus="true"
                    android:textColor="@color/white"
                    android:textColorHint="@color/text_grey" />

                <ImageView
                    android:id="@+id/img_clear"
                    android:contentDescription="@string/name"
                    app:srcCompat="@drawable/ic_close"
                    android:layout_centerVertical="true"
                    android:layout_alignParentEnd="true"
                    android:padding="9dp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"/>
            </RelativeLayout>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="23dp"
                android:fontFamily="@font/poppins"
                android:text="Payment Options"
                android:textColor="@color/text_grey_light"
                android:textSize="12sp" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rec_view_payment_option"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:orientation="horizontal"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                tools:itemCount="5"
                tools:listitem="@layout/item_payment_nominal" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/layout_detail_card"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="19dp">

                <TextView
                    android:id="@+id/textView1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/poppins"
                    android:text="@string/card_payment"
                    android:textColor="@color/text_grey_light"
                    android:textSize="12sp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/txt_bank_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/poppins_semibold"
                    android:textColor="@color/grey_light"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/textView1"
                    tools:text="GoPay" />

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="1dp"
                    android:layout_marginEnd="1dp"
                    app:layout_constraintBottom_toBottomOf="@id/txt_change_bank"
                    android:paddingTop="9dp"
                    android:paddingBottom="9dp"
                    android:paddingStart="9dp"
                    android:src="@drawable/ic_expand_more_black_18dp"
                    app:layout_constraintEnd_toStartOf="@id/txt_change_bank"
                    app:layout_constraintTop_toTopOf="@id/txt_change_bank"
                    app:tint="@color/text_grey_light" />

                <TextView
                    android:id="@+id/txt_change_bank"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/change"
                    android:textColor="@color/grey_light"
                    android:textSize="12sp"
                    android:paddingTop="9dp"
                    android:paddingBottom="9dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintBottom_toBottomOf="@id/txt_bank_name"
                    app:layout_constraintTop_toTopOf="@id/txt_bank_name" />

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/edt_card_information"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="5dp"
                    android:background="@drawable/rounded_edittext_background"
                    android:hint="type card information..."
                    android:padding="9dp"
                    android:text="@={viewmodel.cardInformation}"
                    android:textColor="@color/text_grey_light"
                    android:textColorHint="@color/text_grey_light"
                    android:textSize="14sp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/txt_bank_name" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/edit_payment_information"
                android:text="@={viewmodel.paymentInformation}"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:background="@drawable/rounded_edittext_background"
                android:hint="type information..."
                android:padding="9dp"
                android:textColor="@color/text_grey_light"
                android:textColorHint="@color/text_grey_light"
                android:textSize="14sp" />

        </LinearLayout>
    </ScrollView>
</layout>
