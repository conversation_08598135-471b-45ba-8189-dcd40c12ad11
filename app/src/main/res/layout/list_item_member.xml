<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="model"
            type="com.uniq.uniqpos.data.local.entity.MemberEntity" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="?android:attr/selectableItemBackground"
        android:clickable="true"
        tools:background="@color/background">

        <de.hdodenhof.circleimageview.CircleImageView
            android:id="@+id/circleImageView"
            android:layout_width="55dp"
            android:layout_height="55dp"
            android:layout_marginStart="8dp"
            android:layout_marginLeft="8dp"
            android:layout_marginTop="8dp"
            android:src="@mipmap/ic_launcher_round"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/txt_ticket_order_count"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="18dp"
            android:layout_marginLeft="18dp"
            android:layout_marginTop="8dp"
            android:text="@{model.name}"
            android:textSize="16sp"
            android:textColor="@color/grey_light"
            app:layout_constraintStart_toEndOf="@+id/circleImageView"
            app:layout_constraintTop_toTopOf="@+id/circleImageView"
            tools:text="Lori Anderson" />

        <TextView
            android:id="@+id/textView8"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:text="@{model.phone}"
            android:textSize="12sp"
            android:textColor="@color/text_grey_light"
            app:layout_constraintBottom_toBottomOf="@+id/circleImageView"
            app:layout_constraintStart_toStartOf="@+id/txt_ticket_order_count"
            app:layout_constraintTop_toBottomOf="@+id/txt_ticket_order_count"
            tools:text="+62 0857 4225 7881" />

        <TextView
            android:id="@+id/textView9"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:layout_marginLeft="8dp"
            android:layout_marginEnd="8dp"
            android:layout_marginRight="8dp"
            android:gravity="right"
            android:textColor="@color/text_grey_light"
            android:text="@{model.typeName}"
            android:textSize="12sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/textView8"
            app:layout_constraintTop_toTopOf="@+id/textView8"
            tools:text="Silver" />

        <View
            android:layout_width="0dp"
            android:layout_height="1dp"
            android:layout_marginTop="8dp"
            android:background="@color/text_grey"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="@+id/textView8"
            app:layout_constraintTop_toBottomOf="@+id/textView8" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
