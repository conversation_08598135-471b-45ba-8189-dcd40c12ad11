<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/background"
        android:padding="9dp"
        tools:context=".view.setting.kitchendisplay.KitchenDisplaySettingFragment">

        <TextView
            android:id="@+id/txt_title"
            android:text="Kitchen Display List"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            android:fontFamily="@font/poppins_medium"
            android:textSize="19sp"
            android:textColor="@color/text_grey_light"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>

        <TextView
            android:id="@+id/txt_data_count"
            app:layout_constraintTop_toBottomOf="@id/txt_title"
            app:layout_constraintStart_toStartOf="parent"
            android:text="2 kitchen display"
            android:textSize="14sp"
            android:fontFamily="@font/poppins"
            android:textColor="@color/text_grey"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>

        <Button
            android:id="@+id/btn_add"
            android:text="Add Display"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:paddingLeft="17dp"
            android:paddingRight="17dp"
            android:textSize="12sp"
            android:background="@drawable/btn_round_blue"
            android:textColor="@android:color/white"
            android:layout_width="wrap_content"
            android:layout_height="40dp" />

       <androidx.recyclerview.widget.RecyclerView
           android:id="@+id/recview_kitchen_display"
           app:layout_constraintTop_toBottomOf="@id/txt_title"
           android:layout_marginTop="42dp"
           tools:listitem="@layout/list_item_kitchen_display"
           app:spanCount="@integer/printer_span_count"
           app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
           app:layout_constraintStart_toStartOf="parent"
           app:layout_constraintEnd_toEndOf="parent"
           app:layout_constraintBottom_toBottomOf="parent"
           android:layout_width="0dp"
           android:layout_height="0dp"/>

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
