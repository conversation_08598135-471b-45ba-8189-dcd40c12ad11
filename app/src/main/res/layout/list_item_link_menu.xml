<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="model"
            type="com.uniq.uniqpos.model.LinkMenuProduct" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:clickable="true"
        android:focusable="true"
        android:foreground="?android:attr/selectableItemBackground"
        tools:background="@color/background">

        <CheckBox
            android:id="@+id/checkBox"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:buttonTint="@color/grey_light"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@+id/textView4"
            tools:text="" />

        <TextView
            android:id="@+id/textView4"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:text="@{model.product.name}"
            android:textColor="@color/grey_light"
            android:textSize="16sp"
            android:textStyle="bold"
            app:layout_constraintStart_toEndOf="@+id/checkBox"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="Alive Fried Chicken Dada" />

        <TextView
            android:id="@+id/textView5"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/grey_light"
            app:layout_constraintStart_toEndOf="@+id/checkBox"
            app:layout_constraintTop_toBottomOf="@+id/textView4"
            app:symbol="@{`Rp`}"
            app:textCurrency="@{model.linkMenuDetail.priceAdd}"
            tools:text="Rp0" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
