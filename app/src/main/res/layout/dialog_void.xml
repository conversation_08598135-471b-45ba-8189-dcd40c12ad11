<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    <LinearLayout
        android:padding="16dp"
        android:background="@drawable/dialog_round"
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            android:text="VOID"
            app:fontFamily="@font/poppins_medium"
            android:textColor="@color/orange_background"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" />
        <View
            android:background="@color/text_grey"
            android:layout_width="match_parent"
            android:layout_height="1dp"/>

        <com.google.android.material.textfield.TextInputLayout
            android:theme="@style/InputStyle"
            android:layout_marginTop="19dp"
            android:textColorHint="@color/text_grey_light"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/edt_info"
                android:textColor="@color/text_grey_light"
                android:hint="@string/information"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />
        </com.google.android.material.textfield.TextInputLayout>
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">
            <com.google.android.material.textfield.TextInputLayout
                android:theme="@style/InputStyle"
                android:textColorHint="@color/text_grey_light"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1">
                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/edt_qty"
                    android:text="1"
                    android:hint="@string/quantity"
                    android:inputType="number"
                    android:textColor="@color/text_grey_light"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content" />
            </com.google.android.material.textfield.TextInputLayout>
            <ImageView
                android:id="@+id/img_add"
                android:visibility="gone"
                android:paddingLeft="9dp"
                android:paddingRight="9dp"
                app:srcCompat="@drawable/ic_add_white_24dp"
                android:layout_width="40dp"
                android:layout_height="match_parent" />
            <ImageView
                android:id="@+id/img_min"
                android:visibility="gone"
                android:paddingLeft="9dp"
                android:paddingRight="9dp"
                android:layout_marginLeft="7dp"
                app:srcCompat="@drawable/ic_remove_white_24dp"
                android:layout_width="40dp"
                android:layout_height="match_parent" />
        </LinearLayout>
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">
            <Button
                android:id="@+id/btn_cancel"
                android:text="@string/cancel"
                android:layout_marginTop="19dp"
                android:layout_marginRight="5dp"
                android:textColor="@android:color/white"
                android:background="@color/orange_background"
                android:layout_weight="1"
                android:layout_width="0dp"
                android:layout_height="45dp" />
            <Button
                android:id="@+id/btn_submit"
                android:text="@string/save"
                android:layout_marginLeft="5dp"
                android:layout_marginTop="19dp"
                android:textColor="@android:color/white"
                android:background="@color/blue_background"
                android:layout_weight="1"
                android:layout_width="0dp"
                android:layout_height="45dp" />
        </LinearLayout>

    </LinearLayout>
</layout>
