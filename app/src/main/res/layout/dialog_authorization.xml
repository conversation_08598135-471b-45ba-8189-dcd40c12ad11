<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/dialog_round">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:paddingBottom="16dp">

            <TextView
                android:id="@+id/textView12"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginLeft="16dp"
                android:layout_marginTop="16dp"
                android:text="@string/choose_employee"
                android:textColor="@color/text_grey_light"
                app:fontFamily="@font/poppins_medium"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/textView33" />

            <View
                android:layout_width="0dp"
                android:layout_height="1dp"
                android:layout_marginLeft="16dp"
                android:layout_marginTop="3dp"
                android:layout_marginRight="16dp"
                android:background="@color/grey_light"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/textView33" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recyclerView"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:nestedScrollingEnabled="false"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/textView12"
                tools:listitem="@layout/list_item_auth_dialog" />

            <TextView
                android:id="@+id/textView13"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginLeft="16dp"
                android:layout_marginTop="24dp"
                android:text="@string/input_pin_for"
                android:textColor="@color/text_grey_light"
                app:fontFamily="@font/poppins_medium"
                app:layout_constraintBottom_toTopOf="@+id/textInputLayoutPin"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/txt_err" />

            <TextView
                android:id="@+id/txt_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/text_grey_light"
                app:fontFamily="@font/poppins_semibold"
                app:layout_constraintBottom_toBottomOf="@+id/textView13"
                app:layout_constraintStart_toEndOf="@+id/textView13"
                app:layout_constraintTop_toTopOf="@+id/textView13"
                tools:text="Annas" />

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/textInputLayoutPin"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginLeft="16dp"
                android:layout_marginTop="7dp"
                android:layout_marginEnd="16dp"
                android:layout_marginRight="16dp"
                android:textColorHint="@color/text_grey_light"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/textView13">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/edt_pin"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="PIN"
                    android:inputType="numberPassword"
                    android:maxLength="5"
                    android:textColor="@color/text_grey_light"
                    android:textColorHint="@color/text_grey_light" />

            </com.google.android.material.textfield.TextInputLayout>

            <Button
                android:id="@+id/btn_authorize"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginLeft="16dp"
                android:layout_marginTop="8dp"
                android:layout_marginEnd="16dp"
                android:layout_marginRight="16dp"
                android:background="@drawable/btn_round_blue"
                android:text="Authorize"
                android:textColor="#ffff"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/textInputLayoutPin" />

            <TextView
                android:id="@+id/textView33"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginLeft="16dp"
                android:layout_marginTop="16dp"
                android:layout_marginEnd="16dp"
                android:layout_marginRight="16dp"
                android:layout_marginBottom="16dp"
                android:text="@string/authorization_request"
                android:textColor="@color/text_grey_light"
                app:fontFamily="@font/poppins_semibold"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/txt_err"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:fontFamily="@font/poppins_medium"
                android:gravity="center"
                android:paddingLeft="16dp"
                android:paddingRight="16dp"
                android:textColor="@color/red_background"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/recyclerView"
                tools:text="No Employee Authorized" />

        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.core.widget.NestedScrollView>
</layout>
