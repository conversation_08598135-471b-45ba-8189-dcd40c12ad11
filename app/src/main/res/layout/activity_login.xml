<layout>

    <data>

        <variable
            name="viewModel"
            type="com.uniq.uniqpos.view.login.LoginViewModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/background">

        <ImageView
            android:id="@+id/imageView2"
            android:layout_width="120dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:layout_marginBottom="8dp"
            android:adjustViewBounds="true"
            app:layout_constraintBottom_toTopOf="@+id/textInputLayout2"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:srcCompat="@drawable/ic_logo_uniq_white" />

        <TextView
            android:id="@+id/textView6"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/login_to_continue"
            android:textColor="@color/text_grey_light"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/imageView2" />

        <TextView
            android:id="@+id/signup"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:text="@string/dont_have_account"
            android:textColor="#BDBDBD"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent" />

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/textInputLayout2"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginLeft="16dp"
            android:layout_marginEnd="16dp"
            android:layout_marginRight="16dp"
            android:textColorHint="@color/text_grey_light"
            android:theme="@style/InputStyle"
            app:layout_constraintBottom_toTopOf="@+id/textInputLayout"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_chainStyle="packed">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/username"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="Email"
                android:inputType="textEmailAddress"
                android:text="@={viewModel.userName}"
                android:textColor="@color/text_grey_light" />
        </com.google.android.material.textfield.TextInputLayout>

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/textInputLayout"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginLeft="16dp"
            android:layout_marginTop="8dp"
            android:layout_marginEnd="16dp"
            android:layout_marginRight="16dp"
            android:textColorHint="@color/text_grey_light"
            android:theme="@style/InputStyle"
            app:layout_constraintBottom_toTopOf="@+id/btn_login"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/textInputLayout2"
            app:passwordToggleEnabled="true">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/password"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="Password"
                android:inputType="textPassword"
                android:text="@={viewModel.password}"
                android:textColor="@color/text_grey_light" />
        </com.google.android.material.textfield.TextInputLayout>

        <TextView
            android:id="@+id/txt_forgot_password"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginRight="16dp"
            android:paddingTop="3dp"
            android:text="@string/forgot_password"
            android:textColor="@color/text_grey_light"
            android:textSize="13sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/textInputLayout" />

        <Button
            android:id="@+id/btn_login"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="37dp"
            android:layout_marginEnd="16dp"
            android:background="@drawable/btn_round"
            android:onClick="@{() -> viewModel.processLogin()}"
            android:text="@string/login"
            android:textColor="@color/text_grey_light"
            app:layout_constraintBottom_toTopOf="@+id/signup"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/textInputLayout" />

        <TextView
            android:id="@+id/textView1"
            android:text="OR"
            app:layout_constraintTop_toBottomOf="@id/btn_login"
            android:textColor="@color/text_grey_light"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_marginTop="19dp"
            android:textSize="13sp"
            android:fontFamily="@font/poppins"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" />

        <Button
            android:id="@+id/btn_login_code"
            android:text="Login With Code"
            android:textAllCaps="false"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_marginTop="5dp"
            android:background="@android:color/transparent"
            android:textColor="@color/text_grey_light"
            app:layout_constraintTop_toBottomOf="@id/textView1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
