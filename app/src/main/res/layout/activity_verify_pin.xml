<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="verify"
            type="com.uniq.uniqpos.view.verifypin.VerifyPINActivity" />

        <variable
            name="viewModel"
            type="com.uniq.uniqpos.viewmodel.AuthViewModel" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/background"
        android:orientation="vertical"
        android:padding="@dimen/activity_horizontal_margin"
        tools:context="id.co.uniq.uniqpos.view.verifypin.VerifyPINActivity">

        <TextView
            android:id="@+id/txt_default_info"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingLeft="9dp"
            android:paddingTop="5dp"
            android:paddingRight="16dp"
            android:paddingBottom="16dp"
            android:text="default PIN : 12345"
            android:textColor="@color/text_grey_light"
            android:textSize="12sp"
            android:textStyle="italic"
            android:visibility="gone" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_horizontal"
            android:text="Type Your 5 Digit PIN"
            android:textColor="@color/text_grey_light"
            android:textSize="21sp" />

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_marginLeft="16dp"
            android:layout_marginTop="30dp"
            android:layout_marginRight="16dp">

            <EditText
                android:id="@+id/edt_pin"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@android:color/transparent"
                android:gravity="center_horizontal"
                android:inputType="numberPassword"
                android:text="@={viewModel.pin}"
                android:textColor="@android:color/white"
                android:textSize="29sp" />

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_alignParentBottom="true"
                android:background="@color/text_grey" />

            <ImageView
                android:id="@+id/img_backspace"
                android:layout_width="30dp"
                android:layout_height="30dp"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:src="@drawable/ic_backspace_grey_500_36dp" />
        </RelativeLayout>

        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="50dp"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:background="@drawable/btn_grey_effect"
                        android:gravity="center_horizontal"
                        android:onClick="@{(view) -> verify.keypadClicked(view, `1`)}"
                        android:text="1"
                        android:textColor="@color/text_grey_light"
                        android:textSize="@dimen/keypad_font_size" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:background="@drawable/btn_grey_effect"
                        android:gravity="center_horizontal"
                        android:onClick="@{(view) -> verify.keypadClicked(view, `2`)}"
                        android:text="2"
                        android:textColor="@color/text_grey_light"
                        android:textSize="@dimen/keypad_font_size" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:background="@drawable/btn_grey_effect"
                        android:gravity="center_horizontal"
                        android:onClick="@{(view) -> verify.keypadClicked(view, `3`)}"
                        android:text="3"
                        android:textColor="@color/text_grey_light"
                        android:textSize="@dimen/keypad_font_size" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="50dp"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:background="@drawable/btn_grey_effect"
                        android:gravity="center_horizontal"
                        android:onClick="@{(view) -> verify.keypadClicked(view, `4`)}"
                        android:text="4"
                        android:textColor="@color/text_grey_light"
                        android:textSize="@dimen/keypad_font_size" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:background="@drawable/btn_grey_effect"
                        android:gravity="center_horizontal"
                        android:onClick="@{(view) -> verify.keypadClicked(view, `5`)}"
                        android:text="5"
                        android:textColor="@color/text_grey_light"
                        android:textSize="@dimen/keypad_font_size" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:background="@drawable/btn_grey_effect"
                        android:gravity="center_horizontal"
                        android:onClick="@{(view) -> verify.keypadClicked(view, `6`)}"
                        android:text="6"
                        android:textColor="@color/text_grey_light"
                        android:textSize="@dimen/keypad_font_size" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="50dp"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:background="@drawable/btn_grey_effect"
                        android:gravity="center_horizontal"
                        android:onClick="@{(view) -> verify.keypadClicked(view, `7`)}"
                        android:text="7"
                        android:textColor="@color/text_grey_light"
                        android:textSize="@dimen/keypad_font_size" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:background="@drawable/btn_grey_effect"
                        android:gravity="center_horizontal"
                        android:onClick="@{(view) -> verify.keypadClicked(view, `8`)}"
                        android:text="8"
                        android:textColor="@color/text_grey_light"
                        android:textSize="@dimen/keypad_font_size" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:background="@drawable/btn_grey_effect"
                        android:gravity="center_horizontal"
                        android:onClick="@{(view) -> verify.keypadClicked(view, `9`)}"
                        android:text="9"
                        android:textColor="@color/text_grey_light"
                        android:textSize="@dimen/keypad_font_size" />
                </LinearLayout>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginTop="50dp"
                    android:background="@drawable/btn_grey_effect"
                    android:onClick="@{(view) -> verify.keypadClicked(view, `0`)}"
                    android:text="0"
                    android:textColor="@color/text_grey_light"
                    android:textSize="@dimen/keypad_font_size" />
            </LinearLayout>
        </ScrollView>
    </LinearLayout>
</layout>
