<?xml version="1.0" encoding="utf-8"?>
<layout>

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/round_grey">

        <TextView
            android:id="@+id/txt_message"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:fontFamily="@font/poppins"
            android:paddingLeft="19dp"
            android:paddingTop="9dp"
            android:paddingRight="19dp"
            android:paddingBottom="9dp"
            android:textColor="@color/white"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="data successfully saved" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
