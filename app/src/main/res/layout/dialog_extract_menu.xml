<?xml version="1.0" encoding="utf-8"?>
<layout>
    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/colorPrimary"
        android:padding="16dp"
        xmlns:app="http://schemas.android.com/apk/res-auto">

        <TextView
            android:id="@+id/txt_title"
            android:text="Turn Photos into Menus"
            android:textAppearance="@style/TextAppearance.AppCompat.Large"
            android:textColor="@color/white"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>

        <TextView
            android:id="@+id/txt_subtitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="33dp"
            android:paddingStart="1dp"
            android:text="Easily add your menu items with just a picture!"
            android:textColor="@color/grey_light"
            android:textSize="12sp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/txt_info"
            android:text="@string/info_product_ocr"
            android:textColor="@color/text_grey_light"
            app:layout_constraintTop_toBottomOf="@id/txt_subtitle"
            app:layout_constraintStart_toStartOf="parent"
            android:layout_marginTop="16dp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"/>

        <Button
            android:id="@+id/btn_camera"
            android:text="@string/camera"
            app:layout_constraintTop_toBottomOf="@id/txt_info"
            android:layout_marginTop="19dp"
            android:textColor="@color/white"
            android:background="@drawable/btn_stroke_grey_round"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"/>

        <Button
            android:id="@+id/btn_gallery"
            android:text="@string/gallery"
            app:layout_constraintTop_toBottomOf="@id/btn_camera"
            android:layout_marginTop="9dp"
            android:textColor="@color/white"
            android:background="@drawable/btn_stroke_grey_round"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"/>

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
