<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

    <androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:fitsSystemWindows="true"
        android:paddingLeft="16dp"
        android:paddingTop="9dp"
        android:paddingRight="16dp"
        android:paddingBottom="9dp"
        app:layout_behavior="com.google.android.material.bottomsheet.BottomSheetBehavior"
        tools:background="@color/background">
        <!--    app:layout_behavior="com.google.android.material.bottomsheet.BottomSheetBehavior"-->

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <View
                    android:id="@+id/view1"
                    android:layout_width="50dp"
                    android:layout_height="1dp"
                    android:layout_marginTop="7dp"
                    android:background="@color/text_grey_light"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <View
                    android:id="@+id/view2"
                    android:layout_width="0dp"
                    android:layout_height="1dp"
                    android:layout_marginTop="1dp"
                    android:background="@color/text_grey_light"
                    app:layout_constraintEnd_toEndOf="@id/view1"
                    app:layout_constraintStart_toStartOf="@id/view1"
                    app:layout_constraintTop_toBottomOf="@id/view1" />

                <ImageView
                    android:id="@+id/img_close"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:paddingRight="9dp"
                    android:paddingBottom="9dp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="@id/textView1"
                    app:srcCompat="@drawable/ic_clear_white_24dp"
                    app:tint="@color/grey_light" />

                <TextView
                    android:id="@+id/textView1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:fontFamily="@font/poppins_medium"
                    android:text="Free Item Promo"
                    android:textColor="@color/grey_light"
                    android:textSize="17sp"
                    app:layout_constraintStart_toEndOf="@id/img_close"
                    app:layout_constraintTop_toBottomOf="@id/view1" />

                <TextView
                    android:id="@+id/textView2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="please choose the quantity of free products below"
                    android:textColor="@color/text_grey_light"
                    app:layout_constraintStart_toStartOf="@id/textView1"
                    app:layout_constraintTop_toBottomOf="@id/textView1" />

                <androidx.appcompat.widget.SwitchCompat
                    android:id="@+id/sw_auto_show"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:checked="true"
                    android:text="auto show"
                    android:textColor="@color/grey_light"
                    android:textSize="12sp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="@id/textView1" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rec_view_free"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="13dp"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/textView2"
                tools:listitem="@layout/list_item_promo_free" />
        </LinearLayout>

    </androidx.coordinatorlayout.widget.CoordinatorLayout>
</layout>
