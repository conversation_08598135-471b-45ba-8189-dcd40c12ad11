<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>
        <variable
            name="model"
            type="com.uniq.uniqpos.model.PromotionFree" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingTop="9dp"
        android:paddingBottom="3dp"
        tools:background="@color/background">

        <TextView
            android:id="@+id/txt_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:fontFamily="@font/poppins_medium"
            android:maxLines="1"
            android:text="@{model.promoName}"
            android:textColor="@color/white"
            android:textSize="16sp"
            app:layout_constraintEnd_toStartOf="@+id/img_arrow"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="Free Es Teh" />

        <ImageView
            android:id="@+id/img_arrow"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintBottom_toBottomOf="@id/textView1"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/txt_title"
            app:srcCompat="@drawable/ic_keyboard_arrow_right_white_24dp"
            app:tint="@color/text_grey_light" />

        <TextView
            android:id="@+id/textView1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="21dp"
            android:text="@{model.promoQtyInfo}"
            android:textColor="@color/text_grey_light"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/txt_title"
            tools:text="Max : 3 items  |  selected : 0 items" />

        <TextView
            android:id="@+id/txt_promo_detail"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="11dp"
            android:text="@{model.promoInfoDetail}"
            android:textColor="@color/text_grey_light"
            android:visibility="gone"
            tools:visibility="visible"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/textView1"
            tools:text="@string/lorem_ipsum" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
