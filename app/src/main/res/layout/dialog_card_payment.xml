<?xml version="1.0" encoding="utf-8"?>
<layout>

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/dialog_round"
        android:paddingBottom="16dp"
        tools:layout_editor_absoluteY="81dp">

        <TextView
            android:id="@+id/textView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginLeft="16dp"
            android:layout_marginTop="16dp"
            android:text="@string/card_payment"
            android:textColor="@android:color/white"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <View
            android:id="@+id/view"
            android:layout_width="0dp"
            android:layout_height="1dp"
            android:layout_marginStart="16dp"
            android:layout_marginLeft="16dp"
            android:layout_marginTop="3dp"
            android:layout_marginEnd="16dp"
            android:layout_marginRight="16dp"
            android:background="@color/text_grey"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/textView" />

        <TextView
            android:id="@+id/txt_bank_count"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="16dp"
            android:layout_marginTop="3dp"
            android:text="@string/bank_accounts"
            android:textColor="@color/text_grey_light"
            android:textSize="12sp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@+id/view" />

        <TextView
            android:id="@+id/txt_add_bank"
            app:layout_constraintTop_toTopOf="@id/txt_bank_count"
            app:layout_constraintEnd_toEndOf="parent"
            android:text="Add Bank"
            android:textSize="13sp"
            android:paddingRight="16dp"
            android:paddingLeft="16dp"
            android:fontFamily="@font/poppins_medium"
            android:textColor="@color/greeen_background"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rec_view_card"
            android:layout_width="0dp"
            android:layout_height="145dp"
            android:layout_marginStart="16dp"
            android:layout_marginLeft="16dp"
            android:layout_marginTop="5dp"
            android:layout_marginEnd="16dp"
            android:layout_marginRight="16dp"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/txt_bank_count" />

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/textInputLayout3"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginLeft="16dp"
            android:layout_marginTop="5dp"
            android:layout_marginEnd="16dp"
            android:layout_marginRight="16dp"
            android:textColorHint="@color/text_grey_light"
            android:theme="@style/InputStyle"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/rec_view_card">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/card_number"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="@string/account"
                android:inputType="textCapCharacters"
                android:maxLength="20"
                android:maxLines="1"
                android:textColor="@color/text_grey_light" />
        </com.google.android.material.textfield.TextInputLayout>

        <Button
            android:id="@+id/btn_cancel"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginLeft="16dp"
            android:layout_marginTop="16dp"
            android:layout_marginEnd="3dp"
            android:layout_marginRight="3dp"
            android:background="@color/orange_background"
            android:text="@string/cancel"
            android:textColor="@android:color/white"
            app:layout_constraintEnd_toStartOf="@+id/btn_submit"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/textInputLayout3" />

        <Button
            android:id="@+id/btn_submit"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="3dp"
            android:layout_marginLeft="3dp"
            android:layout_marginTop="16dp"
            android:layout_marginEnd="16dp"
            android:layout_marginRight="16dp"
            android:background="@color/blue_background"
            android:text="@string/save"
            android:textColor="@android:color/white"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toEndOf="@+id/btn_cancel"
            app:layout_constraintTop_toBottomOf="@+id/textInputLayout3" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
