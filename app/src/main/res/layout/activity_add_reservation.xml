<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="model"
            type="com.uniq.uniqpos.data.local.entity.ReservationEntity" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:context=".view.reservation.AddReservationActivity">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/edt_name"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="@string/name"
                    android:inputType="textCapSentences"
                    android:text="@{model.name}" />
            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/edt_phone"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="Phone"
                    android:inputType="number"
                    android:text="@{model.phone}" />
            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/edt_table"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="Table"
                    android:inputType="number"
                    android:text="@{model.tableName}" />
            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/edt_date"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:focusable="false"
                    android:hint="@string/date"
                    app:format="@{`dd-MM-yyyy`}"
                    app:timeMillisToDate="@{model.timeStart}" />
            </com.google.android.material.textfield.TextInputLayout>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:text="JAM"
                android:textStyle="bold"
                app:fontFamily="@font/poppins" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/edt_time_start"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:focusable="false"
                        android:hint="Dari"
                        app:format="@{`HH:mm`}"
                        app:timeMillisToDate="@{model.timeStart}" />
                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/edt_time_end"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:focusable="false"
                        android:hint="Sampai"
                        app:format="@{`HH:mm`}"
                        app:timeMillisToDate="@{model.timeEnd}" />
                </com.google.android.material.textfield.TextInputLayout>
            </LinearLayout>

            <Button
                android:id="@+id/btn_save"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="22dp"
                android:background="@color/blue_background"
                android:text="@string/save"
                android:textColor="#ffff" />
        </LinearLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
