<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="android.view.View" />

        <variable
            name="model"
            type="com.uniq.uniqpos.data.local.entity.ProductEntity" />
    </data>

    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="1dp"
        android:clickable="true"
        android:foreground="?android:attr/selectableItemBackground"
        app:cardBackgroundColor="@color/background"
        app:cardUseCompatPadding="false">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <ImageView
                android:id="@+id/imageView4"
                android:layout_width="75dp"
                android:layout_height="75dp"
                android:scaleType="centerCrop"
                app:imgUrlText="@{model.photo}"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:name="@{model.name}"
                tools:srcCompat="@drawable/ayam" />

            <LinearLayout
                android:id="@+id/layout_product"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginEnd="8dp"
                android:layout_marginRight="8dp"
                android:orientation="vertical"
                app:layout_constraintBottom_toBottomOf="@+id/imageView4"
                app:layout_constraintEnd_toStartOf="@+id/txt_qty_stock"
                app:layout_constraintStart_toEndOf="@+id/imageView4"
                app:layout_constraintTop_toTopOf="parent">

                <TextView
                    android:id="@+id/textView24"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@{model.name}"
                    android:textColor="@color/grey_light"
                    app:fontFamily="@font/poppins_semibold"
                    tools:text="Yamie Panda Couple" />

                <TextView
                    android:id="@+id/txt_sku"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@{model.sku}"
                    android:textColor="@color/text_grey_light"
                    android:textSize="10sp"
                    android:visibility="@{model.sku != null &amp;&amp; !model.sku.empty ? View.VISIBLE : View.GONE}"
                    tools:text="SKU123" />

                <TextView
                    android:id="@+id/textView25"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/text_grey_light"
                    android:textSize="12sp"
                    app:textCurrency="@{safeUnbox(model.priceSell)}"
                    tools:text="12,500" />
            </LinearLayout>

            <TextView
                android:id="@+id/txt_qty_stock"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginRight="11dp"
                android:paddingStart="16dp"
                android:text="@{ `stock: ` +String.valueOf(model.stockQty)}"
                android:textColor="@color/text_grey_light"
                android:visibility="@{model.isShowStock ? View.VISIBLE : View.GONE}"
                app:fontFamily="@font/poppins"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="@id/layout_product"
                tools:text="stock: 15" />

            <View
                android:id="@+id/view1"
                android:layout_width="0dp"
                android:layout_height="27dp"
                android:layout_marginBottom="5dp"
                android:background="@drawable/btn_round_dark"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="@id/img_add"
                app:layout_constraintStart_toStartOf="@id/img_minus" />

            <ImageView
                android:id="@+id/img_add"
                android:layout_width="30dp"
                android:layout_height="27dp"
                android:layout_marginRight="8dp"
                android:layout_marginBottom="5dp"
                android:padding="3dp"
                app:layout_constraintBottom_toBottomOf="@+id/imageView4"
                app:layout_constraintEnd_toEndOf="parent"
                app:srcCompat="@drawable/ic_add_white_24dp"
                app:tint="@color/grey_light" />

            <EditText
                android:id="@+id/edt_qty"
                android:layout_width="40dp"
                android:layout_height="0dp"
                android:background="@android:color/transparent"
                android:ems="10"
                android:focusable="false"
                android:gravity="center"
                android:inputType="number"
                android:textColor="@color/grey_light"
                android:textSize="13sp"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="@+id/img_add"
                app:layout_constraintEnd_toStartOf="@+id/img_add"
                app:layout_constraintTop_toTopOf="@+id/img_add"
                tools:text="10"
                tools:visibility="visible" />

            <ImageView
                android:id="@+id/img_minus"
                android:layout_width="30dp"
                android:layout_height="27dp"
                android:layout_marginBottom="5dp"
                android:padding="1dp"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="@+id/imageView4"
                app:layout_constraintEnd_toStartOf="@+id/edt_qty"
                app:srcCompat="@drawable/ic_remove_white_24dp"
                app:tint="@color/grey_light"
                tools:visibility="visible" />

        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.cardview.widget.CardView>
</layout>
