<?xml version="1.0" encoding="utf-8"?>
<layout>

    <data>

        <import type="android.view.View" />

        <variable
            name="model"
            type="com.uniq.uniqpos.model.PaymentSelected" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        android:id="@+id/layout_item_payment"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="3dp"
        android:background="@drawable/basic_background"
        android:padding="16dp">

        <ImageView
            android:id="@+id/img_payment_check"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:srcCompat="@drawable/ic_check_circle_blue_500_24dp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="9dp"
            android:fontFamily="@font/poppins_medium"
            android:text="@{model.method}"
            android:textColor="@color/grey_light"
            android:textSize="16sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@id/img_payment_check"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="Cash" />

        <TextView
            android:id="@+id/txt_payment_nominal"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/text_grey_light"
            android:textSize="12sp"
            android:visibility="@{model.total > 0 ? View.VISIBLE : View.INVISIBLE}"
            app:layout_constraintBottom_toTopOf="@id/txt_payment_info"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:textCurrency="@{model.total}"
            tools:text="15.000" />

        <TextView
            android:id="@+id/txt_payment_info"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@{model.description}"
            android:textColor="@color/text_grey_light"
            android:textSize="11sp"
            android:visibility="@{model.description.empty ? View.GONE : View.VISIBLE}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/txt_payment_nominal"
            tools:text="due: 23/09/2023"
            tools:visibility="visible" />

        <RelativeLayout
            android:id="@+id/txt_label_new"
            android:visibility="@{model.method.equals(`INSTANT PAYMENT`) ? View.VISIBLE : View.GONE}"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/btn_round_orange"
            android:paddingLeft="9dp"
            android:paddingRight="9dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="New"
                android:textColor="@color/white"
                android:textSize="11sp" />
        </RelativeLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
