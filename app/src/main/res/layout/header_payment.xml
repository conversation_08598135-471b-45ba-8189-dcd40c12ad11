<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:background="@color/background"
    android:paddingBottom="9dp"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/textView1"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        android:layout_marginTop="16dp"
        android:layout_marginLeft="16dp"
        android:text="Grand Total"
        android:textSize="13sp"
        android:textColor="@color/text_grey_light"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"/>

    <TextView
        android:id="@+id/txt_grand_total"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/textView1"
        tools:text="145.000"
        android:fontFamily="@font/poppins_medium"
        android:textSize="18sp"
        android:layout_marginLeft="16dp"
        android:textColor="@color/grey_light"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"/>

    <Button
        android:id="@+id/btn_split"
        android:text="Split Bill"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_margin="16dp"
        android:textSize="13sp"
        android:textColor="@color/grey_light"
        android:textAllCaps="false"
        android:background="@drawable/round_stroke_semi_dark"
        android:layout_width="wrap_content"
        android:layout_height="35dp"/>

</androidx.constraintlayout.widget.ConstraintLayout>