<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <FrameLayout
        android:id="@+id/field_container"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toStartOf="@+id/btn_pin"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageButton
        android:id="@+id/btn_pin"
        android:layout_width="36dp"
        android:layout_height="36dp"
        android:background="?attr/selectableItemBackgroundBorderless"
        android:contentDescription="Pin/Unpin field"
        android:padding="8dp"
        android:src="@android:drawable/btn_star"
        app:layout_constraintBottom_toBottomOf="@+id/field_container"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/field_container" />

</androidx.constraintlayout.widget.ConstraintLayout> 