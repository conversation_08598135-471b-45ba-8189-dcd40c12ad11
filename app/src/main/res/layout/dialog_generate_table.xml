<?xml version="1.0" encoding="utf-8"?>
<layout>

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        android:padding="16dp"
        android:background="@drawable/dialog_round"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            android:id="@+id/textView1"
            android:text="Generate Multiple Table"
            android:textColor="@color/grey_original"
            android:fontFamily="@font/poppins_medium"
            android:textSize="17sp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>
        <TextView
            android:text="fill the form below to generate multiple table at once"
            android:textColor="@color/text_grey_light"
            android:textSize="12sp"
            android:layout_marginBottom="9dp"
            app:layout_constraintBottom_toTopOf="@id/view1"
            app:layout_constraintStart_toStartOf="parent"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>

        <View
            android:id="@+id/view1"
            app:layout_constraintTop_toBottomOf="@id/textView1"
            android:layout_marginTop="23dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:background="@color/text_grey"
            android:layout_width="0dp"
            android:layout_height="1dp"/>

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/layout_edt_start"
            android:layout_marginTop="16dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/view1"
            app:layout_constraintEnd_toStartOf="@id/layout_edt_end"
            android:textColorHint="@color/text_grey_light"
            android:theme="@style/InputStyle"
            android:layout_width="0dp"
            android:layout_height="wrap_content">
            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/edt_start"
                android:hint="Start At"
                android:inputType="number"
                android:textColor="@color/text_grey_light"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"/>
        </com.google.android.material.textfield.TextInputLayout>

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/layout_edt_end"
            app:layout_constraintStart_toEndOf="@id/layout_edt_start"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/layout_edt_start"
            android:textColorHint="@color/text_grey_light"
            android:theme="@style/InputStyle"
            android:layout_width="0dp"
            android:layout_height="wrap_content">
            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/edt_end"
                android:hint="End At"
                android:inputType="number"
                android:textColor="@color/text_grey_light"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"/>
        </com.google.android.material.textfield.TextInputLayout>

        <Button
            android:id="@+id/btn_save"
            android:layout_marginTop="29dp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toBottomOf="@id/layout_edt_start"
            android:background="@drawable/btn_round_green"
            android:minHeight="40dp"
            android:text="Generate"
            android:textColor="@android:color/white" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
