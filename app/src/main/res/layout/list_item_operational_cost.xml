<?xml version="1.0" encoding="utf-8"?>
<layout>

    <data>
        <variable
            name="model"
            type="com.uniq.uniqpos.data.local.entity.OperationalCostEntity"/>
    </data>

    <androidx.cardview.widget.CardView
        xmlns:android="http://schemas.android.com/apk/res/android"
        app:cardUseCompatPadding="false"
        app:cardBackgroundColor="@color/background_selected"
        android:foreground="?android:attr/selectableItemBackground"
        android:clickable="true"
        android:layout_marginTop="3dp"
        xmlns:tools="http://schemas.android.com/tools"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:padding="9dp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
            <TextView
                android:id="@+id/txt_oc_name"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="Bayar Listrik"
                android:text="@{model.opcostName}"
                android:maxLines="1"
                android:ellipsize="end"
                android:paddingRight="16dp"
                android:textColor="@color/grey_light"
                app:fontFamily="@font/poppins"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toStartOf="@+id/txt_grandtotal"
                android:layout_width="0dp"
                android:layout_height="wrap_content" />

            <TextView
                android:id="@+id/txt_status"
                tools:text="Peralatan"
                android:text="@{model.purchaseCategoryName}"
                app:layout_constraintTop_toBottomOf="@+id/txt_oc_name"
                app:layout_constraintStart_toStartOf="parent"
                android:textColor="@color/text_grey_light"
                android:textSize="12sp"
                app:fontFamily="@font/poppins"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content" />

            <TextView
                android:text="|"
                app:layout_constraintTop_toBottomOf="@+id/txt_oc_name"
                app:layout_constraintStart_toEndOf="@+id/txt_status"
                android:layout_marginLeft="3dp"
                android:textColor="@color/text_grey_light"
                android:textSize="12sp"
                app:fontFamily="@font/poppins"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content" />

            <TextView
                android:id="@+id/txt_hour"
                tools:text="18:00"
                app:timeMillisToTime="@{model.timeCreated}"
                app:layout_constraintTop_toBottomOf="@+id/txt_oc_name"
                app:layout_constraintStart_toEndOf="@+id/txt_status"
                android:layout_marginLeft="9dp"
                android:textColor="@color/text_grey_light"
                android:textSize="12sp"
                app:fontFamily="@font/poppins"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content" />
            <TextView
                android:id="@+id/txt_grandtotal"
                tools:text="Rp149,000"
                app:textCurrency="@{model.total}"
                app:symbol="@{`Rp`}"
                app:fontFamily="@font/poppins"
                android:textColor="@color/text_orange"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content" />
            <TextView
                android:id="@+id/txt_tunai"
                tools:text="Tunai"
                android:text="@{model.payment}"
                android:textSize="12sp"
                app:layout_constraintTop_toBottomOf="@+id/txt_oc_name"
                app:layout_constraintEnd_toEndOf="parent"
                app:fontFamily="@font/poppins"
                android:textColor="@color/text_grey_light"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content" />

        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.cardview.widget.CardView>
</layout>
