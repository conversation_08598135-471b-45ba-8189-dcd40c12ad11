<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/background"
        tools:context="com.uniq.uniqpos.view.purchase.AddSupplierActivity">

        <ScrollView
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toTopOf="@+id/btn_save"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_width="0dp"
            android:layout_height="0dp">
            <androidx.constraintlayout.widget.ConstraintLayout
                android:padding="16dp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">
                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/layout_supplier_name"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    android:textColorHint="@color/text_grey_light"
                    android:theme="@style/InputStyle"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content">
                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/edt_name"
                        android:hint="@string/supplier_name"
                        android:textColor="@color/text_grey_light"
                        android:inputType="textCapWords"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content" />
                </com.google.android.material.textfield.TextInputLayout>
                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/layout_phone_number"
                    android:layout_marginTop="5dp"
                    app:layout_constraintTop_toBottomOf="@+id/layout_supplier_name"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    android:textColorHint="@color/text_grey_light"
                    android:theme="@style/InputStyle"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content">
                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/edt_phone"
                        android:hint="@string/phone_number"
                        android:textColor="@color/text_grey_light"
                        android:inputType="phone"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content" />
                </com.google.android.material.textfield.TextInputLayout>

                <TextView
                    android:id="@+id/txt_advance_mode"
                    android:text="ADVANCE MODE"
                    android:textColor="@color/grey_light"
                    android:fontFamily="@font/poppins_semibold"
                    app:layout_constraintTop_toBottomOf="@+id/layout_phone_number"
                    app:layout_constraintStart_toStartOf="parent"
                    android:layout_marginTop="16dp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content" />
                <View
                    android:id="@+id/view1"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintBottom_toBottomOf="@id/txt_advance_mode"
                    android:background="@color/text_grey_light"
                    android:layout_width="0dp"
                    android:layout_height="1dp"/>

                <TextView
                    android:id="@+id/txt_advance_detail"
                    android:textColor="@color/text_grey_light"
                    android:text="@string/advance_mode_detail_show"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/view1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content" />
                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/layout_address"
                    app:layout_constraintTop_toBottomOf="@+id/txt_advance_detail"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    android:layout_marginTop="16dp"
                    android:textColorHint="@color/text_grey_light"
                    android:theme="@style/InputStyle"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content">
                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/edt_address"
                        android:hint="Address"
                        android:inputType="textCapSentences"
                        android:textColor="@color/text_grey_light"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content" />
                </com.google.android.material.textfield.TextInputLayout>
                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/layout_city"
                    app:layout_constraintTop_toBottomOf="@+id/layout_address"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    android:layout_marginTop="5dp"
                    android:textColorHint="@color/text_grey_light"
                    android:theme="@style/InputStyle"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content">
                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/edt_city"
                        android:hint="City"
                        android:inputType="textCapWords"
                        android:textColor="@color/text_grey_light"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content" />
                </com.google.android.material.textfield.TextInputLayout>
                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/layout_email"
                    app:layout_constraintTop_toBottomOf="@+id/layout_city"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    android:layout_marginTop="5dp"
                    android:textColorHint="@color/text_grey_light"
                    android:theme="@style/InputStyle"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content">
                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/edt_email"
                        android:hint="Email"
                        android:inputType="textEmailAddress"
                        android:textColor="@color/text_grey_light"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content" />
                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/layout_npwp"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="5dp"
                    android:textColorHint="@color/text_grey_light"
                    android:theme="@style/InputStyle"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/layout_email">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/edt_npwp"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="NPWP"
                        android:inputType="number"
                        android:textColor="@color/text_grey_light" />
                </com.google.android.material.textfield.TextInputLayout>

            </androidx.constraintlayout.widget.ConstraintLayout>
        </ScrollView>
        <Button
            android:id="@+id/btn_save"
            android:text="@string/save"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:layout_margin="9dp"
            android:textColor="#ffff"
            android:background="@color/greeen_background"
            android:layout_width="0dp"
            android:layout_height="wrap_content" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
