<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/background"
        tools:context="com.uniq.uniqpos.view.cashdrawer.InputCashDrawerFragment">

        <TextView
            android:id="@+id/txt_warning"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_margin="16dp"
            android:fontFamily="@font/poppins"
            android:textColor="@color/text_orange"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="Masa trial anda akan berakhir dalam waktu 3 hari, segera lakukan perpanjangan. Setelah masa trial habis, akun ini tidak dapat digunakan lagi" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center"
            android:orientation="vertical">

            <ImageView
                android:layout_width="70dp"
                android:layout_height="70dp"
                android:src="@drawable/operator" />

            <TextView
                android:id="@+id/employee_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Annas BlackHat"
                android:textColor="@color/grey_light"
                android:textSize="16sp" />

            <LinearLayout
                android:id="@+id/layout_progress_shift"
                android:layout_width="250dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:layout_marginBottom="16dp"
                android:gravity="center_horizontal"
                android:orientation="vertical">

                <ProgressBar
                    android:id="@+id/progress_shift"
                    android:layout_width="35dp"
                    android:layout_height="50dp" />

                <ImageView
                    android:id="@+id/img_reload_shift"
                    android:layout_width="45dp"
                    android:layout_height="50dp"
                    android:visibility="gone"
                    app:srcCompat="@drawable/ic_replay_grey_500_24dp" />

                <TextView
                    android:id="@+id/txt_info_shift"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_horizontal"
                    android:text="@string/load_shift"
                    android:textColor="@color/text_grey_light"
                    android:textSize="13sp" />

                <TextView
                    android:id="@+id/txt_add_shift"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:text="@string/add_new_shift"
                    android:textColor="@color/greeen_background"
                    android:visibility="gone"
                    tools:visibility="visible" />
            </LinearLayout>

            <com.weiwangcn.betterspinner.library.material.MaterialBetterSpinner
                android:id="@+id/spin_shift"
                android:layout_width="250dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="26dp"
                android:hint="Shift"
                android:textColor="@color/text_grey_light"
                android:textColorHint="@color/text_grey_light"
                android:visibility="gone"
                app:met_baseColor="@color/text_grey_light"
                app:met_floatingLabel="normal"
                app:met_primaryColor="@color/text_grey_light" />

            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="250dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="9dp"
                android:textColorHint="@color/text_grey_light"
                android:theme="@style/InputStyle">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/edt_cash_awal"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="Kas Awal"
                    android:inputType="number"
                    android:maxLines="1"
                    android:textColor="@color/text_grey_light" />
            </com.google.android.material.textfield.TextInputLayout>

            <Button
                android:id="@+id/btn_submit"
                android:layout_width="250dp"
                android:layout_height="40dp"
                android:layout_marginTop="16dp"
                android:background="@color/greeen_background"
                android:text="@string/begin_transaction"
                android:textColor="@android:color/white" />
        </LinearLayout>

        <!--        <com.google.android.material.floatingactionbutton.ExtendedFloatingActionButton-->
        <!--            app:layout_constraintBottom_toBottomOf="parent"-->
        <!--            app:layout_constraintEnd_toEndOf="parent"-->
        <!--            android:text="Add Shift"-->
        <!--            app:layout_anchorGravity="bottom|right|end"-->
        <!--            app:icon="@drawable/ic_add_white_24dp"-->
        <!--            android:layout_width="wrap_content"-->
        <!--            android:layout_height="wrap_content"/>-->

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
