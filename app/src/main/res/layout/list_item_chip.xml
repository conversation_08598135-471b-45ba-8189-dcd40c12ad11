<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="model"
            type="String" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingRight="9dp">

        <Button
            android:id="@+id/btn_name"
            android:layout_width="wrap_content"
            android:layout_height="40dp"
            android:background="@drawable/round_orange"
            android:paddingLeft="7dp"
            android:paddingRight="7dp"
            android:text="@{model}"
            android:textAllCaps="false"
            android:textColor="@color/white"
            android:textSize="12sp"
            android:fontFamily="@font/poppins"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="Pikcup" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
