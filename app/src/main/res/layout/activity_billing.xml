<?xml version="1.0" encoding="utf-8"?>
<layout>

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/background"
        android:padding="16dp"
        tools:context=".view.billing.BillingActivity">

        <ScrollView
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginBottom="16dp"
            app:layout_constraintBottom_toTopOf="@id/txt_subtotal"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <com.weiwangcn.betterspinner.library.material.MaterialBetterSpinner
                    android:id="@+id/spin_service"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="choose service"
                    app:met_floatingLabel="normal"
                    app:met_floatingLabelTextColor="@color/text_grey_light"
                    app:met_textColor="@color/text_grey_light"
                    app:met_textColorHint="@color/text_grey_light"
                    app:met_underlineColor="@color/text_grey_light" />

                <com.weiwangcn.betterspinner.library.material.MaterialBetterSpinner
                    android:id="@+id/spin_period"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:hint="subscription period"
                    app:met_floatingLabel="normal"
                    app:met_floatingLabelTextColor="@color/text_grey_light"
                    app:met_textColor="@color/text_grey_light"
                    app:met_textColorHint="@color/text_grey_light"
                    app:met_underlineColor="@color/text_grey_light" />

                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:textColorHint="@color/text_grey_light"
                    android:theme="@style/InputStyle">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/edt_qty_slot"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="quantity slot"
                        android:inputType="number"
                        android:textColor="@color/text_grey_light" />
                </com.google.android.material.textfield.TextInputLayout>

                <com.weiwangcn.betterspinner.library.material.MaterialBetterSpinner
                    android:id="@+id/spin_payment_method"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:hint="payment method"
                    app:met_floatingLabel="normal"
                    app:met_floatingLabelTextColor="@color/text_grey_light"
                    app:met_textColor="@color/text_grey_light"
                    app:met_textColorHint="@color/text_grey_light"
                    app:met_underlineColor="@color/text_grey_light" />
            </LinearLayout>
        </ScrollView>

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="subtotal"
            android:textColor="@color/text_grey_light"
            app:layout_constraintBottom_toTopOf="@id/txt_subtotal"
            app:layout_constraintStart_toStartOf="parent" />

        <TextView
            android:id="@+id/txt_subtotal"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:fontFamily="@font/poppins_medium"
            android:text="Rp149,000"
            android:textColor="@color/grey_light"
            android:textSize="21sp"
            app:layout_constraintBottom_toTopOf="@id/btn_pay"
            app:layout_constraintStart_toStartOf="parent" />

        <Button
            android:id="@+id/btn_pay"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:background="@drawable/btn_round_green"
            android:text="Pay Now"
            android:textColor="@color/white"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
