<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>
        <variable
            name="model"
            type="com.uniq.uniqpos.data.remote.model.Employee"/>
    </data>
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/layout_root"
        android:foreground="?android:attr/selectableItemBackground"
        android:clickable="true"
        android:paddingLeft="16dp"
        android:paddingRight="16dp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <ImageView
            android:id="@+id/imageView"
            android:layout_width="wrap_content"
            android:layout_height="30dp"
            android:layout_marginTop="8dp"
            android:adjustViewBounds="true"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:srcCompat="@drawable/operator" />

        <TextView
            android:id="@+id/textView34"
            android:textColor="@color/text_grey_light"
            android:layout_width="wrap_content"
            android:layout_height="0dp"
            android:layout_marginEnd="8dp"
            android:layout_marginLeft="8dp"
            android:layout_marginRight="8dp"
            android:layout_marginStart="8dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:text="@{model.name}"
            app:fontFamily="@font/poppins"
            android:gravity="bottom"
            app:layout_constraintBottom_toBottomOf="@+id/imageView"
            android:paddingRight="13dp"
            app:layout_constraintStart_toEndOf="@+id/imageView"
            app:layout_constraintTop_toTopOf="@+id/imageView"
            tools:text="Ahmad Husain" />

        <ImageView
            android:id="@+id/img_check"
            android:visibility="invisible"
            tools:visibility="visible"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="8dp"
            android:layout_marginRight="8dp"
            app:layout_constraintBottom_toBottomOf="@+id/imageView"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:srcCompat="@drawable/ic_done_grey_600_24dp" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
