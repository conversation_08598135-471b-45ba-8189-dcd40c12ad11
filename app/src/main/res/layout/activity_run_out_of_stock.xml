
<layout  xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">
    <LinearLayout
        android:baselineAligned="false"
        android:background="@color/background"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:context="com.uniq.uniqpos.view.runoutstock.RunOutOfStockActivity">

        <RelativeLayout
            android:layout_width="1dp"
            android:layout_weight="1"
            android:layout_height="match_parent">
            <TextView
                android:id="@+id/textView1"
                android:padding="15dp"
                android:text="Available Products"
                android:textColor="@color/text_orange"
                tools:text="Available Products - 120 Items"
                app:fontFamily="@font/poppins_semibold"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content" />
            <View
                android:id="@+id/view1"
                android:layout_below="@+id/textView1"
                android:background="@color/text_grey"
                android:layout_width="match_parent"
                android:layout_height="1dp"/>
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recview_available"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                tools:listitem="@layout/list_item_menu_card"
                android:padding="15dp"
                android:layout_below="@+id/view1"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"/>
            <ProgressBar
                android:id="@+id/progress_available"
                android:visibility="gone"
                android:layout_centerVertical="true"
                android:layout_centerHorizontal="true"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content" />
        </RelativeLayout>

        <RelativeLayout
            android:layout_width="1dp"
            android:layout_weight="1"
            android:layout_height="match_parent">
            <TextView
                android:id="@+id/textView2"
                android:padding="15dp"
                android:text="Out Of Stock"
                android:textColor="@color/text_orange"
                tools:text="Out Of Stock - 12 Items"
                app:fontFamily="@font/poppins_semibold"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content" />
            <View
                android:id="@+id/view2"
                android:layout_below="@+id/textView2"
                android:background="@color/text_grey"
                android:layout_width="match_parent"
                android:layout_height="1dp"/>
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recview_unavailable"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                tools:listitem="@layout/list_item_menu_card"
                android:padding="15dp"
                android:layout_below="@+id/view2"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"/>
            <ProgressBar
                android:id="@+id/progress_runout"
                android:visibility="gone"
                android:layout_centerVertical="true"
                android:layout_centerHorizontal="true"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content" />
        </RelativeLayout>

    </LinearLayout>
</layout>
