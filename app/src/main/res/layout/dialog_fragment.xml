<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="9dp"
        tools:background="@color/background">

        <androidx.constraintlayout.widget.Group
            android:id="@+id/group_header"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone"
            app:constraint_referenced_ids="txt_save,img_close,txt_title"
            tools:visibility="visible" />

        <FrameLayout
            android:id="@+id/container_dialog"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingTop="9dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/txt_title" />

        <TextView
            android:id="@+id/txt_save"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="3dp"
            android:text="@string/save"
            android:textAllCaps="true"
            android:textColor="@color/greeen_background"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/img_close"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingRight="3dp"
            android:src="@drawable/ic_close"
            app:layout_constraintBottom_toBottomOf="@id/txt_title"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/txt_title" />

        <TextView
            android:id="@+id/txt_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:fontFamily="@font/poppins_medium"
            android:paddingRight="16dp"
            android:textColor="@color/grey_light"
            android:textSize="19sp"
            app:layout_constraintEnd_toStartOf="@id/txt_save"
            app:layout_constraintStart_toEndOf="@id/img_close"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="Ayam Bakar Asin" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
