<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="model"
            type="com.uniq.uniqpos.data.local.entity.EmployeeEntity" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="90dp"
        android:gravity="center_vertical"
        android:orientation="vertical"
        tools:background="@color/background">

        <ImageView
            android:id="@+id/img"
            android:layout_width="70dp"
            android:layout_height="70dp"
            android:layout_marginLeft="9dp"
            android:background="@android:color/transparent"
            android:padding="7dp"
            android:src="@drawable/operator"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="wrap_content"
            android:layout_height="45dp"
            android:layout_marginLeft="9dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@id/img"
            app:layout_constraintTop_toTopOf="parent">

            <TextView
                android:id="@+id/textView1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:text="@{model.name}"
                android:textColor="@color/grey_light"
                android:textSize="21sp"
                app:fontFamily="@font/poppins_medium"
                tools:text="Annas" />

            <TextView
                android:id="@+id/txt_position_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="27dp"
                tools:text="Kasir"
                android:text="Kasir"
                android:textSize="13sp"
                android:textColor="@color/text_grey_light"
                app:layout_constraintStart_toStartOf="@id/textView1"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <View
            android:layout_width="0dp"
            android:layout_height="1dp"
            android:background="@color/text_grey"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
