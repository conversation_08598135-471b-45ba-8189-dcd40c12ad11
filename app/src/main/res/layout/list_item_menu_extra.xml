<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">
    <data>
        <variable
            name="model"
            type="com.uniq.uniqpos.data.local.entity.ProductEntity"/>
    </data>
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="150dp"
        android:background="#F2F2F2"
        android:orientation="vertical"
        android:layout_marginLeft="2dp"
        android:layout_marginRight="2dp"
        android:layout_marginTop="4dp">

        <androidx.cardview.widget.CardView
            app:cardCornerRadius="3dp"
            android:innerRadius="0dp"
            android:shape="ring"
            android:thicknessRatio="1.9"
            android:layout_width="match_parent"
            android:layout_height="110dp">
            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">
                <ImageView
                    app:imgUrlText="@{model.photo}"
                    app:name="@{model.name}"
                    tools:src="@drawable/ayam"
                    android:scaleType="centerCrop"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent" />
                <RelativeLayout
                    android:id="@+id/layout_color"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"/>
                <RelativeLayout
                    android:id="@+id/layout_add"
                    android:layout_centerVertical="true"
                    android:layout_centerHorizontal="true"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent">
                    <ImageView
                        android:visibility="gone"
                        app:srcCompat="@drawable/ic_add_white_24dp"
                        android:layout_centerHorizontal="true"
                        android:layout_centerVertical="true"
                        android:layout_width="30dp"
                        android:layout_height="30dp" />
                </RelativeLayout>
                <LinearLayout
                    android:id="@+id/layout_edit_qty"
                    android:visibility="gone"
                    android:layout_centerVertical="true"
                    android:layout_centerHorizontal="true"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">
                    <RelativeLayout
                        android:id="@+id/min_qty"
                        android:layout_marginRight="7dp"
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:background="@drawable/back_round_stroke_white">
                        <ImageView
                            app:srcCompat="@drawable/ic_remove_white_24dp"
                            android:layout_margin="5dp"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent" />
                    </RelativeLayout>
                    <RelativeLayout
                        android:id="@+id/add_qty"
                        android:layout_marginLeft="7dp"
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:background="@drawable/back_round_stroke_white">
                        <ImageView
                            app:srcCompat="@drawable/ic_add_white_24dp"
                            android:layout_margin="5dp"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent" />
                    </RelativeLayout>
                </LinearLayout>
                <TextView
                    android:id="@+id/txt_qty"
                    android:visibility="gone"
                    android:text="0"
                    android:textSize="21sp"
                    android:layout_centerHorizontal="true"
                    android:layout_alignParentBottom="true"
                    android:layout_marginBottom="9dp"
                    android:textColor="@android:color/white"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content" />
            </RelativeLayout>
        </androidx.cardview.widget.CardView>

        <TextView
            android:id="@+id/title"
            android:text="@{model.name}"
            android:gravity="center_vertical"
            tools:text="Ayam Goreng"
            app:fontFamily="@font/poppins_medium"
            android:maxLines="1"
            android:ellipsize="end"
            android:layout_marginLeft="9dp"
            android:layout_marginRight="9dp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />
        <TextView
            app:strCurrency="@{model.priceSell ?? `0`}"
            app:symbol="@{`Rp`}"
            tools:text="Rp35,000"
            android:textSize="11sp"
            android:layout_marginLeft="9dp"
            android:layout_marginRight="9dp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" />
    </LinearLayout>
</layout>
