<?xml version="1.0" encoding="utf-8"?>
<layout>

    <data>

        <import type="android.view.View" />

        <variable
            name="model"
            type="com.uniq.uniqpos.data.remote.model.SelfOrder" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="9dp"
        android:background="@{model.appliedAt > 0 ? @color/background : @color/background_selected}"
        android:padding="9dp"
        tools:background="@color/background">

        <TextView
            android:id="@+id/textView1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@{model.customerName}"
            android:textColor="@color/grey_light"
            android:textSize="16sp"
            android:visibility="@{model.customerName == null ? View.GONE : View.VISIBLE}"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="John Done"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/textView2"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:maxLines="2"
            android:text="@{model.itemNames}"
            android:textColor="@color/text_grey_light"
            android:textSize="13sp"
            app:layout_constraintEnd_toStartOf="@id/txt_qty"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/textView1"
            tools:text="Nasi Goreng, Es Teh, Ayam Geprek Sayap" />

        <TextView
            android:id="@id/txt_qty"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@{model.orderCode}"
            android:textColor="@color/white"
            android:textSize="17sp"
            app:layout_constraintBottom_toBottomOf="@id/textView4"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="436750" />

        <TextView
            android:id="@id/textView4"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/text_grey_light"
            android:textSize="12sp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/textView2"
            app:timeMillisToTime="@{model.timeCreated}"
            tools:text="20 menit yang lalu" />

        <View
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="#99252E3A"
            tools:visibility="gone"
            android:visibility="@{model.appliedAt > 0 ? View.VISIBLE : View.GONE}"
            app:layout_constraintBottom_toBottomOf="@id/textView4"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/textView1" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
