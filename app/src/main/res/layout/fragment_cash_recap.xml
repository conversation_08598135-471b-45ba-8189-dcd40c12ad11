<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="fragment"
            type="com.uniq.uniqpos.view.closeshift.CloseShiftFragment" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/background"
        android:baselineAligned="false"
        android:orientation="horizontal"
        tools:context="id.uniq.uniqpos.view.closecashier.CashRecapFragment">

        <RelativeLayout
            android:id="@+id/layout_nota"
            android:layout_width="290dp"
            android:layout_height="0dp"
            android:background="@color/background_selected"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <include
                android:id="@+id/lay_no_selected"
                layout="@layout/layout_no_data"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true" />

        </RelativeLayout>

        <LinearLayout
            android:id="@+id/layout_header"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:background="@color/colorPrimary"
            android:orientation="horizontal"
            android:padding="7dp"
            android:visibility="gone"
            app:layout_constraintEnd_toStartOf="@+id/layout_nota"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/layout_warning">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="No."
                android:textColor="@color/grey_light"
                app:fontFamily="@font/poppins_semibold" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="4"
                android:text="Operator"
                android:textColor="@color/grey_light"
                app:fontFamily="@font/poppins_semibold" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="5"
                android:text="@string/date_time"
                android:textColor="@color/grey_light"
                app:fontFamily="@font/poppins_semibold" />
        </LinearLayout>

        <include
            android:id="@+id/layout_permission"
            layout="@layout/layout_no_permission"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/layout_nota"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recviewCashRecap"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginRight="2dp"
            android:paddingBottom="3dp"
            android:visibility="invisible"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            app:layout_constraintBottom_toTopOf="@+id/btn_add_close_cashier"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/layout_nota"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/layout_header"
            tools:listitem="@layout/list_item_cash_recap" />

        <TextView
            android:id="@+id/title_sales_today"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginRight="9dp"
            android:gravity="right"
            android:text="@string/sales_today"
            android:textColor="@color/text_grey_light"
            android:textSize="12sp"
            app:layout_constraintBottom_toTopOf="@+id/txt_total_sales"
            app:layout_constraintEnd_toStartOf="@+id/layout_nota"
            app:layout_constraintStart_toEndOf="@+id/btn_daily_recap" />

        <TextView
            android:id="@+id/txt_total_sales"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginRight="9dp"
            android:gravity="right"
            android:textColor="@color/grey_light"
            android:textSize="17sp"
            app:autoSizeTextType="uniform"
            app:fontFamily="@font/poppins_medium"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/layout_nota"
            app:layout_constraintStart_toEndOf="@+id/btn_daily_recap"
            tools:text="2.105.000" />

        <RelativeLayout
            android:id="@+id/layout_warning"
            tools:visibility="visible"
            android:visibility="gone"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:background="@color/orange_background"
            android:layout_width="0dp"
            android:padding="6dp"
            android:layout_height="wrap_content">

            <TextView
                android:id="@+id/txt_warning"
                android:fontFamily="@font/poppins_medium"
                tools:text="sinkronisasi terakhir 30 menit yang lalu"
                android:textColor="@color/white"
                android:textSize="12sp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>

           <RelativeLayout
               android:id="@+id/layout_close_warning"
               android:layout_alignParentRight="true"
               android:paddingRight="13dp"
               android:paddingLeft="13dp"
               android:paddingBottom="3dp"
               android:paddingTop="1dp"
               android:background="@drawable/round_grey"
               android:backgroundTint="@color/text_orange"
               android:layout_width="wrap_content"
               android:layout_height="wrap_content">
               <TextView
                   android:text="@string/close"
                   android:textSize="12sp"
                   android:textColor="@color/white"
                   android:layout_width="wrap_content"
                   android:layout_height="wrap_content"/>
           </RelativeLayout>
        </RelativeLayout>

        <Button
            android:id="@+id/btn_add_close_cashier"
            android:layout_width="wrap_content"
            android:layout_height="39dp"
            android:layout_marginLeft="9dp"
            android:layout_marginBottom="9dp"
            android:background="@color/blue_background"
            android:paddingLeft="9dp"
            android:paddingRight="9dp"
            android:text="@string/close_cashier"
            android:textAllCaps="false"
            android:textColor="#ffff"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <Button
            android:id="@+id/btn_daily_recap"
            android:layout_width="wrap_content"
            android:layout_height="39dp"
            android:layout_marginLeft="9dp"
            android:layout_marginBottom="9dp"
            android:background="@color/blue_background"
            android:paddingLeft="9dp"
            android:paddingRight="9dp"
            android:text="@string/daily_recap"
            android:textAllCaps="false"
            android:textColor="#ffff"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@+id/btn_add_close_cashier" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
