<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="viewmodel"
            type="com.uniq.uniqpos.view.payment.PaymentViewModel" />
    </data>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="10dp"
            tools:background="@color/background"
            tools:context=".view.payment.PaymentListFragment"
            tools:layout_height="match_parent">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="3dp"
                    android:fontFamily="@font/poppins"
                    android:text="@string/choose_payment_method"
                    android:textColor="@color/grey_light" />

                <TextView
                    android:id="@+id/txt_setting_payment"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:paddingRight="4dp"
                    android:text="@string/setting_underscore"
                    android:textColor="@color/text_grey_light"
                    android:textSize="12sp" />
            </RelativeLayout>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rec_view_paymnet"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:nestedScrollingEnabled="false"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                tools:itemCount="4"
                tools:listitem="@layout/list_item_payment_v2" />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginTop="19dp"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/send_receipt"
                    android:textColor="@color/grey_light"
                    android:textSize="16sp" />

                <ImageView
                    android:id="@+id/img_help_receipt"
                    android:layout_width="23dp"
                    android:layout_height="wrap_content"
                    android:paddingLeft="3dp"
                    app:srcCompat="@drawable/baseline_info_24" />
            </LinearLayout>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/send_receipt_info"
                android:textColor="@color/text_grey_light"
                android:textSize="13sp" />

            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content">

                <AutoCompleteTextView
                    android:id="@+id/edt_receipt_address"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:completionThreshold="4"
                    android:hint="@string/email_or_wa"
                    android:paddingRight="29dp"
                    android:text="@={viewmodel.receiptReceiver}"
                    android:textColor="@color/text_grey_light"
                    android:textColorHint="@color/text_grey_light"
                    android:textSize="14sp"
                    android:theme="@style/InputStyle" />

                <ImageView
                    android:id="@+id/img_clear_receipt"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentEnd="true"
                    android:paddingTop="7dp"
                    app:srcCompat="@drawable/ic_close" />
            </RelativeLayout>
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>
</layout>
