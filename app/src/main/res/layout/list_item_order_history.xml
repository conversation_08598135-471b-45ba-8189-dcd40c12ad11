<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>
        <import type="android.view.View" />
        <variable
            name="model"
            type="com.uniq.uniqpos.model.Order" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_rounded_dark"
        android:orientation="vertical"
        android:layout_marginBottom="8dp"
        android:padding="16dp">

        <!-- Item Header -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@{model.product.name}"
                android:textColor="@color/grey_light"
                android:textSize="16sp"
                android:textStyle="bold"
                tools:text="Americano" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:textCurrency="@{model.subTotal}"
                android:textColor="@color/grey_light"
                android:textSize="16sp"
                android:textStyle="bold"
                tools:text="50.000" />
        </LinearLayout>

        <!-- Price and Quantity -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:text='@{model.price + " × " + model.qty}'
            android:textColor="@color/text_grey_light"
            android:textSize="14sp"
            tools:text="25.000 × 2" />

        <!-- Extra Items Container -->
        <LinearLayout
            android:id="@+id/layoutExtra"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:orientation="vertical" />

        <!-- Discount Container -->
        <LinearLayout
            android:id="@+id/layoutDiscount"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:orientation="horizontal"
            android:visibility="@{model.discount.discountNominal > 0 ? View.VISIBLE : View.GONE}">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text='@{"Discount: " + (model.discount.discountType == "persen" ? model.discount.discount + "%" : "")}'
                android:textColor="@color/text_orange"
                android:textSize="14sp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:textCurrency="@{model.discount.discountNominal}"
                android:textColor="@color/text_orange"
                android:textSize="14sp"
                tools:text="5.000" />
        </LinearLayout>

        <!-- Notes Container -->
        <TextView
            android:id="@+id/txt_notes"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:text="@{model.note}"
            android:textColor="@color/text_grey_light"
            android:textSize="14sp"
            android:textStyle="italic"
            android:visibility="@{model.note != null &amp;&amp; !model.note.empty ? View.VISIBLE : View.GONE}"
            tools:text="Extra hot, less sugar" />

        <!-- Void Status -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:text="VOID"
            android:textColor="@color/red_text"
            android:textSize="14sp"
            android:textStyle="bold"
            android:visibility="@{model.itemVoid ? View.VISIBLE : View.GONE}" />

    </LinearLayout>
</layout>
