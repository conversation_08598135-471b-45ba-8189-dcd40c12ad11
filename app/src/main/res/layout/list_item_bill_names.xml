<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="android.view.View" />

        <variable
            name="model"
            type="com.uniq.uniqpos.data.local.entity.SalesEntity" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingBottom="9dp"
        tools:background="@color/background">

        <CheckBox
            android:id="@+id/cb_bill"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="@{model.customer}"
            android:textColor="@color/grey_light"
            android:theme="@style/CustomCheckBox"
            app:layout_constraintEnd_toStartOf="@id/textView2"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="Maruf Amin" />

        <TextView
            android:id="@+id/textView1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingRight="9dp"
            android:text="@{model.table}"
            android:textColor="@color/orange_background"
            android:visibility="@{model.table.equals(``) ? View.GONE : View.VISIBLE}"
            app:layout_constraintBottom_toBottomOf="@id/cb_bill"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/cb_bill"
            tools:text="06" />

        <TextView
            android:id="@id/textView2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingRight="9dp"
            android:text="#table"
            android:textColor="@color/text_grey_light"
            android:visibility="@{model.table.equals(``) ? View.GONE : View.VISIBLE}"
            app:layout_constraintBottom_toBottomOf="@id/textView1"
            app:layout_constraintEnd_toStartOf="@id/textView1"
            app:layout_constraintTop_toTopOf="@id/textView1" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
