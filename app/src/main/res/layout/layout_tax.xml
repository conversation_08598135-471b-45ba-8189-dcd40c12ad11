<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/colorPrimary"
        android:paddingBottom="9dp">

        <TextView
            android:id="@+id/textView1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_margin="8dp"
            android:text="Tax, Service and Discount"
            android:textColor="@color/text_grey_light"
            android:textSize="12sp"
            app:fontFamily="@font/poppins_medium"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/img_detail"
            android:layout_width="wrap_content"
            android:layout_height="0dp"
            android:paddingLeft="16dp"
            android:paddingTop="11dp"
            android:paddingBottom="15dp"
            android:visibility="invisible"
            app:layout_constraintBottom_toBottomOf="@id/txt_detail_tax"
            app:layout_constraintEnd_toStartOf="@id/txt_detail_tax"
            app:layout_constraintTop_toTopOf="@id/txt_detail_tax"
            app:srcCompat="@drawable/ic_border_color_white_24dp"
            app:tint="@color/greeen_background" />

        <TextView
            android:id="@+id/txt_detail_tax"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingTop="8dp"
            android:paddingRight="8dp"
            android:paddingBottom="8dp"
            android:text="Edit"
            android:textColor="@color/greeen_background"
            android:textSize="12sp"
            app:fontFamily="@font/poppins_medium"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <View
            android:id="@+id/view1"
            android:layout_width="0dp"
            android:layout_height="1dp"
            android:layout_marginLeft="3dp"
            android:layout_marginRight="3dp"
            android:background="@color/text_grey"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/textView1" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recview_tax"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="7dp"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/view1"
            tools:listitem="@layout/list_item_tax" />

        <ImageView
            android:id="@+id/img_add_free"
            android:layout_width="20dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="9dp"
            app:layout_constraintBottom_toBottomOf="@id/txt_add_free"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/txt_add_free"
            app:srcCompat="@drawable/ic_local_offer_grey_24dp"
            app:tint="@color/text_grey_light" />

        <TextView
            android:id="@+id/txt_add_free"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:fontFamily="@font/poppins"
            android:paddingLeft="9dp"
            android:paddingTop="3dp"
            android:paddingRight="16dp"
            android:paddingBottom="3dp"
            android:text="Add Free Item"
            android:textColor="@color/greeen_background"
            android:textSize="12sp"
            app:layout_constraintStart_toEndOf="@id/img_add_free"
            app:layout_constraintTop_toBottomOf="@id/recview_tax" />

        <TextView
            android:id="@+id/textView6"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="9dp"
            android:fontFamily="@font/poppins_medium"
            android:text="Voucher"
            android:textColor="@color/grey_light"
            android:textSize="13sp"
            android:visibility="gone"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/txt_add_free" />

        <ImageView
            android:id="@+id/imageView1"
            android:layout_width="20dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="9dp"
            android:layout_marginTop="9dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/txt_add_free"
            app:srcCompat="@drawable/ic_confirmation_number_white_24dp"
            app:tint="@color/text_grey_light" />

        <TextView
            android:id="@+id/txt_voucher"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="9dp"
            android:layout_marginTop="2dp"
            android:fontFamily="@font/poppins"
            android:text="@string/have_voucher_code"
            android:textColor="@color/text_grey_light"
            android:textSize="12sp"
            app:layout_constraintBottom_toBottomOf="@id/imageView1"
            app:layout_constraintEnd_toStartOf="@id/imageView2"
            app:layout_constraintStart_toEndOf="@id/imageView1"
            app:layout_constraintTop_toTopOf="@id/imageView1" />

        <ImageView
            android:id="@+id/imageView2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintBottom_toBottomOf="@id/txt_voucher"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/txt_voucher"
            app:srcCompat="@drawable/ic_keyboard_arrow_right_white_24dp"
            app:tint="@color/text_grey_light" />

        <androidx.constraintlayout.widget.Group
            android:id="@+id/group_voucher"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="visible"
            app:constraint_referenced_ids="imageView2,txt_voucher,imageView1" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>
