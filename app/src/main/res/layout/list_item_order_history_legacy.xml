<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">
    <data>
        <import type="android.view.View"/>
        <variable
            name="model"
            type="com.uniq.uniqpos.model.Order"/>
    </data>
    <LinearLayout
        tools:background="@color/background"
        android:orientation="horizontal"
        android:minHeight="50dp"
        android:paddingBottom="9dp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <RelativeLayout
            android:layout_width="45dp"
            android:layout_height="wrap_content">
            <de.hdodenhof.circleimageview.CircleImageView
                app:imgUrlTextRound="@{model.product.photo}"
                app:name="@{model.product.name}"
                tools:src="@drawable/ayam"
                android:layout_width="38dp"
                android:layout_height="38dp" />
            <TextView
                android:text="@{Integer.toString(model.qty)}"
                tools:text="3"
                android:textColor="@android:color/white"
                android:gravity="center"
                android:textSize="10sp"
                android:layout_alignParentRight="true"
                android:background="@drawable/round_grey"
                android:layout_width="23dp"
                android:layout_height="23dp" />
        </RelativeLayout>
        <LinearLayout
            android:layout_marginLeft="9dp"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@{model.product.name}"
                android:textColor="@color/grey_light"
                android:textSize="14sp"
                app:fontFamily="@font/poppins_medium"
                tools:text="Yamie Polos Asim Sedang" />
            <TextView
                tools:visibility="visible"
                android:visibility="gone"
                tools:text="Cabai 10"
                android:textSize="12sp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content" />
            <TextView
                tools:visibility="visible"
                android:visibility="gone"
                tools:text="Extra kuah"
                android:textSize="12sp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content" />
            <LinearLayout
                android:id="@+id/layoutExtra"
                android:orientation="vertical"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"/>
            <LinearLayout
                android:id="@+id/layoutDiscount"
                android:orientation="vertical"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"/>
            <TextView
                android:text="VOID"
                android:visibility="@{model.itemVoid ? View.VISIBLE : View.GONE}"
                android:textColor="@color/red_background"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content" />
        </LinearLayout>

        <LinearLayout
            android:gravity="right"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical">
            <TextView
                app:textCurrency="@{model.subTotal}"
                android:textColor="@color/grey_light"
                android:textSize="14sp"
                tools:text="9.500"
                app:fontFamily="@font/poppins_semibold"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content" />
            <LinearLayout
                android:id="@+id/layoutPriceExtra"
                android:orientation="vertical"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>
            <LinearLayout
                android:id="@+id/layoutPriceDiscount"
                android:orientation="vertical"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>
        </LinearLayout>
    </LinearLayout>
</layout>
