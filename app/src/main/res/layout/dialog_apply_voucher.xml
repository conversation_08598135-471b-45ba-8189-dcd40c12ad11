<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

    <ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingBottom="9dp"
        app:layout_behavior="android.support.design.widget.BottomSheetBehavior"
        tools:background="@color/background">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingBottom="9dp">

            <View
                android:id="@+id/view1"
                android:layout_width="50dp"
                android:layout_height="2dp"
                android:layout_marginTop="7dp"
                android:background="@color/grey_original"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />


            <androidx.constraintlayout.widget.Guideline
                android:id="@+id/guide_left"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginLeft="16dp"
                android:orientation="vertical"
                app:layout_constraintGuide_begin="16dp"
                app:layout_constraintStart_toStartOf="parent" />

            <androidx.constraintlayout.widget.Guideline
                android:id="@+id/guide_right"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="16dp"
                android:layout_marginRight="16dp"
                android:orientation="vertical"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintGuide_end="16dp" />

            <ImageView
                android:id="@+id/img_close"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:layout_constraintBottom_toBottomOf="@id/textView1"
                app:layout_constraintStart_toStartOf="@id/guide_left"
                app:layout_constraintTop_toTopOf="@id/textView1"
                app:srcCompat="@drawable/ic_clear_white_24dp"
                app:tint="@color/grey_light" />

            <TextView
                android:id="@+id/textView1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="9dp"
                android:layout_marginTop="16dp"
                android:fontFamily="@font/poppins_medium"
                android:text="@string/have_voucher_code"
                android:textColor="@color/grey_light"
                android:textSize="16sp"
                app:layout_constraintStart_toEndOf="@id/img_close"
                app:layout_constraintTop_toBottomOf="@id/view1" />

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/edt_voucher"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:hint="@string/enter_voucher_code"
                android:inputType="textCapCharacters"
                android:textColor="@color/text_grey_light"
                android:textColorHint="@color/text_grey_light"
                android:theme="@style/InputStyle"
                app:layout_constraintEnd_toEndOf="@id/guide_right"
                app:layout_constraintStart_toStartOf="@id/guide_left"
                app:layout_constraintTop_toBottomOf="@id/textView1" />

            <TextView
                android:id="@+id/txt_err_msg"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="3dp"
                android:ellipsize="end"
                android:maxLines="1"
                android:textColor="@color/red_background"
                app:layout_constraintEnd_toEndOf="@id/guide_right"
                app:layout_constraintStart_toStartOf="@id/guide_left"
                app:layout_constraintTop_toBottomOf="@id/edt_voucher"
                tools:text="invalid voucher code" />

            <com.uniq.materialanimation.ButtonLoading
                android:id="@+id/btn_apply"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="33dp"
                app:buttonColor="@color/greeen_background"
                app:buttonText="Apply Voucher"
                app:layout_constraintEnd_toEndOf="@id/guide_right"
                app:layout_constraintStart_toStartOf="@id/guide_left"
                app:layout_constraintTop_toBottomOf="@id/edt_voucher"
                app:textColor="@color/white" />
        </androidx.constraintlayout.widget.ConstraintLayout>

    </ScrollView>
</layout>
