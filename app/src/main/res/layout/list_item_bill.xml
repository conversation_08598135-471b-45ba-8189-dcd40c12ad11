<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="android.view.View" />

        <variable
            name="model"
            type="com.uniq.uniqpos.model.Order" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        tools:background="@color/background">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:minHeight="51dp"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/txtQty"
                android:layout_width="35dp"
                android:layout_height="match_parent"
                android:background="@color/colorPrimary"
                android:gravity="center"
                android:text="@{String.valueOf(model.qty)}"
                android:textColor="@android:color/white"
                tools:text="3" />

            <View
                android:layout_width="1dp"
                android:layout_height="match_parent"
                android:background="#2B3037" />

            <LinearLayout
                android:layout_width="25dp"
                android:layout_height="match_parent"
                android:background="@color/colorPrimary"
                android:orientation="vertical">

                <ImageView
                    android:id="@+id/imgAdd"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_weight="1"
                    android:padding="3dp"
                    android:visibility="@{model.enableAddQty? View.VISIBLE : View.INVISIBLE}"
                    app:srcCompat="@drawable/ic_add_white_24dp" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="#2B3037" />

                <ImageView
                    android:id="@+id/imgRemove"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_weight="1"
                    android:padding="3dp"
                    android:visibility="@{model.itemVoid ? View.INVISIBLE : View.VISIBLE}"
                    app:srcCompat="@drawable/ic_remove_white_24dp" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/layoutMenu"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="3"
                android:orientation="vertical"
                android:paddingLeft="9dp"
                android:paddingRight="9dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@{model.product.name}"
                    android:textColor="@color/text_orange"
                    app:fontFamily="@font/poppins"
                    tools:text="Paha Atas" />

                <TextView
                    android:id="@+id/txt_info"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@{model.info}"
                    android:textColor="@color/text_grey_light"
                    android:textSize="12sp"
                    android:textStyle="italic"
                    android:visibility="@{model.info == null ? View.GONE : View.VISIBLE}"
                    tools:text="(Special price applied)"
                    tools:visibility="visible" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@{`(` + model.note + `)`}"
                    android:textColor="@color/text_grey_light"
                    android:textSize="12sp"
                    android:visibility="@{model.note == null ? View.GONE : View.VISIBLE}"
                    tools:text="jangan pedes"
                    tools:visibility="visible" />

                <LinearLayout
                    android:id="@+id/layoutExtra"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical" />

                <LinearLayout
                    android:id="@+id/layoutDiscount"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="VOID"
                    android:textColor="@color/red_background"
                    android:visibility="@{model.itemVoid ? View.VISIBLE : View.GONE}" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/layout_price"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:gravity="right|center_vertical"
                android:orientation="vertical"
                android:paddingRight="7dp">

                <TextView
                    android:id="@+id/txtPrice"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/grey_light"
                    app:fontFamily="@font/poppins_semibold"
                    app:textCurrency="@{model.subTotal}"
                    tools:text="25.000" />

                <LinearLayout
                    android:id="@+id/layoutPriceExtra"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="vertical" />

                <LinearLayout
                    android:id="@+id/layoutDiscountPrice"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="vertical" />
            </LinearLayout>

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:srcCompat="@drawable/ic_keyboard_arrow_right_white_24dp"
                app:tint="@color/text_grey_light" />
        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:background="#797A7C" />
    </LinearLayout>
</layout>
