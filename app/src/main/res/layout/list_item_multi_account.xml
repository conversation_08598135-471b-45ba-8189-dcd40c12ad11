<?xml version="1.0" encoding="utf-8"?>
<layout>
    <data>
        <variable
            name="model"
            type="com.uniq.uniqpos.model.Account" />
    </data>
    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:tools="http://schemas.android.com/tools"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        tools:background="@color/background"
        android:paddingTop="16dp"
        android:paddingBottom="16dp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            android:id="@+id/textView1"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            android:textSize="16sp"
            android:text="@{model.businessName}"
            tools:text="UNIQ String Business"
            android:textColor="@color/grey_light"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>

        <TextView
            android:id="@id/textView2"
            app:layout_constraintTop_toBottomOf="@id/textView1"
            app:layout_constraintStart_toStartOf="parent"
            tools:text="Agus Gunadi"
            android:text="@{model.name}"
            android:textColor="@color/text_grey_light"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>

        <TextView
            tools:text="employee"
            android:textSize="12sp"
            android:visibility="gone"
            app:layout_constraintStart_toEndOf="@id/textView2"
            android:layout_marginLeft="16dp"
            android:textColor="@color/text_grey"
            android:gravity="center_vertical"
            app:layout_constraintTop_toTopOf="@id/textView2"
            app:layout_constraintBottom_toBottomOf="@id/textView2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>

        <ImageView
            app:srcCompat="@drawable/ic_keyboard_arrow_right_white_24dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
