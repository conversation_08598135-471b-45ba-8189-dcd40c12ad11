<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/dialog_round"
        android:padding="16dp">

        <TextView
            android:id="@+id/menuName"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:fontFamily="@font/poppins"
            android:maxLines="1"
            android:textColor="@color/orange_background"
            android:textSize="16sp"
            tools:text="Sayap Ayam Goreng" />

        <RelativeLayout
            android:id="@+id/layout_remove"
            android:layout_alignParentEnd="true"
            android:paddingRight="9dp"
            android:paddingLeft="9dp"
            android:paddingTop="4dp"
            android:paddingBottom="4dp"
            android:background="@drawable/round_stroke_semi_dark"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">
            <TextView
                android:text="@string/remove"
                android:textAllCaps="true"
                android:textSize="12sp"
                android:textColor="@color/red_background"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>
        </RelativeLayout>

        <View
            android:id="@+id/view1"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_below="@id/menuName"
            android:layout_marginTop="5dp"
            android:background="@color/text_grey" />

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_above="@id/btn_save"
            android:layout_below="@id/view1"
            android:layout_marginBottom="9dp">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/root_view"
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <Button
                    android:id="@+id/btn_note"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:layout_marginRight="5dp"
                    android:background="@drawable/stroke_white"
                    android:minHeight="40dp"
                    android:text="@string/note"
                    android:textColor="@android:color/white"
                    android:visibility="gone"
                    app:layout_constraintEnd_toStartOf="@id/btn_extra"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <Button
                    android:id="@+id/btn_extra"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="5dp"
                    android:layout_marginTop="16dp"
                    android:background="@drawable/stroke_white"
                    android:minHeight="40dp"
                    android:text="Extra / Topping"
                    android:textColor="@android:color/white"
                    android:visibility="gone"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@id/btn_note"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/txt_promo_info"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:fontFamily="@font/poppins"
                    android:text="@string/promo_applied_info"
                    android:textColor="@color/text_orange"
                    android:textSize="12sp"
                    android:visibility="gone"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/btn_note"
                    tools:visibility="visible" />

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/layout_price"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:textColorHint="@color/text_grey_light"
                    android:theme="@style/InputStyle"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/txt_promo_info">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/edt_price"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="5dp"
                        android:hint="@string/price"
                        android:inputType="number"
                        android:maxLength="13"
                        android:minHeight="35dp"
                        android:textColor="@color/text_grey_light" />
                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/layout_qty"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:textColorHint="@color/text_grey_light"
                    android:theme="@style/InputStyle"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/layout_price">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/edtQty"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="Quantity"
                        android:inputType="number"
                        android:maxLength="9"
                        android:minHeight="35dp"
                        android:textColor="@color/text_grey_light" />
                </com.google.android.material.textfield.TextInputLayout>

                <Button
                    android:id="@+id/btn_qty_add"
                    android:layout_width="59dp"
                    android:layout_height="30dp"
                    android:background="@drawable/backg_round_right"
                    android:backgroundTint="@color/background_selected"
                    android:fontFamily="@font/poppins_medium"
                    android:text="+"
                    android:textColor="@color/white"
                    android:textSize="19sp"
                    app:layout_constraintBottom_toBottomOf="@id/layout_qty"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="@id/layout_qty" />

                <Button
                    android:id="@+id/btn_qty_min"
                    android:layout_width="59dp"
                    android:layout_height="30dp"
                    android:background="@drawable/backg_round_left"
                    android:backgroundTint="#2C394A"
                    android:fontFamily="@font/poppins_medium"
                    android:text="-"
                    android:textColor="@color/white"
                    android:textSize="21sp"
                    app:layout_constraintBottom_toBottomOf="@id/btn_qty_add"
                    app:layout_constraintEnd_toStartOf="@id/btn_qty_add"
                    app:layout_constraintTop_toTopOf="@id/btn_qty_add" />

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/layout_note"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:hint="@string/note"
                    android:textColorHint="@color/text_grey_light"
                    android:theme="@style/InputStyle"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/layout_qty">

                    <com.google.android.material.textfield.MaterialAutoCompleteTextView
                        android:id="@+id/material_input_note"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:completionHint="tambah catatan"
                        android:completionThreshold="3"
                        android:inputType="textCapWords"
                        android:longClickable="false"
                        android:textColor="@color/text_grey_light" />
                </com.google.android.material.textfield.TextInputLayout>

                <androidx.appcompat.widget.AppCompatAutoCompleteTextView
                    android:id="@+id/input_note"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="9dp"
                    android:completionHint="silahkan gunakan string"
                    android:hint="@string/note"
                    android:textColor="@color/text_grey_light"
                    android:textColorHint="@color/text_grey_light"
                    android:theme="@style/InputStyle"
                    android:visibility="gone"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/layout_qty" />

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/layout_disc"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="9dp"
                    android:layout_marginRight="16dp"
                    android:textColorHint="@color/text_grey_light"
                    android:theme="@style/InputStyle"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintEnd_toStartOf="@id/btn_disc_nominal"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/layout_note">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/edt_discount"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:clickable="true"
                        android:hint="@string/discount"
                        android:inputType="number"
                        android:longClickable="false"
                        android:maxLength="13"
                        android:minHeight="35dp"
                        android:selectAllOnFocus="true"
                        android:textColor="@color/text_grey_light" />
                </com.google.android.material.textfield.TextInputLayout>

                <com.weiwangcn.betterspinner.library.material.MaterialBetterSpinner
                    android:id="@+id/spin_type"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="9dp"
                    android:layout_marginTop="5dp"
                    android:hint="Discount Type"
                    android:textColor="@android:color/white"
                    android:textColorHint="@android:color/white"
                    android:visibility="gone"
                    app:layout_constraintBottom_toBottomOf="@+id/layout_disc"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:met_baseColor="@android:color/white"
                    app:met_floatingLabel="normal"
                    app:met_primaryColor="@android:color/white" />

                <Button
                    android:id="@+id/btn_disc_nominal"
                    android:layout_width="59dp"
                    android:layout_height="30dp"
                    android:layout_gravity="bottom"
                    android:layout_marginBottom="9dp"
                    android:background="@drawable/backg_round_left_diselect"
                    android:text="@string/nominal"
                    android:textAllCaps="false"
                    android:textColor="#ffff"
                    android:textSize="11sp"
                    app:layout_constraintBottom_toBottomOf="@id/layout_disc"
                    app:layout_constraintEnd_toStartOf="@id/btn_disc_percent" />

                <Button
                    android:id="@+id/btn_disc_percent"
                    android:layout_width="59dp"
                    android:layout_height="30dp"
                    android:layout_gravity="bottom"
                    android:layout_marginBottom="5dp"
                    android:background="@drawable/backg_round_right"
                    android:text="@string/percent"
                    android:textAllCaps="false"
                    android:textColor="#ffff"
                    android:textSize="11sp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="@id/btn_disc_nominal" />

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/layout_disc_inf"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:textColorHint="@color/text_grey_light"
                    android:theme="@style/InputStyle"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/layout_disc">

                    <!--                    <com.google.android.material.textfield.TextInputEditText-->
                    <!--                        android:id="@+id/edt_info"-->
                    <!--                        android:layout_width="match_parent"-->
                    <!--                        android:layout_height="wrap_content"-->
                    <!--                        android:hint="@string/discount_information"-->
                    <!--                        android:inputType="textCapWords"-->
                    <!--                        android:longClickable="false"-->
                    <!--                        android:minHeight="35dp"-->
                    <!--                        android:textColor="@color/text_grey_light" />-->

                    <com.google.android.material.textfield.MaterialAutoCompleteTextView
                        android:id="@+id/edt_info"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="@string/discount_information"
                        android:inputType="textCapWords"
                        android:longClickable="false"
                        android:textColor="@color/text_grey_light" />
                </com.google.android.material.textfield.TextInputLayout>

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="9dp"
                    android:paddingLeft="5dp"
                    android:text="@string/discount_is_disable"
                    android:textColor="@color/red_background"
                    android:visibility="gone"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="@id/layout_disc" />

                <ImageView
                    android:id="@+id/imageView1"
                    android:layout_width="wrap_content"
                    android:layout_height="17dp"
                    android:layout_marginTop="16dp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/layout_disc_inf"
                    app:srcCompat="@drawable/ic_baseline_money_off_24" />

                <TextView
                    android:id="@+id/txt_free"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/poppins"
                    android:paddingLeft="9dp"
                    android:paddingTop="3dp"
                    android:text="@string/set_as_free_item"
                    android:textAllCaps="true"
                    android:textColor="@color/grey_light"
                    app:layout_constraintBottom_toBottomOf="@id/imageView1"
                    app:layout_constraintStart_toEndOf="@id/imageView1"
                    app:layout_constraintTop_toTopOf="@id/imageView1" />

                <ImageView
                    android:id="@+id/imageView2"
                    android:visibility="gone"
                    android:layout_width="23dp"
                    android:layout_height="17dp"
                    android:layout_marginTop="9dp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/imageView1"
                    app:srcCompat="@drawable/ic_trash"
                    app:tint="@color/red_background" />

                <TextView
                    android:id="@+id/txt_delete"
                    android:visibility="gone"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/poppins"
                    android:paddingLeft="9dp"
                    android:paddingTop="3dp"
                    android:text="@string/remove_item"
                    android:textAllCaps="true"
                    android:textColor="@color/red_background"
                    app:layout_constraintBottom_toBottomOf="@id/imageView2"
                    app:layout_constraintStart_toEndOf="@id/imageView2"
                    app:layout_constraintTop_toTopOf="@id/imageView2" />

            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.core.widget.NestedScrollView>

        <Button
            android:id="@+id/btn_save"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:background="@drawable/btn_round_green"
            android:minHeight="40dp"
            android:text="@string/save"
            android:textColor="@android:color/white" />

    </RelativeLayout>
</layout>
