<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="model"
            type="com.uniq.uniqpos.model.Order" />
    </data>

    <androidx.cardview.widget.CardView
        android:foreground="?android:attr/selectableItemBackground"
        android:clickable="true"
        app:cardUseCompatPadding="false"
        app:cardBackgroundColor="@color/background"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="65dp">

            <TextView
                android:id="@+id/img_food"
                android:layout_width="65dp"
                android:layout_height="0dp"
                android:layout_centerHorizontal="true"
                android:layout_centerVertical="true"
                android:gravity="center"
                android:text="@{String.valueOf(model.qty)}"
                android:textColor="#ffff"
                android:textSize="23sp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:textRandomBack="@{model.product.name}"
                tools:background="@color/greeen_background"
                tools:text="7" />

            <TextView
                android:id="@+id/txt_menu"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="8dp"
                android:layout_marginRight="8dp"
                android:background="@android:color/transparent"
                android:ellipsize="end"
                android:maxLines="2"
                android:text="@{model.product.name}"
                android:textColor="@color/grey_light"
                android:textSize="15sp"
                app:fontFamily="@font/poppins_medium"
                app:layout_constraintBottom_toTopOf="@+id/textView36"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/img_food"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_chainStyle="packed"
                tools:text="Yamie Polos Asin Sedang" />

            <TextView
                android:id="@+id/textView36"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="8dp"
                android:textColor="@color/text_grey_light"
                android:textSize="12sp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toEndOf="@+id/img_food"
                app:layout_constraintTop_toBottomOf="@+id/txt_menu"
                app:textCurrency="@{safeUnbox(model.product.priceSell)}"
                tools:text="Rp12,500" />
            <View
                android:background="@color/background_selected"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                android:layout_width="0dp"
                android:layout_height="1dp"/>
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.cardview.widget.CardView>
</layout>
