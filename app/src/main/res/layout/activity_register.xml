<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/colorPrimary"
        tools:context="com.uniq.uniqpos.view.login.LoginActivity">

        <ScrollView
            android:layout_alignParentTop="true"
            android:layout_marginBottom="37dp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
            <LinearLayout
                android:gravity="center_horizontal"
                android:orientation="vertical"
                android:padding="@dimen/activity_horizontal_margin"
                android:focusableInTouchMode="true"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
                <ImageView
                    app:srcCompat="@drawable/ic_logo_uniq_white"
                    android:layout_marginTop="50dp"
                    android:adjustViewBounds="true"
                    android:layout_width="120dp"
                    android:layout_height="wrap_content" />
                <TextView
                    android:text="@string/login_to_continue"
                    android:textColor="@color/text_grey"
                    android:layout_marginTop="5dp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content" />
                <com.google.android.material.textfield.TextInputLayout
                    android:layout_marginTop="60dp"
                    android:theme="@style/InputStyle"
                    android:textColorHint="@color/text_grey_light"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">
                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/edt_name"
                        android:hint="Name"
                        android:textColor="@color/text_grey_light"
                        android:inputType="textCapWords"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content" />
                </com.google.android.material.textfield.TextInputLayout>
                <com.google.android.material.textfield.TextInputLayout
                    android:layout_marginTop="9dp"
                    android:theme="@style/InputStyle"
                    android:textColorHint="@color/text_grey_light"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">
                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/edt_email"
                        android:hint="Email"
                        android:textColor="@color/text_grey_light"
                        android:inputType="textEmailAddress"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content" />
                </com.google.android.material.textfield.TextInputLayout>
                <com.google.android.material.textfield.TextInputLayout
                    android:layout_marginTop="9dp"
                    android:theme="@style/InputStyle"
                    android:textColorHint="@color/text_grey_light"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">
                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/edt_phone"
                        android:hint="Phone Number"
                        android:textColor="@color/text_grey_light"
                        android:inputType="number"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content" />
                </com.google.android.material.textfield.TextInputLayout>
                <com.google.android.material.textfield.TextInputLayout
                    android:layout_marginTop="9dp"
                    android:theme="@style/InputStyle"
                    android:textColorHint="@color/text_grey_light"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">
                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/bussiness_name"
                        android:hint="Bussiness Name"
                        android:textColor="@color/text_grey_light"
                        android:inputType="textCapWords"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content" />
                </com.google.android.material.textfield.TextInputLayout>
                <Button
                    android:id="@+id/btn_register"
                    android:text="@string/register"
                    android:textColor="@color/text_grey_light"
                    android:layout_marginTop="30dp"
                    android:background="@drawable/btn_round"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content" />
            </LinearLayout>
        </ScrollView>

        <TextView
            android:id="@+id/sigin"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_centerHorizontal="true"
            android:layout_marginBottom="16dp"
            android:text="@string/have_account"
            android:textColor="#BDBDBD"
            app:fontFamily="@font/poppins" />
    </RelativeLayout>
</layout>
