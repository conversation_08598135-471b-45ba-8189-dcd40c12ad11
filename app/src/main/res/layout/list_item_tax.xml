<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="model"
            type="com.uniq.uniqpos.model.TaxSales" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:paddingRight="5dp"
        tools:background="@color/colorPrimary">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:ellipsize="end"
            android:gravity="right"
            android:maxLines="2"
            android:text="@{model.name}"
            android:textColor="@color/text_grey_light"
            android:textSize="13sp"
            app:fontFamily="@font/poppins"
            tools:text="Pajak" />

        <TextView
            android:layout_width="150dp"
            android:layout_height="wrap_content"
            android:gravity="right"
            android:textColor="@color/text_orange"
            android:textSize="13sp"
            app:textCurrency="@{model.total}"
            tools:text="23,000" />

    </LinearLayout>
</layout>
