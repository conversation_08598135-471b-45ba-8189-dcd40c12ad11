<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@color/background"
    android:padding="16dp"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <ImageView
        android:id="@+id/img_close"
        app:srcCompat="@drawable/ic_close"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"/>

    <TextView
        android:id="@+id/txt_title"
        app:layout_constraintEnd_toStartOf="@+id/img_close"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        android:text="Oopss! Voucher Promo ini sudah pernah digunakan!"
        android:textSize="19sp"
        android:textColor="@color/grey_light"
        android:fontFamily="@font/poppins_medium"
        android:layout_width="0dp"
        android:layout_height="wrap_content"/>

    <TextView
        android:id="@+id/txt_subtitle"
        app:layout_constraintTop_toBottomOf="@id/txt_title"
        app:layout_constraintStart_toStartOf="parent"
        android:text="berikut list penggunaan promo ini:"
        android:textColor="@color/text_grey_light"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"/>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recview_usage"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        tools:listitem="@layout/list_item_promotion_usage"
        tools:itemCount="3"
        app:layout_constraintTop_toBottomOf="@id/txt_subtitle"
        android:layout_marginTop="9dp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"/>

</androidx.constraintlayout.widget.ConstraintLayout>