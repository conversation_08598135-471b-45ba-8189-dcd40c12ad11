<?xml version="1.0" encoding="utf-8"?>
<layout>

    <data>

        <variable
            name="model"
            type="com.uniq.uniqpos.data.local.entity.BankEntity" />
    </data>

    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:tools="http://schemas.android.com/tools"
        android:clickable="true"
        android:foreground="?android:attr/selectableItemBackground"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingLeft="9dp"
        android:paddingTop="16dp"
        android:paddingRight="9dp"
        android:paddingBottom="16dp"
        tools:background="@color/background">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@{model.name}"
            android:fontFamily="@font/poppins_medium"
            android:textColor="@color/text_grey_light"
            tools:text="GoPay" />

    </LinearLayout>
</layout>
