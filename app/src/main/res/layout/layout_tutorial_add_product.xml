<?xml version="1.0" encoding="utf-8"?>
<layout>

    <ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp"
            tools:background="@color/colorPrimaryDark">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/warning_have_no_product"
                android:textColor="@color/grey_light" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="9dp"
                android:fontFamily="@font/poppins"
                android:text="@string/tutorial_product_first"
                android:textColor="@color/grey_light"
                android:textSize="12sp" />

            <ImageView
                android:layout_width="330dp"
                android:layout_height="200dp"
                android:src="@drawable/tutorial_add_product_1" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:fontFamily="@font/poppins"
                android:text="@string/tutorial_product_second"
                android:textColor="@color/grey_light"
                android:textSize="12sp" />

            <ImageView
                android:layout_width="330dp"
                android:layout_height="200dp"
                android:scaleType="fitStart"
                android:src="@drawable/tutorial_add_product_2" />
        </LinearLayout>

    </ScrollView>
</layout>
