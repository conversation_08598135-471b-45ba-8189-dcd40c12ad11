<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="model"
            type="com.uniq.uniqpos.data.local.entity.CashRecapEntity" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/layout_item"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/background"
            android:paddingLeft="9dp"
            android:paddingTop="15dp"
            android:paddingRight="9dp"
            android:paddingBottom="15dp">

            <TextView
                android:id="@+id/textView1"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:fontFamily="@font/poppins"
                android:maxLines="1"
                android:paddingLeft="2dp"
                android:text="@{model.shiftName}"
                android:textColor="@color/grey_light"
                android:textSize="15sp"
                app:layout_constraintEnd_toStartOf="@id/textView2"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="Shift Pagi" />

            <TextView
                android:id="@id/textView2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/poppins"
                android:textColor="@color/text_grey_light"
                android:textSize="13sp"
                app:format="@{`dd MMM yyyy HH:mm`}"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:timeMillisToDate="@{model.timeCreated}"
                tools:text="29 August 2020 14:30" />

            <ImageView
                android:id="@+id/imageView1"
                android:layout_width="17dp"
                android:layout_height="17dp"
                android:paddingBottom="1dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/textView1"
                app:srcCompat="@drawable/ic_person_grey_500_24dp" />

            <TextView
                android:id="@id/txt_qty"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="3dp"
                android:text="@{model.employeeName}"
                android:textColor="@color/text_grey_light"
                android:textSize="13sp"
                app:layout_constraintBottom_toBottomOf="@id/imageView1"
                app:layout_constraintStart_toEndOf="@id/imageView1"
                tools:text="Feni Rosyana" />

            <TextView
                android:id="@id/textView4"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/orange_background"
                android:textSize="12sp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="@id/txt_qty"
                app:textCurrency="@{model.card + model.cash}"
                tools:text="3,500,000" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginRight="5dp"
                android:text="#aktual"
                android:textColor="@color/text_grey_light"
                android:textSize="12sp"
                app:layout_constraintEnd_toStartOf="@id/textView4"
                app:layout_constraintTop_toTopOf="@id/textView4" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginLeft="9dp"
            android:layout_marginRight="9dp"
            android:background="@color/text_grey"/>
    </LinearLayout>

</layout>
