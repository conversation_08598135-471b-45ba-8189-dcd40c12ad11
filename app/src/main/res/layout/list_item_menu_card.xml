<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="android.view.View" />

        <variable
            name="model"
            type="com.uniq.uniqpos.data.local.entity.ProductEntity" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        tools:background="@color/background"
        android:background="@android:color/transparent"
        android:layout_height="65dp"
        android:clickable="true"
        android:foreground="?android:attr/selectableItemBackground">

        <View
            android:id="@+id/separator"
            android:background="@android:color/black"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:layout_width="0dp"
            android:layout_height="1dp"/>
        <ImageView
            android:id="@+id/img_product"
            android:layout_width="65dp"
            android:layout_height="65dp"
            app:layout_constraintTop_toBottomOf="@+id/separator"
            app:layout_constraintStart_toStartOf="parent"
            android:scaleType="centerCrop"
            app:imgUrlText="@{model.photo}"
            app:name="@{model.name}"
            tools:src="@drawable/ayam" />

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginLeft="8dp"
            android:layout_marginRight="8dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/txt_qty"
            app:layout_constraintStart_toEndOf="@+id/img_product"
            app:layout_constraintTop_toBottomOf="@+id/separator">

            <TextView
                android:id="@+id/txt_menu"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:maxLines="2"
                android:text="@{model.name}"
                android:textColor="@color/grey_light"
                android:textSize="15sp"
                app:fontFamily="@font/poppins_medium"
                tools:text="Yamie Polos Asin Sedang" />

            <TextView
                android:id="@+id/txt_sku"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/text_grey_light"
                android:textSize="10sp"
                android:text="@{model.sku}"
                android:visibility="@{model.sku != null &amp;&amp; !model.sku.empty ? View.VISIBLE : View.GONE}"
                tools:text="SKU123" />

            <TextView
                android:id="@+id/textView36"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/text_grey_light"
                android:textSize="12sp"
                app:textCurrency="@{safeUnbox(model.priceSell)}"
                tools:text="Rp12,500" />
        </LinearLayout>

        <TextView
            android:id="@+id/txt_qty"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_margin="8dp"
            android:fontFamily="@font/poppins"
            android:text="@{`stock: ` + String.valueOf(model.stockQty)}"
            android:textColor="@color/text_grey_light"
            android:textSize="13sp"
            android:visibility="@{model.isShowStock ? View.VISIBLE : View.GONE}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/separator"
            tools:text="stock: 3" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
