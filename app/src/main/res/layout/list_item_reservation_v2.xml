<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">


    <data>
        <variable
            name="model"
            type="com.uniq.uniqpos.data.local.entity.ReservationEntity"/>
    </data>

    <androidx.cardview.widget.CardView
        android:foreground="?android:attr/selectableItemBackground"
        android:clickable="true"
        app:cardUseCompatPadding="true"
        android:layout_marginRight="3dp"
        android:layout_marginLeft="3dp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <TextView
                android:id="@+id/textView26"
                app:timeMillisToDate="@{model.timeStart}"
                app:format="@{`dd`}"
                android:layout_width="50dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="16dp"
                android:layout_marginStart="16dp"
                android:gravity="center"
                tools:text="17"
                android:textSize="28sp"
                app:layout_constraintBottom_toTopOf="@+id/textView27"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_chainStyle="packed" />

            <TextView
                android:id="@+id/textView27"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:timeMillisToDate="@{model.timeStart}"
                app:format="@{`MMM`}"
                tools:text="MAY"
                app:fontFamily="@font/poppins"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="@+id/textView26"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toStartOf="@+id/textView26"
                app:layout_constraintTop_toBottomOf="@+id/textView26" />

            <View
                android:id="@+id/view3"
                android:layout_width="1dp"
                android:layout_height="90dp"
                android:layout_marginLeft="8dp"
                android:layout_marginStart="8dp"
                android:background="@color/grey_light"
                app:layout_constraintStart_toEndOf="@+id/textView26"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/textView28"
                android:text="@{model.name}"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="14dp"
                android:layout_marginStart="14dp"
                android:layout_marginTop="8dp"
                tools:text="Buya Hamka"
                android:textStyle="bold"
                app:fontFamily="@font/poppins"
                app:layout_constraintStart_toEndOf="@+id/view3"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/textView29"
                android:text="@{model.phone}"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="14dp"
                android:layout_marginStart="14dp"
                tools:text="085632678219"
                android:textColor="@color/grey_light"
                app:layout_constraintStart_toEndOf="@+id/view3"
                app:layout_constraintTop_toBottomOf="@+id/textView28" />

            <ImageView
                android:id="@+id/imageView3"
                android:layout_width="wrap_content"
                android:layout_height="20dp"
                android:layout_marginLeft="14dp"
                android:layout_marginStart="14dp"
                android:layout_marginTop="8dp"
                app:layout_constraintStart_toEndOf="@+id/view3"
                app:layout_constraintTop_toBottomOf="@+id/textView29"
                app:srcCompat="@android:drawable/ic_lock_idle_alarm" />

            <TextView
                android:id="@+id/textView30"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="8dp"
                android:layout_marginStart="8dp"
                android:layout_marginTop="8dp"
                android:text="10:30 - 12:00"
                android:textStyle="bold"
                app:layout_constraintStart_toEndOf="@+id/imageView3"
                app:layout_constraintTop_toBottomOf="@+id/textView29" />

            <TextView
                android:id="@+id/textView31"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="16dp"
                android:layout_marginRight="16dp"
                android:text="table"
                android:textColor="@color/grey_light"
                app:layout_constraintBottom_toTopOf="@+id/textView32"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_chainStyle="packed" />

            <TextView
                android:id="@+id/textView32"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="16dp"
                android:layout_marginRight="16dp"
                android:text="@{model.tableName}"
                tools:text="09"
                android:textSize="18sp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/textView31" />
        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.cardview.widget.CardView>
</layout>
