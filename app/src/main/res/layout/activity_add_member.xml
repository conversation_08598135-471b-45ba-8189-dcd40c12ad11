<?xml version="1.0" encoding="utf-8"?>
<layout>

    <ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:context="com.uniq.uniqpos.view.member.AddMemberActivity">

        <LinearLayout
            android:padding="16dp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">
            <com.weiwangcn.betterspinner.library.material.MaterialBetterSpinner
                android:id="@+id/spin_type"
                android:hint="@string/type"
                app:met_floatingLabel="normal"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />
            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/edt_name"
                    android:hint="@string/name"
                    android:inputType="textCapWords"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content" />
            </com.google.android.material.textfield.TextInputLayout>
            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/edt_phone"
                    android:hint="No HP"
                    android:inputType="phone"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content" />
            </com.google.android.material.textfield.TextInputLayout>
            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/edt_email"
                    android:hint="Email"
                    android:inputType="textEmailAddress"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content" />
            </com.google.android.material.textfield.TextInputLayout>
            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/edt_birthday"
                    android:hint="@string/birthday"
                    android:focusable="false"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content" />
            </com.google.android.material.textfield.TextInputLayout>
            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/edt_address"
                    android:hint="@string/address"
                    android:inputType="textCapSentences"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content" />
            </com.google.android.material.textfield.TextInputLayout>
            <Button
                android:id="@+id/btn_next"
                android:text="NEXT"
                android:layout_marginTop="16dp"
                android:textColor="@android:color/white"
                android:background="@color/colorPrimary"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />
        </LinearLayout>
    </ScrollView>
</layout>
