<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/background"
        tools:context="com.uniq.uniqpos.view.ordersummary.OrderSummaryActivity">

        <TextView
            android:id="@+id/textView2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_margin="9dp"
            android:fontFamily="@font/poppins"
            android:text="Order Detail"
            android:textColor="@color/text_grey_light"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/txt_add_more"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_margin="9dp"
            android:fontFamily="@font/poppins"
            android:text="+ ADD MORE"
            android:textColor="@color/greeen_background"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recview_bill"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginTop="9dp"
            android:layout_marginEnd="8dp"
            android:layout_marginRight="8dp"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            app:layout_constraintBottom_toTopOf="@id/layout_tax_disc"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="1.0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/textView2"
            app:layout_constraintVertical_bias="1.0"
            tools:listitem="@layout/list_item_bill" />

        <include
            android:id="@+id/layout_tax_disc"
            layout="@layout/layout_tax"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toTopOf="@+id/layout_pay"
            android:layout_marginBottom="3dp"
            android:layout_height="wrap_content"
            android:layout_width="0dp" />


        <!--<View-->
            <!--android:id="@+id/layout_payment"-->
            <!--android:layout_width="0dp"-->
            <!--android:layout_height="0dp"-->
            <!--android:background="@color/greeen_background"-->
            <!--android:visibility="invisible"-->
            <!--app:layout_constraintBottom_toBottomOf="parent"-->
            <!--app:layout_constraintEnd_toEndOf="parent"-->
            <!--app:layout_constraintStart_toStartOf="parent"-->
            <!--app:layout_constraintTop_toTopOf="@id/txt_grand_total" />-->

        <!--<TextView-->
            <!--android:layout_width="wrap_content"-->
            <!--android:layout_height="wrap_content"-->
            <!--android:text="@string/payment"-->
            <!--android:layout_marginLeft="32dp"-->
            <!--android:layout_marginStart="32dp"-->
            <!--android:textColor="#ffff"-->
            <!--android:visibility="invisible"-->
            <!--app:layout_constraintTop_toTopOf="@id/txt_grand_total"-->
            <!--app:layout_constraintBottom_toBottomOf="@id/txt_grand_total"-->
            <!--app:layout_constraintStart_toStartOf="parent" />-->

        <!--<TextView-->
            <!--android:id="@+id/txt_grand_total"-->
            <!--android:layout_width="wrap_content"-->
            <!--android:layout_height="wrap_content"-->
            <!--android:layout_marginEnd="8dp"-->
            <!--android:layout_marginRight="8dp"-->
            <!--android:paddingTop="12dp"-->
            <!--android:paddingBottom="12dp"-->
            <!--android:text="Rp135,000"-->
            <!--android:textColor="#ffff"-->
            <!--android:visibility="invisible"-->
            <!--app:fontFamily="@font/poppins_medium"-->
            <!--app:layout_constraintBottom_toBottomOf="parent"-->
            <!--app:layout_constraintEnd_toEndOf="parent" />-->

        <View
            android:id="@+id/layout_pay"
            android:background="@color/colorPrimaryDark"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/textView5"
            android:layout_width="0dp"
            android:layout_height="wrap_content"/>
        <TextView
            android:id="@+id/textView5"
            android:text="Grand Total"
            android:paddingTop="11dp"
            android:layout_marginLeft="16dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintBottom_toTopOf="@id/txt_grand_total"
            android:textColor="@color/grey_light"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" />

        <TextView
            android:id="@+id/txt_grand_total"
            android:text="Rp39.000"
            android:textSize="16sp"
            android:layout_marginLeft="16dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:textColor="@color/text_orange"
            android:fontFamily="@font/poppins_medium"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" />

        <Button
            android:id="@+id/btn_pay"
            android:text="@string/pay"
            android:textColor="#FFFFFF"
            android:layout_margin="9dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:background="@drawable/btn_round_green"
            android:layout_height="40dp"
            android:layout_width="150dp" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
