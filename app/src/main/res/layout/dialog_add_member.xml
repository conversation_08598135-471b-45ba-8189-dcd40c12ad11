<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/background"
        android:padding="16dp">

        <TextView
            android:id="@+id/textView1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="ADD MEMBER"
            android:textColor="@color/white"
            android:textSize="17sp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/textView2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="type member information below"
            android:textColor="@color/grey_light"
            android:textSize="13sp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/textView1" />

        <View
            android:layout_width="0dp"
            android:layout_height="1dp"
            android:layout_marginTop="9dp"
            android:background="@color/text_grey"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/textView2" />

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/layout_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:textColorHint="@color/text_grey_light"
            android:theme="@style/InputStyle"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/textView2">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/edt_name"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="Name"
                android:inputType="textCapWords"
                android:textColor="@color/text_grey_light" />
        </com.google.android.material.textfield.TextInputLayout>

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/layout_phone"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="9dp"
            android:textColorHint="@color/text_grey_light"
            android:theme="@style/InputStyle"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/layout_name">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/edt_phone"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="Phone"
                android:inputType="phone"
                android:textColor="@color/text_grey_light" />
        </com.google.android.material.textfield.TextInputLayout>


        <com.uniq.materialanimation.ButtonLoading
            android:id="@+id/btn_save"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="32dp"
            android:layout_marginRight="3dp"
            app:buttonColor="@color/greeen_background"
            app:buttonText="@string/save"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/layout_phone"
            app:textColor="@color/white" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
