<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/background"
        android:padding="16dp"
        tools:context="com.uniq.uniqpos.view.setting.printerserver.PrinterServerFragment">

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/input_layout_ip"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:textColorHint="@color/text_grey_light"
            android:theme="@style/InputStyle"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/edt_ip_server"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="IP Address Server"
                android:inputType="phone"
                android:maxLength="15"
                android:textColor="@color/text_grey_light" />
        </com.google.android.material.textfield.TextInputLayout>

        <ImageView
            android:id="@+id/img_clear_ip"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toBottomOf="@id/input_layout_ip"
            app:layout_constraintTop_toTopOf="@id/input_layout_ip"
            app:srcCompat="@drawable/ic_clear_white_24dp"
            app:tint="@color/text_grey_light" />

        <androidx.appcompat.widget.SwitchCompat
            android:id="@+id/swt_server"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="@string/set_as_server"
            android:textColor="@color/text_grey_light"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/input_layout_ip" />

        <LinearLayout
            android:id="@+id/layout_ip"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/swt_server">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Your IP Address : "
                android:textColor="@color/text_grey_light"
                android:textSize="13sp" />

            <TextView
                android:id="@+id/txt_local_ip"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="*************"
                android:textColor="@color/text_grey_light"
                android:textSize="13sp"
                android:textStyle="bold" />
        </LinearLayout>

        <Button
            android:id="@+id/btn_ping"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:background="@drawable/btn_round_blue"
            android:text="Connect to Server"
            android:textColor="@android:color/white"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
