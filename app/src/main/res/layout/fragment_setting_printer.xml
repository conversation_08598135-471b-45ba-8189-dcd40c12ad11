<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <RelativeLayout
        android:background="@color/background"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:padding="16dp"
        tools:context="id.uniq.uniqpos.view.setting.SettingPrinterFragment">

        <TextView
            android:id="@+id/txtprinterlist"
            android:text="@string/printer_list"
            android:textSize="17sp"
            android:textColor="@color/grey_light"
            app:fontFamily="@font/poppins_medium"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" />
        <TextView
            android:id="@+id/txtPrinterCount"
            android:text="@string/no_printer_connected"
            app:fontFamily="@font/poppins"
            android:textColor="@color/grey_light"
            android:layout_marginTop="-7dp"
            android:layout_below="@+id/txtprinterlist"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" />
        <Button
            android:id="@+id/btnAddPrinter"
            android:text="@string/add_printer"
            android:paddingLeft="17dp"
            android:paddingRight="17dp"
            android:textSize="12sp"
            android:background="@drawable/btn_round_blue"
            android:textColor="@android:color/white"
            android:layout_alignParentEnd="true"
            android:layout_width="wrap_content"
            android:layout_height="40dp" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recviewPrinter"
            app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
            app:spanCount="@integer/printer_span_count"
            tools:listitem="@layout/list_item_printer"
            android:layout_below="@id/txtprinterlist"
            android:layout_marginTop="29dp"
            android:layout_width="match_parent"
            android:layout_height="match_parent"/>
    </RelativeLayout>
</layout>
