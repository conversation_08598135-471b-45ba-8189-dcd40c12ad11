<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/background"
        tools:context="com.uniq.uniqpos.view.chooseoperator.ChooseOperatorActivity">

        <View
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_margin="9dp"
            android:background="@color/background_selected"
            app:layout_constraintBottom_toBottomOf="@id/btn_subscribe"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/img_close"
            android:layout_width="17dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="3dp"
            android:layout_marginRight="13dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/txt_warning"
            app:srcCompat="@drawable/met_ic_close"
            app:tint="@color/grey_light" />

        <TextView
            android:id="@+id/txt_warning"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_margin="9dp"
            android:fontFamily="@font/poppins_medium"
            android:padding="9dp"
            android:text="Masa berlangganan anda segera berakhir dalam waktu 3 hari"
            android:textColor="@color/white"
            app:layout_constraintEnd_toStartOf="@id/img_close"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <Button
            android:id="@+id/btn_subscribe"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="19dp"
            android:background="@android:color/transparent"
            android:text="Mulai Berlangganan"
            android:textColor="@color/greeen_background"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/txt_warning" />

        <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
            android:id="@+id/swipe_refresh"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/btn_subscribe">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rec_view"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                tools:listitem="@layout/list_item_operator" />
        </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

        <com.google.android.material.floatingactionbutton.ExtendedFloatingActionButton
            android:id="@+id/fab_add_employee"
            android:visibility="gone"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_margin="16dp"
            android:text="@string/add_employee"
            app:backgroundTint="@color/blue_background"
            app:iconTint="@color/white"
            android:textColor="@color/white"
            app:icon="@drawable/ic_add_white_24dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
