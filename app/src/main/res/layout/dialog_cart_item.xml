<?xml version="1.0" encoding="utf-8"?>
<layout>

    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/background"
        android:orientation="vertical"
        android:padding="9dp">

        <TextView
            android:id="@+id/txt_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:fontFamily="@font/poppins_medium"
            android:textAppearance="@style/TextAppearance.AppCompat.Medium"
            android:textColor="@color/white"
            tools:text="Steve Jobs - table 04" />

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rec_view_item"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:nestedScrollingEnabled="false"
                    app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                    tools:listitem="@layout/list_item_cart_dialog" />

                <TextView
                    android:id="@+id/txt_note"
                    android:fontFamily="@font/poppins"
                    tools:text="Note: Jangan pakai cabai"
                    android:textColor="@color/text_grey_light"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"/>

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="9dp">

                    <TextView
                        android:id="@+id/txt_pay"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:layout_alignParentBottom="true"
                        android:layout_centerVertical="true"
                        android:paddingLeft="16dp"
                        android:paddingRight="9dp"
                        android:text="@string/pay"
                        android:textAllCaps="true"
                        android:textColor="@color/greeen_background" />

                    <TextView
                        android:id="@+id/txt_edit"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentBottom="true"
                        android:layout_centerVertical="true"
                        android:layout_toLeftOf="@id/txt_pay"
                        android:paddingLeft="16dp"
                        android:paddingRight="9dp"
                        android:text="@string/edit"
                        android:textAllCaps="true"
                        android:textColor="@color/greeen_background" />

                    <ImageView
                        android:id="@+id/img_print"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentBottom="true"
                        android:layout_centerVertical="true"
                        android:tint="@color/text_grey_light"
                        app:srcCompat="@drawable/ic_print_black_24dp" />

                    <TextView
                        android:id="@+id/txt_print"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentBottom="true"
                        android:layout_centerVertical="true"
                        android:layout_toRightOf="@id/img_print"
                        android:paddingLeft="11dp"
                        android:text="Print"
                        android:textAllCaps="true"
                        android:textColor="@color/text_grey_light" />
                </RelativeLayout>
            </LinearLayout>
        </androidx.core.widget.NestedScrollView>
    </LinearLayout>
</layout>
