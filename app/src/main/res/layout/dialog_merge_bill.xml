<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>
        <variable
            name="model"
            type="com.uniq.uniqpos.data.local.entity.SalesEntity" />
    </data>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/background"
        android:orientation="vertical"
        android:padding="9dp">

        <TextView
            android:id="@+id/textView1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingTop="5dp"
            android:paddingBottom="9dp"
            android:text="Merge Bill"
            android:textAppearance="@style/TextAppearance.AppCompat.Title"
            android:textColor="@color/white"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/layout_top"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@id/textView1">

            <TextView
                android:id="@id/textView2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Pilih bill yang akan di merge dengan bill"
                android:textColor="@color/text_grey_light"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:maxLines="1"
                android:paddingLeft="7dp"
                android:text="@{model.customer}"
                android:textColor="@color/orange_background"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/textView2"
                app:layout_constraintTop_toTopOf="@id/textView2"
                tools:text="Annas" />

            <View
                android:id="@+id/view1"
                android:layout_width="0dp"
                android:layout_height="30dp"
                android:layout_marginTop="9dp"
                android:background="@drawable/round_stroke_grey_search"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/textView2" />

            <ImageView
                android:id="@+id/imageView1"
                android:layout_width="wrap_content"
                android:layout_height="0dp"
                android:layout_marginLeft="9dp"
                android:paddingTop="5dp"
                android:paddingBottom="5dp"
                android:tint="@color/text_grey_light"
                app:layout_constraintBottom_toBottomOf="@id/view1"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@id/view1"
                app:srcCompat="@drawable/ic_search_white_24dp" />

            <EditText
                android:id="@+id/edt_search"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:background="@null"
                android:hint="search name or table..."
                android:textColor="@color/text_grey_light"
                android:textColorHint="@color/text_grey_light"
                android:textSize="13sp"
                app:layout_constraintBottom_toBottomOf="@id/view1"
                app:layout_constraintEnd_toEndOf="@id/view1"
                app:layout_constraintStart_toEndOf="@id/imageView1"
                app:layout_constraintTop_toTopOf="@id/view1" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recview_bill_names"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_above="@id/txt_next"
            android:layout_below="@id/layout_top"
            android:layout_marginTop="16dp"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            tools:listitem="@layout/list_item_bill_names" />

        <TextView
            android:id="@+id/txt_next"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_alignParentBottom="true"
            android:layout_marginTop="7dp"
            android:paddingLeft="15dp"
            android:paddingTop="9dp"
            android:paddingRight="9dp"
            android:paddingBottom="9dp"
            android:text="NEXT"
            android:textColor="@color/greeen_background"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <TextView
            android:id="@+id/txt_cancel"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_marginRight="9dp"
            android:layout_toLeftOf="@id/txt_next"
            android:padding="9dp"
            android:text="CANCEL"
            android:textColor="@color/greeen_background"
            app:layout_constraintEnd_toStartOf="@id/txt_next"
            app:layout_constraintTop_toTopOf="@id/txt_next" />
    </RelativeLayout>
</layout>
