<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/dialog_round"
        android:paddingBottom="16dp">

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_above="@+id/layout_button"
            android:layout_alignParentTop="true">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/taxes_and_services"
                        android:textColor="@color/orange_background"
                        app:fontFamily="@font/poppins_semibold" />

                    <TextView
                        android:id="@+id/txt_add_tax"
                        android:layout_width="wrap_content"
                        android:layout_height="25dp"
                        android:layout_alignParentRight="true"
                        android:fontFamily="@font/poppins_medium"
                        android:paddingLeft="16dp"
                        android:text="@string/add"
                        android:textColor="@color/greeen_background"
                        android:textSize="12sp" />
                </RelativeLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="@color/text_grey" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recViewTaxes"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:nestedScrollingEnabled="false"
                    app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                    tools:listitem="@layout/list_item_taxes" />

                <RelativeLayout
                    android:id="@+id/view_show_more_tax"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="9dp"
                    android:visibility="gone"
                    tools:visibility="visible">

                    <View
                        android:layout_width="100dp"
                        android:layout_height="1dp"
                        android:layout_centerVertical="true"
                        android:layout_toLeftOf="@id/txt_show_more"
                        android:background="@color/text_grey" />

                    <View
                        android:layout_width="100dp"
                        android:layout_height="1dp"
                        android:layout_centerVertical="true"
                        android:layout_toRightOf="@id/txt_show_more"
                        android:background="@color/text_grey" />

                    <TextView
                        android:id="@+id/txt_show_more"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerHorizontal="true"
                        android:layout_centerVertical="true"
                        android:paddingLeft="9dp"
                        android:paddingRight="9dp"
                        android:textSize="13sp"
                        android:text="show more"
                        android:textColor="@color/greeen_background" />
                </RelativeLayout>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="25dp"
                    android:text="@string/disc_and_voucher"
                    android:textColor="@color/orange_background"
                    app:fontFamily="@font/poppins_medium" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="@color/text_grey" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recview_disc_history"
                    android:layout_marginTop="9dp"
                    app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                    tools:itemCount="5"
                    android:orientation="horizontal"
                    tools:listitem="@layout/list_item_bill_names"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"/>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:layout_marginRight="16dp"
                        android:layout_weight="1"
                        android:textColorHint="@color/text_grey_light"
                        android:theme="@style/InputStyle">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/dialog_discount"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:hint="Discount"
                            android:inputType="number"
                            android:longClickable="false"
                            android:selectAllOnFocus="true"
                            android:textColor="@color/text_grey_light" />
                    </com.google.android.material.textfield.TextInputLayout>

                    <Button
                        android:id="@+id/btn_disc_nominal"
                        android:layout_width="59dp"
                        android:layout_height="30dp"
                        android:layout_gravity="bottom"
                        android:layout_marginBottom="5dp"
                        android:background="@drawable/backg_round_left_diselect"
                        android:text="@string/nominal"
                        android:textAllCaps="false"
                        android:textColor="@color/text_grey_light"
                        android:textSize="11sp" />

                    <Button
                        android:id="@+id/btn_disc_percent"
                        android:layout_width="59dp"
                        android:layout_height="30dp"
                        android:layout_gravity="bottom"
                        android:layout_marginBottom="5dp"
                        android:background="@drawable/backg_round_right"
                        android:text="@string/percent"
                        android:textAllCaps="false"
                        android:textColor="#ffff"
                        android:textSize="11sp" />
                </LinearLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textColorHint="@color/text_grey_light"
                    android:theme="@style/InputStyle">

                    <!--                    <com.google.android.material.textfield.TextInputEditText-->
                    <!--                        android:id="@+id/dialog_discount_info"-->
                    <!--                        android:layout_width="match_parent"-->
                    <!--                        android:layout_height="wrap_content"-->
                    <!--                        android:hint="@string/discount_information"-->
                    <!--                        android:maxLines="1"-->
                    <!--                        android:singleLine="true"-->
                    <!--                        android:textColor="@color/text_grey_light" />-->

                    <com.google.android.material.textfield.MaterialAutoCompleteTextView
                        android:id="@+id/dialog_discount_info"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="@string/discount_information"
                        android:textColor="@color/text_grey_light" />

                </com.google.android.material.textfield.TextInputLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:layout_marginRight="16dp"
                        android:layout_weight="1"
                        android:textColorHint="@color/text_grey_light"
                        android:theme="@style/InputStyle">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/edt_voucher"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:hint="Voucher"
                            android:inputType="number"
                            android:maxLines="1"
                            android:selectAllOnFocus="true"
                            android:singleLine="true"
                            android:textColor="@color/text_grey_light" />
                    </com.google.android.material.textfield.TextInputLayout>

                    <Button
                        android:id="@+id/btn_voucher_nominal"
                        android:layout_width="59dp"
                        android:layout_height="30dp"
                        android:layout_gravity="bottom"
                        android:layout_marginBottom="5dp"
                        android:background="@drawable/backg_round_left_diselect"
                        android:text="@string/nominal"
                        android:textAllCaps="false"
                        android:textColor="@color/text_grey_light"
                        android:textSize="11sp" />

                    <Button
                        android:id="@+id/btn_voucher_percent"
                        android:layout_width="59dp"
                        android:layout_height="30dp"
                        android:layout_gravity="bottom"
                        android:layout_marginBottom="5dp"
                        android:background="@drawable/backg_round_right"
                        android:text="@string/percent"
                        android:textAllCaps="false"
                        android:textColor="#ffff"
                        android:textSize="11sp" />
                </LinearLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textColorHint="@color/text_grey_light"
                    android:theme="@style/InputStyle">

                    <!--                    <com.google.android.material.textfield.TextInputEditText-->
                    <!--                        android:id="@+id/edt_voucher_info"-->
                    <!--                        android:layout_width="match_parent"-->
                    <!--                        android:layout_height="wrap_content"-->
                    <!--                        android:hint="@string/voucher_information"-->
                    <!--                        android:maxLines="1"-->
                    <!--                        android:singleLine="true"-->
                    <!--                        android:textColor="@color/text_grey_light" />-->

                    <com.google.android.material.textfield.MaterialAutoCompleteTextView
                        android:id="@+id/edt_voucher_info"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="@string/voucher_information"
                        android:textColor="@color/text_grey_light" />
                </com.google.android.material.textfield.TextInputLayout>
            </LinearLayout>
        </androidx.core.widget.NestedScrollView>

        <LinearLayout
            android:id="@+id/layout_button"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_marginLeft="16dp"
            android:layout_marginTop="11dp"
            android:layout_marginRight="16dp"
            android:orientation="horizontal">

            <com.uniq.materialanimation.ButtonLoading
                android:id="@+id/btn_cancel"
                android:layout_width="0dp"
                android:layout_height="45dp"
                android:layout_weight="1"
                app:buttonColor="@color/red_background"
                app:buttonText="@string/cancel"
                app:textColor="@color/white" />

            <com.uniq.materialanimation.ButtonLoading
                android:id="@+id/btn_submit"
                android:layout_width="0dp"
                android:layout_height="45dp"
                android:layout_weight="1"
                app:buttonColor="@color/greeen_background"
                app:buttonText="@string/save"
                app:textColor="@color/white" />

        </LinearLayout>
    </RelativeLayout>
</layout>
