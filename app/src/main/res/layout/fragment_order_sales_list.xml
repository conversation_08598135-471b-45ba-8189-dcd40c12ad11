<layout xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:context="com.uniq.uniqpos.view.ordersales.OrderSalesListFragment">

        <include
            android:id="@+id/inc_lay_no_data"
            layout="@layout/layout_no_data"/>

       <androidx.recyclerview.widget.RecyclerView
           android:id="@+id/recview_order"
           app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
           tools:listitem="@layout/list_item_order_sales"
           app:layout_constraintTop_toTopOf="parent"
           app:layout_constraintBottom_toBottomOf="parent"
           app:layout_constraintStart_toStartOf="parent"
           app:layout_constraintEnd_toEndOf="parent"
           android:layout_width="0dp"
           android:layout_height="0dp"/>

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
