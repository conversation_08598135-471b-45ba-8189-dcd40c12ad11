<layout>

    <androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/background"
        tools:context="com.uniq.uniqpos.view.transaction.ChooseMenuFragment">

        <com.google.android.material.appbar.AppBarLayout
            android:id="@+id/appbar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:theme="@style/ThemeOverlay.AppCompat.Dark"
            app:elevation="0dp">

            <com.google.android.material.tabs.TabLayout
                android:id="@+id/tablayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/colorPrimary"
                app:tabMode="scrollable" />
        </com.google.android.material.appbar.AppBarLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            app:layout_behavior="@string/appbar_scrolling_view_behavior">

            <androidx.viewpager.widget.ViewPager
                android:id="@+id/view_pager"
                android:layout_width="0dp"
                android:layout_height="0dp"
                app:layout_constraintBottom_toTopOf="@id/inc_layout_cart"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/txt_pending_order" />

            <TextView
                android:id="@+id/txt_error"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:background="@color/background"
                android:ellipsize="end"
                android:maxLines="1"
                android:padding="5dp"
                android:text="@string/pending_print_count"
                android:textColor="@color/red_background"
                android:textSize="12sp"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:visibility="visible" />

            <TextView
                android:id="@+id/txt_pending_order"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:padding="9dp"
                android:text="@string/pending_order_count"
                android:textColor="@color/grey_light"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/txt_error"
                tools:visibility="visible" />

            <ImageView
                android:id="@+id/img_pending_order"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:layout_constraintBottom_toBottomOf="@id/txt_pending_order"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="@id/txt_pending_order"
                app:srcCompat="@drawable/ic_keyboard_arrow_right_white_24dp"
                app:tint="@color/text_grey_light" />

            <include
                android:id="@+id/inc_layout_cart"
                layout="@layout/layout_cart_bottom"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="9dp"
                android:layout_marginRight="9dp"
                android:layout_marginBottom="5dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent" />

            <include
                android:id="@+id/layout_tutorial_product"
                layout="@layout/layout_tutorial_add_product"
                android:visibility="gone" />

            <com.google.android.material.floatingactionbutton.FloatingActionButton
                android:id="@+id/fab_scan"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginRight="16dp"
                android:layout_marginBottom="16dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:srcCompat="@drawable/ic_barcode" />
        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.coordinatorlayout.widget.CoordinatorLayout>
</layout>
