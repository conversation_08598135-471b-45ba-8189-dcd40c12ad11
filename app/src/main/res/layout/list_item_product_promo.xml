<?xml version="1.0" encoding="utf-8"?>
<layout>

    <data>

        <import type="android.view.View" />

        <variable
            name="model"
            type="com.uniq.uniqpos.model.PromotionProductDialog" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:clickable="true"
        android:foreground="?android:attr/selectableItemBackground"
        tools:background="@color/background">

        <View
            android:id="@+id/separator"
            android:layout_width="0dp"
            android:layout_height="1dp"
            android:background="@color/text_grey"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/txt_menu"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="8dp"
            android:layout_marginRight="8dp"
            android:ellipsize="end"
            android:maxLines="2"
            android:text="@{model.productName}"
            android:textColor="@color/grey_light"
            android:textSize="15sp"
            app:fontFamily="@font/poppins_medium"
            app:layout_constraintBottom_toTopOf="@+id/txt_price_original"
            app:layout_constraintEnd_toStartOf="@+id/txt_promo_info"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/separator"
            app:layout_constraintVertical_chainStyle="packed"
            tools:text="Yamie Polos Asin Sedang" />

        <TextView
            android:id="@+id/txt_price_original"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="8dp"
            android:textColor="@color/text_grey_light"
            android:textSize="12sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/txt_menu"
            app:textCurrency="@{safeUnbox(model.priceOriginal)}"
            tools:text="Rp12,500" />

        <TextView
            android:id="@+id/txt_price_promo"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingRight="5dp"
            android:paddingBottom="5dp"
            android:visibility="@{model.showPricePromo ? View.VISIBLE : View.GONE}"
            android:textColor="@color/text_grey_light"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/txt_price_original"
            app:textCurrency="@{safeUnbox(model.pricePromo)}"
            tools:text="Rp8,000" />

        <TextView
            android:id="@+id/txt_promo_info"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingRight="5dp"
            android:text="@{model.promoInfo}"
            android:textColor="@color/text_orange"
            android:textSize="12sp"
            app:layout_constraintBottom_toTopOf="@id/txt_price_promo"
            app:layout_constraintEnd_toEndOf="parent"
            tools:text="-30%" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
