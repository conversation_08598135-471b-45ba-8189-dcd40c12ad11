<?xml version="1.0" encoding="utf-8"?>
<layout>

    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/background"
        android:orientation="vertical"
        android:padding="16dp">


        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:text="Choose Image Source"
            android:textAlignment="center"
            android:textColor="@color/white"
            android:textSize="18sp"
            android:fontFamily="@font/poppins_medium" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btnCamera"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:text="Take Photo"
            android:textColor="@color/white"
            app:backgroundTint="@color/blue_background"
            app:icon="@drawable/camera_24"
            app:iconTint="@color/white" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btnGallery"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Choose from Gallery"
            android:textColor="@color/white"
            app:backgroundTint="@color/blue_background"
            app:icon="@drawable/baseline_photo_library_24"
            app:iconTint="@color/white" />

    </LinearLayout>
</layout>
