<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="model"
            type="String" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingRight="9dp"
        tools:background="@color/background">

        <Button
            android:id="@+id/btn_name"
            android:layout_width="wrap_content"
            android:layout_height="30dp"
            android:background="@drawable/round_grey"
            android:paddingLeft="7dp"
            android:paddingRight="7dp"
            android:text="@{model}"
            android:textAllCaps="false"
            android:textColor="@color/text_grey_light"
            android:textSize="12sp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="XXL" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
