<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="model"
            type="com.uniq.uniqpos.model.PrintTicket" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        tools:background="@color/background">

        <CheckBox
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:checked="true"
            android:enabled="false"
            android:text="@{model.title}"
            android:textColor="@color/text_grey_light"
            android:theme="@style/CustomCheckBox"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="Coffee" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
