<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>
        <variable
            name="model"
            type="com.uniq.uniqpos.data.local.entity.SalesEntity" />
    </data>

    <LinearLayout
        android:id="@+id/container"
        android:background="@drawable/round_stroke_grey"
        tools:background="@color/background"
        android:layout_marginBottom="19dp"
        android:layout_width="100dp"
        android:layout_height="100dp">

        <TextView
            android:id="@+id/txt_table"
            android:text="@{model.table}"
            tools:text="1"
            android:textColor="@color/text_grey_light"
            app:fontFamily="@font/poppins"
            android:textSize="25sp"
            android:gravity="center"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

    </LinearLayout>
</layout>
