<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="model"
            type="com.uniq.uniqpos.data.local.entity.PrinterEntity" />
    </data>

    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:cardBackgroundColor="@color/background_selected"
        app:cardUseCompatPadding="true">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="?attr/selectableItemBackground"
            android:clickable="true"
            android:orientation="vertical">

            <ImageView
                android:id="@+id/imageView1"
                android:layout_width="50dp"
                android:layout_height="50dp"
                android:layout_marginTop="11dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:srcCompat="@drawable/ic_print_black_24dp"
                app:tint="#989898" />

            <ImageView
                android:id="@+id/img_icon_type"
                android:layout_marginTop="7dp"
                app:tint="@color/text_orange"
                android:layout_marginLeft="9dp"
                android:src="@drawable/baseline_wifi_24_orange"
                app:layout_constraintTop_toTopOf="@id/imageView1"
                app:layout_constraintEnd_toEndOf="@id/imageView1"
                android:layout_width="20dp"
                android:layout_height="20dp"/>

            <TextView
                android:id="@+id/textView1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="7dp"
                android:text="@{model.name}"
                android:textColor="@color/text_orange"
                app:fontFamily="@font/poppins_medium"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/imageView1"
                tools:text="Dapur Makanan" />

            <TextView
                android:id="@+id/textView2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text="@{model.address}"
                android:textColor="@color/grey_light"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/textView1"
                tools:text="BT:0F:02:HI:32:8U:G7" />

            <View
                android:id="@+id/view_printer"
                android:layout_width="0dp"
                android:layout_height="0dp"
                app:layout_constraintBottom_toTopOf="@+id/view1"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.0"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0.0" />

            <View
                android:id="@+id/view1"
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_marginTop="13dp"
                android:background="@color/text_grey"
                app:layout_constraintTop_toBottomOf="@+id/textView2" />

            <ImageView
                android:id="@+id/img_more"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:padding="9dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:srcCompat="@drawable/ic_more_vert_grey_600_24dp" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                app:layout_constraintTop_toBottomOf="@+id/view1">

                <TextView
                    android:id="@+id/txtSetting"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="?attr/selectableItemBackground"
                    android:clickable="true"
                    android:paddingLeft="17dp"
                    android:paddingTop="9dp"
                    android:paddingRight="17dp"
                    android:paddingBottom="9dp"
                    android:text="@string/delete"
                    android:textAllCaps="true"
                    android:textColor="@color/grey_light"
                    android:textSize="13sp"
                    app:fontFamily="@font/poppins_semibold" />

                <TextView
                    android:id="@+id/txtPrintTest"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="?attr/selectableItemBackground"
                    android:clickable="true"
                    android:paddingLeft="17dp"
                    android:paddingTop="9dp"
                    android:paddingRight="17dp"
                    android:paddingBottom="9dp"
                    android:text="PRINT TEST"
                    android:textColor="@color/grey_light"
                    android:textSize="13sp"
                    app:fontFamily="@font/poppins" />
            </LinearLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.cardview.widget.CardView>
</layout>
