<?xml version="1.0" encoding="utf-8"?>
<layout>
    <data>
        <variable
            name="model"
            type="com.uniq.uniqpos.data.local.entity.ProductEntity" />
    </data>
    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:id="@+id/container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/background"
        android:padding="9dp"
        android:clickable="true"
        android:foreground="?android:attr/selectableItemBackground"
        xmlns:tools="http://schemas.android.com/tools"
        xmlns:app="http://schemas.android.com/apk/res-auto">

        <TextView
            android:id="@+id/txt_name"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toStartOf="@id/txt_price"
            android:textColor="@color/grey_light"
            tools:text="Nasi Goreng Ayam"
            android:text="@{model.name}"
            android:fontFamily="@font/poppins_medium"
            android:layout_width="0dp"
            android:layout_height="wrap_content"/>
        <TextView
            app:layout_constraintTop_toBottomOf="@id/txt_name"
            app:layout_constraintStart_toStartOf="parent"
            tools:text="Makanan"
            android:text="@{model.productcategoryName}"
            android:fontFamily="@font/poppins"
            android:textColor="@color/text_grey_light"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>

        <TextView
            android:id="@+id/txt_price"
            tools:text="Rp. 15.000"
            app:textCurrency="@{model.priceSell}"
            android:fontFamily="@font/poppins"
            android:textColor="@color/text_orange"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
