<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="model"
            type="com.uniq.uniqpos.data.local.entity.SalesEntity" />
    </data>

    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:clickable="true"
        android:foreground="?android:attr/selectableItemBackground"
        app:cardBackgroundColor="@color/background_selected"
        app:cardUseCompatPadding="true">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/txt_items"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <TextView
                android:id="@+id/textView13"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:layout_marginLeft="8dp"
                android:layout_marginTop="8dp"
                android:layout_marginEnd="16dp"
                android:layout_marginRight="16dp"
                android:text="@{model.customer}"
                android:textColor="@color/grey_light"
                app:fontFamily="@font/poppins"
                app:layout_constraintEnd_toStartOf="@+id/textView14"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="Mustofa" />

            <TextView
                android:id="@+id/textView18"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:layout_marginLeft="8dp"
                android:layout_marginEnd="16dp"
                android:layout_marginRight="16dp"
                android:text="@{`Table ` + model.table}"
                android:textColor="@color/text_grey_light"
                app:layout_constraintEnd_toStartOf="@+id/textView14"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/textView13"
                tools:text="Table 09" />

            <TextView
                android:id="@+id/textView14"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:layout_marginEnd="8dp"
                android:layout_marginRight="8dp"
                android:textColor="@color/text_orange"
                app:fontFamily="@font/poppins_medium"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:symbol="@{`IDR `}"
                app:textCurrency="@{model.grandTotal}"
                tools:text="IDR 120.000" />

            <TextView
                android:id="@+id/textView16"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:layout_marginLeft="8dp"
                android:layout_marginEnd="8dp"
                android:layout_marginRight="8dp"
                android:ellipsize="end"
                android:maxLines="2"
                android:text="@{model.items}"
                android:textColor="@color/text_grey_light"
                android:textSize="13sp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/textView18"
                tools:text="Nasi ayam, Es Teh, Bakso Goreng, Lumpeng, Ikan Bakar, Sajen" />

            <TextView
                android:id="@+id/textView17"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginRight="8dp"
                android:textColor="@color/text_grey_light"
                android:textSize="11sp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/textView14"
                app:timeMillisToDate="@{model.timeCreated}"
                tools:text="12 May 2018 14:30" />

            <ImageView
                android:id="@+id/btn_print"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:layout_marginEnd="16dp"
                android:layout_marginRight="16dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/textView16"
                app:srcCompat="@drawable/ic_print_black_24dp"
                app:tint="@color/grey_light" />

            <TextView
                android:id="@+id/btn_pay"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:layout_marginLeft="8dp"
                android:layout_marginTop="8dp"
                android:paddingLeft="9dp"
                android:paddingTop="3dp"
                android:paddingRight="9dp"
                android:paddingBottom="3dp"
                android:text="@string/pay"
                android:textColor="@color/grey_light"
                app:fontFamily="@font/poppins_medium"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/textView16" />

            <TextView
                android:id="@+id/btn_edit"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:layout_marginLeft="8dp"
                android:layout_marginTop="8dp"
                android:paddingLeft="9dp"
                android:paddingTop="3dp"
                android:paddingRight="9dp"
                android:paddingBottom="3dp"
                android:text="Edit"
                android:textColor="@color/grey_light"
                app:fontFamily="@font/poppins_medium"
                app:layout_constraintStart_toEndOf="@+id/btn_pay"
                app:layout_constraintTop_toBottomOf="@+id/textView16" />

            <TextView
                android:id="@+id/btn_merge"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:layout_marginLeft="8dp"
                android:layout_marginTop="8dp"
                android:paddingLeft="9dp"
                android:paddingTop="3dp"
                android:paddingRight="9dp"
                android:paddingBottom="3dp"
                android:text="Merge"
                android:textColor="@color/grey_light"
                app:fontFamily="@font/poppins_medium"
                app:layout_constraintStart_toEndOf="@+id/btn_edit"
                app:layout_constraintTop_toBottomOf="@+id/textView16" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.cardview.widget.CardView>
</layout>
