<layout xmlns:tools="http://schemas.android.com/tools"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/background"
        tools:context="com.uniq.uniqpos.view.closeshift.CloseShiftDetailFragment">

        <ScrollView
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintBottom_toTopOf="@id/btn_filter"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

<!--            <WebView-->
<!--                android:id="@+id/web_report"-->
<!--                android:visibility="gone"-->
<!--                android:layout_width="match_parent"-->
<!--                android:layout_height="wrap_content" />-->


            <TextView
                android:id="@+id/txt_report"
                android:padding="9dp"
                android:textSize="13sp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:fontFamily="monospace"
                android:textColor="#C1C0C0"
                tools:text="Close Shift Report" />
        </ScrollView>

        <Button
            android:id="@+id/btn_filter"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:background="@android:color/transparent"
            android:text="Filter"
            android:textColor="@color/grey_light"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/btn_print"
            app:layout_constraintStart_toStartOf="parent"
            tools:visibility="visible" />

        <com.uniq.materialanimation.ButtonLoading
            android:id="@+id/btn_print"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:background="@android:color/transparent"
            android:text="Print"
            android:textColor="@color/grey_light"
            android:visibility="gone"
            app:buttonColor="@color/background"
            app:buttonText="Print"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/btn_filter"
            tools:visibility="visible" />


    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
