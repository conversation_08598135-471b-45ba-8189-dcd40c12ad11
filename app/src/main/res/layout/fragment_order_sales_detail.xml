<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="sales"
            type="com.uniq.uniqpos.data.local.entity.SalesEntity" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/background"
        android:padding="9dp">

        <TextView
            android:id="@+id/txt_ket_invoice"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="9dp"
            android:text="@string/invoice_no"
            android:textColor="@color/grey_light"
            android:textSize="12sp"
            app:fontFamily="@font/poppins"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/txt_ket_payment"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/payment_method"
            android:textColor="@color/grey_light"
            android:textSize="12sp"
            app:fontFamily="@font/poppins"
            app:layout_constraintEnd_toStartOf="@+id/txt_ket_customer"
            app:layout_constraintStart_toEndOf="@+id/txt_ket_invoice"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/txt_ket_customer"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginRight="9dp"
            android:text="@string/customer"
            android:textColor="@color/grey_light"
            android:textSize="12sp"
            app:fontFamily="@font/poppins"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/txt_ket_payment"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/txt_invoice"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:maxLines="2"
            android:text='@{sales.displayNota.equals("") ? sales.noNota : sales.displayNota}'
            android:textColor="@color/text_orange"
            android:textSize="13sp"
            app:fontFamily="@font/poppins_medium"
            app:layout_constraintEnd_toStartOf="@id/txt_ket_payment"
            app:layout_constraintStart_toStartOf="@+id/txt_ket_invoice"
            app:layout_constraintTop_toBottomOf="@+id/txt_ket_invoice"
            tools:text="KA67SFBHSD7" />

        <TextView
            android:id="@+id/txt_payment"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:maxLines="2"
            android:textColor="@color/text_orange"
            android:textSize="13sp"
            app:fontFamily="@font/poppins_medium"
            app:layout_constraintEnd_toStartOf="@id/txt_customer"
            app:layout_constraintStart_toStartOf="@+id/txt_ket_payment"
            app:layout_constraintTop_toBottomOf="@+id/txt_ket_payment"
            tools:text="Cash" />

        <TextView
            android:id="@+id/txt_customer"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:maxLines="1"
            android:text="@{sales.customer}"
            android:textColor="@color/text_orange"
            android:textSize="13sp"
            app:fontFamily="@font/poppins_medium"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="@+id/txt_ket_customer"
            app:layout_constraintTop_toBottomOf="@+id/txt_ket_customer"
            tools:text="Widya" />

        <androidx.constraintlayout.widget.Barrier
            android:id="@+id/barrier"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:barrierDirection="bottom"
            app:constraint_referenced_ids="txt_customer,txt_payment,txt_invoice"
            tools:layout_editor_absoluteY="962dp" />

        <View
            android:id="@+id/view1"
            android:layout_width="match_parent"
            android:layout_height="1.5dp"
            android:layout_marginTop="9dp"
            android:background="@color/text_grey"
            app:layout_constraintTop_toBottomOf="@id/barrier" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="16dp"
            android:layout_marginTop="16dp"
            android:text="Order Detail"
            android:textColor="@color/greeen_background"
            app:fontFamily="@font/poppins_semibold"
            app:layout_constraintTop_toBottomOf="@id/view1" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recview_history_detail"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginTop="43dp"
            android:layout_marginBottom="3dp"
            android:paddingLeft="16dp"
            android:paddingRight="16dp"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            app:layout_constraintBottom_toTopOf="@id/layout_subtotal"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/view1"
            tools:listitem="@layout/list_item_order_history" />

        <LinearLayout
            android:id="@+id/layout_subtotal"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginRight="5dp"
            android:gravity="right"
            android:orientation="horizontal"
            app:layout_constraintBottom_toTopOf="@id/recViewTax">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Subtotal"
                android:textColor="@color/grey_light"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/txt_subtotal"
                android:layout_width="150dp"
                android:layout_height="wrap_content"
                android:gravity="right"
                android:text="0"
                android:textColor="@color/text_orange"
                android:textStyle="bold"
                tools:text="150,000" />
        </LinearLayout>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recViewTax"
            android:layout_width="match_parent"
            android:layout_height="70dp"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            app:layout_constraintBottom_toTopOf="@id/btn_primary"
            tools:listitem="@layout/list_item_tax" />

        <Button
            android:id="@+id/btn_primary"
            android:layout_width="0dp"
            android:layout_height="43dp"
            android:layout_marginRight="3dp"
            android:background="@color/blue_background"
            android:text="Print"
            android:textColor="@android:color/white"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/btn_secondary"
            app:layout_constraintStart_toStartOf="parent" />

        <Button
            android:id="@+id/btn_secondary"
            android:layout_width="0dp"
            android:layout_height="43dp"
            android:layout_marginLeft="3dp"
            android:background="@color/red_background"
            android:text="Refund"
            android:textColor="@android:color/white"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/btn_primary" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
