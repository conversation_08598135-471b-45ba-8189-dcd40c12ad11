<?xml version="1.0" encoding="utf-8"?>
<layout>

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        android:id="@+id/main"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/background"
        tools:context=".view.productcatalogue.ocr.ProductEditList">

        <include
            android:id="@+id/vi_list"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintHorizontal_weight="2"
            app:layout_constraintEnd_toStartOf="@id/view_detail"
            layout="@layout/product_edit_list"/>

        <ScrollView
            android:id="@+id/view_detail"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintHorizontal_weight="1"
            app:layout_constraintBottom_toTopOf="@id/txt_update"
            app:layout_constraintStart_toEndOf="@id/vi_list"
            android:layout_width="0dp"
            android:layout_height="0dp">

            <FrameLayout
                android:id="@+id/container_detail"
                app:layout_constraintHorizontal_weight="1"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"/>
        </ScrollView>

        <TextView
            android:id="@+id/txt_update"
            android:text="Update"
            android:padding="13dp"
            android:fontFamily="@font/poppins"
            android:textColor="@color/white"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="@id/view_detail"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
