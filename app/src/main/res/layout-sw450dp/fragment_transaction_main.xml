<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <EditText
            android:id="@+id/edt_barcode"
            android:layout_width="150dp"
            android:layout_height="wrap_content"
            android:background="@null"
            android:cursorVisible="false"
            android:textColor="@android:color/transparent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/txt_wide_screen"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/layout_warning"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:background="@color/text_orange"
            android:padding="5dp"
            android:textColor="@color/white"
            android:textSize="12sp"
            android:visibility="gone"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            tools:text="5 transaksi belum tersingkronkan"
            tools:visibility="visible" />

        <FrameLayout
            android:id="@+id/transaction_container"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/layout_warning" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>
