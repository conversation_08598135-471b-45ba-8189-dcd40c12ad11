package com.uniq.uniqpos.ui.form

import com.google.gson.Gson
import com.uniq.uniqpos.data.local.entity.TransactionSettingEntity

data class FormFieldConfig(
    val id: String,
    val label: String,
    val type: String,
    val isRequired: <PERSON><PERSON><PERSON>,
    val displayOrder: Int,
    val optionValues: List<String>?
) {
    companion object {
        fun fromEntity(entity: TransactionSettingEntity): FormFieldConfig {
            val options = entity.optionValues?.let { optionsJson ->
                try {
                    // Parse JSON array string like ["Member Baru", "Member Lama"]
                    Gson().fromJson(optionsJson, Array<String>::class.java)?.toList()
                } catch (e: Exception) {
                    null
                }
            }
            
            return FormFieldConfig(
                id = entity.id.toString(),
                label = entity.label,
                type = entity.type,
                isRequired = entity.isRequired,
                displayOrder = entity.displayOrder,
                optionValues = options
            )
        }
        
        const val TYPE_TEXT = "TEXT"
        const val TYPE_SINGLE_CHOICE = "SINGLE_CHOICE"
        const val TYPE_MULTI_CHOICE = "MULTI_CHOICE"
    }
}
