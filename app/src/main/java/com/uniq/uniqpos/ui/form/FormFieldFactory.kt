package com.uniq.uniqpos.ui.form

class FormFieldFactory {
    fun createField(config: FormFieldConfig): FormFieldComponent {
        return when(config.type) {
            FormFieldConfig.TYPE_TEXT -> TextFieldComponent(config)
            FormFieldConfig.TYPE_SINGLE_CHOICE -> SingleChoiceComponent(config)
            FormFieldConfig.TYPE_MULTI_CHOICE -> TextFieldComponent(config) // Fallback to text for now
            else -> TextFieldComponent(config) // Default fallback
        }
    }
}
