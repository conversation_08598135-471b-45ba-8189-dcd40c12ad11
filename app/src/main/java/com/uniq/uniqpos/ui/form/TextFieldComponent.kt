package com.uniq.uniqpos.ui.form

import android.content.Context
import android.graphics.Color
import android.text.SpannableString
import android.text.Spanned
import android.text.style.ForegroundColorSpan
import android.view.View
import android.widget.EditText
import android.widget.LinearLayout
import android.widget.TextView
import com.uniq.uniqpos.R

class TextFieldComponent(config: FormFieldConfig) : FormFieldComponent(config) {
    private var editText: EditText? = null
    
    override fun createView(context: Context): View {
        return LinearLayout(context).apply {
            orientation = LinearLayout.VERTICAL
            setPadding(0, 0, 0, dpToPx(context, 16))
            
            // Label
            addView(TextView(context).apply {
                if (config.isRequired) {
                    val labelText = "${config.label} *"
                    val spannable = SpannableString(labelText)
                    val asteriskIndex = labelText.indexOf('*')
                    spannable.setSpan(
                        ForegroundColorSpan(Color.RED),
                        asteriskIndex,
                        asteriskIndex + 1,
                        Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                    )
                    text = spannable
                } else {
                    text = config.label
                }
                setTextColor(Color.parseColor("#CCCCCC"))
                textSize = 13f
                setPadding(0, 0, 0, dpToPx(context, 8))
            })
            
            // Input
            editText = EditText(context).apply {
                hint = "Enter ${config.label.lowercase()}..."
                setBackgroundResource(R.drawable.rounded_edittext_background) // Use existing input background
                setTextColor(Color.WHITE)
                setHintTextColor(Color.parseColor("#888888"))
                setPadding(dpToPx(context, 12), dpToPx(context, 12), dpToPx(context, 12), dpToPx(context, 12))
            }
            addView(editText)
        }
    }
    
    override fun getValue(): String? = editText?.text?.toString()?.trim()
    
    override fun validate(): ValidationResult {
        val value = getValue()
        if (config.isRequired && value.isNullOrBlank()) {
            return ValidationResult.Error("Please enter ${config.label}")
        }
        return ValidationResult.Success
    }
    
    override fun reset() {
        editText?.setText("")
    }

    // Helper method for testing
    fun setValue(value: String) {
        editText?.setText(value)
    }

    private fun dpToPx(context: Context, dp: Int): Int {
        return (dp * context.resources.displayMetrics.density).toInt()
    }
}
