package com.uniq.uniqpos.app

import android.app.Activity
import android.app.Application
import android.app.Service
import android.content.Context
import androidx.multidex.MultiDex
import android.util.Log
import com.bugsnag.android.Bugsnag
import com.bugsnag.android.Configuration
import com.bugsnag.android.OnErrorCallback
import com.google.android.gms.common.GooglePlayServicesNotAvailableException
import com.google.android.gms.security.ProviderInstaller
import com.google.firebase.analytics.FirebaseAnalytics
import com.google.firebase.analytics.ktx.analytics
import com.google.firebase.analytics.ktx.logEvent
import com.google.firebase.ktx.Firebase
import com.google.firebase.remoteconfig.FirebaseRemoteConfig
import com.google.firebase.remoteconfig.ktx.remoteConfig
import com.google.firebase.remoteconfig.ktx.remoteConfigSettings
import com.gu.toolargetool.TooLargeTool
import com.uniq.uniqpos.BuildConfig
import com.uniq.uniqpos.R
import com.uniq.uniqpos.di.AppComponent
import com.uniq.uniqpos.di.DaggerAppComponent
import com.uniq.uniqpos.util.appendLog
import com.uniq.uniqpos.util.bundleOf
import com.uniq.uniqpos.util.outlet
import dagger.android.DispatchingAndroidInjector
import dagger.android.HasActivityInjector
import dagger.android.HasServiceInjector
import timber.log.Timber
import javax.inject.Inject

/**
 * Created by Git Solution on 29/09/2017.
 */

class UniqApplication : Application(), HasActivityInjector, HasServiceInjector {

    @Inject
    lateinit var activityDispatchingInjector: DispatchingAndroidInjector<Activity>

    @Inject
    lateinit var serviceDispatchingInjector: DispatchingAndroidInjector<Service>
//    @Inject
//    lateinit var fragmentDispatchingInjector: DispatchingAndroidInjector<Fragment>

    private val component: AppComponent by lazy {
        DaggerAppComponent.builder()
            .application(this)
            .build()
    }

//    init {
//        System.loadLibrary("bugsnag-ndk")
//        System.loadLibrary("bugsnag-plugin-android-anr")
//    }

    override fun onCreate() {
        super.onCreate()

        TooLargeTool.startLogging(this)

        //bugsnag
        val config = Configuration.load(this)
        config.enabledReleaseStages = setOf("testing", "production")
        if (BuildConfig.FLAVOR == "alive" && !BuildConfig.DEBUG) {
            config.releaseStage = "testing"
        }
        Bugsnag.start(this, config)

        component.inject(this)

        try {
            ProviderInstaller.installIfNeeded(this)
        } catch (e: GooglePlayServicesNotAvailableException) {
            Timber.i("Can not install TLS v1.2 : Google play service is not available")
            val outlet = outlet()
            Firebase.analytics
                .logEvent("warning") {
                    param("message", "Google Play Service Unavailable")
                    param("outlet", "GPSU:${outlet?.outletId}:${outlet?.name}")
                }

        } catch (e: Exception) {
            Timber.i("[EXCEPTION] installing TLS error $e")
            Bugsnag.notify(e)
        }

        //configure Timber
//        if(BuildConfig.FLAVOR != "production" || (BuildConfig.FLAVOR == "production" && BuildConfig.DEBUG)){
//            Timber.plant(object : Timber.DebugTree(){
//                override fun createStackElementTag(element: StackTraceElement): String? {
//                    return super.createStackElementTag(element) + " : "+element.lineNumber
//                }
//
//                override fun log(priority: Int, tag: String?, message: String, t: Throwable?) {
//                    if(priority != Log.DEBUG) appendLog("$tag : $message", "logcat", this@UniqApplication)
//                    super.log(priority, tag, message, t)
//                }
//            })
//        }else Timber.plant(ReleaseTree())

        Timber.plant(object : Timber.DebugTree() {
            override fun createStackElementTag(element: StackTraceElement): String? {
                return super.createStackElementTag(element) + ":" + element.lineNumber
            }

            override fun log(priority: Int, tag: String?, message: String, t: Throwable?) {
                if (priority != Log.DEBUG)
                    appendLog("$tag : $message", "logcat", this@UniqApplication)
                if (BuildConfig.DEBUG) super.log(priority, tag, message, t)
            }
        })

        //config firebase
        val remoteConfig: FirebaseRemoteConfig = Firebase.remoteConfig
        val configSettings = remoteConfigSettings {
            minimumFetchIntervalInSeconds = if (BuildConfig.DEBUG) 60 else 60 *3
        }
        remoteConfig.setConfigSettingsAsync(configSettings)
        remoteConfig.setDefaultsAsync(R.xml.remote_config_defaults)
    }

    override fun attachBaseContext(base: Context?) {
        super.attachBaseContext(base)
        MultiDex.install(this)
    }

    override fun activityInjector() = activityDispatchingInjector
    override fun serviceInjector() = serviceDispatchingInjector
//    override fun supportFragmentInjector() = fragmentDispatchingInjector
}
