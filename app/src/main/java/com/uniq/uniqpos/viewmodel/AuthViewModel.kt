package com.uniq.uniqpos.viewmodel

import android.os.Build
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.bugsnag.android.Bugsnag
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.uniq.uniqpos.BuildConfig
import com.uniq.uniqpos.data.local.entity.EmployeeEntity
import com.uniq.uniqpos.data.local.sharedpref.SharedPref
import com.uniq.uniqpos.data.local.sharedpref.getJson
import com.uniq.uniqpos.data.remote.model.LoginEmployee
import com.uniq.uniqpos.data.remote.model.ServerResponse
import com.uniq.uniqpos.data.remote.repository.AuthRepository
import com.uniq.uniqpos.data.remote.repository.SystemRepository
import com.uniq.uniqpos.model.EmployeePosition
import com.uniq.uniqpos.model.SubscriptionStatus
import com.uniq.uniqpos.util.WarnException
import com.uniq.uniqpos.util.diffMinutes
import com.uniq.uniqpos.util.encrypt
import com.uniq.uniqpos.util.getDeviceName
import com.uniq.uniqpos.util.lifecycle.BaseViewModel
import com.uniq.uniqpos.util.lifecycle.Event
import com.uniq.uniqpos.util.lifecycle.Failed
import com.uniq.uniqpos.util.lifecycle.Loading
import kotlinx.coroutines.launch
import retrofit2.await
import timber.log.Timber
import javax.inject.Inject
import kotlin.math.abs

/**
 * Created by ANNASBlackHat on 19/10/2017.
 */
class AuthViewModel @Inject constructor(
    private val authRepository: AuthRepository,
    private var sharePref: SharedPref,
    private val systemRepository: SystemRepository
) : BaseViewModel() {

    val pin = MutableLiveData<String>()

    data class UnAvailableSlot(val msg: String) : Exception()
    data class SubscriptionEnded(val msg: String) : Exception()
    class UnAuthorize : Exception()
    data class InvalidTime(val diffMinute: Int) : Exception()

    private val _loginSuccess = MutableLiveData<String?>()
    val loginSuccess: LiveData<String?>
        get() = _loginSuccess

    private val _subscriptionRefresh = MutableLiveData<Event<Unit>>()
    val subscriptionRefresh: LiveData<Event<Unit>>
        get() = _subscriptionRefresh

    val employeePositionList = ArrayList<EmployeePosition>()

    fun getOutlet() = authRepository.getOutlet()

    fun requestQuotation(name: String, email: String, phone: String, bussiness: String) =
        authRepository.requestQuotation(name, email, phone, bussiness)

    fun getEmployeeList(outletId: Int) = authRepository.loadEmployee(outletId)

    fun storeEmployeeList(employeeList: List<EmployeeEntity>) {
        val employeeValid = ArrayList<EmployeeEntity>()
        employeeList.forEach { employee ->
            try {
                //try to encrypt data, if failed, skip - will not save to local db
                val employeeJson = Gson().toJson(employee)
                employeeJson.encrypt()
                employeeValid.add(employee)
            } catch (e: Exception) {
                Bugsnag.notify(Exception(" Failed to try enc employee data - $employee"))
            }
        }

        try {
            sharePref.saveJson(SharedPref.EMPLOYEE_LIST, employeeValid)
        } catch (e: Exception) {
            Bugsnag.notify(e)
        }
    }

    fun loginPin(employeeId: Int, outletId: Int, firebaseToken: String, deviceId: String) {
        var version = BuildConfig.VERSION_NAME
        if (version.contains("-")) {
            version =
                BuildConfig.VERSION_NAME.substring(0, BuildConfig.VERSION_NAME.lastIndexOf("-"))
        }

        val deviceInfo = hashMapOf(
            "name" to getDeviceName(),
            "model" to Build.MODEL,
            "device" to Build.DEVICE,
            "manufacture" to Build.MANUFACTURER,
            "board" to Build.BOARD,
            "brand" to Build.BRAND,
            "os_version" to Build.VERSION.SDK_INT,
            "architecture" to System.getProperty("os.arch")
        )

        val mapData = mapOf(
            "employee_id" to employeeId.toString(),
            "pin" to pin.value,
            "outlet_id" to outletId.toString(),
            "device_id" to deviceId,
            "device_name" to getDeviceName(),
            "version" to version,
            "firebase_token" to firebaseToken,
            "device_info" to Gson().toJson(deviceInfo)
        )

        viewModelScope.launch {
            _state.value = Loading(true)
            try {
                val resp = authRepository.loginEmployee(mapData).await()
                if (resp.status) {
                    val diffMinute = abs((System.currentTimeMillis()..resp.millis).diffMinutes())
                    if (diffMinute >= 60) {
                        _state.value = Failed(InvalidTime(diffMinute))
                    } else {
                        //saving user session
                        resp.data?.let { loginData ->
                            sharePref.saveJson(SharedPref.EMPLOYEE_DATA, loginData.employee)
                            loginData.shift?.let { shift ->
                                sharePref.saveJson(
                                    SharedPref.SHIFT_DATA,
                                    shift
                                )
                            }
                            sharePref.putData(SharedPref.LOGIN_EMPLOYEE_STATUS, true)
                            sharePref.clearJson(SharedPref.SUBSCRIPTION_STATUS) //clear previous subscription

                            Timber.i("login employee '${loginData.employee.name}' | role : ${loginData.employee.roleMobile}")
                        }
                        _loginSuccess.value = if (resp.code == "3") resp.message else null
                    }
                } else {
                    when (resp.code) {
                        "100" -> {
                            _state.value = Failed(
                                UnAvailableSlot(
                                    resp.message
                                        ?: "Unavailable slot"
                                )
                            )
                        }
                        "101" -> _state.value = Failed(
                            SubscriptionEnded(
                                resp.message
                                    ?: "subscription end"
                            )
                        )
                        "401" -> {
                            _state.value = Failed(UnAuthorize())
                        }
                        else -> {
                            _state.value = Failed(WarnException(resp.message ?: "failed"))
                        }
                    }
                }
            } catch (e: Exception) {
                _state.value = Failed(e)
            } finally {
                _state.value = Loading(false)
            }
        }
    }

    fun refreshSubscription() {
        viewModelScope.launch {
            sharePref.getString(SharedPref.DEVICE_ID)?.let { id ->
                try {
                    val result = systemRepository.getSubscription(id).await()
                    result.takeIf { it.status }?.data?.let { subscription ->
                        sharePref.saveJson(SharedPref.SUBSCRIPTION_STATUS, subscription)
                        _subscriptionRefresh.postValue(Event(Unit))
                    }
                } catch (e: Exception) {
                    Timber.i("fetching subscription err: $e")
                }
            }
        }
    }

    fun loadEmployeePosition() {
        employeePositionList.clear()
        sharePref.getString(SharedPref.EMPLOYEE_POSITION_DATA)?.takeIf { it.isNotBlank() }
            ?.let { data ->
                val type = object : TypeToken<List<EmployeePosition>>() {}.type
                val json = Gson().fromJson<List<EmployeePosition>>(data.trim(), type)
                employeePositionList.addAll(json)
            }

        viewModelScope.launch {
            try {
                val result = authRepository.fetchEmployeePosition().await()
                result.data?.let { data ->
                    sharePref.saveJson(SharedPref.EMPLOYEE_POSITION_DATA, data)
                    employeePositionList.clear()
                    employeePositionList.addAll(data)
                }
            } catch (e: Exception) {
                Timber.i("loadEmployeePosition err: $e")
            }
        }
    }
}