package com.uniq.uniqpos.view.transactionhistory

import android.os.Bundle
import android.view.MenuItem
import androidx.fragment.app.commit
import com.uniq.uniqpos.R
import com.uniq.uniqpos.data.local.entity.SalesEntity
import com.uniq.uniqpos.view.global.BaseActivity
import com.uniq.uniqpos.databinding.ActivityHistoryDetailBinding
import com.uniq.uniqpos.util.lifecycle.setupSnackbar
import timber.log.Timber

class HistoryDetailActivity :
    BaseActivity<TransactionHistoryViewModel, ActivityHistoryDetailBinding>() {

    override fun getLayoutRes() = R.layout.activity_history_detail
    override fun getViewModel() = TransactionHistoryViewModel::class.java

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)

        binding.root.setupSnackbar(this, viewModel.snackbarText)

        if (savedInstanceState == null) {
            val args = Bundle()
            val sales = intent.getParcelableExtra("data") as SalesEntity?
            Timber.d("sales-history, salesId: ${sales?.noNota}")
            args.putParcelable("data", sales)
            val salesRefund = intent.getSerializableExtra("sales_refund") as Array<String>?
            salesRefund?.forEach { viewModel.salesList.add(SalesEntity(it, status = "Refund")) }
                ?: run { viewModel.enableRefund = false }

            val fragment = HistoryDetailFragment()
            fragment.arguments = args

            supportFragmentManager.commit {
                replace(R.id.history_detail, fragment)
            }
        }
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        finish()
        return super.onOptionsItemSelected(item)
    }

}
