package com.uniq.uniqpos.view.purchase

import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.RecyclerView
import com.bugsnag.android.Bugsnag
import com.uniq.uniqpos.R
import com.uniq.uniqpos.data.local.entity.OperationalCostEntity
import com.uniq.uniqpos.data.local.sharedpref.SharedPref
import com.uniq.uniqpos.data.local.sharedpref.getJson
import com.uniq.uniqpos.data.remote.model.ShiftOpen
import com.uniq.uniqpos.databinding.FragmentOperationalCostBinding
import com.uniq.uniqpos.databinding.ListItemOperationalCostBinding
import com.uniq.uniqpos.util.outlet
import com.uniq.uniqpos.util.role
import com.uniq.uniqpos.util.safe
import com.uniq.uniqpos.util.showMessage
import com.uniq.uniqpos.view.global.BaseFragment
import com.uniq.uniqpos.view.global.GlobalRecViewAdapter

/**
 * A simple [Fragment] subclass.
 *
 */
class OperationalCostFragment :
    BaseFragment<OperationalCostViewModel, FragmentOperationalCostBinding>() {

    override val layoutRes = R.layout.fragment_operational_cost
    override fun getViewModel() = OperationalCostViewModel::class.java

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        Bugsnag.leaveBreadcrumb(this::class.simpleName.safe())

        val outletId = context?.outlet()?.outletId
        val adapter =
            object : GlobalRecViewAdapter<OperationalCostEntity, ListItemOperationalCostBinding>(
                R.layout.list_item_operational_cost,
                viewModel.getOperationalCostLive(outletId),
                this,
                viewNoData = binding.incLayNoData.root
            ) {}
        binding.recviewOc.adapter = adapter
        binding.recviewOc.addOnScrollListener(object : RecyclerView.OnScrollListener() {
            override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                super.onScrolled(recyclerView, dx, dy)
                if (dy > 0 && binding.fabAdd.isShown) binding.fabAdd.hide()
            }

            override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
                if (newState == RecyclerView.SCROLL_STATE_IDLE) {
                    binding.fabAdd.show()
                }
                super.onScrollStateChanged(recyclerView, newState)
            }
        })

        binding.fabAdd.setOnClickListener {
            if (role().inputkaskeluar) {
                addOperationalCost()
            } else {
                showMsg(getString(R.string.no_permission))
            }
        }
    }

    private fun addOperationalCost() {
        context?.getJson(SharedPref.SHIFT_DATA, ShiftOpen::class.java)
            ?.takeIf { it.openShiftId > 0 }?.let {
                startActivity(Intent(context, AddOperationalCostActivity::class.java))
            } ?: kotlin.run {
            context?.showMessage(getString(R.string.no_active_shift_msg), "NO ACTIVE SHIFT")
        }
    }

}
