package com.uniq.uniqpos.view.payment

import android.os.Bundle
import android.text.TextWatcher
import androidx.fragment.app.Fragment
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.google.gson.Gson
import com.uniq.uniqpos.R
import com.uniq.uniqpos.data.local.sharedpref.putData
import com.uniq.uniqpos.databinding.FragmentPaymentDetailBinding
import com.uniq.uniqpos.databinding.ItemPaymentNominalBinding
import com.uniq.uniqpos.model.PaymentSelected
import com.uniq.uniqpos.model.ShowCaseData
import com.uniq.uniqpos.util.*
import com.uniq.uniqpos.view.global.GlobalAdapter
import com.uniq.uniqpos.view.global.GlobalViewHolder
import smartdevelop.ir.eram.showcaseviewlib.GuideView
import smartdevelop.ir.eram.showcaseviewlib.config.DismissType
import timber.log.Timber
import java.util.*

// TODO: Rename parameter arguments, choose names that match
// the fragment initialization parameters, e.g. ARG_ITEM_NUMBER
private const val ARG_PARAM1 = "param1"
private const val ARG_PARAM2 = "param2"

/**
 * A simple [Fragment] subclass.
 * Use the [PaymentDetailFragment.newInstance] factory method to
 * create an instance of this fragment.
 */
class PaymentDetailFragment : Fragment() {
    // TODO: Rename and change types of parameters
    private var param1: String? = null
    private var param2: String? = null

    private var _binding: FragmentPaymentDetailBinding? = null
    private val binding: FragmentPaymentDetailBinding get() = _binding!!

    private val appActivity: PaymentV2Activity get() = activity as PaymentV2Activity
    private val viewModel: PaymentViewModel get() = (activity as PaymentV2Activity).getPaymentViewModel()
    private val selectedPayment: PaymentSelected get() = viewModel.selectedPayment

    private var edtPayWatcher: TextWatcher? = null
    private var edtPaymentInfoWatcher: TextWatcher? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.let {
            param1 = it.getString(ARG_PARAM1)
            param2 = it.getString(ARG_PARAM2)
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        // Inflate the layout for this fragment
        _binding = FragmentPaymentDetailBinding.inflate(inflater, container, false)
        return _binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        binding.viewmodel = viewModel
        binding.lifecycleOwner = this

        Timber.d("change payment selected to: ${Gson().toJson(viewModel.selectedPayment)}")
        binding.edtPay.setText(viewModel.selectedPayment.total.toCurrency())
        edtPayWatcher = binding.edtPay.liveToCurrencyAndWatch { listenToInputPayment(it) }
        binding.recViewPaymentOption.adapter = getPaymentOptionAdapter()

        binding.txtChangeBank.setOnClickListener { viewModel.taskShowCardDialog.call() }
        binding.imgClear.setOnClickListener { clearCurrentPayment() }
        edtPaymentInfoWatcher = binding.editPaymentInformation.simpleOnTextChanged {
//            Timber.d("current total pay: ${viewModel.selectedPayment.total}")
            if(viewModel.selectedPayment.total == 0){
                Timber.i("autoset pay.. as user type pay info!")
                choosePaymentOption(0)
            }
        }

        adjustUiInput()
        observeTask()
    }

    private fun clearCurrentPayment() {
        binding.edtPay.setText("")
    }

    private fun adjustUiInput() {
        binding.layoutDetailCard.setVisible(viewModel.selectedPayment.method == "CARD")
        binding.editPaymentInformation.setVisible(
            listOf(
                "COMPLIMENT",
                "PIUTANG"
            ).any { it == viewModel.selectedPayment.method })
//        binding.editPaymentInformation.isEnabled =  viewModel.selectedPayment.payment == "COMPLIMENT"

        if (viewModel.selectedPayment.method == "CARD" && viewModel.selectedPayment.selectedBankIdx >= 0) {
            if (viewModel.cardInformation.value.safe().isEmpty()) {
                binding.edtCardInformation.requestFocus()
//                Handler(Looper.getMainLooper()).postDelayed({
//                    showCase(ShowCase.PaymentInputCardInfo)
//                }, 3000)
            }
            binding.txtBankName.text = viewModel.bankList[viewModel.selectedPayment.selectedBankIdx].name
            binding.edtCardInformation.setText(viewModel.selectedPayment.bankInformation)
            viewModel.cardInformation.value = viewModel.selectedPayment.bankInformation
        } else if (viewModel.selectedPayment.method == "PIUTANG") {
            binding.editPaymentInformation.setAsDateinput(
                childFragmentManager,
                minDate = Calendar.getInstance()
            )
            binding.editPaymentInformation.hint = "select due date"
        }
    }

    private fun listenToInputPayment(input: String) {
        if (input.fromCurrency() <= 0 && selectedPayment.isSelected) {
            selectedPayment.isSelected = false
            viewModel.taskRefreshPaymentList.postValue(viewModel.selectedPaymentIdx)
        } else if (input.fromCurrency() > 0 && !selectedPayment.isSelected) {
            selectedPayment.isSelected = true
            viewModel.taskRefreshPaymentList.postValue(viewModel.selectedPaymentIdx)
        } else if (input == "0" && viewModel.getGrandTotal() == 0) {
            selectedPayment.isSelected = true
            viewModel.taskRefreshPaymentList.postValue(viewModel.selectedPaymentIdx)
        }
        viewModel.paymentValue[viewModel.selectedPaymentIdx] = input.fromCurrency()
        viewModel.selectedPayment.total = input.fromCurrency()
        selectedPayment.total = input.fromCurrency()
        viewModel.countChange()
    }

    private fun getPaymentOptionAdapter(): GlobalAdapter<ItemPaymentNominalBinding> {
        return object : GlobalAdapter<ItemPaymentNominalBinding>(
            R.layout.item_payment_nominal,
            viewModel.paymentOpion
        ) {
            override fun onBindViewHolder(
                holder: GlobalViewHolder<ItemPaymentNominalBinding>,
                position: Int
            ) {
                super.onBindViewHolder(holder, position)
                holder.itemView.setOnClickListener {
                    choosePaymentOption(position)
                }
            }
        }
    }

    private fun choosePaymentOption(position: Int){
        if(viewModel.paymentOpion.isEmpty() || position >= viewModel.paymentOpion.size)return
        binding.edtPay.setText(viewModel.paymentOpion[position].toCurrency())
        val payment = viewModel.selectedPayment.method
        if (payment != PaymentViewModel.PaymentType.CARD.toString() || !viewModel.cardInformation.value.isNullOrEmpty()) {
            if (!appActivity.isTabletMode) { //automatically close bottom dialog
                appActivity.dismissPaymentDetailDialog()
            }
        }
    }

    private fun observeTask() {
        viewModel.taskRefreshPaymentOption.observe(this) {
            binding.recViewPaymentOption.adapter?.notifyDataSetChanged()
        }
    }

    private fun showCase(showCaseType: String = "") {
        val employee = context.employee()
        val key = "$showCaseType ${employee?.employeeId}"
//        if(requireContext().getLocalDataBoolean(key)){
//            return
//        }

        val showCaseData = when (showCaseType) {
            ShowCase.PaymentInputCardInfo -> {
                ShowCaseData(
                    "Card Information", "Masukan informasi kartu di sini!", binding.editPaymentInformation
                ) {  }
            }
            else -> null
        }

        showCaseData?.let { showCaseData ->
            GuideView.Builder(context).setTitle(showCaseData.title)
                .setContentText(showCaseData.message).setTargetView(showCaseData.view)
                .setTitleTextSize(17).setDismissType(DismissType.anywhere)
                .setGuideListener { showCaseData.next.invoke() }.build().show()
            context?.putData(key, true)
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        edtPayWatcher?.let { _binding?.edtPay?.removeTextChangedListener(it) }
        edtPaymentInfoWatcher?.let { _binding?.editPaymentInformation?.removeTextChangedListener(it) }
        _binding = null
        Timber.d("onDestroyView PaymentDetailFragment $this")
    }

    companion object {
        /**
         * Use this factory method to create a new instance of
         * this fragment using the provided parameters.
         *
         * @param param1 Parameter 1.
         * @param param2 Parameter 2.
         * @return A new instance of fragment PaymentDetailFragment.
         */
        // TODO: Rename and change types and number of parameters
        @JvmStatic
        fun newInstance(param1: String, param2: String) =
            PaymentDetailFragment().apply {
                arguments = Bundle().apply {
                    putString(ARG_PARAM1, param1)
                    putString(ARG_PARAM2, param2)
                }
            }
    }
}