package com.uniq.uniqpos.view.productcatalogue

import android.Manifest
import android.app.Activity
import android.content.Intent
import android.content.pm.PackageManager
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.drawable.Drawable
import android.net.Uri
import android.os.Bundle
import android.os.Environment
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import android.widget.ArrayAdapter
import androidx.core.content.ContextCompat
import androidx.core.graphics.drawable.toBitmap
import androidx.lifecycle.lifecycleScope
import com.bumptech.glide.Glide
import com.bumptech.glide.request.target.CustomTarget
import com.bumptech.glide.request.transition.Transition
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.android.material.snackbar.Snackbar
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.uniq.uniqpos.R
import com.uniq.uniqpos.app.GlideApp
import com.uniq.uniqpos.data.local.entity.CategoryEntity
import com.uniq.uniqpos.data.local.entity.ProductEntity
import com.uniq.uniqpos.data.local.sharedpref.getJson
import com.uniq.uniqpos.data.local.sharedpref.getLocalDataBoolean
import com.uniq.uniqpos.data.local.sharedpref.getLocalDataString
import com.uniq.uniqpos.data.local.sharedpref.putData
import com.uniq.uniqpos.data.local.sharedpref.putJson
import com.uniq.uniqpos.databinding.ActivityAddProductBinding
import com.uniq.uniqpos.databinding.DialogExtractMenuBinding
import com.uniq.uniqpos.model.BottomDialogModel
import com.uniq.uniqpos.model.SimplePageListData
import com.uniq.uniqpos.util.*
import com.uniq.uniqpos.util.data.DataHolder
import com.uniq.uniqpos.util.intent.CameraOrGalleryIntent
import com.uniq.uniqpos.util.intent.openCamera
import com.uniq.uniqpos.util.lifecycle.setupLoadingDialog
import com.uniq.uniqpos.util.lifecycle.setupLoadingDialogMessage
import com.uniq.uniqpos.util.lifecycle.setupToast
import com.uniq.uniqpos.util.lifecycle.setupToastMessage
import com.uniq.uniqpos.view.global.BaseActivity
import com.uniq.uniqpos.view.global.BottomDialogInput
import com.uniq.uniqpos.view.productcatalogue.ocr.ProductEditList
import com.uniq.uniqpos.view.simplepagelist.SimplePageListActivity
import com.weiwangcn.betterspinner.library.material.MaterialBetterSpinner
import kotlinx.coroutines.launch
import timber.log.Timber
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import java.util.*
import kotlin.coroutines.resume
import kotlin.coroutines.suspendCoroutine

class AddProductActivity : BaseActivity<ProductCatalogViewModel, ActivityAddProductBinding>() {

    private val PERMISSION_GALERY_REQUEST_CODE = 11
    private val PICK_IMAGE = 12
    private val REQUEST_TAKE_PHOTO = 13
    private val PERMISSION_CAMERA_REQUEST_CODE = 14
    private val RC_SCAN = 15
    private val RC_CHOOSE_CATEGORY = 16
    private val PICK_IMAGE_OCR = 17

    private val requestCodeCameraOcr = 18
    private val requestCodeCameraProduct = 19

    private var isInAdvanceMode = true
    private var tmpImgPath: String? = null
    private var imgPath: String? = null
    private lateinit var productTypeAdadpter: ArrayAdapter<String>
    private lateinit var subCategoryAdadpter: ArrayAdapter<String>
    private lateinit var categoryAdadpter: ArrayAdapter<String>
    private lateinit var purchaseCategoryAdadpter: ArrayAdapter<String>
    private lateinit var unitAdadpter: ArrayAdapter<String>
    private var addNew = "--Add New--"
    
    // Data class to store pinned fields configuration
    data class PinnedFieldsConfig(
        val pinnedFields: MutableList<String> = mutableListOf()
    )
    
    // List of all available advanced fields
    private val advancedFields = listOf(
        "product_type",
        "product_subcategory",
        "purchase_category",
        "unit",
        "barcode",
        "sku",
        "buying_price",
        "active"
    )
    
    // Map of field IDs to their corresponding views
    private lateinit var fieldViewMap: Map<String, View>
    
    // Current pinned fields configuration
    private val pinnedFields = mutableListOf<String>()
    
    // Shared preferences key for pinned fields
    private val PINNED_FIELDS_PREF_KEY = "pinned_fields_config"

    override fun getLayoutRes() = R.layout.activity_add_product
    override fun getViewModel() = ProductCatalogViewModel::class.java

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)

        productTypeAdadpter =
            ArrayAdapter(this, android.R.layout.simple_list_item_1, arrayListOf(addNew))
        subCategoryAdadpter =
            ArrayAdapter(this, android.R.layout.simple_list_item_1, arrayListOf(addNew))
        categoryAdadpter =
            ArrayAdapter(this, android.R.layout.simple_list_item_1, arrayListOf(addNew))
        purchaseCategoryAdadpter =
            ArrayAdapter(this, android.R.layout.simple_list_item_1, arrayListOf(addNew))
        unitAdadpter = ArrayAdapter(this, android.R.layout.simple_list_item_1, arrayListOf(addNew))

        // Initialize field view map
        fieldViewMap = mapOf(
            "product_type" to binding.spinProdType,
            "product_subcategory" to binding.spinProdSubcategory,
            "purchase_category" to binding.spinPurcCateegory,
            "unit" to binding.spinUnit,
            "barcode" to binding.layoutBarcode,
            "sku" to binding.layoutSku,
            "buying_price" to binding.layoutBuyPrice,
            "active" to binding.spinActive
        )
        
        // Load pinned fields from SharedPreferences
        loadPinnedFields()
        
        // Setup pin buttons for each field
        setupPinButtons()
        
        // Show pin feature explanation if first time
        showPinFeatureExplanationIfNeeded()

        addNew = getString(R.string.add_new)
        val activeStatus = listOf("Active All", "Active On Sales", "Active On Link", "No Active")
        binding.spinActive.setAdapter(
            ArrayAdapter(
                this,
                android.R.layout.simple_list_item_1,
                activeStatus
            )
        )
        binding.spinProdType.setAdapter(productTypeAdadpter)
        binding.spinCategory.setAdapter(categoryAdadpter)
        binding.spinProdSubcategory.setAdapter(subCategoryAdadpter)
        binding.spinPurcCateegory.setAdapter(purchaseCategoryAdadpter)
        binding.spinUnit.setAdapter(unitAdadpter)

        listOf(binding.spinProdType, binding.spinCategory, binding.spinProdSubcategory, binding.spinPurcCateegory, binding.spinUnit)
            .forEach { view ->
                view.setOnItemClickListener { _, _, _, _ ->
                    if (view.value() == addNew) {
                        view.setText("")
                        addNewItem(view)
                    }
                }
            }

        binding.spinCategory.setOnClickListener {
            if (categoryAdadpter.count > 10) {
                val data = ArrayList<SimplePageListData>()
                viewModel.categoryList.forEach {
                    data.add(
                        SimplePageListData(
                            it.name,
                            it.productCategoryId
                        )
                    )
                }
                val intent = Intent(this, SimplePageListActivity::class.java)
                intent.putExtra("data", data)
                intent.putExtra("type", "Product Category")
                startActivityForResult(intent, RC_CHOOSE_CATEGORY)
            }
        }

        binding.spinActive.setText(activeStatus[0])
        Utils.registerToCurrency(binding.edtBuyingPrice, binding.edtSellingPrice)

        binding.txtCamera.setOnClickListener { openCamera(this, false, REQUEST_TAKE_PHOTO ) }
        binding.txtGallery.setOnClickListener { openGallery() }
        binding.btnSave.setOnClickListener { saveProduct() }
        binding.btnTryOcr.setOnClickListener { showOcrDialog()}

        binding.layoutAdvance.setOnClickListener { setAdvanceMode() }
        setAdvanceMode()

        binding.edtProductName.setOnFocusChangeListener { _, hasFocus ->
            if (!hasFocus && imgPath == null && binding.edtProductName.value().isNotEmpty()) {
                setImageUrlText(binding.imgProduct, null, binding.edtProductName.value())
            }
        }
    }

    private fun setAdvanceMode() {
        isInAdvanceMode = !isInAdvanceMode
        
        // Update the UI based on the new mode
        updateFieldsVisibility()
        
        // Update the text
        binding.txtAdvanceDetail.text =
            getString(if (isInAdvanceMode) R.string.advance_mode_detail_hide else R.string.advance_mode_detail_show)
    }

    private fun addNewItem(view: MaterialBetterSpinner) {
        val bottomDialog = BottomDialogInput()
        bottomDialog.setOnButtonClick { result ->
            if (result == addNew || result.isBlank()) {
                toast("Value is not allowed!")
            } else {
                val adapter = view.adapter as ArrayAdapter<String>
                adapter.remove(addNew)
                adapter.add(result)
                adapter.add(addNew)
                view.setText(result)

                if (view == binding.spinProdType) binding.spinCategory.requestFocus()
                else binding.spinProdType.requestFocus()

                bottomDialog.dismiss()
            }
        }
        bottomDialog.setModel(
            BottomDialogModel(
                "ADD NEW",
                "SAVE",
                view.hint.toString(),
                hint = view.hint.toString()
            )
        )
        bottomDialog.show(supportFragmentManager, "add-new")
    }

    private fun saveProduct() {
        if (!Utils.isValidField(binding.edtProductName, binding.spinCategory, binding.edtSellingPrice)) {
            return
        }

        var imgEncode = imgPath
        val stockManagement = if (binding.swStockManagement.isChecked) 1 else 0
        val productTypeId =
            viewModel.productTypeList.firstOrNull { it.name == binding.spinProdType.value() }?.productsTypeId
        val productCategoryId =
            viewModel.categoryList.firstOrNull { it.name == binding.spinCategory.value() }?.productCategoryId
        val prodSubCatId =
            viewModel.subCategoryList.firstOrNull { it.name == binding.spinProdSubcategory.value() }?.productCategoryId
                ?: 0
        val unitId = viewModel.unitList.firstOrNull { it.name == binding.spinUnit.value() }?.unitId
        val purcRepCat =
            viewModel.purchaseReportCategoryList.firstOrNull { it.name == binding.spinPurcCateegory.value() }?.purchaseReportCategoryId
        val activeStatus = getActiveStatus(binding.spinActive.value())
        val product = ProductEntity(
            productId = System.currentTimeMillis(),
            name = binding.edtProductName.value(),
            productTypeName = binding.spinProdType.value(),
            productTypeFkid = productTypeId,
            productcategoryName = binding.spinCategory.value(),
            productCategoryFkid = productCategoryId,
            productSubCategoryName = binding.spinProdSubcategory.value(),
            productSubcategoryFkid = prodSubCatId,
            unitName = binding.spinUnit.value(),
            unitFkid = unitId,
            purchaseReportCategoryName = binding.spinPurcCateegory.value(),
            purchaseReportCategoryFkid = purcRepCat.safeToInt(),
            stockManagement = stockManagement,
            barcode = binding.edtBarcode.value(),
            sku = binding.edtSku.value(),
            priceBuy = binding.edtBuyingPrice.value().fromCurrency(),
            priceSell = binding.edtSellingPrice.value().fromCurrency(),
            active = activeStatus,
            outletFkid = outlet()?.outletId,
            productDetailId = 0,
            photo = imgEncode
        )

        if (product.productTypeName == "") product.productTypeName = binding.spinCategory.value()
        if (product.productSubCategoryName == "") product.productSubCategoryName =
            binding.spinCategory.value()
        if (product.purchaseReportCategoryName == "") product.purchaseReportCategoryName =
            binding.spinCategory.value()
        if (product.unitName == "") product.unitName = "pcs"

        Timber.i("Add Product >> ${Gson().toJson(product)}")
        showDialog(true)
        viewModel.addProductAsyn(product) { status, message ->
            showDialog(false)
            if (status) {
                showSnackbar(
                    "${product.name?.uppercase()} berhasil di tambahkan!",
                    Snackbar.LENGTH_LONG
                )
                resetView()
                binding.edtProductName.requestFocus()
            } else {
                showMessage(message?.readableError(this), "FAILED")
            }
        }
    }

    private fun resetView() {
        binding.edtProductName.setText("")
        binding.edtBuyingPrice.setText("")
        binding.edtSellingPrice.setText("")
        binding.edtBarcode.setText("")
        binding.edtSku.setText("")
        imgPath = null
        binding.imgProduct.setImageResource(R.drawable.default_image)
    }

    //on_all', 'on_sales', 'on_link', 'off
    private fun getActiveStatus(status: String) = when (status.lowercase()) {
        "active on sales" -> "on_sales"
        "active on link" -> "on_link"
        "no active" -> "off"
        else -> "on_all"
    }

    override fun observeData() {
        setupLoadingDialog(this, this, viewModel.loadingDialog)
        setupLoadingDialogMessage(this, this, viewModel.loadingDialogMessage)
        setupToast(this, viewModel.toastMsg)
        setupToastMessage(this, viewModel.toastMessage)

        viewModel.loadProductType()
            .observe(this) {
                viewModel.productTypeList.clear()
                it?.data?.let {
                    viewModel.productTypeList.addAll(it)
                    productTypeAdadpter.clear()
                    it.forEach { productTypeAdadpter.add(it.name) }
                    productTypeAdadpter.add(addNew)
                }
            }

        viewModel.loadCategory()
            .observe(this) {
                viewModel.categoryList.clear()
                it?.data?.let {
                    viewModel.categoryList.addAll(it)
                    categoryAdadpter.clear()
                    it.forEach { categoryAdadpter.add(it.name) }
                    categoryAdadpter.add(addNew)
                }
            }

        viewModel.loadPurchaseReportCategory()
            .observe(this) {
                viewModel.purchaseReportCategoryList.clear()
                it?.data?.let {
                    viewModel.purchaseReportCategoryList.addAll(it)
                    purchaseCategoryAdadpter.clear()
                    it.forEach { purchaseCategoryAdadpter.add(it.name) }
                    purchaseCategoryAdadpter.add(addNew)
                }
            }

        viewModel.loadUnit()
            .observe(this) {
                viewModel.unitList.clear()
                it?.data?.let {
                    viewModel.unitList.addAll(it)
                    unitAdadpter.clear()
                    it.forEach { unitAdadpter.add(it.name) }
                    unitAdadpter.add(addNew)
                }
            }

        viewModel.subCategories.observe(this) {
            it?.let { subcategories ->
                viewModel.subCategoryList.clear()
                viewModel.subCategoryList.addAll(subcategories)

                subCategoryAdadpter.clear()
                subcategories.forEach { subCategoryAdadpter.add(it.name) }
                subCategoryAdadpter.add(addNew)
            }
        }

        viewModel.state.observe(this) {state ->
            when(state){
                is ProductCatalogViewModel.SuccessExtractMenu -> {
                    val menu = Gson().toJson(state.data)
                    Timber.i("menu extracted: $menu")
                    if (state.data.isNotEmpty()){
                        DataHolder.setData(menu)
                        startActivity(Intent(this, ProductEditList::class.java))
                    }else{
                        showMessage("no menu found in the image", "INFO")
                    }
                }
            }
        }
    }

    private fun openGallery(requestCode: Int = PICK_IMAGE) {
        if (ContextCompat.checkSelfPermission(
                this,
                Manifest.permission.READ_EXTERNAL_STORAGE
            ) != PackageManager.PERMISSION_GRANTED
        ) {
            toast("read storage permission not granted")
        }

        val intent = Intent()
        intent.type = "image/*"
        intent.action = Intent.ACTION_GET_CONTENT
        startActivityForResult(Intent.createChooser(intent, "Select Picture"), requestCode)
    }

    @Throws(IOException::class)
    private fun createImageFile(): File {
        // Create an image file name
        val timeStamp = Date().dateFormat("yyyyMMdd_HHmmss")
        val imageFileName = "JPEG_" + timeStamp + "_"
        val storageDir = getExternalFilesDir(Environment.DIRECTORY_PICTURES)
        Timber.i(">> Storage Dir : $storageDir")

        val image = File.createTempFile(
            imageFileName, /* prefix */
            ".jpg", /* suffix */
            storageDir      /* directory */
        )

        // Save a file: path for use with ACTION_VIEW intents
        tmpImgPath = image.absolutePath
        Timber.i(">> tmpImgPath : $tmpImgPath")
        return image
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (resultCode == Activity.RESULT_OK) {
            when (requestCode) {
                PICK_IMAGE -> {
                    try {
                        val selectedImg = data?.data
                        Glide.with(this)
                            .load(selectedImg)
                            .into(binding.imgProduct)
                        setImagePathFromUri(selectedImg)
                    } catch (e: Exception) {
                        toast("image not found", level = Level.ERROR)
                    }
                }
                REQUEST_TAKE_PHOTO -> {
                    imgPath = CameraOrGalleryIntent.currentPhotoPath
                    imgPath?.let { filePath ->
                        Glide.with(this)
                            .load(filePath)
                            .into(binding.imgProduct)
                        lifecycleScope.launch {
                            compressImageFile(filePath)
                        }
                    }
                }
                RC_SCAN -> {
                    binding.edtBarcode.setText(data?.getStringExtra("data"))
                }
                RC_CHOOSE_CATEGORY -> {
                    val simpleData = data?.getParcelableExtra<SimplePageListData>("data")
                    if (data?.getStringExtra("source") == "new") {
                        viewModel.categoryList.add(CategoryEntity(0, 0, simpleData?.title ?: ""))
                    }
                    binding.spinCategory.setText(simpleData?.title)
                    binding.edtBuyingPrice.requestFocus()
                }
                CameraOrGalleryIntent.CAMERA -> {
                    try {
                        val bmp = BitmapFactory.decodeFile(CameraOrGalleryIntent.currentPhotoPath)
                        viewModel.extractMenu(bmp, getExternalFilesDir(Environment.DIRECTORY_PICTURES))
                    } catch (e: Exception) {
                        showMessage("image not found\n$e", "error")
                    }
                }
                PICK_IMAGE_OCR -> {
                    Timber.i("pickImage data: ${data?.data}")
                    Timber.i("pickImage extras: ${data?.extras}")
                    try {
                        val imgGallery = data?.data
                        showDialog(true, "reading file...")
                        lifecycleScope.launch {
                            val bmp = convertUriToBitmap(imgGallery!!)
                            runOnUiThread {
                                viewModel.extractMenu(bmp)
                            }
                        }
                    } catch (e: Exception) {
                        Timber.i("failed loadImg to bitmap: $e")
                        toast("image not found", level = Level.ERROR)
                    }
                }
            }

            imgPath?.let {
                GlideApp.with(this).load(File(it)).into(binding.imgProduct)
            }
        }
    }

    private fun convertUriToBitmap(imgUri: Uri, func: (Bitmap) -> Unit){
        Glide.with(this)
            .load(imgUri)
            .into(object : CustomTarget<Drawable>() {
                override fun onResourceReady(
                    resource: Drawable,
                    transition: Transition<in Drawable>?
                ) {
                    func(resource.toBitmap())
                }

                override fun onLoadCleared(placeholder: Drawable?) {}
            })
    }

    private fun setImagePathFromUri(selectedImg: Uri?, func: (String) -> Unit = {}) {
        GlideApp.with(this)
            .load(selectedImg)
            .into(object : CustomTarget<Drawable>() {
                override fun onResourceReady(
                    resource: Drawable,
                    transition: Transition<in Drawable>?
                ) {
                    val bmp = resource.toBitmap()
                    Timber.i("toBitmap: $bmp")
                    val file = File(
                        getExternalFilesDir(Environment.DIRECTORY_PICTURES),
                        "product_" + System.currentTimeMillis() + ".jpeg"
                    )

                    Timber.i("image file, absolute: ${file.absolutePath} - path: ${file.path}")
                    val out = FileOutputStream(file)
                    bmp.compress(Bitmap.CompressFormat.JPEG, 70, out)
                    out.close()
                    imgPath = file.path

                    func(file.path)
                }

                override fun onLoadCleared(placeholder: Drawable?) {}
            })
    }

    fun showSnackbar(msg: String, duration: Int = Snackbar.LENGTH_SHORT) {
        Timber.i("[[SNACKBAR]] $msg")
        val snack = Snackbar.make(binding.container, msg, duration)
        snack.setAction("OK") { snack.dismiss() }
        snack.show()
    }

    private  fun showOcrDialog(){
        val dialogOcr = DialogExtractMenuBinding.inflate(layoutInflater)
        val bottomSheetDialog = BottomSheetDialog(this)
        bottomSheetDialog.setContentView(dialogOcr.root)
        bottomSheetDialog.show()

        dialogOcr.btnCamera.setOnClickListener {
            openCamera(this, isThumbOnly = false)
            bottomSheetDialog.dismiss()
        }
        dialogOcr.btnGallery.setOnClickListener {
            openGallery(PICK_IMAGE_OCR)
            bottomSheetDialog.dismiss()
        }
    }

    override fun onCreateOptionsMenu(menu: Menu): Boolean {
        val inflater: MenuInflater = menuInflater
        inflater.inflate(R.menu.menu_add_product, menu)
        return true
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        if (requestCode == PERMISSION_GALERY_REQUEST_CODE) {
            openGallery()
        }
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        when (item.itemId) {
            R.id.action_use_ocr -> {
                showOcrDialog()
                return true
            }
            R.id.action_manage_pins -> {
                showManagePinsDialog()
                return true
            }
            else -> {
                finish()
                return super.onOptionsItemSelected(item)
            }
        }
    }

    private suspend fun compressImageFile(imagePath: String): Bitmap {
        val externalFilesDir = getExternalFilesDir(Environment.DIRECTORY_PICTURES)
        return suspendCoroutine { continuation ->
            BACKGROUND.submit {
                val res = Glide.with(this).asBitmap().load(imagePath).submit()
                val bitmap = res.get()
                val resizedBitmap =  viewModel.compressBitmap(bitmap, externalFilesDir)
                val outStream = FileOutputStream(imagePath)
                resizedBitmap.compress(Bitmap.CompressFormat.JPEG, 100, outStream)
                outStream.close()
                continuation.resume(resizedBitmap)
            }
        }
    }

    private suspend fun convertUriToBitmap(uri: Uri): Bitmap {
        val externalFilesDir = getExternalFilesDir(Environment.DIRECTORY_PICTURES)
        return suspendCoroutine { continuation ->
            BACKGROUND.submit {
                val res = Glide.with(this).asBitmap().load(uri).submit()
                val bitmap = res.get()
                val resizedBitmap =  viewModel.compressBitmap(bitmap, externalFilesDir)
                continuation.resume(resizedBitmap)
            }
        }
    }

    private fun loadPinnedFields() {
        val pinnedFieldsJson = getLocalDataString(PINNED_FIELDS_PREF_KEY, "[]")
        pinnedFields.clear()
        if(pinnedFieldsJson.isNotBlank())
        pinnedFields.addAll(Gson().fromJson(pinnedFieldsJson, object : TypeToken<List<String>>() {}.type))
    }

    private fun savePinnedFields() {
        val pinnedFieldsJson = Gson().toJson(pinnedFields)
//        sharedPreferences.edit().putString(PINNED_FIELDS_PREF_KEY, pinnedFieldsJson).apply()
        putData(PINNED_FIELDS_PREF_KEY, pinnedFieldsJson)
    }

    private fun setupPinButtons() {
        // For each field, add a long click listener to toggle pin state
        fieldViewMap.forEach { (fieldId, view) ->
            view.setOnLongClickListener {
                togglePinField(fieldId)
                true
            }
            
            // Add a visual indicator for pinned fields
            updatePinIndicator(fieldId, view)
        }
    }
    
    private fun togglePinField(fieldId: String) {
        if (pinnedFields.contains(fieldId)) {
            // Unpin the field
            pinnedFields.remove(fieldId)
            toast("Field unpinned")
        } else {
            // Pin the field
            pinnedFields.add(fieldId)
            toast("Field pinned to basic mode")
        }
        
        // Save the updated configuration
        savePinnedFields()
        
        // Update the UI
        updateFieldsVisibility()
    }
    
    private fun updatePinIndicator(fieldId: String, view: View) {
        // Add a visual indicator for pinned fields
        // This could be a background color, a border, or an icon
        // For simplicity, we'll just change the background tint
        if (pinnedFields.contains(fieldId)) {
            view.setBackgroundResource(R.drawable.bg_pinned_field)
        } else {
            view.setBackgroundResource(0)
        }
    }
    
    private fun updateFieldsVisibility() {
        // Update visibility of all fields based on current mode and pinned status
        fieldViewMap.forEach { (fieldId, view) ->
            // Update the pin indicator
            updatePinIndicator(fieldId, view)
            
            if (pinnedFields.contains(fieldId)) {
                // Pinned fields are always visible
                view.visibility = View.VISIBLE
            } else {
                // Non-pinned fields follow the advanced mode visibility
                view.visibility = if (isInAdvanceMode) View.VISIBLE else View.GONE
            }
        }
    }

    private fun showPinFeatureExplanationIfNeeded() {
        val hasShownExplanation = getLocalDataBoolean("has_shown_pin_explanation", false)
        
        if (!hasShownExplanation) {
            // Show explanation dialog
            androidx.appcompat.app.AlertDialog.Builder(this)
                .setTitle("New Feature: Pin Fields")
                .setMessage("You can now pin fields from advanced mode to basic mode! Long press on any field in advanced mode to pin it. Pinned fields will always be visible, even in basic mode.")
                .setPositiveButton("Got it") { _, _ ->
                    // Mark as shown
//                    sharedPreferences.edit().putBoolean("has_shown_pin_explanation", true).apply()
                    putData("has_shown_pin_explanation", true)
                }
                .show()
        }
    }

    private fun showManagePinsDialog() {
        Timber.i("showManagePinsDialog")
        // Create a list of field names for the dialog
        val fieldNames = mapOf(
            "product_type" to "Product Type",
            "product_subcategory" to "Product Subcategory",
            "purchase_category" to "Purchase Category",
            "unit" to "Unit",
            "barcode" to "Barcode",
            "sku" to "SKU",
            "buying_price" to "Buying Price",
            "active" to "Active Status"
        )
        
        // Create a boolean array of which fields are pinned
        val checkedItems = advancedFields.map { pinnedFields.contains(it) }.toBooleanArray()
        
        // Show a multi-choice dialog
        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("Manage Pinned Fields")
            .setMultiChoiceItems(
                advancedFields.map { fieldNames[it] ?: it }.toTypedArray(),
                checkedItems
            ) { _, which, isChecked ->
                val fieldId = advancedFields[which]
                if (isChecked) {
                    // Pin the field
                    if (!pinnedFields.contains(fieldId)) {
                        pinnedFields.add(fieldId)
                    }
                } else {
                    // Unpin the field
                    pinnedFields.remove(fieldId)
                }
            }
            .setPositiveButton("Save") { _, _ ->
                // Save the updated configuration
                savePinnedFields()
                
                // Update the UI
                updateFieldsVisibility()
                
                // Show a confirmation
                toast("Pinned fields updated")
            }
            .setNegativeButton("Cancel", null)
            .show()
    }
}
