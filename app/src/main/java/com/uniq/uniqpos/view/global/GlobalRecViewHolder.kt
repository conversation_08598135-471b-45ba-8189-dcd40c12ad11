package com.uniq.uniqpos.view.global

import android.view.View
import androidx.recyclerview.widget.RecyclerView
import androidx.databinding.ViewDataBinding
import androidx.databinding.DataBindingUtil
import timber.log.Timber
import java.lang.Exception

/**
 * Created by ANNASBlackHat on 13/09/2017.
 */
class GlobalRecViewHolder(itemView: View?) : RecyclerView.ViewHolder(
    itemView!!
) {
    private var binding: ViewDataBinding? = null

    fun onBind(variable: Int, value: Any?) {
        if (value == null) return
        binding?.setVariable(variable, value)
        binding?.executePendingBindings()
    }

    fun <T : ViewDataBinding?> getBinding(): T? {
        return try {
            binding as T?
        } catch (e: Exception) {
            Timber.i("getBinding err: $e")
            null
        }
    }

    init {
        try {
            binding = DataBindingUtil.bind(itemView!!)
        } catch (e: Exception) {
            Timber.i(e)
        }
    }
}