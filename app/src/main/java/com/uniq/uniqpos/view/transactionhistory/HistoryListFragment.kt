package com.uniq.uniqpos.view.transactionhistory


import android.content.Context
import android.graphics.Color
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.lifecycle.LiveData
import androidx.lifecycle.Observer
import com.uniq.uniqpos.R
import com.uniq.uniqpos.data.local.entity.OpenShiftEntity
import com.uniq.uniqpos.data.local.entity.SalesEntity
import com.uniq.uniqpos.data.local.entity.ShiftEntity
import com.uniq.uniqpos.data.local.sharedpref.SharedPref
import com.uniq.uniqpos.data.local.sharedpref.getJson
import com.uniq.uniqpos.data.remote.model.Outlet
import com.uniq.uniqpos.data.remote.model.ShiftOpen
import com.uniq.uniqpos.databinding.FragmentHistoryListBinding
import com.uniq.uniqpos.databinding.ListItemTransactionHistoryBinding
import com.uniq.uniqpos.model.Order
import com.uniq.uniqpos.util.*
import com.uniq.uniqpos.util.view.RecyclerItemClickListener
import com.uniq.uniqpos.view.global.GlobalAdapter
import com.uniq.uniqpos.view.global.GlobalViewHolder
import com.uniq.uniqpos.view.main.MainActivity
import timber.log.Timber
import java.util.*

/**
 * A simple [Fragment] subclass.
 *
 */
class HistoryListFragment : Fragment() {

    private var _binding: FragmentHistoryListBinding? = null
    private val binding: FragmentHistoryListBinding get() = _binding!!
    private val openShiftList = ArrayList<OpenShiftEntity>()
    private lateinit var viewModel: TransactionHistoryViewModel
    private lateinit var outlet: Outlet
    var selectedItem = -1
    private var mContext: Context? = null
    private var isFirstLoad = true
    private var isTabLayout = false
    private var recViewHistoryListener: RecyclerItemClickListener? = null
    private var openShift: ShiftOpen? = null
    private var liveDataHistoryList: LiveData<List<SalesEntity>>? = null
    private var originalSalesList = mutableListOf<SalesEntity>()
    private var searchQuery = ""

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentHistoryListBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        (activity as MainActivity).getTransactionHistoryFragment()?.let { fragment ->
            outlet = context?.getJson(SharedPref.OUTLET_DATA, Outlet::class.java) ?: Outlet()
            try {
                viewModel = fragment.viewModel
            } catch (e: Exception) {

            }
            observeData()
            initLayout()
            setupSearchListener()
        }

        if ((activity as MainActivity).isTabLayout) {
            isTabLayout = true
            selectedItem = 0
        }

        openShift = context?.getJson(SharedPref.SHIFT_DATA, ShiftOpen::class.java)

        initFeatureAndRole()
    }

    fun initFeatureAndRole() {
        val role = role()
        context?.outletFeature()?.let { feature ->
            val isShowHistory = feature.viewtransactionhistory && role.viewtransactionhistory
            binding.recviewHistoryList.visibility = isShowHistory.visibility()
            binding.searchContainer.visibility = isShowHistory.visibility()
            binding.layoutNoPermission.visibility = (!isShowHistory).visibility()
            binding.layoutNoData.layoutNoData.visibility = isShowHistory.visibility()
            val cardTotalVisible = (role.viewtotalachievement && feature.viewtotalachievement)
            binding.layoutTotalRevenue.visibility = cardTotalVisible.visibility()
            binding.layoutTotalPax.visibility = (!cardTotalVisible).visibility()

            if (!isShowHistory && !feature.viewtransactionhistory){
                binding.txtFeatureHide.text = getString(R.string.feature_disable)
            }
        }
    }

    private fun initLayout() {
        binding.recviewHistoryList.adapter =
            object : GlobalAdapter<ListItemTransactionHistoryBinding>(
                R.layout.list_item_transaction_history,
                viewModel.salesList
            ) {
                override fun onBindViewHolder(
                    holder: GlobalViewHolder<ListItemTransactionHistoryBinding>,
                    position: Int
                ) {
                    super.onBindViewHolder(holder, position)
//                    if (position == selectedItem) {
//                        holder.binding.container.setBackgroundColor(Color.parseColor("#353F4C"))
//                        if (isFirstLoad && isTabLayout) {
//                            isFirstLoad = false
//                            (activity as MainActivity).getTransactionHistoryFragment()
//                                ?.onItemSelected(viewModel.salesList[holder.adapterPosition])
//                        }
//                    } else {
//                        holder.binding.container.setBackgroundColor(0)
//                    }

                    if (binding.layoutNoPermission.visibility != View.VISIBLE) {
//                        binding.layoutNoData.layoutNoData.setVisible(false)
                    }
                }
            }

        recViewHistoryListener = RecyclerItemClickListener(context) { _, position ->
            if (position < viewModel.salesList.size && position >= 0) {
                (activity as MainActivity).getTransactionHistoryFragment()
                    ?.onItemSelected(viewModel.salesList[position])
                val tmpSelected = selectedItem
                selectedItem = position
                binding.recviewHistoryList.adapter?.notifyItemChanged(tmpSelected)
                binding.recviewHistoryList.adapter?.notifyItemChanged(selectedItem)
            } else {
                context?.toast("Item out of range!")
            }
        }
        binding.recviewHistoryList.addOnItemTouchListener(recViewHistoryListener!!)
    }

    private fun setupSearchListener() {
        binding.editSearch.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
            override fun afterTextChanged(s: Editable?) {
                searchQuery = s?.toString()?.lowercase() ?: ""
                filterTransactions()
            }
        })
    }

    private fun filterTransactions() {
        if (searchQuery.isEmpty()) {
            viewModel.salesList.clear()
            viewModel.salesList.addAll(originalSalesList)
        } else {
            viewModel.salesList.clear()
            viewModel.salesList.addAll(originalSalesList.filter { sale -> runSearch(sale)})
        }
        updateTransactionStats()
        binding.recviewHistoryList.adapter?.notifyDataSetChanged()
    }

    private fun runSearch(sale: SalesEntity): Boolean{
        val q = searchQuery.lowercase()
        val searchFields = listOf(sale.noNota, sale.customer, sale.payment, sale.orderList?.map { it.product?.name }?.joinToString())
        return searchFields.any { it.safe().lowercase().contains(q) }
    }

    private fun updateTransactionStats() {
        val itemCount = viewModel.salesList.groupBy { it.noNota }.size
        val totalRevenue = viewModel.salesList.sumOf { it.grandTotal }
        val totalPax = viewModel.salesList.sumOf { it.customersQty ?: 0 }
        
        // Calculate refund count
        val refundCount = viewModel.salesList.count { it.status?.equals("Refund", ignoreCase = true) == true }

        binding.txtTransCount.text = itemCount.toCurrency()
        binding.txtTotalRevenue.text = totalRevenue.toCurrency()
        binding.txtTotalPax.text = totalPax.toCurrency()
        
        // Show/hide and update refund count
        if (refundCount > 0) {
            binding.txtRefundCount.apply {
                visibility = View.VISIBLE
                text = context.getString(
                    if (refundCount == 1) R.string.refund_count_single
                    else R.string.refund_count_plural,
                    refundCount
                )
            }
            binding.txtRefundSeparator.visibility = View.VISIBLE
        } else {
            binding.txtRefundCount.visibility = View.GONE
            binding.txtRefundSeparator.visibility = View.GONE
        }
    }

    private fun observeData() {
        viewModel.getShifts().observe(viewLifecycleOwner) {
            viewModel.shiftList.clear()
            it?.data?.let { items -> viewModel.shiftList.addAll(items) }
        }

        viewModel.refreshList.observe(viewLifecycleOwner) {
            binding.recviewHistoryList.adapter?.notifyDataSetChanged()
        }

        outlet.outletId?.let { outletId ->
            viewModel.getOpenShift(outletId)
                .observe(viewLifecycleOwner) {
                    openShiftList.clear()
                    Timber.i("total open shift list: ${it.data?.size}")
                    it?.data?.forEach { openShift ->
                        openShift.timeOpenFormat = openShift.timeOpen.dateTimeFormat("dd-MM-yyyy")
                        openShiftList.add(openShift)
                    }

                    if (openShiftList.isEmpty()) {
                        loadTransactionHistory(openShiftId = openShift?.openShiftId)
                    } else {
                        loadTransactionHistory()
                    }
                }
        }
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        outState.putInt("selected", selectedItem)
//        outState.putSerializable("data", salesList)
//        outState.putSerializable("openshift", openShiftList)
    }

    override fun onViewStateRestored(savedInstanceState: Bundle?) {
        super.onViewStateRestored(savedInstanceState)
        savedInstanceState?.takeIf { ::viewModel.isInitialized }?.apply {
            selectedItem = getInt("selected")
//            (getSerializable("data") as? ArrayList<SalesEntity>)?.let { salesList.addAll(it) }
//            (getSerializable("openshift") as? ArrayList<OpenShiftEntity>)?.let { openShiftList.addAll(it) }
            binding.txtTransCount.text =  viewModel.salesList.size.toString()
            loadTransactionHistory()
        }
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        mContext = context
    }

    fun loadTransactionHistory(
        openShiftId: Long? = null,
        isClearDataFirst: Boolean = false,
        paymentMethod: String = "",
        isOnlyDiscVouc: Boolean = false,
        isPromotionOnly: Boolean = false
    ) {
        setLoadingState(true)
        if (isClearDataFirst) {
            viewModel.salesList.clear()
            binding.recviewHistoryList.adapter?.notifyDataSetChanged()
        }

        openShiftList.sortBy { it.openShiftId }
        val selectedId = openShiftId ?: openShiftList.lastOrNull()?.let { lastOpenShift ->
            val id = lastOpenShift.openShiftId
            val currentId = openShift?.openShiftId ?: 0
            if (currentId > id) {
                Timber.i("openshiftId getting from sharedPref (from login) is bigger than from db. SP : $currentId | DB : $id")
                currentId
            } else {
                Timber.i("Getting shift id from database. Date open : ${lastOpenShift.timeOpen.dateTimeFormat()}")
                val shiftName =
                    viewModel.shiftList.firstOrNull { it.shiftId == lastOpenShift.shiftFkid }?.let {
                        "Shift - ${it.name}"
                    } ?: ""

                binding.txtShiftName.text = shiftName
                binding.txtShiftTime.text = lastOpenShift.timeOpen.dateFormat("dd MMM yyyy HH:mm")
//                if (lastOpenShift.timeOpen.dateFormat() != Date().dateFormat() && lastOpenShift.timeOpen.diffHour() >= 9) {
//                    binding.txtDate.text = lastOpenShift.timeOpen.dateFormat("dd MMM") + shiftName
//                } else {
//                    binding.txtDate.text = getString(R.string.today) + shiftName
//                }
                id
            }
        }

        Timber.i(">> Load Transaction History By OpenShiftId : '$id'")
        selectedId?.let { id ->
            if (::viewModel.isInitialized) {
                liveDataHistoryList?.removeObservers(viewLifecycleOwner)
                liveDataHistoryList = viewModel.getTransactionHistoryByOpenShift(id)
                liveDataHistoryList?.observe(viewLifecycleOwner) { items ->
                    viewModel.setData(items, paymentMethod, isOnlyDiscVouc, isPromotionOnly)
                    
                    // Store original list and update stats
                    originalSalesList.clear()
                    originalSalesList.addAll(viewModel.salesList)
                    updateTransactionStats()
                    
                    // Apply any existing search filter
                    if (searchQuery.isNotEmpty()) {
                        filterTransactions()
                    }

                    binding.recviewHistoryList.adapter?.notifyDataSetChanged()
                    setLoadingState(false)
                }
            } else {
                Timber.i("viewModel (HistoryListFragment) has not been initialized")
                setLoadingState(false)
            }
        }
            ?: kotlin.run {
                Timber.i(">> Can not load history transaction. OpenShift Size : ${openShiftList.size}")
                setLoadingState(false)
            }
    }

    fun filter(
        shiftSelected: ShiftEntity,
        date: String,
        paymentMethod: String = "",
        isOnlyDiscVouc: Boolean = false,
        isPromotionOnly: Boolean = false
    ) {
        Timber.i("# Filter Transaction History, OpenShiftId: ${shiftSelected.shiftId}, Date: $date, Payment: $paymentMethod, voucherOnly: $isOnlyDiscVouc, promoOnly: $isPromotionOnly")
        binding.recviewHistoryList.visibility = View.INVISIBLE
        setLoadingState(true)
//        var openShiftId: Long? = openShiftList.firstOrNull { it.timeOpenFormat == date && it.shiftFkid == shiftSelected.shiftId }?.openShiftId
        var openShift =
            openShiftList.firstOrNull { it.timeOpenFormat == date && it.shiftFkid == shiftSelected.shiftId }

        openShift?.let { shift ->
            isFirstLoad = true
            selectedItem = 0
            loadTransactionHistory(
                shift.openShiftId,
                true,
                paymentMethod,
                isOnlyDiscVouc,
                isPromotionOnly
            )

            val shiftName = viewModel.shiftList.firstOrNull { it.shiftId == shift.shiftFkid }?.let {
                " - ${it.name}"
            } ?: ""
            binding.txtShiftName.text = shiftName
            binding.txtShiftTime.text = shift.timeOpen.dateFormat("dd MMM yyyy HH:mm")

//            binding.txtDate.text =
//                (if (date == Date().dateFormat()) getString(R.string.today) else date) + shiftName
        } ?: kotlin.run {
            context?.showMessage("tidak ditemukan transaksi pada shift ${shiftSelected.name} di tanggal $date")
            setLoadingState(false)
        }
        binding.recviewHistoryList.visibility = View.VISIBLE
    }

    private fun setLoadingState(isLoading: Boolean) {
        binding.progressBar2.setVisible(isLoading)
        if (isLoading) {
            binding.layoutNoData.layoutNoData.setVisible(false)
        } else {
            binding.layoutNoData.layoutNoData.setVisible(viewModel.salesList.isEmpty())
        }
    }

    override fun onDestroyView() {
        _binding = null
        super.onDestroyView()
    }

    override fun onDestroy() {
        isFirstLoad = true
        recViewHistoryListener?.onDestroy()
        super.onDestroy()
    }
}
