package com.uniq.uniqpos.view.piutang

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.google.gson.Gson
import com.uniq.uniqpos.data.local.entity.PiutangEntity
import com.uniq.uniqpos.data.local.entity.PiutangHistoryEntity
import com.uniq.uniqpos.data.local.entity.SalesEntity
import com.uniq.uniqpos.data.remote.repository.SalesRepository
import com.uniq.uniqpos.util.lifecycle.SingleLiveAction
import com.uniq.uniqpos.util.lifecycle.SingleLiveEvent
import com.uniq.uniqpos.util.safe
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import timber.log.Timber
import javax.inject.Inject

/**
 * Created by annasblackhat on 01/08/18
 */

class PiutangViewModel @Inject constructor(val salesRepository: SalesRepository) : ViewModel() {

    val piutangList = ArrayList<PiutangEntity>()
    val piutangListShow = ArrayList<PiutangEntity>() //which used to show to ui
    val piutangHistoryList = ArrayList<PiutangHistoryEntity>()
    val salesList = ArrayList<SalesEntity>()
    var isFirstLoad = true

    private val _selectedPiutang = MutableLiveData<PiutangEntity>()
    val selectedPiutang: LiveData<PiutangEntity> = _selectedPiutang

    val refreshPiutang = SingleLiveAction()

        fun getPiutangLive(outletId: Int?) = salesRepository.getPiutangLive(outletId)
//    fun getPiutangLive(outletId: Int): LiveData<List<PiutangEntity>> {
//        Timber.d("start get piutang")
//        viewModelScope.launch {
//            try {
//                salesRepository.loadPiutang(outletId.safe())
//                Timber.d("end get piutang from api")
//            }catch (e: Exception){
//                Timber.i("[ERROR] get piutang err : $e")
//            }
//        }
//        Timber.d("end get piutang from db")
//        return salesRepository.getPiutangLiveDb()
//    }

    fun getPiutangBySalesIdLive(id: String) = salesRepository.getPiutangBySalesIdLive(id)

    fun getPiutangHistoryByIdLive(piutangId: Long) =
        salesRepository.getPiutangHistoryById(piutangId)

    fun getSalesByIdLive(id: String) = salesRepository.getSalesByIdLive(id)

    fun findSales() {
        val salesIds =
            piutangList.map { it.salesFkid }.filter { id -> !salesList.any { it.noNota == id } }
        Timber.i("saved sales total : ${salesList.size} | new sales ids total : ${salesIds.size}")
        viewModelScope.launch {
            try {
                val unKnwonSalesIds = ArrayList<String>()
                val salesByIds = salesRepository.getSalesByIds(salesIds)
//                salesList.clear()
                salesList.addAll(salesByIds)
                piutangList.forEach { piutang ->
                    salesList.firstOrNull { it.noNota == piutang.salesFkid }
                        ?.let { sales ->
                            var isRefund = salesByIds.any {
                                it.noNota == sales.noNota && it.status.safe()
                                    .lowercase() == "refund"
                            }

                            Timber.d("${sales.noNota} is refund: $isRefund")
                            if (!isRefund) {
                                sales.salesTag = null
                                piutang.sales = sales
                            }
                        }
                        ?: run {
                            Timber.i("not found for sales id ${piutang.salesFkid}")
                            unKnwonSalesIds.add(piutang.salesFkid)
                        }
                }
                Timber.d("piutang list : ${Gson().toJson(piutangList)}")
                Timber.d("piutang list (with sales) : ${Gson().toJson(piutangList.filter { it.sales != null })}")

                Timber.i("all piutang size : ${piutangList.size}, piutang with sales size: ${piutangList.filter { it.sales != null }.size}, salesByIds size: ${salesByIds.size}")
                piutangListShow.clear()
                piutangListShow.addAll(piutangList.filter { it.sales != null })
                piutangListShow.sortWith(compareBy<PiutangEntity> { it.unpaid <= 0 }.thenByDescending { it.piutangId })
                refreshPiutang.call()

                //sync sales
                withContext (Dispatchers.IO) { salesRepository.syncSalesIds(unKnwonSalesIds) }
            } catch (e: Exception) {
                Timber.i("[ERROR] finding sales err : $e")
            }
        }
    }

    fun searchData(q: String?) {
        piutangListShow.clear()
        q?.let { query ->
            piutangListShow.addAll(piutangList.filter {
                it.sales?.customer?.contains(query).safe() || it.sales?.displayNota?.contains(query)
                    .safe()
            })
        } ?: run { piutangListShow.addAll(piutangList) }

        isFirstLoad = true //set this, in order to select first item
        refreshPiutang.call()
    }

    fun loadPiutang(salesId: String){
        viewModelScope.launch(Dispatchers.IO) {
            salesRepository.getPiutangBySalesId(salesId)?.let {
                _selectedPiutang.postValue(it)
            }

        }
    }
}