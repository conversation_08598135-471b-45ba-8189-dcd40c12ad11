package com.uniq.uniqpos.view.login

import android.Manifest
import android.app.ActivityManager
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Build
import android.os.Bundle
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import com.bugsnag.android.Bugsnag
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.firebase.messaging.FirebaseMessaging
import com.uniq.uniqpos.BuildConfig
import com.uniq.uniqpos.R
import com.uniq.uniqpos.data.local.sharedpref.SharedPref
import com.uniq.uniqpos.data.local.sharedpref.putJson
import com.uniq.uniqpos.databinding.ActivityLoginBinding
import com.uniq.uniqpos.databinding.DialogMultiAccountBinding
import com.uniq.uniqpos.databinding.ListItemMultiAccountBinding
import com.uniq.uniqpos.model.AuthToken
import com.uniq.uniqpos.model.BottomDialogModel
import com.uniq.uniqpos.model.MultiAccount
import com.uniq.uniqpos.util.Utils
import com.uniq.uniqpos.util.decrypt
import com.uniq.uniqpos.util.deviceInfo
import com.uniq.uniqpos.util.getDeviceName
import com.uniq.uniqpos.util.isGooglePlayServicesAvailable
import com.uniq.uniqpos.util.launchUrl
import com.uniq.uniqpos.util.lifecycle.Failed
import com.uniq.uniqpos.util.lifecycle.Loading
import com.uniq.uniqpos.util.lifecycle.Success
import com.uniq.uniqpos.util.lifecycle.setupToast
import com.uniq.uniqpos.util.outlet
import com.uniq.uniqpos.util.readableError
import com.uniq.uniqpos.util.safe
import com.uniq.uniqpos.util.showMessage
import com.uniq.uniqpos.util.toast
import com.uniq.uniqpos.view.chooseoperator.ChooseOperatorActivity
import com.uniq.uniqpos.view.chooseoutlet.ChooseOutletActivity
import com.uniq.uniqpos.view.global.BaseActivity
import com.uniq.uniqpos.view.global.BottomDialogInput
import com.uniq.uniqpos.view.global.GlobalAdapter
import com.uniq.uniqpos.view.global.GlobalViewHolder
import com.uniq.uniqpos.view.login.code.LoginWithCodeActivity
import timber.log.Timber


class LoginAdminActivity : BaseActivity<LoginViewModel, ActivityLoginBinding>() {

    override fun getLayoutRes() = R.layout.activity_login
    override fun getViewModel() = LoginViewModel::class.java

    private val RC_PERMISSION = 30

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding.viewModel = viewModel
//        if (resources.getBoolean(R.bool.landscape_only)) {
//            requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE
//        }

//        binding.password.setOnEditorActionListener { _, actionId, _ ->
//            if (actionId == EditorInfo.IME_ACTION_DONE) viewModel.processLogin()
//            false
//        }

        binding.signup.setOnClickListener { signUp() }
        binding.txtForgotPassword.setOnClickListener { forgotPassword() }
        binding.btnLoginCode.setOnClickListener { loginWithCode() }

        logDeviceInfo()
        if (isGooglePlayServicesAvailable()) {
            FirebaseMessaging.getInstance().token.addOnCompleteListener { task ->
                if (task.isSuccessful) {
                    val token = task.result
                    viewModel.firebaseToken = token
                }
            }.addOnFailureListener { err ->
                if (err is java.io.IOException){
                    toast(err.message)
                }else{
                    toast(err.readableError(this))
                }
            }
        } else {
            toast("Google Play Service is unavailable. Some function might not works properly")
        }
    }

    private fun forgotPassword() {
        launchUrl(BuildConfig.WEB_URL + "forgot")
    }

    private fun signUp() {
//        startActivity(Intent(this, RegisterActivity::class.java))
        launchUrl("https://y.uniq.id/register-android")
    }

    private  fun loginWithCode(){
        val bottomDialog = BottomDialogInput()
        bottomDialog.setOnButtonClick {
            resetAuthToken()
            viewModel.sendAuthCode(it, deviceInfo())
        }
        bottomDialog.setModel(
            BottomDialogModel(
                "Login With Code",
                "SEND CODE",
                "type your registered email address account, we will send you a code",
            )
        )
        bottomDialog.show(supportFragmentManager, "input-email")
    }
    override fun observeTask() {
        setupToast(this, viewModel.toastMsg)

        viewModel.checkPermission.observe(this) {
            Utils.hideKeyboard(this)
            it.getContentIfNotHandled()?.let {
                if (isAndroidVersionInRange() && ContextCompat.checkSelfPermission(
                        this,
                        Manifest.permission.WRITE_EXTERNAL_STORAGE
                    ) != PackageManager.PERMISSION_GRANTED
                ) {
                    requestPermissions()
                } else {
                    resetAuthToken()
                    viewModel.loginAdmin()
                }
            }
        }

        viewModel.requestClearData.observe(this) {
            showMessage(
                "Maaf, anda harus melakukan Clear Data terlebih dahulu agar bisa melanjutkan",
                "WARNING",
                { _, _ -> clearData() },
                positiveMsg = "CLEAR DATA NOW"
            )
        }

        viewModel.state.observe(this) { state ->
            when (state) {
                is Loading -> showDialog(state.isLoading)
                is Failed -> showMessage(state.exception.readableError(this), "Failed")
                is Success -> nextPage()
            }
        }

        viewModel.userNameErr.observe(this) { err ->
            if (err.isNotBlank()) {
                binding.username.error = err
                binding.username.requestFocus()
            }
        }

        viewModel.multiAccount.observe(this) { event ->
            event.getContentIfNotHandled()?.let { multiAccount ->
                showMultiAccountDialog(multiAccount)
            }
        }

        viewModel.authCodeSent.observe(this) { evt ->
            evt.getContentIfNotHandled()?.let { authCode ->
                var intent = Intent(this, LoginWithCodeActivity::class.java)
                intent.putExtra("data", authCode)
                startActivity(intent)
            }
        }
    }

    private  fun resetAuthToken(){
        Timber.i("reset auth token...")
        val auth = AuthToken(token = getString(R.string.authorization).decrypt())
        putJson(SharedPref.TOKEN_DATA, auth)
    }

    private fun nextPage() {
        var destination: Class<*> = ChooseOutletActivity::class.java
        //if somehow outlet is exist, which mean user already logged in before,
        //then continue with that outlet
        outlet()?.takeIf { it.outletId.safe() > 0 }?.let { outlet ->
            destination = ChooseOperatorActivity::class.java
        }

        startActivity(Intent(this, destination))
        finish()
    }

    fun isAndroidVersionInRange(): Boolean {
        return Build.VERSION.SDK_INT > Build.VERSION_CODES.M && Build.VERSION.SDK_INT < Build.VERSION_CODES.S
    }

    private fun requestPermissions() {
        Timber.i("requestPermissions...")
        ActivityCompat.requestPermissions(
            this,
            arrayOf(Manifest.permission.WRITE_EXTERNAL_STORAGE),
            RC_PERMISSION
        )
    }

    private fun clearData() {
        try {
            Timber.i("begin clear data...")
            if (Build.VERSION_CODES.KITKAT <= Build.VERSION.SDK_INT) {
                val appManager = getSystemService(ACTIVITY_SERVICE) as ActivityManager
                appManager.clearApplicationUserData()
            } else {
                val packName = applicationContext.packageName
                val runtime = Runtime.getRuntime()
                runtime.exec("pm clear $packName")
            }
            showMessage("Clear Data selesai, \n" +
                    "Jika proses Clear Data ternyata gagal, silahkan uninstall terlebih dahulu kemudian install kembali",
                "FINISH",
                onDismissListener = {
                    resetAuthToken()
                    viewModel.loginAdmin()
                })
        } catch (e: Exception) {
            Bugsnag.notify(e)
            Timber.i("clear data err: $e")
            showMessage(
                "Sistem gagal melakukan Clear Data, \n\n" +
                        "Silahkan melakukan Clear Data secara manual, \natau coba uninstall dan install kembali",
                "GAGAL"
            )
        }
    }

    private fun showMultiAccountDialog(multiAccount: MultiAccount){
        val dialog = DialogMultiAccountBinding.inflate(layoutInflater)
        val bottomSheetDialog = BottomSheetDialog(this)
        bottomSheetDialog.setContentView(dialog.root)

        dialog.recViewAccount.adapter = object : GlobalAdapter<ListItemMultiAccountBinding>(
        R.layout.list_item_multi_account,
            multiAccount.accounts,
        ){
            override fun onBindViewHolder(
                holder: GlobalViewHolder<ListItemMultiAccountBinding>,
                position: Int
            ) {
                super.onBindViewHolder(holder, position)
                holder.itemView.setOnClickListener {
                    viewModel.chooseMultiAccount(multiAccount.accounts.get(holder.adapterPosition))
                }
            }
        }

        //set to be expanded
//        bottomSheetDialog.setOnShowListener {
//            val dialog = dialog as BottomSheetDialog
//            val bottomSheet =
//                dialog.findViewById(com.google.android.material.R.id.design_bottom_sheet) as FrameLayout?
//            val behavior = BottomSheetBehavior.from(bottomSheet!!)
//            behavior.state = BottomSheetBehavior.STATE_EXPANDED
//        }
        bottomSheetDialog.show()
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        if (requestCode == RC_PERMISSION) {
            if (grantResults.any { it != PackageManager.PERMISSION_GRANTED }) {
                requestPermissions()
            } else {
                resetAuthToken()
                viewModel.loginAdmin()
            }
        }
    }

    private fun logDeviceInfo(){
        val config = resources.configuration
        val displayMetrics = resources.displayMetrics
        val dpHeight = displayMetrics.heightPixels / displayMetrics.density
        val dpWidth = displayMetrics.widthPixels / displayMetrics.density

        val infoBuffer = StringBuilder()
        infoBuffer.append("-------------------------------------\n");
        infoBuffer.append("Name        : " + getDeviceName() + "\n")
        infoBuffer.append("Model       : " + Build.MODEL + "\n") //The end-user-visible name for the end product.
        infoBuffer.append("Device      : " + Build.DEVICE + "\n") //The name of the industrial design.
        infoBuffer.append("Manufacturer: " + Build.MANUFACTURER + "\n") //The manufacturer of the product/hardware.
        infoBuffer.append("Board       : " + Build.BOARD + "\n") //The name of the underlying board, like "goldfish".
        infoBuffer.append("Brand       : " + Build.BRAND + "\n") //The consumer-visible brand with which the product/hardware will be associated, if any.
        infoBuffer.append("Serial      : " + Build.SERIAL + "\n")
        infoBuffer.append("-------------------------------------\n")
        infoBuffer.append("Version     : ${BuildConfig.VERSION_NAME}\n")
        infoBuffer.append("Version Code: ${BuildConfig.VERSION_CODE}\n")
        infoBuffer.append("Package     : ${BuildConfig.APPLICATION_ID}\n")
        infoBuffer.append("OS          : ${Build.VERSION.SDK_INT}\n")
        infoBuffer.append("Screen Width: ${config.smallestScreenWidthDp}\n")
        infoBuffer.append("Architecture: ${System.getProperty("os.arch")}\n")
        infoBuffer.append("Heigh       : $dpHeight\n")
        infoBuffer.append("Width       : $dpWidth\n\n")
        Timber.i("### DEVICE INFO \n $infoBuffer")
    }

}
