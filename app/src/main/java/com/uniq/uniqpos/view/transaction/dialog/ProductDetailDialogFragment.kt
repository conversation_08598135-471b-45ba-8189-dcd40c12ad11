package com.uniq.uniqpos.view.transaction.dialog

import android.app.AlertDialog
import android.content.DialogInterface
import android.graphics.Color
import android.os.Bundle
import android.text.InputType
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewTreeObserver
import android.widget.FrameLayout
import androidx.core.content.ContextCompat
import androidx.fragment.app.FragmentManager
import androidx.lifecycle.lifecycleScope
import com.bugsnag.android.Bugsnag
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.uniq.uniqpos.R
import com.uniq.uniqpos.data.local.entity.ProductEntity
import com.uniq.uniqpos.databinding.LayoutProductDetailBinding
import com.uniq.uniqpos.databinding.ListItemNamesBinding
import com.uniq.uniqpos.util.*
import com.uniq.uniqpos.view.global.GlobalAdapter
import com.uniq.uniqpos.view.global.GlobalViewHolder
import com.uniq.uniqpos.view.transaction.TransactionViewModel
import timber.log.Timber
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.content.FileProvider
import java.io.File
import android.app.Activity
import android.content.Intent
import com.bumptech.glide.Glide
import com.uniq.uniqpos.util.intent.CameraOrGalleryIntent
import com.uniq.uniqpos.util.intent.openCamera
import com.uniq.uniqpos.util.intent.openGallery
import com.uniq.uniqpos.util.extensions.toBitmap
import com.uniq.uniqpos.util.extensions.toFile
import com.uniq.uniqpos.util.intent.createImageFile
import com.uniq.uniqpos.util.intent.getImageFileUri
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.io.FileOutputStream
import java.net.URL

class ProductDetailDialogFragment : BottomSheetDialogFragment() {

    private var binding: LayoutProductDetailBinding? = null
    private var product: ProductEntity? = null
    private var viewModel: TransactionViewModel? = null
    private var layoutListener: ViewTreeObserver.OnGlobalLayoutListener? = null
    private var subCategoryName: String? = ""
    private var selectedVariant: ProductEntity? = null
    private lateinit var permissionHandler: PermissionHandler
    private var photoFile: File? = null

    private val takePictureLauncher = registerForActivityResult(
        ActivityResultContracts.TakePicture()
    ) { success ->
        Timber.i("takePicture... is success $success")
        if (success) {
            photoFile?.let { file ->
                uploadImage(file)
            }
        }
    }

    private val pickImageLauncher = registerForActivityResult(
        ActivityResultContracts.GetContent()
    ) { uri ->
        Timber.i("pick image.. ${uri == null}")
        uri?.let { imageUri ->
            val compressedFile = ImageUtils.compressImage(requireContext(), imageUri)
            uploadImage(compressedFile)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        permissionHandler = PermissionHandler(this)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = LayoutProductDetailBinding.inflate(inflater, container, false)
        product?.apply {
            subCategoryName =
                viewModel?.categoriesTmp?.firstOrNull { it.productCategoryId == this.productSubcategoryFkid }?.name
            this.productSubCategoryName = subCategoryName
            binding!!.model = this
        }
        return binding!!.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        Bugsnag.leaveBreadcrumb(this::class.simpleName.safe())

        binding!!.imgClose.setOnClickListener { dismiss() }
        binding!!.txtChangePrice.setOnClickListener { changePrice() }
        binding!!.swAvailability.setOnCheckedChangeListener { _, isChecked ->
            if (isChecked) {
                binding!!.stockAvailability.text = Constant.STOCK_AVAILABLE
                binding!!.stockAvailability.setTextColor(Color.parseColor("#C8CFD2"))
            } else {
                binding!!.stockAvailability.text = Constant.STOCK_UNAVAILABLE
                binding!!.stockAvailability.setTextColor(Color.parseColor("#D04959"))
            }
        }

        layoutListener = object : ViewTreeObserver.OnGlobalLayoutListener {
            override fun onGlobalLayout() {
                view.viewTreeObserver.removeOnGlobalLayoutListener(this)
                val dialog = dialog as BottomSheetDialog
                val bottomSheet =
                    dialog.findViewById(com.google.android.material.R.id.design_bottom_sheet) as FrameLayout?
                val behavior = BottomSheetBehavior.from(bottomSheet!!)
                behavior.state = BottomSheetBehavior.STATE_EXPANDED
            }
        }

        val variants = viewModel?.variants?.filter {
            it.productFkid == product?.productId?.toInt()
                .safe() && viewModel?.products?.any { p -> p.variantFkid == it.variantId }.safe()
        }
        val taxes =
            viewModel?.taxes?.filter { it.productDetailFkid == product?.productDetailId && it.dataStatus == "on" }
        val gratuities =
            viewModel?.gratuities?.filter { g -> taxes?.any { g.gratuityId == it.taxFkid }.safe() }

        var selectedVariantPosition = 0
        binding!!.recviewVariant.adapter = object : GlobalAdapter<ListItemNamesBinding>(
            R.layout.list_item_names,
            variants?.map { it.variantName }) {
            override fun onBindViewHolder(
                holder: GlobalViewHolder<ListItemNamesBinding>,
                position: Int
            ) {
                super.onBindViewHolder(holder, position)
                holder.binding!!.btnName.setOnClickListener {
                    selectedVariantPosition = holder.adapterPosition
                    notifyDataSetChanged()
                    changeVariant(variants?.getSafe(position)?.variantId)
                }

                if (selectedVariantPosition == position) {
                    holder.binding!!.btnName.setBackgroundResource(R.drawable.btn_round_blue)
                } else {
                    holder.binding!!.btnName.setBackgroundResource(R.drawable.btn_round)
                }
                holder.binding!!.btnName.setTextColor(Color.WHITE)
            }
        }
        binding!!.recviewTaxes.adapter = object : GlobalAdapter<ListItemNamesBinding>(
            R.layout.list_item_names,
            gratuities?.map { it.name }) {}

        binding!!.btnAddCart.setOnClickListener {
            (selectedVariant ?: product)?.let { prod ->
                //update product name according to selected variant
                //and set variantFkid to null, this will prevent app to show dialog to choose variant
                val variantName =
                    variants?.getSafe(selectedVariantPosition)?.variantName?.let { " ($it)" }.safe()
                viewModel?.taskAddBillNoVariant?.postValue(
                    Pair(
                        prod.copy(
                            name = "${prod.name}$variantName",
                            variantFkid = null
                        ), 1
                    )
                )
                dismiss()
            }
        }

        if (variants?.isEmpty().safe()) {
            binding!!.recviewVariant.visibility = View.GONE
            binding!!.textView7.visibility = View.GONE
        }

        binding!!.txtWarnTax.setVisible(gratuities?.isEmpty().safe())
        binding!!.btnDisc.setVisible(product?.discount == "on")
        binding!!.btnVoucher.setVisible(product?.voucher == "on")

        binding!!.txtWarnDisc.text = if (product?.discount == "on" && product?.voucher == "on") {
            getString(R.string.disc_voucher_enable)
        } else if (product?.discount == "on") {
            getString(R.string.disc_only_enable)
        } else if (product?.voucher == "on") {
            getString(R.string.voucher_only_enable)
        } else {
            binding!!.txtWarnDisc.setTextColor(
                ContextCompat.getColor(
                    requireContext(),
                    R.color.red_background
                )
            )
            getString(R.string.disc_voucher_disable)
        }

        binding!!.imgMore1.setOnClickListener { setVisibilityAdvanceView() }
        binding!!.imgMore2.setOnClickListener { setVisibilityAdvanceView() }

        binding!!.swAvailability.setOnCheckedChangeListener { _, enable ->
            val whichProduct = selectedVariant ?: product
            viewModel?.updateProduct(whichProduct?.copy(stock = if (enable) Constant.STOCK_AVAILABLE else Constant.STOCK_UNAVAILABLE))
            Timber.i("update availability product : ${product?.name} (${selectedVariant?.name}) to $enable")
        }

        binding!!.imageView1.setOnClickListener {
//            product?.photo?.let { photoUrl ->
//                showImagePreview(photoUrl)
//            }
            showImagePreview(product?.photo.safe("https://developers.elementor.com/docs/assets/img/elementor-placeholder-image.png"))
        }

        view.viewTreeObserver.addOnGlobalLayoutListener(layoutListener)

        viewModel?.taskPhotoUpdated?.observe(viewLifecycleOwner, { result ->
            dismiss()
        })
    }

    private fun changeVariant(variantId: Int?) {
        viewModel?.products?.firstOrNull { it.variantFkid == variantId }?.let { product ->
            product.productSubCategoryName = subCategoryName
            binding!!.model = product
            selectedVariant = product
        } ?: kotlin.run { context?.toast("no product found!") }
    }

    private fun setVisibilityAdvanceView() {
        val isVisible = binding!!.groupAdvance.visibility == View.VISIBLE
        binding!!.groupAdvance.setVisible(!isVisible)
        binding!!.imgMore1.rotation = if (isVisible) 90f else -90f
        binding!!.imgMore2.rotation = if (isVisible) 90f else -90f
    }

    private fun changePrice() {
        if (!role().productCreate) {
            context?.showMessage(getString(R.string.no_permission))
            return
        }

        context?.apply {
            val textLayout = com.google.android.material.textfield.TextInputLayout(this)
            val edt = com.google.android.material.textfield.TextInputEditText(this)
            edt.hint = "${product?.name}"
            edt.inputType = InputType.TYPE_CLASS_NUMBER
            Utils.registerToCurrency(edt)
            textLayout.addView(edt)
            val pad = resources.getDimensionPixelOffset(R.dimen.default_padding)
            textLayout.setPadding(pad, pad, pad, pad)

            AlertDialog.Builder(this)
                .setTitle(getString(R.string.change_price).uppercase())
                .setView(textLayout)
                .setNegativeButton(R.string.cancel, null)
                .setPositiveButton(R.string.save) { _, _ ->
                    if (edt.value().isNotEmpty()) {
                        binding!!.txtPrice.text = "Rp${edt.value()}"
                        val whichProduct = selectedVariant ?: product
                        whichProduct?.priceSell = edt.value().fromCurrency()
                        viewModel?.updateProduct(whichProduct)
                        Timber.i("update price product : ${product?.name} (${selectedVariant?.name}) to ${binding!!.txtPrice.text}")
                    }
                }
                .show()
        }
    }

    private fun showImagePreview(imageUrl: String) {
        val productName = (selectedVariant ?: product)?.name ?: ""
        val dialog = ImagePreviewDialogFragment.newInstance(imageUrl, productName)
        dialog.setOnCameraClickListener {
            permissionHandler.requestCameraPermission {
                createImageFile(requireActivity())?.let { file ->
                    photoFile = file
                    val fileUri = FileProvider.getUriForFile(
                        requireActivity(),
                        requireActivity().getString(R.string.file_authority),
                        file
                    )
                    takePictureLauncher.launch(fileUri)
                }
            }
        }
        dialog.setOnGalleryClickListener {
            permissionHandler.requestGalleryPermission {
                pickImageLauncher.launch("image/*")
            }
        }
        dialog.setOnAiGenerateClickListener { description ->
            viewModel?.generateProductImage(productName, description)
        }
        dialog.show(childFragmentManager, "ImagePreview")

        // Observe generated image URL
        viewModel?.generatedImageUrl?.observe(viewLifecycleOwner) { event ->
            event.getContentIfNotHandled()?.let { generatedUrl ->
                context?.toast("Downloading generated image...")
                try {
                    lifecycleScope.launch(Dispatchers.IO) {
                        val futuredTarget =
                            Glide.with(requireContext()).asFile().load(generatedUrl).submit()
                        uploadImage(futuredTarget.get())

                        // Download the image and convert to file
//                        val file = File.createTempFile("ai_generated", ".jpg", requireContext().cacheDir)
//                        URL(generatedUrl).openStream().use { input ->
//                            FileOutputStream(file).use { output ->
//                                input.copyTo(output)
//                            }
//                        }
//                        uploadImage(file)
                    }
                } catch (e: Exception) {
                    Timber.e(e)
                    context?.toast("Failed to process generated image: ${e.message}")
                }
            }
        }
    }

    @Deprecated("")
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        Timber.i("onActivityResult, resultCode ok? ${resultCode == Activity.RESULT_OK}, requestCode: $requestCode")
        if (resultCode != Activity.RESULT_OK) {
            return
        }

        when (requestCode) {
            CameraOrGalleryIntent.CAMERA -> {
                try {
                    CameraOrGalleryIntent.currentPhotoPath?.let { path ->
                        File(path).takeIf { it.exists() }?.let { imgFile ->
                            lifecycleScope.launch {
                                imgFile.toBitmap(requireContext(), 300.0)
                                uploadImage(imgFile)
                            }
                        } ?: run {
                            context?.toast("Image not found")
                        }
                    }
                } catch (e: Exception) {
                    context?.toast("Failed to process camera image")
                    Timber.e(e)
                }
            }

            CameraOrGalleryIntent.GALLERY -> {
                try {
                    val imgGallery = data?.data
                    lifecycleScope.launch {
                        imgGallery?.toFile(requireContext(), 500.0)?.let { imgFile ->
                            uploadImage(imgFile)
                        } ?: run {
                            context?.toast("Image not found")
                        }
                    }
                } catch (e: Exception) {
                    context?.toast("Failed to process gallery image")
                    Timber.e(e)
                }
            }
        }
    }

    private fun uploadImage(file: File) {
        val whichProduct = selectedVariant ?: product
        Timber.i("upload image to server : ${file.path}")
        whichProduct?.let { prod ->
            viewModel?.updateProductPhoto(prod.productId.toInt(), file)
        }
    }

    fun setViewModel(viewModel: TransactionViewModel) {
        this.viewModel = viewModel
    }

    fun setProduct(product: ProductEntity) {
        this.product = product
    }

    private fun updateProduct() {
//        if (binding!!.swAvailability.isChecked != (product?.stock == Constant.STOCK_AVAILABLE)) {
//            context?.toast("change status product...")
//        }
    }

    override fun dismiss() {
        updateProduct()
        super.dismiss()
    }

    override fun onCancel(dialog: DialogInterface) {
        updateProduct()
        super.onCancel(dialog)
    }

    override fun show(manager: FragmentManager, tag: String?) {
        if (!isAdded)
            super.show(manager, tag)
    }

    override fun onDestroyView() {
        super.onDestroyView()
//        clearFindViewByIdCache()
        binding = null
    }

    override fun onDestroy() {
        layoutListener?.let { view?.viewTreeObserver?.removeOnGlobalLayoutListener(it) }
        super.onDestroy()
    }
}