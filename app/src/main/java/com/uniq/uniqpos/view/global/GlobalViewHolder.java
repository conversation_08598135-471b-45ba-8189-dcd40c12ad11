package com.uniq.uniqpos.view.global;

import androidx.databinding.DataBindingUtil;
import androidx.databinding.ViewDataBinding;
import androidx.recyclerview.widget.RecyclerView;

import android.view.View;

import com.uniq.uniqpos.databinding.ListItemTransactionHistoryBinding;

import timber.log.Timber;

/**
 * Created by ANNASBlackHat on 13/09/2017.
 */

public class GlobalViewHolder<T extends ViewDataBinding> extends RecyclerView.ViewHolder {

    //    private ViewDataBinding binding;
    public T binding;

    public GlobalViewHolder(View itemView) {
        super(itemView);
        try {
            binding = DataBindingUtil.bind(itemView);
        } catch (Exception e) {
            Timber.i(e);
        }
    }

    public void onBind(int variable, Object value) {
        if (value == null) return;
        binding.setVariable(variable, value);
        binding.executePendingBindings();
    }

}
