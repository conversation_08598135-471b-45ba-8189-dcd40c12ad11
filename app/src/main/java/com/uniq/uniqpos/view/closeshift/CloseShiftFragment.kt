package com.uniq.uniqpos.view.closeshift


import android.content.Intent
import android.content.pm.PackageManager
import android.os.Build
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.webkit.WebView
import android.widget.LinearLayout
import androidx.appcompat.app.AlertDialog
import androidx.core.content.ContextCompat
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import com.bugsnag.android.Bugsnag
import com.google.android.material.textfield.TextInputEditText
import com.google.android.material.textfield.TextInputLayout
import com.google.firebase.analytics.ktx.analytics
import com.google.firebase.ktx.Firebase
import com.google.gson.Gson
import com.uniq.uniqpos.R
import com.uniq.uniqpos.binding.closeshift.CloseShiftDetailFragment
import com.uniq.uniqpos.data.local.entity.CashRecapEntity
import com.uniq.uniqpos.data.local.entity.EmployeeEntity
import com.uniq.uniqpos.data.local.sharedpref.SharedPref
import com.uniq.uniqpos.data.local.sharedpref.getJson
import com.uniq.uniqpos.data.local.sharedpref.getLocalDataBoolean
import com.uniq.uniqpos.data.local.sharedpref.getLocalDataInt
import com.uniq.uniqpos.data.local.sharedpref.putData
import com.uniq.uniqpos.data.remote.model.Employee
import com.uniq.uniqpos.data.remote.model.Outlet
import com.uniq.uniqpos.data.remote.model.ShiftOpen
import com.uniq.uniqpos.databinding.DialogCloseCashierBinding
import com.uniq.uniqpos.databinding.FragmentCashRecapBinding
import com.uniq.uniqpos.databinding.ListItemCloseShiftBinding
import com.uniq.uniqpos.model.RoleMobile
import com.uniq.uniqpos.model.ShowCaseData
import com.uniq.uniqpos.util.ShowCase
import com.uniq.uniqpos.util.Utils
import com.uniq.uniqpos.util.bundleOf
import com.uniq.uniqpos.util.dateFormat
import com.uniq.uniqpos.util.employee
import com.uniq.uniqpos.util.employeeList
import com.uniq.uniqpos.util.fromCurrency
import com.uniq.uniqpos.util.lifecycle.setupDialogMessage
import com.uniq.uniqpos.util.outlet
import com.uniq.uniqpos.util.outletFeature
import com.uniq.uniqpos.util.role
import com.uniq.uniqpos.util.safe
import com.uniq.uniqpos.util.setAsDateinput
import com.uniq.uniqpos.util.setVisible
import com.uniq.uniqpos.util.shiftOpen
import com.uniq.uniqpos.util.showMessage
import com.uniq.uniqpos.util.toBoolean
import com.uniq.uniqpos.util.toCurrency
import com.uniq.uniqpos.util.toInt
import com.uniq.uniqpos.util.value
import com.uniq.uniqpos.util.view.DialogAuthorization
import com.uniq.uniqpos.util.view.RecyclerItemClickListener
import com.uniq.uniqpos.util.visibility
import com.uniq.uniqpos.view.chooseoperator.ChooseOperatorActivity
import com.uniq.uniqpos.view.global.BaseFragment
import com.uniq.uniqpos.view.global.DynamicDialog
import com.uniq.uniqpos.view.global.GlobalAdapter
import com.uniq.uniqpos.view.global.GlobalViewHolder
import com.uniq.uniqpos.view.main.MainActivity
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withTimeoutOrNull
import smartdevelop.ir.eram.showcaseviewlib.GuideView
import smartdevelop.ir.eram.showcaseviewlib.config.DismissType
import timber.log.Timber
import java.util.Date


/**
 * A simple [Fragment] subclass.
 */
class CloseShiftFragment : BaseFragment<CloseShiftViewModel, FragmentCashRecapBinding>() {

    private var closeShiftCashRecap: CashRecapEntity? = null
    private var lastCashRecap: Long? = 0
    private var selected = -1
    private lateinit var roleMobile: RoleMobile


    //    private lateinit var dialogCloseCashier: AlertDialog
    private lateinit var bindingDialog: DialogCloseCashierBinding
    private var offlineModeEnable = true
    private val employeeList = ArrayList<EmployeeEntity>()
    private var currentShift: ShiftOpen? = null
    private lateinit var dialogCloseShift: DynamicDialog
    private lateinit var closeShiftDetailFragment: CloseShiftDetailFragment

    override val layoutRes = R.layout.fragment_cash_recap
    override fun getViewModel() = CloseShiftViewModel::class.java

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        isSubscribeToSharedPref = true
        return super.onCreateView(inflater, container, savedInstanceState)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        Bugsnag.leaveBreadcrumb(this::class.simpleName.safe())

        binding.fragment = this
        closeShiftDetailFragment = CloseShiftDetailFragment()

        viewModel.loadShift()
        viewModel.checkWarning()
        initDialogCloseCashier()
        roleMobile = role()
        context?.employeeList()?.let { employeeList.addAll(it) }

        initView()
        initActions()
        observeData()
        showCase(ShowCase.CashRecap_CloseShift)
        initViewFeature()

        currentShift = context?.getJson(SharedPref.SHIFT_DATA, ShiftOpen::class.java)
        val previous = context?.getLocalDataInt(
            "${SharedPref.PREV_CLOSE_SHIFT_STATUS}${currentShift?.shiftFkid}",
            -1
        ).safe(-1)
        //if no history, build suggestion
        if (previous < 0) {
            viewModel.buildSuggestionPrintRecap(currentShift)
        }
    }

    private fun initView() {
        binding.recviewCashRecap.adapter = object : GlobalAdapter<ListItemCloseShiftBinding>(
            R.layout.list_item_close_shift,
            viewModel.cashRecapList
        ) {
            override fun onBindViewHolder(
                holder: GlobalViewHolder<ListItemCloseShiftBinding>,
                position: Int
            ) {
                super.onBindViewHolder(holder, position)
                if ((activity as MainActivity).isTabLayout) {
                    if (position == selected) {
                        holder.binding.layoutItem.setBackgroundColor(
                            Utils.getColor(
                                context,
                                R.color.background_selected
                            )
                        )
                    } else {
                        holder.binding.layoutItem.setBackgroundColor(
                            ContextCompat.getColor(
                                context!!,
                                R.color.background
                            )
                        )
                    }
                }
            }
        }

        binding.layNoSelected.txtNoDataMsg.text = getString(R.string.no_close_shift_selected)
//        view?.txt_no_data_msg?.text = getString(R.string.no_close_shift_selected)

        if ((activity as MainActivity).isTabLayout){
            try {
                childFragmentManager.beginTransaction()
                    .replace(R.id.layout_nota, closeShiftDetailFragment)
                    .commit()
            } catch (e: Exception) {
                Bugsnag.notify(e)
                showMsg(e.message, "ERROR")
            }
        }

        //log webview version
        try {
            val packageManager: PackageManager? = context?.packageManager
            val packageInfo =
                packageManager?.getPackageInfo("com.android.webview", PackageManager.GET_META_DATA)
            Timber.i("webview package: ${Gson().toJson(packageInfo)}")
        } catch (e: Exception) {
//            Bugsnag.notify(e)
            Timber.i("error get webview package: $e")
        }

        if(Build.VERSION.SDK_INT >= 26){
            try {
                val webviewPackage = WebView.getCurrentWebViewPackage()
//                Timber.i("webview package (26+): ${Gson().toJson(webviewPackage)}")
            } catch (e: Exception) {
                Bugsnag.notify(e)
                Timber.i("error get webview package (26+): $e")
                //584517233
            }
        }
    }

    private fun initActions() {
        binding.recviewCashRecap.addOnItemTouchListener(RecyclerItemClickListener(context) { _, position ->
            Bugsnag.leaveBreadcrumb("select cash recap $position")
            val cashRecap = viewModel.cashRecapList[position]
            viewModel.getCloseShiftReport(cashRecap)

            val tmpSelected = selected
            selected = position
            binding.recviewCashRecap.adapter?.notifyItemChanged(tmpSelected)
            binding.recviewCashRecap.adapter?.notifyItemChanged(selected)
        })

        binding.btnDailyRecap.setOnClickListener { showDailyRecapDialog() }
        binding.btnAddCloseCashier.setOnClickListener {
            context?.getJson(SharedPref.SHIFT_DATA, ShiftOpen::class.java)
                ?.takeIf { it.openShiftId > 0 }?.let {
                    if (role().tutupkasir) {
//                        if (!dialogCloseCashier.isShowing)
//                            dialogCloseCashier.show()
//                        DynamicDialog(bindingDialog.root, context, childFragmentManager).show()
                        dialogCloseShift.show()
                    } else {
                        requestAuthorization(DialogAuthorization.AuthType.CLOSE_SHIFT)
                    }
                } ?: kotlin.run {
                context?.showMessage(getString(R.string.can_not_close_shift), "NO ACTIVE SHIFT")
            }
        }

        binding.layoutCloseWarning.setOnClickListener {
            binding.layoutWarning.setVisible(false)
        }
    }

    private fun observeData() {
        viewModel.initCloseShiftPrinter()
        viewModel.getOpenShift(outlet.outletId ?: 0)
            .observe(viewLifecycleOwner) {
                viewModel.openShiftList.clear()
                it?.data?.let { viewModel.openShiftList.addAll(it) }
                viewModel.initCloseShiftData()
            }

        val employeeList = context?.employeeList()
        Timber.d("employees: ${employeeList?.map { "${it.employeeId}:${it.name}" }}")
        viewModel.getCashRecap()
            .observe(viewLifecycleOwner) {
                viewModel.cashRecapList.clear()
                it?.data?.let { data ->
                    data.forEach { cashRecap ->
                        cashRecap.employeeName =
                            employeeList?.firstOrNull { el -> el.employeeId == cashRecap.employeeId.safe() }?.name
                        Timber.d("cashRecapEmployeeId: ${cashRecap.employeeId}:${cashRecap.employeeName}")
                    }
                    viewModel.cashRecapList.addAll(data)
//                    Timber.i("cash-recap ids: ${Gson().toJson(data.map { it.id })}")
                }
                viewModel.initCloseShiftData()
            }

        val shift = context?.getJson(SharedPref.SHIFT_DATA, ShiftOpen::class.java) ?: ShiftOpen()
        Timber.i("Open Shift ID ${shift.openShiftId}")

        shift.takeIf { it.openShiftId > 0 }?.openShiftId?.let { id ->
            viewModel.getTotalTransactionByShift(id)
                .observe(viewLifecycleOwner) {
                    binding.txtTotalSales.text = it.toCurrency()
                }
        }

//        viewModel.dialogMessageTask.observe(viewLifecycleOwner) { showMsg(it) }
        context?.setupDialogMessage(viewLifecycleOwner, viewModel.dialogMessageTask)

        viewModel.pDialogTask.observe(viewLifecycleOwner) {
            it?.takeIf { it.isNotEmpty() }?.let { msg -> showDialog(true, msg) }
                ?: run { showDialog(false) }
        }

        viewModel.refreshCashRecap.observe(viewLifecycleOwner) {
            binding.recviewCashRecap.adapter?.notifyDataSetChanged()
            context?.getJson(SharedPref.SHIFT_DATA, ShiftOpen::class.java)?.let { shift ->
                val shiftName =
                    viewModel.shiftList.firstOrNull { it.shiftId == shift.shiftFkid }?.name.safe()
                bindingDialog.textView1.text =
                    getString(R.string.shift_info, shiftName, shift.earlyCash.toCurrency())
            }
        }

        viewModel.taskShowRecapDetail.observe(viewLifecycleOwner) { data ->
            val bundle = Bundle()
            bundle.putString("report", data.reportFormat)
//            bundle.putString("open_shift_ids", data.openShiftId.joinToString(","))
//            bundle.putParcelableArrayList("cash_recap", data.cashRecapList)

            Bugsnag.leaveBreadcrumb("show daily-recap")
//            Timber.i("showing recap detail, tab ui: ${(activity as MainActivity).isTabLayout}, visibility: ${binding.layoutNota.visibility}")
            if ((activity as MainActivity).isTabLayout) {
                binding.layNoSelected.layoutNoData.setVisible(false)
                if (binding.layoutNota.visibility != View.VISIBLE) {
                    Timber.i("layoutNota is not visible, set it so...")
                    binding.layoutNota.setVisible(true)
                }
//                bundle.putParcelable("data", data.cashRecapData)
//                bundle.putString("data-json", Gson().toJson(data.cashRecapData))
                try {
//                    val fragment = CloseShiftDetailFragment()
//                    fragment.arguments = bundle
//                    childFragmentManager.beginTransaction()
//                        .replace(R.id.layout_nota, fragment)
//                        .commit()
                    closeShiftDetailFragment.reInitCashRecapData(data.reportFormat)
                } catch (e: Exception) {
                    Bugsnag.notify(e)
                    showMsg(e.message, "ERROR")
                }
            } else {
                val intent = Intent(context, CloseShiftDetailActivity::class.java)
                intent.putExtra("bundle", bundle)
                startActivity(intent)
            }
        }

        viewModel.taskLogOut.observe(viewLifecycleOwner) {
            logoutEmployee()
        }

        viewModel.taskPrint.observe(viewLifecycleOwner) { printData ->
            lifecycleScope.launch {
                managePrintWifi(printData)
                withTimeoutOrNull(10000) {
                    do {
                        delay(500)
                    } while (isPrintingInProcess())
                }
                Timber.i("[PRINT] cash recap printed...")
                logoutEmployee()
            }
        }

        viewModel.warning.observe(viewLifecycleOwner) { evt ->            
            evt.getContentIfNotHandled()?.let { warning ->
                binding.txtWarning.text = warning
                binding.layoutWarning.setVisible(warning.isNotBlank())
            }
        }
    }

    private fun initViewFeature() {
        val role = role()
        context?.outletFeature()?.let { feature ->
            Timber.i("Outlet Feature : ${Gson().toJson(feature)}")
//            v.layout_button.visibility = feature.reprintCloseregister.visibility()
            val isHistoryShow = feature.viewcloseregister && role.viewcloseregister
//            binding.layoutNota.setVisible(isHistoryShow)
            binding.recviewCashRecap.visibility = isHistoryShow.visibility(View.INVISIBLE)
            binding.btnDailyRecap.visibility = (role.printDailyrecap).visibility(View.INVISIBLE)
            binding.layoutPermission.root.visibility = (!isHistoryShow).visibility()
            binding.layoutNota.visibility =
                ((((feature.viewcloseregister && role.viewcloseregister) || role.printDailyrecap) && (activity as MainActivity).isTabLayout)).visibility()
            binding.txtTotalSales.visibility =
                (feature.viewtotalachievement && role.viewtotalachievement).visibility()
            binding.titleSalesToday.visibility =
                (feature.viewtotalachievement && role.viewtotalachievement).visibility()

            if (!isHistoryShow && !feature.viewcloseregister) {
                binding.layoutPermission.txtNoPermission.text = getString(R.string.feature_disable)
            }
        }
    }

    private fun initDialogCloseCashier() {
        bindingDialog = DataBindingUtil.inflate(
            LayoutInflater.from(context),
            R.layout.dialog_close_cashier,
            null,
            false
        )

        bindingDialog.fragment = this

        Utils.registerToCurrency(bindingDialog.edtCard, bindingDialog.edtCash)
        bindingDialog.btnSave.setOnButtonClickListener {
            if (binding.txtTotalSales.text.toString() == "0") {
                context?.showMessage(
                    "Total transaksi anda sepertinya masih kosong, yakin untuk melanjutkan?",
                    "WARNING",
                    { _, _ ->
                        submit()
                    },
                    positiveMsg = getString(R.string.continue_text)
                )
            } else {
                submit()
            }
        }

        dialogCloseShift = DynamicDialog(bindingDialog.root, context)
        dialogCloseShift.setOnShowListener {
            bindingDialog.cbPrintDaily.isChecked = isShouldPrintRecap()
        }
    }

    private fun isShouldPrintRecap(): Boolean {
        val previous = context?.getLocalDataInt(
            "${SharedPref.PREV_CLOSE_SHIFT_STATUS}${currentShift?.shiftFkid}",
            -1
        ).safe(-1)
        Timber.i(">>> is cbPrintDaily checked ? -> Previous : $previous | System Suggestion : ${viewModel.suggestionToPrintRecap}")
        return if (previous < 0) viewModel.suggestionToPrintRecap else previous.toBoolean()
    }

    private fun requestAuthorization(authType: DialogAuthorization.AuthType) {
        context?.apply {
            DialogAuthorization(this)
                .setAuthType(authType)
                .setOnAuthorizedListener {
                    when (authType) {
                        DialogAuthorization.AuthType.CLOSE_SHIFT -> dialogCloseShift.show() //dialogCloseCashier.show()
                        else -> {}
                    }
                }
                .show()
        }
    }

    private fun showDailyRecapDialog() {
        val edt = TextInputEditText(requireContext())
        val layout = TextInputLayout(requireContext())
        val params = LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.WRAP_CONTENT,
            LinearLayout.LayoutParams.WRAP_CONTENT
        )

        params.setMargins(16, 16, 16, 16)
        layout.layoutParams = params
        layout.setPadding(19, 19, 19, 19)
        val date = viewModel.cashRecapList.firstOrNull()?.timeCreated?.dateFormat()
            .safe(System.currentTimeMillis().dateFormat())
        edt.hint = getString(R.string.open_shift_date)
        edt.setText(date)
        edt.isFocusable = false
        edt.isLongClickable = false
        edt.setAsDateinput(childFragmentManager)
        layout.addView(edt)

        AlertDialog.Builder(requireContext())
            .setView(layout)
            .setTitle(R.string.daily_recap)
            .setNegativeButton(R.string.cancel, null)
            .setPositiveButton("Generate") { _, _ ->
                if (edt.value().trim().isNotEmpty()) {
                    viewModel.generateDailyRecap(edt.value())

                    val tmpSelected = selected
                    selected = -1
                    binding.recviewCashRecap.adapter?.notifyItemChanged(tmpSelected)

                    Firebase.analytics
                        .logEvent(
                            "daily_recap",
                            bundleOf(
                                "Outlet" to "M:${outlet.outletId}:${outlet.name}",
                                "Type" to "Manual",
                                "Time" to "M:${Date().dateFormat("HH")}"
                            )
                        )
                }
            }
            .show()
    }

    private fun submit() {
        if (context?.getLocalDataBoolean(SharedPref.LOGIN_EMPLOYEE_STATUS) == false) {
            Bugsnag.notify(Exception("resubmit close shift, user is not logged in"))
            toast("please try to login again!")
            logoutEmployee()
            return
        }

        Bugsnag.leaveBreadcrumb("close shift")
        bindingDialog.btnSave.startLoading()
        Timber.i(">> Tutup Kasir -> CASH : ${bindingDialog.edtCash.value()} | CARD : ${bindingDialog.edtCard.value()} || Total : ${binding.txtTotalSales.text} | is print daily recap ? ${bindingDialog.cbPrintDaily.isChecked}")

        if (!(bindingDialog.edtCash.value().isBlank() && bindingDialog.edtCard.value().isBlank())) {
            toast("Process... Please Wait!")
            showDialog(true)
            offlineModeEnable = context?.outletFeature()?.offlineMode ?: false
            Utils.hideKeyboard(activity)

            val employee = context?.employee() ?: Employee()
            val outlet = context?.outlet() ?: Outlet()
            val shift = context?.shiftOpen() ?: ShiftOpen()

            closeShiftCashRecap = CashRecapEntity(
               viewModel.reservedCloseShiftId,
                bindingDialog.edtCash.text.toString().fromCurrency(),
                bindingDialog.edtCard.text.toString().fromCurrency(),
                employeeId = employee.employeeId,
                outletId = outlet.outletId,
                employeeName = employee.name,
                previousRecap = lastCashRecap
                    ?: 0,
                openShiftId = shift.openShiftId
            )

            context?.putData(
                "${SharedPref.PREV_CLOSE_SHIFT_STATUS}${shift.shiftFkid}",
                bindingDialog.cbPrintDaily.isChecked.toInt()
            )

            viewModel.saveCloseShift(closeShiftCashRecap!!, bindingDialog.cbPrintDaily.isChecked)
        } else {
            toast(getString(R.string.cash_card_must_filled))
            bindingDialog.edtCash.error = "Field can't be blank"
        }
        bindingDialog.btnSave.stopLoading()
    }

    private fun logoutEmployee() {
        Timber.i("logging out employee")
        var intent = Intent(context, ChooseOperatorActivity::class.java)
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK)
        startActivity(intent)
    }

    private fun showCase(showCaseType: String = "") {
        val key = "$showCaseType ${context.employee()?.employeeId.safe()}"
        val showCaseData = when (showCaseType) {
            ShowCase.CashRecap_CloseShift -> {
                ShowCaseData(
                    "Tutup Kasir",
                    "Klik tombol ini untuk melakukan proses tutup kasir",
                    binding.btnAddCloseCashier
                ) { }
            }
            else -> null
        }

        showCaseData?.let {
            if (context?.getLocalDataBoolean(key).safe()) {
                GuideView.Builder(context)
                    .setTitle(showCaseData.title)
                    .setContentText(showCaseData.message)
                    .setTargetView(showCaseData.view)
                    .setTitleTextSize(17)
                    .setDismissType(DismissType.anywhere)
                    .setGuideListener { showCaseData.next.invoke() }
                    .build()
                    .show()
                context?.putData(key, true)
            }
        }
    }

    fun onTextChanged(s: CharSequence, start: Int, before: Int, count: Int) {
        var card = 0
        var cash = 0
        if (bindingDialog.edtCard.text.toString().isNotEmpty()) card =
            bindingDialog.edtCard.value().fromCurrency()
        if (bindingDialog.edtCash.text.toString().isNotEmpty()) cash =
            bindingDialog.edtCash.value().fromCurrency()
        bindingDialog.txtCashpluscard.text = (card + cash).toCurrency()
    }

    override fun onSharedPrefChanged(key: String) {
        if(key == SharedPref.LAST_SYNC){
            viewModel.checkWarning()
        }
    }
}
