package com.uniq.uniqpos.view.productcatalogue.ocr

import android.graphics.Color
import android.view.MenuItem
import android.widget.FrameLayout
import android.widget.TextView
import androidx.fragment.app.commit
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.ItemTouchHelper
import com.google.gson.Gson
import com.uniq.uniqpos.R
import com.uniq.uniqpos.data.local.entity.ProductEntity
import com.uniq.uniqpos.data.remote.model.ProductExtractionResponse
import com.uniq.uniqpos.data.remote.model.toProductEntity
import com.uniq.uniqpos.databinding.ActivityProductEditListBinding
import com.uniq.uniqpos.databinding.ListItemProductEditBinding
import com.uniq.uniqpos.util.Level
import com.uniq.uniqpos.util.data.DataHolder
import com.uniq.uniqpos.util.hideKeyboard
import com.uniq.uniqpos.util.lifecycle.Success
import com.uniq.uniqpos.util.lifecycle.setupLoadingDialogMessage
import com.uniq.uniqpos.util.lifecycle.setupToastMessage
import com.uniq.uniqpos.util.safe
import com.uniq.uniqpos.util.showMessage
import com.uniq.uniqpos.util.toast
import com.uniq.uniqpos.util.view.SwipeController
import com.uniq.uniqpos.view.global.BaseActivity
import com.uniq.uniqpos.view.global.DynamicDialogFragment
import com.uniq.uniqpos.view.global.GlobalAdapter
import com.uniq.uniqpos.view.global.GlobalViewHolder
import com.uniq.uniqpos.view.productcatalogue.ProductCatalogViewModel
import com.uniq.uniqpos.view.productcatalogue.ProductFormFragment
import timber.log.Timber

class ProductEditList : BaseActivity<ProductCatalogViewModel, ActivityProductEditListBinding> () {
    override fun getLayoutRes() = R.layout.activity_product_edit_list
    override fun getViewModel() = ProductCatalogViewModel::class.java
    override fun getDisplayHomeAsUpEnabled() = true

    private var productFormFragment: ProductFormFragment = ProductFormFragment()
    private var productFormDialog: DynamicDialogFragment? = null
    val productList = ArrayList<ProductEntity>()
    private var selectedPosition = -1
    private var isUseTabLayout = false

    override fun initView() {
        super.initView()
        DataHolder.getData()?.let { data ->
            val newList = Gson().fromJson(data, Array<ProductExtractionResponse>::class.java).toList() as ArrayList<ProductExtractionResponse>
            productList.clear()
            newList.forEach { product ->
                productList.add(product.toProductEntity("", 0))
            }
            toast("${productList.size} products successfully extracted")
        } ?: run {
            showMessage("no item extracted", "error")
        }

        binding.viList.recViewMenu.adapter = object : GlobalAdapter<ListItemProductEditBinding>(R.layout.list_item_product_edit, productList) {
            override fun onBindViewHolder(
                holder: GlobalViewHolder<ListItemProductEditBinding>,
                position: Int
            ) {
                super.onBindViewHolder(holder, position)
                //adjust color
                if(isUseTabLayout){
                    val backgroundColor = if(selectedPosition == holder.adapterPosition) Color.parseColor("#353F4C") else 0
                    holder.binding.container.setBackgroundColor(backgroundColor)
                }

                holder.binding.root.setOnClickListener { showEditProductDetail(holder.adapterPosition) }
            }
        }

        val swipeListHelper = ItemTouchHelper(SwipeController(object : SwipeController.SwipeListener {
            override fun enableSwipe(position: Int) = true
            override fun onSwiped(position: Int) {
                showMessage("remove '${productList[position].name?.uppercase()}' from the list?",
                    "Confirmation",
                    positiveAction = {_,_ ->
                        productList.removeAt(position)
                        binding.viList.recViewMenu.adapter?.notifyItemRemoved(position)
                        binding.viList.recViewMenu.adapter?.notifyItemRangeChanged(position, productList.size)
                    })
                binding.viList.recViewMenu.adapter?.notifyItemChanged(position)
            }
        }))
        swipeListHelper.attachToRecyclerView(binding.viList.recViewMenu)

        binding.viList.btnSave.setOnButtonClickListener {
            viewModel.saveProductExtraction(productList)
        }

        findViewById<FrameLayout?>(R.id.container_detail)?.let { container ->
            isUseTabLayout = true
            supportFragmentManager.commit {
                setReorderingAllowed(true)
                add(container.id, productFormFragment)
            }
        }
        findViewById<TextView>(R.id.txt_update)?.setOnClickListener {updateItem()}
    }

    private fun updateItem(){
        productFormFragment.getItem()?.let { product ->
            productList[selectedPosition] = product
            binding.viList.recViewMenu.adapter?.notifyItemChanged(selectedPosition)
            toast("${productList[selectedPosition].name?.uppercase()} successfully updated", level = Level.WARNING)
            binding.root.hideKeyboard(this)
            if(!isUseTabLayout)productFormDialog?.dismiss()
        } ?: kotlin.run {
            toast("product is null")
        }
    }

    override fun observeTask() {
        setupLoadingDialogMessage(this, this, viewModel.loadingDialogMessage)
        setupToastMessage(this,  viewModel.toastMessage)

        viewModel.state.observe(this){ state ->
            when (state){
                is Success -> {
                    toast("product successfully saved")
                    finish()
                }
            }
        }
    }

    private fun showEditProductDetail(position: Int) {
//        if(position == selectedPosition) return
        //show dialog (for mobile)
        if(!isUseTabLayout){
            productFormDialog = productFormDialog ?: getProductDetailDialog()
            productFormDialog?.addOnViewCreatedListener {
                productFormDialog?.setHeader(productList[position].name.safe().uppercase()) { updateItem() }
            }
            productFormDialog?.show(supportFragmentManager, "product-form-detail")
        }
        productFormFragment.setItem(productList[position], false)

        //change color
        val tmpSelectedId = selectedPosition
        selectedPosition = position
        if(isUseTabLayout){
            binding.viList.recViewMenu.adapter?.notifyItemChanged(position)
            binding.viList.recViewMenu.adapter?.notifyItemChanged(tmpSelectedId)
        }
    }

    private fun getProductDetailDialog(): DynamicDialogFragment {
        val dialog =  DynamicDialogFragment(productFormFragment, this, layoutInflater, useHeader = true)
        return dialog
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        onBack()
        return true
    }

    fun onBack(){
        showMessage("are you sure? item is not save yet!", "Confirmation", positiveAction = {_,_ ->
            finish()
        })
    }
}