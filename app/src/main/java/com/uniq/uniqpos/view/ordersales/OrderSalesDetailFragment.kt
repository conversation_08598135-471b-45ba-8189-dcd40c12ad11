package com.uniq.uniqpos.view.ordersales


import android.os.Bundle
import android.util.TypedValue
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.fragment.app.Fragment
import com.google.gson.Gson
import com.uniq.uniqpos.R
import com.uniq.uniqpos.data.local.entity.OrderSalesEntity
import com.uniq.uniqpos.data.local.entity.SalesEntity
import com.uniq.uniqpos.data.local.entity.adjustData
import com.uniq.uniqpos.data.local.entity.extractAllTaxes
import com.uniq.uniqpos.databinding.FragmentOrderSalesDetailBinding
import com.uniq.uniqpos.databinding.ListItemOrderHistoryBinding
import com.uniq.uniqpos.databinding.ListItemOrderHistoryLegacyBinding
import com.uniq.uniqpos.databinding.ListItemTaxBinding
import com.uniq.uniqpos.model.Order
import com.uniq.uniqpos.model.TaxSales
import com.uniq.uniqpos.util.Constant
import com.uniq.uniqpos.util.Utils
import com.uniq.uniqpos.util.safe
import com.uniq.uniqpos.util.safeToInt
import com.uniq.uniqpos.util.toCurrency
import com.uniq.uniqpos.view.global.GlobalAdapter
import com.uniq.uniqpos.view.global.GlobalViewHolder
import timber.log.Timber
import java.util.*


/**
 * A simple [Fragment] subclass.
 *
 */
class OrderSalesDetailFragment : Fragment() {

    private lateinit var binding: FragmentOrderSalesDetailBinding
    private val orderList = ArrayList<Order>()
    private val taxList = ArrayList<TaxSales>()
    private var sales: SalesEntity? = null
    private var orderSales: OrderSalesEntity? = null
    private var isTabLayout = false

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentOrderSalesDetailBinding.inflate(inflater, container, false)

        arguments?.apply {
            val orderSalesJson = getString("data")
            orderSales = Gson().fromJson(orderSalesJson, OrderSalesEntity::class.java).adjustData()
            isTabLayout = getBoolean("layout", false)
        }

        orderSales?.let { orderSalesEntity ->
            try {
                sales = Gson().fromJson(orderSalesEntity.items, SalesEntity::class.java)
            } catch (e: Exception) {
                Timber.i("Converting sales error - $e")
            }
        }

        sales?.let { sales ->
            sales.customer = orderSales?.customer.safe(sales.customer.safe())
            binding.sales = sales

            val payments = StringBuilder()
            try {
                sales.payments.forEach {
                    payments.append(it.method)
                    if (it.method == "CARD") payments.append(" (${it.bank?.name})")
                    payments.append(", ")
                }
            } catch (e: Exception) {
                Timber.d("payment is null")
            }
            if (payments.isNotEmpty()) {
                val paymentLength = payments.toString().length
                binding.txtPayment.text = payments.toString().substring(0, paymentLength - 2)
            }

            orderList.addAll(sales.orderList ?: ArrayList())

            var subtotal = orderList.sumOf { it.subTotal }
            orderList.forEach { subtotal += it.extra.sumOf { it.subTotal }  }
            binding.txtSubtotal.text = subtotal.toCurrency()

            taxList.clear()
            taxList.addAll(sales.extractAllTaxes())
        }

        binding.recviewHistoryDetail.adapter = object : GlobalAdapter<ListItemOrderHistoryLegacyBinding>(
            R.layout.list_item_order_history_legacy,
            orderList
        ) {
            override fun onBindViewHolder(
                holder: GlobalViewHolder<ListItemOrderHistoryLegacyBinding>,
                position: Int
            ) {
                super.onBindViewHolder(holder, position)
                holder.binding.layoutExtra.removeAllViews()
                holder.binding.layoutPriceExtra.removeAllViews()

                orderList[position].extra.forEach { o ->
                    val extraText = TextView(context)
                    extraText.setTextColor(Utils.getColor(context, R.color.text_grey_light))
                    extraText.text = o.product?.name
                    extraText.setTextSize(TypedValue.COMPLEX_UNIT_SP, 12f)
                    holder.binding.layoutExtra.addView(extraText)

                    val extraPrice = TextView(context)
                    extraPrice.setTextColor(Utils.getColor(context, R.color.text_grey_light))
                    var price = o.subTotal
                    extraPrice.text = price.toCurrency()
                    extraPrice.setTextSize(TypedValue.COMPLEX_UNIT_SP, 12f)
                    holder.binding.layoutPriceExtra.addView(extraPrice)
                }

                if (orderList[position].discount.discountNominal > 0) {
                    val discText = TextView(context)
                    discText.setTextColor(Utils.getColor(context, R.color.text_grey_light))
                    discText.setTextSize(TypedValue.COMPLEX_UNIT_SP, 12f)
                    discText.text =
                        (if (orderList[position].isItemVoid) "" else "-") + orderList[position].discount.discountNominal.toCurrency()
                    holder.binding.layoutPriceDiscount.addView(discText)

                    val disc =
                        if (orderList[position].discount.discountType == Constant.TYPE_PERSEN) "(${orderList[position].discount.discount}%)" else ""
                    val text = TextView(context)
                    text.setTextColor(Utils.getColor(context, R.color.text_grey_light))
                    text.setTextSize(TypedValue.COMPLEX_UNIT_SP, 12f)
                    text.text = "  - Discount $disc"
                    holder.binding.layoutDiscount.addView(text)
                }
            }
        }

        binding.recViewTax.adapter =
            object : GlobalAdapter<ListItemTaxBinding>(R.layout.list_item_tax, taxList) {}
        return binding.root
    }


}
