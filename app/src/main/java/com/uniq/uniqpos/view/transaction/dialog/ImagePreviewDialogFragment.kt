package com.uniq.uniqpos.view.transaction.dialog

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.TextView
import androidx.fragment.app.DialogFragment
import com.bumptech.glide.Glide
import com.google.android.material.textfield.TextInputEditText
import com.uniq.uniqpos.R
import com.uniq.uniqpos.databinding.LayoutImagePreviewDialogBinding
import com.uniq.uniqpos.view.global.DynamicDialog

class ImagePreviewDialogFragment : DialogFragment() {
    private var binding: LayoutImagePreviewDialogBinding? = null
    private var imageUrl: String? = null
    private var productName: String? = null
    private var onCameraClick: (() -> Unit)? = null
    private var onGalleryClick: (() -> Unit)? = null
    private var onAiGenerateClick: ((String) -> Unit)? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NORMAL, R.style.FullScreenDialog)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = LayoutImagePreviewDialogBinding.inflate(inflater, container, false)
        return binding!!.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        binding?.apply {
            btnClose.setOnClickListener { dismiss() }
            btnCamera.setOnClickListener { 
                onCameraClick?.invoke()
                dismiss()
            }
            btnGallery.setOnClickListener { 
                onGalleryClick?.invoke()
                dismiss()
            }
            btnAiGenerate.setOnClickListener {
                showAiGenerateDialog()
                dismiss()
            }

            imageUrl?.let {
                Glide.with(requireContext())
                    .load(it)
                    .into(imagePreview)
            }
        }
    }

    private fun showAiGenerateDialog() {
        val view = LayoutInflater.from(requireContext()).inflate(R.layout.dialog_ai_generate_image, null)
        
        DynamicDialog(view, requireContext()).apply {
            view.findViewById<TextView>(R.id.txtProductName).text = "Generate image for: ${productName ?: "Product"}"
            
            view.findViewById<Button>(R.id.btnCancel).setOnClickListener {
                dismiss()
            }
            
            view.findViewById<Button>(R.id.btnGenerate).setOnClickListener {
                val description = view.findViewById<TextInputEditText>(R.id.edtDescription).text.toString()
                onAiGenerateClick?.invoke(description)
                dismiss()
            }
            
            show()
        }
    }

    fun setImageUrl(url: String) {
        this.imageUrl = url
    }

    fun setOnCameraClickListener(listener: () -> Unit) {
        this.onCameraClick = listener
    }

    fun setOnGalleryClickListener(listener: () -> Unit) {
        this.onGalleryClick = listener
    }

    fun setOnAiGenerateClickListener(listener: (String) -> Unit) {
        onAiGenerateClick = listener
    }

    override fun onDestroyView() {
        super.onDestroyView()
        binding = null
    }

    companion object {
        fun newInstance(imageUrl: String, productName: String): ImagePreviewDialogFragment {
            return ImagePreviewDialogFragment().apply {
                this.imageUrl = imageUrl
                this.productName = productName
            }
        }
    }
}