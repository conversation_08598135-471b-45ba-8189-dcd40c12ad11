package com.uniq.uniqpos.view.transaction.utils

import com.uniq.uniqpos.data.remote.model.Promotion
import com.uniq.uniqpos.model.Order
import com.uniq.uniqpos.util.Constant
import com.uniq.uniqpos.util.max
import com.uniq.uniqpos.util.safeToInt
import com.uniq.uniqpos.view.transaction.TransactionViewModel
import timber.log.Timber

class PromotionUtil (val vm: TransactionViewModel) {

    fun getPromotionPerItem(order: Order): Int {
        if (vm.promoApplied.isEmpty() && vm.promotionList.isEmpty()) {
            return 0
        }

        var total = 0
        val grandTotal = vm.calculateGrandTotal(isCalculateTax = false, isAddRoundUp = false)

        vm.taxSaleList.filter { it.identifierType == Constant.TAX_TYPE_PROMOTION }.forEach { promoTax ->
            Timber.d("[TAX-PROMO] id: ${promoTax.identifierId} | grandTotal: $grandTotal | subtotal: ${order.subTotal} | disc: ${order.discount.discountNominal}")
            val subtotal = order.subTotal - order.discount.discountNominal
            val promoApplied = vm.promoApplied.firstOrNull { it.code == promoTax.identifierId }
            promoApplied?.let { promo ->
//                val totalPromo = if(promo.promoDiscountType == "percent"){
//                    (promo.value.safeToInt() * order.subTotal)/100
//                }else {
//                    (order.subTotal/grandTotal) * promo.value.safeToInt()
//                }

                val totalPromo = (subtotal.toFloat()/grandTotal.toFloat()) * promoTax.total
                
                total += totalPromo.toInt()
                Timber.d("[TAX-PROMO] $totalPromo, add from scanned: ${promo.name} | ${promo.value} ${promo.promoDiscountType}")
            }

            if (promoApplied == null){
                vm.promotionList.firstOrNull { it.promotionId.toString() == promoTax.identifierId }?.let { promo ->
//                    val totalPromo = if(promo.usePercent == 1) {
//                        (promo.ammount.safeToInt() * order.subTotal)/100
//                    }else{
//                        (order.subTotal.toFloat()/grandTotal.toFloat()) * promo.ammount.safeToInt().max(promoTax.total)
//                    }
                    val totalPromo = (subtotal.toFloat()/grandTotal.toFloat()) * promoTax.total
                    total += totalPromo.toInt()
                    Timber.d("[TAX-PROMO] total: $totalPromo, from promoList: ${promo.name} | ${promo.ammount} | percent: ${promo.usePercent == 1} | promoTaxTotal: ${promoTax.total}")
                }
            }
        }

        return total
    }
}