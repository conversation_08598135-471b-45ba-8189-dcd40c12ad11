package com.uniq.uniqpos.view.global

import androidx.annotation.LayoutRes
import androidx.recyclerview.widget.RecyclerView
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.databinding.ViewDataBinding
import com.uniq.uniqpos.BR

/**
 * Created by ANNASBlackHat on 13/09/2017.
 */

abstract class GlobalAdapterGeneral(
    @field:LayoutRes private var layoutId: Int,
    private val listData: List<*>? = null, private val viewNoData: View? = null
) :
    RecyclerView.Adapter<GlobalRecViewHolder>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): GlobalRecViewHolder {
        val view = LayoutInflater.from(parent.context).inflate(layoutId, parent, false)
        return GlobalRecViewHolder(view)
    }

    override fun onBindViewHolder(holder: GlobalRecViewHolder, position: Int) {
        listData?.apply { holder.onBind(BR.model, listData[position]) }
    }

    override fun getItemCount(): Int {
        viewNoData?.let { vNoData ->
            if (listData?.isEmpty() == true && vNoData.visibility == View.GONE) {
                viewNoData.visibility = View.VISIBLE
            } else if (listData?.isEmpty() == false && vNoData.visibility == View.VISIBLE) {
                viewNoData.visibility = View.GONE
            } else {
            }
        }
        return listData?.size ?: 0
    }

}
