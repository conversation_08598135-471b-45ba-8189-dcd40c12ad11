package com.uniq.uniqpos.view.global

import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.LiveData
import androidx.lifecycle.Observer
import android.view.View
import androidx.databinding.ViewDataBinding
import com.uniq.uniqpos.data.remote.Resource

/**
 * Created by annasblackhat on 18/02/19
 */
abstract class GlobalRecViewAdapter<T, L : ViewDataBinding?>(
    layoutId: Int,
    liveData: LiveData<Resource<List<T>>>? = null,
    lifeCycleOwner: LifecycleOwner? = null,
    private val listData: ArrayList<T> = ArrayList(),
    viewNoData: View? = null
) : GlobalAdapter<L>(layoutId, listData, viewNoData) {

    private var onItemClickListner: ((Int, T?) -> Unit)? = null

    init {
        liveData?.let { liveData ->
            lifeCycleOwner?.let { lifecycleOwner ->
                liveData.observe(lifecycleOwner) { resource ->
                    listData.clear()
                    resource?.data?.let { items ->
                        listData.addAll(items)
                    }
                    notifyDataSetChanged()
                    viewNoData?.visibility = if (listData.isEmpty()) View.VISIBLE else View.GONE
                }
            }
        }
    }

    override fun onBindViewHolder(holder: GlobalViewHolder<L>, position: Int) {
        super.onBindViewHolder(holder, position)
        onItemClickListner?.let { listener ->
            holder.itemView.setOnClickListener {
                listener(holder.adapterPosition, listData?.get(holder.adapterPosition))
            }
        }
    }

    fun onItemClickListener(onItemClickListner: (Int, T?) -> Unit) {
        this.onItemClickListner = onItemClickListner
    }

    fun getItem(position: Int): T? {
        return if (position < listData.size)
            listData[position]
        else null
    }

    fun getAllItem(): List<T> {
        return listData
    }
}