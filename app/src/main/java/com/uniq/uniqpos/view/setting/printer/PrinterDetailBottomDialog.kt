package com.uniq.uniqpos.view.setting.printer

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.uniq.uniqpos.R
import com.uniq.uniqpos.data.local.entity.PrinterEntity
import com.uniq.uniqpos.databinding.DialogPrinterDetailBinding

class PrinterDetailBottomDialog(val printer: PrinterEntity) : BottomSheetDialogFragment() {

    private var _binding: DialogPrinterDetailBinding? = null
    private val binding: DialogPrinterDetailBinding get() = _binding!!

    private lateinit var printerDetailFragment: PrinterDetailFragment

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = DialogPrinterDetailBinding.inflate(inflater, container, false)
        dialog?.setOnShowListener { dialog ->
            val bottomDialog = dialog as BottomSheetDialog
            val sheetView =
                bottomDialog.findViewById<FrameLayout>(com.google.android.material.R.id.design_bottom_sheet)
            BottomSheetBehavior.from(sheetView!!).state = BottomSheetBehavior.STATE_EXPANDED
        }
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        printerDetailFragment = PrinterDetailFragment.newInstance(printer)
        childFragmentManager.beginTransaction()
            .replace(R.id.container, printerDetailFragment)
            .commit()

        binding.txtPrinterInfo.text = "${printer.name} - ${printer.address}"
        binding.imgClose.setOnClickListener { dismiss() }
        binding.btnSave.setOnClickListener {
            printerDetailFragment.saveChange()
            dismiss()
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}