package com.uniq.uniqpos.view.pendingprint

import android.graphics.Color
import android.os.Build
import android.os.Bundle
import android.util.Base64
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewTreeObserver
import android.webkit.WebView
import android.widget.FrameLayout
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.uniq.uniqpos.R
import com.uniq.uniqpos.databinding.DialogPendingPrintDetailBinding
import com.uniq.uniqpos.util.safe
import com.uniq.uniqpos.util.toast


/**
 * Created by annasblackhat on 11/08/20.
 */

class PendingPrintDetailDialog : BottomSheetDialogFragment() {

    private lateinit var binding: DialogPendingPrintDetailBinding
    private var content: String = ""
    private var listener: PendingPrintListener? = null

    companion object {
        fun newInstance(content: String): PendingPrintDetailDialog {
            val args = Bundle()
            args.putString("content", content)
            val fragment = PendingPrintDetailDialog()
            fragment.arguments = args
            return fragment
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.apply {
            content = getString("content").safe()
        }
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        binding = DialogPendingPrintDetailBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

//        binding.webView.setBackgroundColor(Color.TRANSPARENT)
        binding.imgClose.setOnClickListener { dismiss() }
        binding.layoutPrint.setOnClickListener {
            dismiss()
            listener?.print()
        }

//        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
//            WebView.setWebContentsDebuggingEnabled(true)
//        }

        if (resources.getBoolean(R.bool.landscape_only)) {
            view.viewTreeObserver.addOnGlobalLayoutListener(object : ViewTreeObserver.OnGlobalLayoutListener {
                override fun onGlobalLayout() {
                    view.viewTreeObserver.removeOnGlobalLayoutListener(this)
                    val dialog = dialog as BottomSheetDialog
                    val bottomSheet = dialog.findViewById(com.google.android.material.R.id.design_bottom_sheet) as FrameLayout?
                    val behavior = BottomSheetBehavior.from(bottomSheet!!)
                    behavior.state = BottomSheetBehavior.STATE_EXPANDED
                }
            })
        }

//        binding.webView.loadData(convertToHtml(), "text/html", "base64")
        binding.txtReceipt.text = content
    }

    fun setPrintListener(listener: PendingPrintListener): PendingPrintDetailDialog {
        this.listener = listener
        return this
    }

    private fun convertToHtml(): String {
        var fontSize = if (resources.getBoolean(R.bool.landscape_only))"small" else "medium"
        val contentHtml = template.replace("[[CONTENT]]", content).replace("[[SIZE]]", fontSize)
        val encodeHtml = Base64.encodeToString(contentHtml.toByteArray(), Base64.NO_PADDING)
        return encodeHtml
    }

    private val template = "<!DOCTYPE html>\n" +
            "<html\n" +
            "        lang=\"en\"\n" +
            "    <head>\n" +
            "        <meta charset=\"utf-8\">\n" +
            "        <title>Pending</title>\n" +
            "        <style>\n" +
            "      p {\n" +
            "        word-wrap: break-word;      /* IE 5.5-7 */\n" +
            "        white-space: -moz-pre-wrap; /* Firefox 1.0-2.0 */\n" +
            "        white-space: pre-wrap;      /* current browsers */\n" +
            "        font-family: \"Lucida Console\", Courier, monospace;\n" +
            "        font-size: [[size]];\n" +
            "        color: #ffffff; \n" +
            "        text-align: center; \n" +
            "      }\n" +
            "\n" +
            "        </style>\n" +
            "    </head>\n" +
            "    <body>\n" +
            "        <p>\n" +
            "            [[CONTENT]]\n" +
            "        </p>\n" +
            "    </body>\n" +
            "</html>\n"

    interface PendingPrintListener {
        fun print()
    }
}