package com.uniq.uniqpos.view.help

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.uniq.uniqpos.data.local.sharedpref.SharedPref
import com.uniq.uniqpos.data.remote.model.Help
import com.uniq.uniqpos.data.remote.service.HelpService
import com.uniq.uniqpos.util.lifecycle.BaseViewModel
import com.uniq.uniqpos.util.lifecycle.Event
import com.uniq.uniqpos.util.readableError
import kotlinx.coroutines.launch
import retrofit2.await
import timber.log.Timber
import javax.inject.Inject


/**
 * Created by annasblackhat on 17/09/20.
 */

class HelpViewModel @Inject constructor(val service: HelpService, val sharedPref: SharedPref) : BaseViewModel() {

    private val _helpData = MutableLiveData<List<Help>>()
    val helpData: LiveData<List<Help>> = _helpData

    fun loadHelpData() {
        viewModelScope.launch {
            sharedPref.getString(SharedPref.HELP_DATA)?.let { helpJson ->
                val type = object : TypeToken<List<Help>>() {}.type
                val sharedList = Gson().fromJson<List<Help>>(helpJson, type)
                _helpData.postValue(sharedList)
            }

            try {
                val list = service.getHelpData().await()
                list.data?.let {
                    _helpData.postValue(it)
                    sharedPref.putData(SharedPref.HELP_DATA, Gson().toJson(it))
                }
            } catch (e: Exception) {
                _toastMsg.value = Event(e.readableError())
            }
        }
    }
}