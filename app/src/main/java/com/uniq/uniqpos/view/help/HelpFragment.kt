package com.uniq.uniqpos.view.help

import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.google.firebase.analytics.FirebaseAnalytics
import com.uniq.uniqpos.R
import com.uniq.uniqpos.data.remote.model.Help
import com.uniq.uniqpos.databinding.FragmentHelpBinding
import com.uniq.uniqpos.databinding.ListItemHelpBinding
import com.uniq.uniqpos.util.*
import com.uniq.uniqpos.view.global.BaseFragment
import com.uniq.uniqpos.view.global.GlobalAdapter
import com.uniq.uniqpos.view.global.GlobalViewHolder
import com.uniq.uniqpos.view.webview.WebViewActivity
import timber.log.Timber


class HelpFragment : BaseFragment<HelpViewModel, FragmentHelpBinding>() {

    override val layoutRes = R.layout.fragment_help
    override fun getViewModel() = HelpViewModel::class.java

    private val helpListTmp = ArrayList<Help>()
    private val helpList = ArrayList<Help>()
    private var lastSearch: String = ""

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        binding.recViewHelp.adapter = object : GlobalAdapter<ListItemHelpBinding>(R.layout.list_item_help, helpList) {
            override fun onBindViewHolder(holder: GlobalViewHolder<ListItemHelpBinding>, position: Int) {
                super.onBindViewHolder(holder, position)
                holder.binding.root.setOnClickListener {
                    helpList.getSafe(holder.adapterPosition)?.let { help ->
                        when (help.contentType) {
                            Constant.HELP_TYPE_WEB -> {
                                context?.launchUrl(help.content)
                            }
                            Constant.HELP_TYPE_TEXT -> {
                                HelpContentDialog.newInstance(
                                    adjustContentForHtml(help.content),
                                    help.title.uppercase()
                                )
                                    .show(childFragmentManager, "dialog_content")
                            }
                            Constant.HELP_TYPE_HTML -> {
                                HelpContentDialog.newInstance(
                                    help.content,
                                    help.title.uppercase()
                                )
                                    .show(childFragmentManager, "dialog_content")
                            }
                            else -> {
                                context?.toast("action not supported")
                            }
                        }
                        FirebaseAnalytics.getInstance(context!!)
                            .logEvent(
                                "help_content",
                                bundleOf(
                                    "title" to help.title,
                                    "outlet" to "${outlet.outletId}:${outlet.name}"
                                )
                            )
                    }
                }
            }
        }

        binding.recViewHelp.addOnScrollListener(object : RecyclerView.OnScrollListener() {
            override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                val last =
                    (recyclerView.layoutManager as LinearLayoutManager).findLastVisibleItemPosition()
                val first =
                    (recyclerView.layoutManager as LinearLayoutManager).findFirstVisibleItemPosition()
                Timber.i("position first $first | last $last | dy $dy")
                binding.chatWa.visibility = (last <= 7 || (last < (helpList.size - 1))).visibility()
            }
        })

        binding.edtSearch.simpleOnTextChanged { q ->
            logSearch(q)
            helpList.clear()
            if (q.isEmpty()) {
                helpList.addAll(helpListTmp)
                binding.imgClear.setVisible(false)
            } else {
                helpList.addAll(helpListTmp.filter {
                    it.title.lowercase().contains(q.lowercase())
                })
                binding.imgClear.setVisible(true)
            }
            binding.recViewHelp.adapter?.notifyDataSetChanged()
            binding.txtNotFound.setVisible(helpList.isEmpty())
        }

        binding.imgClear.setOnClickListener { binding.edtSearch.setText("") }
        binding.chatWa.setOnClickListener {
//            if (context.isAppInstalled("com.whatsapp") || context.isAppInstalled("com.whatsapp.w4b")) {
//                context?.launchUrl("https://wa.me/6281717172171") //https://wa.me/message/XJX2NYCFXTSZO1
//            } else {
//                context?.launchUrl("https://tawk.to/chat/5b500789df040c3e9e0bb854/default")
//            }

            val intent = Intent(activity, WebViewActivity::class.java)
            intent.putExtra("title", "LIVE CHAT")
            intent.putExtra("url", "https://tawk.to/chat/5b500789df040c3e9e0bb854/1facrs147")
            startActivity(intent)

            FirebaseAnalytics.getInstance(requireContext())
                .logEvent(
                    "help_chat_wa",
                    bundleOf("outlet" to "${outlet.outletId}:${outlet.name}")
                )
        }

        observeData()
        viewModel.loadHelpData()
    }

    private fun observeData() {
        viewModel.helpData.observe(viewLifecycleOwner, Observer {
            helpList.clear()
            helpList.addAll(it)
            helpListTmp.clear()
            helpListTmp.addAll(it)
            binding.recViewHelp.adapter?.notifyDataSetChanged()
        })
    }

    private fun adjustContentForHtml(content: String): String {
        var freshContent = content.replace("\n", " <br/> ")
        val pattern = "\\s(https?[^\\s]+)|\\swww.[^\\s]+"
        pattern.toRegex().findAll(freshContent).forEach {
            freshContent = freshContent.replace(
                it.value,
                " <a href=${it.value} style=color:white> ${it.value} </a> "
            )
        }
        return "<p style=color:white>$freshContent</p>"
    }

    private fun logSearch(q: String = "") {
        Timber.i("log search : $q")
        lastSearch = if (q.length < lastSearch.length) {
            Timber.i("search help : $lastSearch")
            FirebaseAnalytics.getInstance(requireContext())
                .logEvent(
                    "help_search",
                    bundleOf(
                        "query" to lastSearch,
                        "outlet" to "${outlet.outletId}:${outlet.name}"
                    )
                )
            ""
        } else {
            q
        }
    }
}