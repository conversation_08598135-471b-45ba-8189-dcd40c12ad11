package com.uniq.uniqpos.view.setting.printer

import android.R
import android.os.Bundle
import androidx.fragment.app.Fragment
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ArrayAdapter
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.uniq.uniqpos.BuildConfig
import com.uniq.uniqpos.data.local.entity.PrinterEntity
import com.uniq.uniqpos.data.local.sharedpref.SharedPref
import com.uniq.uniqpos.data.local.sharedpref.getLocalDataString
import com.uniq.uniqpos.data.local.sharedpref.putJson
import com.uniq.uniqpos.databinding.FragmentPrinterDetailBinding
import com.uniq.uniqpos.databinding.ListItemPrintTicketDetailBinding
import com.uniq.uniqpos.util.*
import com.uniq.uniqpos.view.global.GlobalAdapter
import com.uniq.uniqpos.view.global.GlobalViewHolder
import com.uniq.uniqpos.view.setting.PrinterSettingViewModel
import timber.log.Timber

private const val ARG_PRINTER = "data"

class PrinterDetailFragment : Fragment() {

    private var _binding: FragmentPrinterDetailBinding? = null
    private val binding: FragmentPrinterDetailBinding get() = _binding!!
    private var viewModel: PrinterSettingViewModel? = null

    private lateinit var printer: PrinterEntity
    private var deactivePrinterList = ArrayList<String>()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.let {
            it.getParcelable<PrinterEntity>(ARG_PRINTER)?.apply {
                printer = this
            }
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentPrinterDetailBinding.inflate(layoutInflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        viewModel = when (activity) {
            is PrinterDetailActivity -> {
                Timber.d("viewmodel take from: PrinterDetailActivity")
                (activity as? PrinterDetailActivity)?.viewModel
            }
            is AddPrinterActivity -> {
                Timber.d("viewmodel take from: AddPrinterActivity")
                (activity as? AddPrinterActivity)?.viewModel
            }
            else -> {
                Timber.d("viewmodel take from: Nothing")
                null
            }
        }

        Timber.d("setting printer of: $printer")
        binding.model = printer
        binding.hasPermission = role().settingPrinter
        printer.printerSettingId?.let { viewModel?.loadPrintTicket(it) }
        val paperOptions = listOf("58 mm", "75 mm", "80 mm")

        //disable edit if user has no permission
        if (!role().settingPrinter) {
            binding.spinPaper.setAdapter(
                ArrayAdapter(
                    requireContext(),
                    R.layout.simple_dropdown_item_1line,
                    ArrayList<String>()
                )
            )
        } else {
            binding.textView1.text = getString(com.uniq.uniqpos.R.string.setting_will_sync)
            binding.spinPaper.setAdapter(
                ArrayAdapter(
                    requireContext(),
                    R.layout.simple_dropdown_item_1line,
                    paperOptions
                )
            )
            binding.imgAdd.setOnClickListener { updateReceiptCopy(1) }
            binding.imgMinus.setOnClickListener { updateReceiptCopy(-1) }
        }

        val inactivePrinter: String =
            requireContext().getLocalDataString(SharedPref.PRINTER_DEACTIVE, null)
        Timber.i("Deactiveate Printer : '$inactivePrinter'")

        inactivePrinter.takeIf { it.isNotEmpty() }?.let {
            val type = object : TypeToken<ArrayList<String>>() {}.type
            this.deactivePrinterList = Gson().fromJson(inactivePrinter, type)
            this.deactivePrinterList.firstOrNull { it == printer.address }
                ?.let { binding.swActive.isChecked = false }
        }

        binding.swReceipt.setOnCheckedChangeListener { _, checked ->
            binding.groupReceiptCopy.setVisible(checked)
            if (checked && binding.edtCopy.text.toString().safeToInt() <= 0) {
                binding.edtCopy.setText("1")
            }
        }

        binding.swOrder.setOnCheckedChangeListener { _, checked ->
            binding.groupPrintOrder.setVisible(checked)
            if (!checked && binding.recviewTicketOrder.visibility == View.VISIBLE) setTicketOrderVisibility()
        }

        binding.swCloseShift.setOnCheckedChangeListener { _, checked ->
            binding.groupPrintClose.setVisible(checked)
            if (!checked && binding.recviewTicketClose.visibility == View.VISIBLE) setTicketCloseVisibility()
        }

        val size =
            if (printer.settingPrintpapersize.safe() > 0) printer.settingPrintpapersize.toString() + " mm" else paperOptions[0]
        binding.spinPaper.setText(size)

        val typeHeader = 1
        binding.recviewTicketOrder.adapter = object :
            GlobalAdapter<ListItemPrintTicketDetailBinding>(
                com.uniq.uniqpos.R.layout.list_item_print_ticket_detail,
                viewModel?.ticketOrderRecView
            ) {
            override fun getItemViewType(position: Int) =
                if (viewModel?.ticketOrderRecView?.get(position)?.isHeader.safe()) typeHeader else 0

            override fun onCreateViewHolder(
                parent: ViewGroup,
                viewType: Int
            ): GlobalViewHolder<ListItemPrintTicketDetailBinding> {
                return if (viewType == typeHeader) GlobalViewHolder(
                    LayoutInflater.from(parent.context)
                        .inflate(
                            com.uniq.uniqpos.R.layout.list_item_print_ticket_title,
                            parent,
                            false
                        )
                )
                else super.onCreateViewHolder(parent, viewType)
            }
        }

        binding.recviewTicketClose.adapter = object :
            GlobalAdapter<ListItemPrintTicketDetailBinding>(
                com.uniq.uniqpos.R.layout.list_item_print_ticket_detail,
                viewModel?.ticketCloseShiftRecView
            ) {
            override fun getItemViewType(position: Int) =
                if (viewModel?.ticketCloseShiftRecView?.get(position)?.isHeader.safe()) typeHeader else 0

            override fun onCreateViewHolder(
                parent: ViewGroup,
                viewType: Int
            ): GlobalViewHolder<ListItemPrintTicketDetailBinding> {
                return if (viewType == typeHeader) GlobalViewHolder(
                    LayoutInflater.from(parent.context)
                        .inflate(
                            com.uniq.uniqpos.R.layout.list_item_print_ticket_title,
                            parent,
                            false
                        )
                )
                else super.onCreateViewHolder(parent, viewType)
            }
        }

        binding.txtTicketOrderCount.setOnClickListener { setTicketOrderVisibility() }
        binding.imageView1.setOnClickListener { setTicketOrderVisibility() }
        binding.txtTicketCloseCount.setOnClickListener { setTicketCloseVisibility() }
        binding.imageView2.setOnClickListener { setTicketCloseVisibility() }

        observeTask()
    }

    private fun initView() {

    }

    fun observeTask() {
        viewModel?.taskRefreshPrinter?.observe(this, {
            val totalTicket = viewModel?.ticketOrderRecView?.filter { it.isHeader }?.size.safe()
            val totalTicketClose =
                viewModel?.ticketCloseShiftRecView?.filter { it.isHeader }?.size.safe()
            binding.txtTicketOrderCount.text =
                resources.getQuantityString(
                    com.uniq.uniqpos.R.plurals.print_ticket_count,
                    totalTicket,
                    totalTicket
                )
            binding.txtTicketCloseCount.text = resources.getQuantityString(
                com.uniq.uniqpos.R.plurals.print_ticket_count,
                totalTicketClose,
                totalTicketClose
            )
            binding.recviewTicketOrder.adapter?.notifyDataSetChanged()
            binding.recviewTicketClose.adapter?.notifyDataSetChanged()
        })
    }

    private fun setTicketOrderVisibility() {
        binding.recviewTicketOrder.setVisible(binding.recviewTicketOrder.visibility == View.GONE)
        binding.imageView1.rotation =
            if (binding.recviewTicketOrder.visibility == View.GONE) 0f else 90f
    }

    private fun setTicketCloseVisibility() {
        binding.recviewTicketClose.setVisible(binding.recviewTicketClose.visibility == View.GONE)
        binding.imageView2.rotation =
            if (binding.recviewTicketClose.visibility == View.GONE) 0f else 90f
    }

    private fun updateReceiptCopy(qty: Int = 1) {
        val result = binding.edtCopy.text.toString().toInt() + qty
        if (result > 0) {
            binding.edtCopy.setText(result.toString())
        }
    }

    //return true if saving process successfully
    fun saveChange(): Boolean {
        if (!Utils.isValidField(binding.edtName)) {
            return false
        }

        if (binding.swActive.isChecked) {
            deactivePrinterList.remove(printer.address)
        } else {
            deactivePrinterList.add(printer.address)
        }
        requireContext().putJson(SharedPref.PRINTER_DEACTIVE, deactivePrinterList)

        if (role().settingPrinter) {
            val printReceiptQty = if (binding.edtCopy.text.toString()
                    .safeToInt() > 0
            ) binding.edtCopy.text.toString() else "1"
            viewModel?.updatePrinterByAddress(
                PrinterEntity(
                    printer.address,
                    settingPrintpapersize = binding.spinPaper.text.toString().takeNumber(),
                    settingPrintreceipt = if (binding.swReceipt.isChecked) "1" else "0",
                    settingPrintorder = if (binding.swOrder.isChecked) "1" else "0",
                    settingClosingshift = if (binding.swCloseShift.isChecked) "1" else "0",
                    settingPrintreceiptJumlah = printReceiptQty,
                    name = binding.edtName.text.toString()
                )
            )
            requireContext().toast("Data Saved!")
        } else {
            requireContext().toast("Sorry, you don't have permission!")
            return false
        }
//        requireActivity().finish()
        return true
    }

    fun setViewModel(viewModel: PrinterSettingViewModel) {
        this.viewModel = viewModel
        observeTask()
    }

    companion object {
        @JvmStatic
        fun newInstance(data: PrinterEntity) =
            PrinterDetailFragment().apply {
                arguments = Bundle().apply {
                    putParcelable(ARG_PRINTER, data)
                }
            }
    }
}