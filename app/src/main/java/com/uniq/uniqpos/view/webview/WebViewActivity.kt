package com.uniq.uniqpos.view.webview

import android.os.Build
import androidx.appcompat.app.AppCompatActivity
import android.os.Bundle
import android.view.MenuItem
import android.view.View
import android.webkit.*
import androidx.databinding.DataBindingUtil
import com.uniq.uniqpos.BuildConfig
import com.uniq.uniqpos.R
import com.uniq.uniqpos.databinding.ActivityWebViewBinding
import com.uniq.uniqpos.util.safe
import timber.log.Timber

class WebViewActivity : AppCompatActivity() {

    private lateinit var binding: ActivityWebViewBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = DataBindingUtil.setContentView(this, R.layout.activity_web_view)
        supportActionBar?.setHomeAsUpIndicator(R.drawable.ic_close)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)

        if (BuildConfig.DEBUG && Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
            WebView.setWebContentsDebuggingEnabled(true)
        }

        //config web-view
        binding.viWebview.settings.javaScriptEnabled = true
        binding.viWebview.settings.domStorageEnabled = true

        intent.getStringExtra("title")?.let { title = it }
        val url = intent.getStringExtra("url")

        binding.viWebview.webViewClient = object : WebViewClient() {
            override fun shouldOverrideUrlLoading(view: WebView, url: String?) = false
            override fun onReceivedError(
                view: WebView?,
                request: WebResourceRequest?,
                error: WebResourceError?
            ) {
                view?.loadUrl("about:blank")
                binding.layoutErr.visibility = View.VISIBLE
                binding.txtErr.text =
                    "Ops! Something didn't go well as we expected\nPlease Check Your Internet Connection"
            }
        }
        binding.viWebview.webChromeClient = object : WebChromeClient() {
            override fun onProgressChanged(view: WebView?, newProgress: Int) {
                if (newProgress == 100) {
                    binding.progressBar.visibility = View.GONE
                    binding.layoutErr.visibility = View.GONE
                }
            }
        }

        binding.viWebview.loadUrl(url.safe("https://www.uniq.id"))
//        binding.viWebview.loadUrl("https://www.uniq.id")
        Timber.i("loading webview: '$url'")
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        finish()
        return super.onOptionsItemSelected(item)
    }
}