package com.uniq.uniqpos.view.register

import android.content.DialogInterface
import android.os.Bundle
import com.uniq.uniqpos.R
import com.uniq.uniqpos.data.remote.model.ServerResponse
import com.uniq.uniqpos.databinding.ActivityRegisterBinding
import com.uniq.uniqpos.util.*
import com.uniq.uniqpos.view.global.BaseActivity
import com.uniq.uniqpos.viewmodel.AuthViewModel
import retrofit2.Call
import retrofit2.Callback
import retrofit2.Response

class RegisterActivity : BaseActivity<AuthViewModel, ActivityRegisterBinding>() {

    override fun getLayoutRes() =  R.layout.activity_register
    override fun getViewModel() = AuthViewModel::class.java

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        binding?.sigin?.setOnClickListener { finish()}
        binding.btnRegister.setOnClickListener { requestQuotation() }
    }

    private fun requestQuotation() {
        if(!Utils.isValidField(binding.edtName, binding.edtPhone, binding.edtEmail, binding.bussinessName)){
            showMessage("Silahkah lengkapi terlebih dahulu semua data yang diperlukan!")
            return
        }

        if(!Utils.isEmailValid(binding.edtEmail)){
            showMessage("Email tidak valid!")
            return
        }

        showDialog(true)
        viewModel.requestQuotation(binding.edtName.value(), binding.edtEmail.value(),
                binding.edtPhone.value(), binding.bussinessName.value())
                .enqueue(object : Callback<ServerResponse<Any>>{
                    override fun onFailure(call: Call<ServerResponse<Any>>?, t: Throwable?) {
                        showDialog(false)
                        showMessage("Terjadi Masalah : \n$t")
                    }

                    override fun onResponse(call: Call<ServerResponse<Any>>?, response: Response<ServerResponse<Any>>?) {
                        showDialog(false)
                        showMessage("Terima Kasih telah mendaftar! Kami akan menghubungi anda secepatnya", onDismissListener = DialogInterface.OnDismissListener {
                            finish()
                        })
                    }
                })
    }
}
