package com.uniq.uniqpos.view.global

import android.app.AlertDialog
import android.app.Application
import android.app.Dialog
import android.content.Context
import android.content.DialogInterface
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.commit
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleCoroutineScope
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.uniq.uniqpos.R
import com.uniq.uniqpos.databinding.DialogFragmentBinding
import com.uniq.uniqpos.util.safe
import com.uniq.uniqpos.util.setVisible
import com.uniq.uniqpos.util.toast
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import timber.log.Timber

class DynamicDialog(
    private val vi: View,
    val context: Context? = null,
    private val fragmentMng: FragmentManager? = null
) {

    var dialog: DynamicDialogListener? = null
    private var isLandscapeOnly = false
    private var onShow: (() -> Unit)? = null
    private var onDismiss: (() -> Unit)? = null

    init {
        isLandscapeOnly = context?.resources?.getBoolean(R.bool.landscape_only).safe()
        if (isLandscapeOnly) {
            if (context != null) {
                dialog = DynamicDialogWindow(context, vi)
            }
        } else {
            if (context != null) {
                dialog = DynamicDialogBottom(vi, context)
            }
        }
    }

    fun show() {
        dialog?.showDialog()
        onShow?.invoke()
    }

    fun dismiss(){
        dialog?.dismissDynamicDialog()
        onDismiss?.invoke()
    }

    fun setOnShowListener(onShow: (() -> Unit)? = null) {
        this.onShow = onShow
    }

    fun setOnDismissListener(onDismiss: (() -> Unit)? = null){
        this.onDismiss = onDismiss
    }
}

interface DynamicDialogListener {
    fun showDialog()
    fun dismissDynamicDialog()
}

private class DynamicDialogWindow(context: Context, val vi: View) :
    AlertDialog(context), DynamicDialogListener {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        window?.clearFlags(WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or WindowManager.LayoutParams.FLAG_ALT_FOCUSABLE_IM)
        setContentView(vi)
    }

    override fun showDialog() {
        if (!isShowing) {
            this.show()
        }

    }

    override fun dismissDynamicDialog() {
        if (isShowing) {
            this.dismiss()
        }
    }
}

class DynamicDialogBottom(private val vi: View, private val context: Context) :
    DynamicDialogListener {
    val dialog = BottomSheetDialog(context)

    init {
        dialog.setContentView(vi)
    }

    override fun showDialog() {
        if (!dialog.isShowing) {
            dialog.show()
        }
    }

    override fun dismissDynamicDialog() {
        if (dialog.isShowing) {
            dialog.dismiss()
        }
    }
}

class DynamicDialogFragment(private val fragment: Fragment,
                            private val context: Context,
                            private val inflater: LayoutInflater,
                            private var useHeader: Boolean = false): BottomSheetDialogFragment() {

    var isShowing = false
    private var listener: ((Boolean) -> Unit)? = null //true: showing, false: dismiss
    private var onViewCreatedListener: (() -> Unit)? = null
    private  var _binding: DialogFragmentBinding? =null
    val binding get() = _binding!!

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = DialogFragmentBinding.inflate(inflater, null, false)
        Timber.i("[LIFE] #onCreateView")
        return _binding!!.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        Timber.i("[LIFE] #onViewCreated")
        childFragmentManager.commit {
            add(R.id.container_dialog, fragment)
        }
        binding.imgClose.setOnClickListener { dismiss() }
        showHideHeader(useHeader)
        onViewCreatedListener?.invoke()
    }

    fun setHeader(title: String, lifecycleScope: LifecycleCoroutineScope? = null, onSave: (() -> Unit)? = null){
        if(_binding == null ){
            Timber.d("[LIFE] #setHeader - waiting.... $this - isShowing $isShowing")
            if(isShowing)
            lifecycleScope?.launch{
                delay(300)
                setHeader(title,lifecycleScope, onSave)
            }
            return
        }
        Timber.d("[LIFE] #setHeader - $title | ${binding.txtTitle}")
        binding.txtTitle.setVisible(true)
        _binding?.txtTitle?.text = title
        binding.txtSave.setOnClickListener{
            onSave?.invoke()
        }
        showHideHeader(true)
    }

    private fun showHideHeader(isShow: Boolean){
        binding.groupHeader.setVisible(isShow)
        Timber.i(">> showHideHeader: $isShow")
    }

    override fun show(manager: FragmentManager, tag: String?) {
        super.show(manager, tag)
        isShowing = true
        listener?.invoke(true)
        Timber.d("dynamic dialog showing..")
    }

    override fun dismiss() {
        super.dismiss()
        isShowing = false
        listener?.invoke(false)
        Timber.d("dynamic dialog dismiss..")
    }

    override fun onCancel(dialog: DialogInterface) {
        listener?.invoke(false)
        Timber.d("dynamic dialog cancel..")
        super.onCancel(dialog)
    }

    override fun onDestroy() {
        super.onDestroy()
        Timber.i("[LIFE] #onDestroy")
        _binding = null
        onViewCreatedListener = null
        listener = null
    }

    fun addDialogListener(listener: ((Boolean) -> Unit)? = null){
        if(this.listener != null){
            this.listener = null
        }
        this.listener = listener
        Timber.d("dyanmic dialog listener setup")
    }

    fun addOnViewCreatedListener(listener: (() -> Unit)? = null){
        onViewCreatedListener = listener
    }
}

