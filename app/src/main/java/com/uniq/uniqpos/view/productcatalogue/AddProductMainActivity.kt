package com.uniq.uniqpos.view.productcatalogue

import android.app.Activity
import android.content.Intent
import android.graphics.BitmapFactory
import android.os.Environment
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import androidx.appcompat.app.AlertDialog
import androidx.fragment.app.commit
import androidx.lifecycle.lifecycleScope
import com.bugsnag.android.Bugsnag
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.uniq.uniqpos.BuildConfig
import com.uniq.uniqpos.R
import com.uniq.uniqpos.databinding.ActivityAddProductMainBinding
import com.uniq.uniqpos.databinding.DialogExtractMenuBinding
import com.uniq.uniqpos.util.Level
import com.uniq.uniqpos.util.Utils
import com.uniq.uniqpos.util.data.DataHolder
import com.uniq.uniqpos.util.extensions.toBitmap
import com.uniq.uniqpos.util.extensions.toFile
import com.uniq.uniqpos.util.intent.CameraOrGalleryIntent
import com.uniq.uniqpos.util.intent.openCamera
import com.uniq.uniqpos.util.intent.openGallery
import com.uniq.uniqpos.util.lifecycle.Success
import com.uniq.uniqpos.util.lifecycle.setupDialogMessage
import com.uniq.uniqpos.util.lifecycle.setupLoadingDialog
import com.uniq.uniqpos.util.lifecycle.setupLoadingDialogMessage
import com.uniq.uniqpos.util.lifecycle.setupSnackbar
import com.uniq.uniqpos.util.lifecycle.setupToast
import com.uniq.uniqpos.util.lifecycle.setupToastMessage
import com.uniq.uniqpos.util.showMessage
import com.uniq.uniqpos.util.toast
import com.uniq.uniqpos.view.global.BaseActivity
import com.uniq.uniqpos.view.productcatalogue.ocr.ProductEditList
import com.uniq.uniqpos.data.local.sharedpref.getLocalDataString
import com.uniq.uniqpos.data.local.sharedpref.putData
import kotlinx.coroutines.launch
import timber.log.Timber
import java.io.File

class AddProductMainActivity :  BaseActivity<ProductCatalogViewModel, ActivityAddProductMainBinding>(){

    private val fragmentForm = ProductFormFragment()
    private var productImagePath: String? = null
    
    // List of all available advanced fields
    private val advancedFields = listOf(
        "product_type",
        "product_subcategory",
        "purchase_category",
        "unit",
        "barcode",
        "sku",
        "buying_price",
        "active"
    )
    
    // Shared preferences key for pinned fields
    private val PINNED_FIELDS_PREF_KEY = "pinned_fields_config"

    override fun getDisplayHomeAsUpEnabled() = true
    override fun getLayoutRes(): Int =  R.layout.activity_add_product_main
    override fun getViewModel() = ProductCatalogViewModel::class.java
    override fun initView() {
        super.initView()
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        supportFragmentManager.commit {
            replace(R.id.container_form, fragmentForm)
        }

        binding.btnSave.setOnClickListener { saveProduct() }
        binding.btnTryOcr.setOnClickListener { showOcrDialog()}
        
        // Show pin feature explanation if first time
        showPinFeatureExplanationIfNeeded()
    }

    override fun observeData() {
        setupLoadingDialog(this, this, viewModel.loadingDialog)
        setupLoadingDialogMessage(this, this, viewModel.loadingDialogMessage)
        setupToast(this, viewModel.toastMsg)
        setupToastMessage(this, viewModel.toastMessage)
        setupDialogMessage(this, viewModel.dialogMsg)
        binding.main.setupSnackbar(this, viewModel.snackbarText)

        viewModel.state.observe(this) {state ->
            when(state){
                is ProductCatalogViewModel.SuccessExtractMenu -> {
                    val menu = Gson().toJson(state.data)
                    Timber.i("menu extracted: $menu")
                    if (state.data.isNotEmpty()){
                        DataHolder.setData(menu)
                        startActivity(Intent(this, ProductEditList::class.java))
                    }else{
                        showMessage("no menu found in the image", "INFO")
                    }
                }
                is Success -> {
                    fragmentForm.resetView()
                }
            }
        }
    }

    private fun saveProduct(){
        fragmentForm.getItem()?.let { product ->
            viewModel.addProduct(product)
        }
    }

    private fun showOcrDialog(){
        if(BuildConfig.DEBUG && false){
            val test = "[{\"category\":\"Mie Ayam\",\"name\":\"Mie Ayam / Yamin Ori\",\"price\":12000},{\"category\":\"Mie Ayam\",\"name\":\"Mie Ayam / Yamin Bakso\",\"price\":14000},{\"category\":\"Mie Ayam\",\"name\":\"Mie Ayam / Yamin Pangsit\",\"price\":13000},{\"category\":\"Mie Ayam\",\"name\":\"Yamin Jontor\",\"price\":13000},{\"category\":\"Mie Ayam\",\"name\":\"Yamin Jontor Bakso\",\"price\":15000},{\"category\":\"Mie Ayam\",\"name\":\"Yamin Jontor Pangsit\",\"price\":15000},{\"category\":\"Mie Ayam\",\"name\":\"Mie Ayam Goreng\",\"price\":15000},{\"category\":\"Mie Ayam\",\"name\":\"Kwetiaw Pangsit Goreng\",\"price\":18000},{\"category\":\"Mie Ayam\",\"name\":\"Kwetiaw Goreng\",\"price\":15000},{\"category\":\"Aneka Pangsit\",\"name\":\"Pangsit Goreng Biasa\",\"price\":11800},{\"category\":\"Aneka Pangsit\",\"name\":\"Pangsit Goreng Bakso\",\"price\":16000},{\"category\":\"Aneka Pangsit\",\"name\":\"Pangsit Goreng Soto\",\"price\":15000},{\"category\":\"Aneka Pangsit\",\"name\":\"Pangsit Goreng Lendacap\",\"price\":17000},{\"category\":\"Aneka Pangsit\",\"name\":\"Pangsit Chili Oil\",\"price\":11800},{\"category\":\"Minuman\",\"name\":\"Es Teh Manis\",\"price\":4000},{\"category\":\"Minuman\",\"name\":\"Es Jeruk\",\"price\":5000}]"
            DataHolder.setData(test)
            startActivity(Intent(this, ProductEditList::class.java))
        }

        val dialogOcr = DialogExtractMenuBinding.inflate(layoutInflater)
        val bottomSheetDialog = BottomSheetDialog(this)
        bottomSheetDialog.setContentView(dialogOcr.root)
        bottomSheetDialog.show()

        dialogOcr.btnCamera.setOnClickListener {
            openCamera(this, isThumbOnly = false)
            bottomSheetDialog.dismiss()
        }
        dialogOcr.btnGallery.setOnClickListener {
            openGallery(this)
            bottomSheetDialog.dismiss()
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        fragmentForm.onActivityResult(requestCode, resultCode, data)
        if (resultCode != Activity.RESULT_OK){
            return
        }

        when(requestCode){
            CameraOrGalleryIntent.CAMERA -> {
                try {
//                    val bmp = BitmapFactory.decodeFile(CameraOrGalleryIntent.currentPhotoPath)
//                    viewModel.extractMenu(bmp, getExternalFilesDir(Environment.DIRECTORY_PICTURES))
                    Timber.i("currentPhotoPath: ${CameraOrGalleryIntent.currentPhotoPath}")
                    CameraOrGalleryIntent.currentPhotoPath?.let{ File(it) }?.takeIf { it.exists() }?.let { imgFile ->
                        try {
                            lifecycleScope.launch {
                                imgFile.toBitmap(this@AddProductMainActivity, 300.0)
                                runOnUiThread {
                                    viewModel.extractMenu(imageFile = imgFile, externalDir = getExternalFilesDir(Environment.DIRECTORY_PICTURES))
                                }
                            }
                        } catch (e: Exception) {
                            showMessage("failed extracting menu", "error")
                            Bugsnag.notify(e)
                        }
                    } ?: run {
                        showMessage("image not found", "error")
                    }
                } catch (e: Exception) {
                    Bugsnag.notify(e)
                    showMessage("image not found\n$e", "error")
                }
            }
            CameraOrGalleryIntent.GALLERY -> {
                Timber.i("pickImage data: ${data?.data}")
                Timber.i("pickImage extras: ${data?.extras}")
                try {
                    val imgGallery = data?.data
                    showDialog(true, "reading file...")
                    lifecycleScope.launch {
                        imgGallery?.toFile(this@AddProductMainActivity, 500.0)?.let { imgFile ->
                            runOnUiThread {
                                viewModel.extractMenu(imageFile = imgFile, externalDir = getExternalFilesDir(Environment.DIRECTORY_PICTURES))
                            }
                        }?: run {
                            toast("image not found", level = Level.ERROR)
                            showDialog(false)
                        }

//                        imgGallery?.toBitmap(this@AddProductMainActivity, 500.0)?.let { bmp ->
//                            runOnUiThread {
//                                viewModel.extractMenu(bmp)
//                            }
//                        } ?: run {
//                            toast("image not found", level = Level.ERROR)
//                            showDialog(false)
//                        }
                    }
                } catch (e: Exception) {
                    Timber.i("failed loadImg to bitmap: $e")
                    toast("image not found", level = Level.ERROR)
                }
            }
        }
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        when (item.itemId) {
            R.id.action_use_ocr -> {
                showOcrDialog()
                return true
            }
            R.id.action_manage_pins -> {
                showManagePinsDialog()
                return true
            }
            else -> {
                return super.onOptionsItemSelected(item)
            }
        }
    }

    private fun showManagePinsDialog() {
        // Load current pinned fields
        val pinnedFieldsJson = getLocalDataString(PINNED_FIELDS_PREF_KEY, "[]")
        val pinnedFields = if (pinnedFieldsJson.isNotBlank()) {
            Gson().fromJson<List<String>>(pinnedFieldsJson, object : TypeToken<List<String>>() {}.type)
        } else {
            listOf<String>()
        }
        
        // Create a list of field names for the dialog
        val fieldNames = mapOf(
            "product_type" to "Product Type",
            "product_subcategory" to "Product Subcategory",
            "purchase_category" to "Purchase Category",
            "unit" to "Unit",
            "barcode" to "Barcode",
            "sku" to "SKU",
            "buying_price" to "Buying Price",
            "active" to "Active Status"
        )
        
        // Create a boolean array of which fields are pinned
        val checkedItems = advancedFields.map { pinnedFields.contains(it) }.toBooleanArray()
        
        // Create a mutable list to track changes
        val updatedPinnedFields = pinnedFields.toMutableList()
        
        // Show a multi-choice dialog
        AlertDialog.Builder(this)
            .setTitle("Manage Pinned Fields")
            .setMultiChoiceItems(
                advancedFields.map { fieldNames[it] ?: it }.toTypedArray(),
                checkedItems
            ) { _, which, isChecked ->
                val fieldId = advancedFields[which]
                if (isChecked) {
                    // Pin the field
                    if (!updatedPinnedFields.contains(fieldId)) {
                        updatedPinnedFields.add(fieldId)
                    }
                } else {
                    // Unpin the field
                    updatedPinnedFields.remove(fieldId)
                }
            }
            .setPositiveButton("Save") { _, _ ->
                // Save the updated configuration
                val updatedJson = Gson().toJson(updatedPinnedFields)
                putData(PINNED_FIELDS_PREF_KEY, updatedJson)
                
                // Notify the fragment to reload pinned fields
                fragmentForm.loadPinnedFields()
                fragmentForm.updateFieldsVisibility()
                
                // Show a confirmation
                toast("Pinned fields updated")
            }
            .setNegativeButton("Cancel", null)
            .show()
    }

    override fun onCreateOptionsMenu(menu: Menu): Boolean {
        val inflater: MenuInflater = menuInflater
        inflater.inflate(R.menu.menu_add_product, menu)
        return true
    }

    private fun showPinFeatureExplanationIfNeeded() {
        val hasShownExplanation = getLocalDataString("has_shown_pin_explanation", "false").toBoolean()
        
        if (!hasShownExplanation) {
            // Show explanation dialog
            AlertDialog.Builder(this)
                .setTitle("New Feature: Pin Fields")
                .setMessage("You can now pin fields from advanced mode to basic mode! Long press on any field in advanced mode to pin it. Pinned fields will always be visible, even in basic mode.")
                .setPositiveButton("Got it") { _, _ ->
                    // Mark as shown
                    putData("has_shown_pin_explanation", "true")
                }
                .show()
        }
    }

}