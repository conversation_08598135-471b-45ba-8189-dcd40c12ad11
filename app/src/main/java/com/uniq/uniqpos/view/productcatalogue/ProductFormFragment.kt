package com.uniq.uniqpos.view.productcatalogue

import android.app.Activity.RESULT_OK
import android.content.Intent
import android.graphics.Bitmap
import android.net.Uri
import android.os.Bundle
import android.os.Environment
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ArrayAdapter
import androidx.activity.result.ActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import com.bumptech.glide.Glide
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.uniq.uniqpos.R
import com.uniq.uniqpos.data.local.entity.CategoryEntity
import com.uniq.uniqpos.data.local.entity.ProductEntity
import com.uniq.uniqpos.databinding.FragmentProructFormBinding
import com.uniq.uniqpos.model.BottomDialogModel
import com.uniq.uniqpos.model.SimplePageListData
import com.uniq.uniqpos.util.BACKGROUND
import com.uniq.uniqpos.util.Level
import com.uniq.uniqpos.util.Utils
import com.uniq.uniqpos.util.extensions.saveAsBitmap
import com.uniq.uniqpos.util.fromCurrency
import com.uniq.uniqpos.util.intent.CameraOrGalleryIntent
import com.uniq.uniqpos.util.intent.openCamera
import com.uniq.uniqpos.util.intent.openGallery
import com.uniq.uniqpos.util.safe
import com.uniq.uniqpos.util.setVisible
import com.uniq.uniqpos.util.toast
import com.uniq.uniqpos.util.value
import com.uniq.uniqpos.view.global.BottomDialogInput
import com.uniq.uniqpos.view.productcatalogue.ocr.ProductEditList
import com.uniq.uniqpos.view.simplepagelist.SimplePageListActivity
import com.weiwangcn.betterspinner.library.material.MaterialBetterSpinner
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import timber.log.Timber
import kotlin.coroutines.resume
import kotlin.coroutines.suspendCoroutine
import com.uniq.uniqpos.data.local.sharedpref.getLocalDataBoolean
import com.uniq.uniqpos.data.local.sharedpref.getLocalDataString
import com.uniq.uniqpos.data.local.sharedpref.putData

// TODO: Rename parameter arguments, choose names that match
// the fragment initialization parameters, e.g. ARG_ITEM_NUMBER
private const val ARG_PARAM1 = "param1"
private const val ARG_PARAM2 = "param2"

/**
 * A simple [Fragment] subclass.
 * Use the [ProductFormFragment.newInstance] factory method to
 * create an instance of this fragment.
 */
class ProductFormFragment : Fragment() {
    // TODO: Rename and change types of parameters
    private var param1: String? = null
    private var param2: String? = null

    private var _binding: FragmentProructFormBinding? = null
    private val binding get() = _binding!!

    private var _viewModel: ProductCatalogViewModel? = null
    private val viewModel get() = _viewModel!!

    private lateinit var productTypeAdadpter: ArrayAdapter<String>
    private lateinit var subCategoryAdadpter: ArrayAdapter<String>
    private lateinit var categoryAdadpter: ArrayAdapter<String>
    private lateinit var purchaseCategoryAdadpter: ArrayAdapter<String>
    private lateinit var unitAdadpter: ArrayAdapter<String>

    private var tempProductExtraction: ProductEntity? = null
    private var isInAdvanceMode = false
    private var addNew = "--Add New--"
    val activeStatus = listOf("Active All", "Active On Sales", "Active On Link", "No Active")
    private var imgPath: String? = null

    // List of all available advanced fields
    private val advancedFields = listOf(
        "product_type",
        "product_subcategory",
        "purchase_category",
        "unit",
        "barcode",
        "sku",
        "buying_price",
        "active"
    )
    
    // Map of field IDs to their corresponding views
    private lateinit var fieldViewMap: Map<String, View>
    
    // Current pinned fields configuration
    private val pinnedFields = mutableListOf<String>()
    
    // Shared preferences key for pinned fields
    private val PINNED_FIELDS_PREF_KEY = "pinned_fields_config"

    private val RC_GALLERY = 12
    private val RC_TAKE_CAMERA = 13
    private val RC_CHOOSE_CATEGORY = 16

    private val startForResultCategory = registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result: ActivityResult ->
        if (result.resultCode == RESULT_OK) {
            val data = result.data
            val simpleData = data?.getParcelableExtra<SimplePageListData>("data")
            onCategoryChosen(simpleData?.title.safe(), data?.getStringExtra("source") == "new")
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.let {
            param1 = it.getString(ARG_PARAM1)
            param2 = it.getString(ARG_PARAM2)
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        // Inflate the layout for this fragment
        _viewModel =  (activity as? AddProductMainActivity)?.viewModel ?: (activity as? ProductEditList)?.viewModel
        _binding = FragmentProructFormBinding.inflate(inflater, container, false)
        initView()
        tempProductExtraction?.let {
            setItem(it)
            tempProductExtraction = null
        }
        return _binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        observerData()
    }

    private  fun initView(){
        addNew = getString(R.string.add_new)

        //init adapters
        productTypeAdadpter =
            ArrayAdapter(requireContext(), android.R.layout.simple_list_item_1, arrayListOf(addNew))
        subCategoryAdadpter =
            ArrayAdapter(requireContext(), android.R.layout.simple_list_item_1, arrayListOf(addNew))
        categoryAdadpter =
            ArrayAdapter(requireContext(), android.R.layout.simple_list_item_1, arrayListOf(addNew))
        purchaseCategoryAdadpter =
            ArrayAdapter(requireContext(), android.R.layout.simple_list_item_1, arrayListOf(addNew))
        unitAdadpter = ArrayAdapter(requireContext(), android.R.layout.simple_list_item_1, arrayListOf(addNew))

        // Initialize field view map
        fieldViewMap = mapOf(
            "product_type" to binding.spinProdType,
            "product_subcategory" to binding.spinProdSubcategory,
            "purchase_category" to binding.spinPurcCateegory,
            "unit" to binding.spinUnit,
            "barcode" to binding.layoutBarcode,
            "sku" to binding.layoutSku,
            "buying_price" to binding.layoutBuyPrice,
            "active" to binding.spinActive
        )
        
        // Load pinned fields from SharedPreferences
        loadPinnedFields()
        
        // Setup pin buttons for each field
        setupPinButtons()

        //set adapter
        binding.spinActive.setAdapter(
            ArrayAdapter(
                requireContext(),
                android.R.layout.simple_list_item_1,
                activeStatus
            )
        )
        binding.spinProdType.setAdapter(productTypeAdadpter)
        binding.spinCategory.setAdapter(categoryAdadpter)
        binding.spinProdSubcategory.setAdapter(subCategoryAdadpter)
        binding.spinPurcCateegory.setAdapter(purchaseCategoryAdadpter)
        binding.spinUnit.setAdapter(unitAdadpter)

        //set view actions
        listOf(binding.spinProdType, binding.spinCategory, binding.spinProdSubcategory, binding.spinPurcCateegory, binding.spinUnit)
            .forEach { view ->
                view.setOnItemClickListener { _, _, _, _ ->
                    if (view.value() == addNew) {
                        view.setText("")
                        addNewItem(view)
                    }
                }
            }

        binding.spinCategory.setOnClickListener {
            if (categoryAdadpter.count > 10) {
                val data = ArrayList<SimplePageListData>()
                viewModel.categoryList.forEach {
                    data.add(
                        SimplePageListData(
                            it.name,
                            it.productCategoryId
                        )
                    )
                }
                val intent = Intent(requireContext(), SimplePageListActivity::class.java)
                intent.putExtra("data", data)
                intent.putExtra("type", "Product Category")
                startForResultCategory.launch(intent)
            }
        }

        binding.spinActive.setText(activeStatus[0])
        Utils.registerToCurrency(binding.edtBuyingPrice, binding.edtSellingPrice)

        binding.txtCamera.setOnClickListener { openCamera(requireActivity(), false, RC_TAKE_CAMERA ) }
        binding.txtGallery.setOnClickListener { openGallery(requireActivity(), RC_GALLERY) }
        
        // Set up the advanced mode switch
        binding.switchAdvancedMode.isChecked = isInAdvanceMode
        binding.switchAdvancedMode.setOnCheckedChangeListener { _, isChecked ->
            isInAdvanceMode = isChecked
            setAdvanceMode()
        }
        
        // Make the entire card clickable for better UX
        binding.cardAdvancedMode.setOnClickListener {
            binding.switchAdvancedMode.isChecked = !binding.switchAdvancedMode.isChecked
        }
        
        setAdvanceMode()

        binding.edtProductName.setOnFocusChangeListener { _, hasFocus ->
//            if (!hasFocus && imgPath == null && binding.edtProductName.value().isNotEmpty()) {
//                setImageUrlText(binding.imgProduct, null, binding.edtProductName.value())
//            }
        }
    }

    private fun setAdvanceMode() {
        // Update the UI based on the new mode
        updateFieldsVisibility()
        
        // Update the text
        binding.txtAdvanceDetail.text =
            getString(if (isInAdvanceMode) R.string.advance_mode_detail_hide else R.string.advance_mode_detail_show)
            
        // Ensure switch state matches the mode
        binding.switchAdvancedMode.isChecked = isInAdvanceMode
    }

    private fun addNewItem(view: MaterialBetterSpinner) {
        val bottomDialog = BottomDialogInput()
        bottomDialog.setOnButtonClick { result ->
            if (result == addNew || result.isBlank()) {
                context?.toast("Value is not allowed!")
            } else {
                val adapter = view.adapter as ArrayAdapter<String>
                adapter.remove(addNew)
                adapter.add(result)
                adapter.add(addNew)
                view.setText(result)

                if (view == binding.spinProdType) binding.spinCategory.requestFocus()
                else binding.spinProdType.requestFocus()

                bottomDialog.dismiss()
            }
        }
        bottomDialog.setModel(
            BottomDialogModel(
                "ADD NEW",
                "SAVE",
                view.hint.toString(),
                hint = view.hint.toString()
            )
        )
        bottomDialog.show(childFragmentManager, "add-new")
    }

    private fun observerData(){
        viewModel.loadProductType()
            .observe(this) {
                viewModel.productTypeList?.clear()
                it?.data?.let {
                    viewModel.productTypeList.addAll(it)
                    productTypeAdadpter.clear()
                    it.forEach { productTypeAdadpter.add(it.name) }
                    productTypeAdadpter.add(addNew)
                }
            }

        viewModel.loadCategory()
            .observe(this) {
                viewModel.categoryList?.clear()
                it?.data?.let {
                    viewModel.categoryList?.addAll(it)
                    categoryAdadpter.clear()
                    it.forEach { categoryAdadpter.add(it.name) }
                    categoryAdadpter.add(addNew)
                }
            }

        viewModel.loadPurchaseReportCategory()
            .observe(this) {
                viewModel.purchaseReportCategoryList.clear()
                it?.data?.let {
                    viewModel.purchaseReportCategoryList.addAll(it)
                    purchaseCategoryAdadpter.clear()
                    it.forEach { purchaseCategoryAdadpter.add(it.name) }
                    purchaseCategoryAdadpter.add(addNew)
                }
            }

        viewModel.loadUnit()
            .observe(this) {
                viewModel.unitList.clear()
                it?.data?.let {
                    viewModel.unitList.addAll(it)
                    unitAdadpter.clear()
                    it.forEach { unitAdadpter.add(it.name) }
                    unitAdadpter.add(addNew)
                }
            }

        viewModel.subCategories.observe(this) {
            it?.let { subcategories ->
                viewModel.subCategoryList.clear()
                viewModel.subCategoryList.addAll(subcategories)

                subCategoryAdadpter.clear()
                subcategories.forEach { subCategoryAdadpter.add(it.name) }
                subCategoryAdadpter.add(addNew)
            }
        }
    }

    fun setItem(product: ProductEntity, showUploadImage: Boolean = true) {
        if(_binding == null) {
            Timber.d("binding is null... ${product.name}")
            lifecycleScope.launch {
                delay(300)
                setItem(product, showUploadImage)
            }
            return
        }
        binding.model = product
        binding.viAddImage.setVisible(showUploadImage)
    }

    fun getItem(): ProductEntity? {
        if(_binding == null) {
            return null
        }
        if (!Utils.isValidField(binding.edtProductName, binding.spinCategory, binding.edtSellingPrice)) {
            return null
        }

        val product = ProductEntity(productDetailId = 0, productId = 0)
        product.name = binding.edtProductName.value()
        product.priceSell = binding.edtSellingPrice.value().fromCurrency()
        product.productcategoryName = binding.spinCategory.value()
        product.priceBuy = binding.edtBuyingPrice.value().fromCurrency()
        product.stockManagement = if(binding.swStockManagement.isChecked) 1 else 0
        product.sku = binding.edtSku.value()
        product.barcode = binding.edtBarcode.value()
        product.productSubCategoryName = binding.spinProdSubcategory.value()
        product.purchaseReportCategoryName = binding.spinPurcCateegory.value()
        product.unitName = binding.spinUnit.value()
        product.active = binding.spinActive.value()
        product.productTypeName = binding.spinProdType.value()
        product.photo = imgPath
        Timber.d("product item: ${Gson().toJson(product)}")
        return product
    }

    private fun onCategoryChosen(category: String, isNew: Boolean = false){
        if (isNew) {
            viewModel.categoryList.add(CategoryEntity(0, 0, category))
        }
        binding.spinCategory.setText(category)
        binding.edtBuyingPrice.requestFocus()
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if(resultCode != RESULT_OK){
            return
        }
        when(requestCode) {
            RC_CHOOSE_CATEGORY -> {
                val simpleData = data?.getParcelableExtra<SimplePageListData>("data")
                onCategoryChosen(simpleData?.title.safe(), data?.getStringExtra("source") == "new")
            }
            RC_TAKE_CAMERA -> {
                Timber.d("[RC_TAKE_CAMERA] imgPath: ${CameraOrGalleryIntent.currentPhotoPath}")
                imgPath = CameraOrGalleryIntent.currentPhotoPath
                imgPath?.let { filePath ->
                    Glide.with(this)
                        .load(filePath)
                        .into(binding.imgProduct)
                }
            }
            RC_GALLERY -> {
                try {
                    val selectedImg = data?.data
                    Glide.with(this)
                        .load(selectedImg)
                        .into(binding.imgProduct)
                    lifecycleScope.launch {
                        Timber.d("[RC_GALLERY] selectedImg: $selectedImg")
                        imgPath = selectedImg?.saveAsBitmap(requireContext(), 500.0)
                        Timber.d("[RC_GALLERY] imgPath: $imgPath")
                    }
                } catch (e: Exception) {
                    context?.toast("image not found", level = Level.ERROR)
                }
            }
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
        _viewModel = null
    }

    fun resetView() {
        binding.model =  ProductEntity(productDetailId = 0, productId = 0)
        binding.imgProduct.setImageResource(R.drawable.default_image)
        binding.edtProductName.requestFocus()
    }

    fun loadPinnedFields() {
        val pinnedFieldsJson = requireContext().getLocalDataString(PINNED_FIELDS_PREF_KEY, "[]")
        pinnedFields.clear()
        if(pinnedFieldsJson.isNotBlank()) {
            pinnedFields.addAll(Gson().fromJson(pinnedFieldsJson, object : TypeToken<List<String>>() {}.type))
        }
    }

    private fun savePinnedFields() {
        val pinnedFieldsJson = Gson().toJson(pinnedFields)
        requireContext().putData(PINNED_FIELDS_PREF_KEY, pinnedFieldsJson)
    }

    private fun setupPinButtons() {
        // For each field, add a long click listener to toggle pin state
        fieldViewMap.forEach { (fieldId, view) ->
            view.setOnLongClickListener {
                togglePinField(fieldId)
                true
            }
            
            // Add a visual indicator for pinned fields
            updatePinIndicator(fieldId, view)
        }
    }
    
    private fun togglePinField(fieldId: String) {
        if (pinnedFields.contains(fieldId)) {
            // Unpin the field
            pinnedFields.remove(fieldId)
            context?.toast("Field unpinned")
        } else {
            // Pin the field
            pinnedFields.add(fieldId)
            context?.toast("Field pinned to basic mode")
        }
        
        // Save the updated configuration
        savePinnedFields()
        
        // Update the UI
        updateFieldsVisibility()
    }
    
    private fun updatePinIndicator(fieldId: String, view: View) {
        // Add a visual indicator for pinned fields
        if (pinnedFields.contains(fieldId)) {
            view.setBackgroundResource(R.drawable.bg_pinned_field)
        } else {
            view.setBackgroundResource(0)
        }
    }
    
    fun updateFieldsVisibility() {
        // Update visibility of all fields based on current mode and pinned status
        fieldViewMap.forEach { (fieldId, view) ->
            // Update the pin indicator
            updatePinIndicator(fieldId, view)
            
            if (pinnedFields.contains(fieldId)) {
                // Pinned fields are always visible
                view.visibility = View.VISIBLE
            } else {
                // Non-pinned fields follow the advanced mode visibility
                view.visibility = if (isInAdvanceMode) View.VISIBLE else View.GONE
            }
        }
    }

    companion object {
        /**
         * Use this factory method to create a new instance of
         * this fragment using the provided parameters.
         *
         * @param param1 Parameter 1.
         * @param param2 Parameter 2.
         * @return A new instance of fragment ProructFormFragment.
         */
        // TODO: Rename and change types and number of parameters
        @JvmStatic
        fun newInstance(param1: String, param2: String) =
            ProductFormFragment().apply {
                arguments = Bundle().apply {
                    putString(ARG_PARAM1, param1)
                    putString(ARG_PARAM2, param2)
                }
            }
    }
}