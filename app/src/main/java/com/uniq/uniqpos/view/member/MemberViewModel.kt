package com.uniq.uniqpos.view.member

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.uniq.uniqpos.data.local.entity.MemberEntity
import com.uniq.uniqpos.data.remote.repository.CustomerRepository
import com.uniq.uniqpos.data.remote.repository.ProductRepository
import com.uniq.uniqpos.util.lifecycle.BaseViewModel
import com.uniq.uniqpos.util.lifecycle.Event
import com.uniq.uniqpos.util.lifecycle.Loading
import com.uniq.uniqpos.util.lifecycle.ViewModelState
import com.uniq.uniqpos.util.readableError
import com.uniq.uniqpos.util.safe
import kotlinx.coroutines.launch
import retrofit2.await
import javax.inject.Inject

class MemberViewModel @Inject constructor(
    private val customerRepository: CustomerRepository,
    private val productRepository: ProductRepository
) : BaseViewModel() {

    private val memberList = ArrayList<MemberEntity>()

    private val _memberData = MutableLiveData<List<MemberEntity>>()
    val memberData: LiveData<List<MemberEntity>> = _memberData

    private val _addMemberState = MutableLiveData<Event<ViewModelState>>()
    val addMemberState: LiveData<Event<ViewModelState>> = _addMemberState

    private val _addMemberSuccess = MutableLiveData<MemberEntity>()
    val addMemberSuccess: LiveData<MemberEntity> = _addMemberSuccess

    fun setMemberList(data: List<MemberEntity>?) {
        data?.let {
            memberList.clear()
            memberList.addAll(data)
            _memberData.postValue(it)
        }
    }

    fun searchMember(q: String?) {
        val result = ArrayList<MemberEntity>()
        q?.let { query ->
            val filter = memberList.filter {
                it.name?.lowercase().safe().contains(query.lowercase()) || it.phone.safe()
                    .contains(q)
            }
            result.addAll(filter)
        } ?: run {
            result.addAll(memberList)
        }
        _memberData.postValue(result!!)
    }

    fun getMemberType() = customerRepository.getMemberTypeSync()
    fun getMembers() = customerRepository.getMemberSync()
    fun getMemberProduct() = productRepository.getProductByCatalogue("member")

    fun addMember(name: String, phone: String) {
        viewModelScope.launch {
            _addMemberState.postValue(Event(Loading(true)))
            try {
                val result =
                    customerRepository.registerMember(MemberEntity("", name = name, phone = phone))
                        .await()
                if (result.status) {
                    result.data?.let { member -> _addMemberSuccess.postValue(member) }
                } else {
                    _dialogMsg.postValue(Event(result.message ?: "Failed"))
                }
            } catch (e: Exception) {
                _dialogMsg.postValue(Event(e.readableError()))
            }
            _addMemberState.postValue(Event(Loading(false)))
        }
    }
}