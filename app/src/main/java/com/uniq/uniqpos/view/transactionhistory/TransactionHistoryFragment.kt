package com.uniq.uniqpos.view.transactionhistory


import android.content.Intent
import android.os.Bundle
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import android.view.View
import android.widget.ArrayAdapter
import android.widget.FrameLayout
import android.widget.SearchView
import androidx.appcompat.app.AlertDialog
import androidx.fragment.app.Fragment
import com.bugsnag.android.Bugsnag
import com.google.firebase.analytics.ktx.analytics
import com.google.firebase.ktx.Firebase
import com.uniq.uniqpos.R
import com.uniq.uniqpos.data.local.entity.SalesEntity
import com.uniq.uniqpos.data.local.entity.ShiftEntity
import com.uniq.uniqpos.data.local.sharedpref.SharedPref
import com.uniq.uniqpos.data.local.sharedpref.getJson
import com.uniq.uniqpos.data.remote.model.ShiftOpen
import com.uniq.uniqpos.databinding.DialogFilterTransactionHistoryBinding
import com.uniq.uniqpos.databinding.FragmentTransactionHistoryV2Binding
import com.uniq.uniqpos.model.Admin
import com.uniq.uniqpos.util.*
import com.uniq.uniqpos.util.lifecycle.setupSnackbar
import com.uniq.uniqpos.view.global.BaseFragment
import com.uniq.uniqpos.view.global.DynamicDialog
import com.uniq.uniqpos.view.global.NoDataFragment
import timber.log.Timber


/**
 * A simple [Fragment] subclass.
 */
class TransactionHistoryFragment :
    BaseFragment<TransactionHistoryViewModel, FragmentTransactionHistoryV2Binding>() {

    //init search
    private var searchView: SearchView? = null
    private var searchListener: SearchView.OnQueryTextListener? = null

    private val DETAIL_TAG = "HDTAG"
    private val shiftList = ArrayList<ShiftEntity>()
    private val historyListFragment = HistoryListFragment()
    private var isInSearchMode = false
    private lateinit var dialogFilter: DynamicDialog
    var isUseTabLayout = false

    override val layoutRes = R.layout.fragment_transaction_history_v2
    override fun getViewModel() = TransactionHistoryViewModel::class.java

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        searchListener = object : SearchView.OnQueryTextListener {
            override fun onQueryTextSubmit(query: String?) = false
            override fun onQueryTextChange(q: String?): Boolean {
                viewModel.search(q)
                if (q.safe()
                        .isNotEmpty() && (!isInSearchMode || historyListFragment.selectedItem >= 0)
                ) {
                    isInSearchMode = true
                    openEmptyHistoryDetail()
                    historyListFragment.selectedItem = -1
                } else if (q.isNullOrBlank()) {
                    isInSearchMode = false
                    viewModel.salesList.firstOrNull()?.takeIf { isUseTabLayout }?.let {
                        historyListFragment.selectedItem = 0
                        onItemSelected(it)
                    }
                }
                return true
            }
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        Bugsnag.leaveBreadcrumb(this::class.simpleName.safe())
        setHasOptionsMenu(true)
        Timber.i("#onViewCreated")

        if (savedInstanceState == null) {
            childFragmentManager.beginTransaction()
                .replace(R.id.fragment, historyListFragment)
                .commit()
        }

        view.findViewById<FrameLayout>(R.id.frameLayout)?.let {
            isUseTabLayout = true
            if (savedInstanceState == null) {
                openEmptyHistoryDetail()
            }
        }

        view.setupSnackbar(this, viewModel.snackbarText)
        initDialogFilter()
    }

    override fun onSharedPrefChanged(key: String) {
        if (key == SharedPref.EMPLOYEE_DATA) {
            historyListFragment.initFeatureAndRole()
        }
    }

    private fun openEmptyHistoryDetail() {
        if (isUseTabLayout) {
            Timber.i("openEmptyHistoryDetail...")
            childFragmentManager.beginTransaction()
                .replace(R.id.frameLayout, NoDataFragment(), DETAIL_TAG)
                .commit()
        }
    }

    fun onItemSelected(sales: SalesEntity) {
        if (isUseTabLayout) {
            val args = Bundle()
            args.putParcelable("data", sales)
            args.putBoolean("layout", isUseTabLayout)
            val historyDetailFragment = HistoryDetailFragment()
            historyDetailFragment.arguments = args
            childFragmentManager.beginTransaction()
                .replace(R.id.frameLayout, historyDetailFragment, DETAIL_TAG)
                .commit()
        } else {
            Timber.d("sales-history, tag: ${sales.salesTag}")
            val intent = Intent(context, HistoryDetailActivity::class.java)
            val salesRefund =
                viewModel.salesList.filter { it.status?.lowercase() == "refund" }.map { it.noNota }
            intent.putExtra("data", sales)
            intent.putExtra("sales_refund", salesRefund.toTypedArray())
            startActivity(intent)
        }
    }

    private fun initDialogFilter() {
        val vFilter = DialogFilterTransactionHistoryBinding.inflate(layoutInflater, null, false)
        vFilter.edtDate.setAsDateinput(childFragmentManager)
        val shifts = ArrayList<String>()
        vFilter.spinShift.setAdapter(
            ArrayAdapter(
                requireContext(),
                android.R.layout.simple_dropdown_item_1line,
                shifts
            )
        )
        vFilter.spinPayment.setAdapter(
            ArrayAdapter(
                requireContext(),
                android.R.layout.simple_list_item_1,
                listOf("Cash", "Card", "Piutang", "Compliment", "Duty Meal")
            )
        )

        val admin = context?.getJson(SharedPref.ADMIN_DATA, Admin::class.java)
        admin?.adminId?.let {
            viewModel.getShifts()
                .observe(viewLifecycleOwner, {
                    shiftList.clear()
                    it?.data?.let { shiftList.addAll(it) }
                    shifts.clear()
                    shiftList.forEach { shifts.add(it.name) }
                })
        }

        vFilter.btnFilter.setOnClickListener {
            when {
                vFilter.edtDate.value().isEmpty() -> toast("Tanggal wajib dipilih")
                vFilter.spinShift.text.toString().isEmpty() -> toast("Shift wajib dipilih")
                else -> {
                    val shift =
                        shiftList.firstOrNull { it.name == vFilter.spinShift.text.toString() }
                    shift?.let { shiftSelected ->
                        Firebase.analytics
                            .logEvent(
                                "app_features", bundleOf(
                                    "Outlet" to "FTH:${outlet.outletId}:${outlet.name}",
                                    "Feature" to "Filter Trans History"
                                )
                            )

                        historyListFragment.filter(
                            shiftSelected,
                            vFilter.edtDate.value(),
                            vFilter.spinPayment.text.toString(),
                            vFilter.swDiscount.isChecked,
                            vFilter.swPromotion.isChecked
                        )
                    }
                    dialogFilter.dismiss()
                }
            }
        }

        vFilter.btnResetFilter.setOnClickListener {
            vFilter.edtDate.setText("")
            vFilter.spinPayment.setText("")
            vFilter.spinShift.setText("")
            vFilter.swDiscount.isChecked = false
            vFilter.swPromotion.isChecked = false
            historyListFragment.loadTransactionHistory(isClearDataFirst = true)
            dialogFilter.dismiss()
        }

//        dialogFilter = AlertDialog.Builder(requireContext())
//            .setView(vFilter.root)
//            .create()
        dialogFilter = DynamicDialog(vFilter.root, context)

        dialogFilter.setOnShowListener {
            //if current history data is not empty, and user has not do a filter before,
            //then set field to current shift
            if (viewModel.salesList.isNotEmpty() && vFilter.edtDate.value().isEmpty()) {
                context?.getJson(SharedPref.SHIFT_DATA, ShiftOpen::class.java)
                    ?.let { currentShift ->
                        vFilter.edtDate.setText(currentShift.timeOpen.dateFormat())
                        viewModel.shiftList.firstOrNull { it.shiftId == currentShift.shiftFkid }
                            ?.let {
                                vFilter.spinShift.setText(it.name)
                            }
                    }
            }
        }
    }

    override fun onCreateOptionsMenu(menu: Menu, inflater: MenuInflater) {
        inflater.inflate(R.menu.menu_transaction_history, menu)
        searchView = menu.findItem(R.id.action_search)?.actionView as SearchView?
        searchListener?.let { searchView?.setOnQueryTextListener(it) }
        super.onCreateOptionsMenu(menu, inflater)
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        when (item.itemId) {
            R.id.action_filter -> dialogFilter.show()
        }
        return super.onOptionsItemSelected(item)
    }

}
