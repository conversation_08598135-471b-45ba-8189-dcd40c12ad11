package com.uniq.uniqpos.binding.setting


import android.content.Intent
import android.graphics.Color
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import com.bugsnag.android.Bugsnag
import com.uniq.uniqpos.R
import com.uniq.uniqpos.databinding.FragmentSettingV2Binding
import com.uniq.uniqpos.databinding.ListItemSettingBinding
import com.uniq.uniqpos.model.Setting
import com.uniq.uniqpos.util.Utils
import com.uniq.uniqpos.util.safe
import com.uniq.uniqpos.util.view.RecyclerItemClickListener
import com.uniq.uniqpos.view.global.GlobalAdapter
import com.uniq.uniqpos.view.global.GlobalViewHolder
import com.uniq.uniqpos.view.setting.printerserver.PrinterServerFragment
import com.uniq.uniqpos.view.setting.SettingDetailActivity
import com.uniq.uniqpos.view.setting.kitchendisplay.KitchenDisplaySettingFragment
import com.uniq.uniqpos.view.setting.printer.SettingPrinterFragment

/**
 * A simple [Fragment] subclass.
 */
class SettingFragment : Fragment() {

    lateinit var fragmnetManager: FragmentManager
    private var isTabLayout = false
    private var selectedItem = -1

    private val setPrinterFragment = SettingPrinterFragment()
    private val printerServerFragment = PrinterServerFragment()
    private val kitchenDisplayFragment: KitchenDisplaySettingFragment get() = KitchenDisplaySettingFragment()
    private lateinit var recviewSettingListener: RecyclerItemClickListener

    private var _binding: FragmentSettingV2Binding? = null
    private val binding get() = _binding!!

    val settings = listOf(
//                Setting(getString(R.string.layout_mode), R.drawable.ic_view_quilt_black_24dp, setPrinterFragment),
//                Setting(getString(R.string.product_position), R.drawable.ic_picture_in_picture_black_24dp, setPrinterFragment),
        Setting("Printer", R.drawable.ic_printer_orange, setPrinterFragment),
        Setting("Printer Server", R.drawable.ic_printer_server_orange, printerServerFragment),
        Setting("Kitchen Display", R.drawable.baseline_monitor_24, kitchenDisplayFragment)
//                Setting(getString(R.string.other), R.drawable.ic_all_inclusive_black_24dp, setPrinterFragment),
//                Setting(getString(R.string.account), R.drawable.ic_account_circle_black_24dp, setPrinterFragment),
//                Setting(getString(R.string.about), R.drawable.ic_info_outline_black_24dp, setPrinterFragment)
    )

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentSettingV2Binding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        Bugsnag.leaveBreadcrumb(this::class.simpleName.safe())
        fragmnetManager = childFragmentManager

        binding.root.findViewById<FrameLayout>(R.id.layoutContainer)?.let {
            isTabLayout = true
            selectedItem = 0
            fragmnetManager.beginTransaction().replace(R.id.layoutContainer, settings[0].fragment)
                .commit()
        }

        binding.recviewSetting.adapter =
            object : GlobalAdapter<ListItemSettingBinding>(R.layout.list_item_setting, settings) {
                override fun onBindViewHolder(
                    holder: GlobalViewHolder<ListItemSettingBinding>,
                    position: Int
                ) {
                    super.onBindViewHolder(holder, position)
                    if (isTabLayout) {
                        if (position == selectedItem) {
                            holder.binding.root.setBackgroundColor(
                                Utils.getColor(
                                    context,
                                    R.color.background_selected
                                )
                            )
                        } else {
                            holder.binding.root.setBackgroundColor(Color.TRANSPARENT)
                        }
                    }
                }
            }

        recviewSettingListener = RecyclerItemClickListener(context) { _, position ->
            //            val tmpSelected = selectedItem
            selectedItem = position
//            v.recviewSetting.adapter.notifyItemChanged(tmpSelected)
//            v.recviewSetting.adapter.notifyItemChanged(selectedItem)
            binding.recviewSetting.adapter?.notifyDataSetChanged()
            onItemSelected(position)
        }
        binding.recviewSetting.addOnItemTouchListener(recviewSettingListener)
    }

    private fun onItemSelected(position: Int) {
        if (isTabLayout) {
            fragmnetManager.beginTransaction()
                .replace(R.id.layoutContainer, settings[position].fragment).commit()
        } else {
            val intent = Intent(context, SettingDetailActivity::class.java)
            intent.putExtra("position", position)
            intent.putExtra("title", settings[position].title)
            startActivity(intent)
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

    override fun onDestroy() {
        recviewSettingListener.onDestroy()
        super.onDestroy()
    }
}
