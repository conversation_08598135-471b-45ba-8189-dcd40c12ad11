package com.uniq.uniqpos.view.productcatalogue

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.ImageButton
import androidx.constraintlayout.widget.ConstraintLayout
import com.uniq.uniqpos.R

/**
 * A custom view that wraps a field and adds a pin/unpin button
 */
class PinnableFieldView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr) {

    private val container: FrameLayout
    private val pinButton: ImageButton
    private var fieldId: String = ""
    private var isPinned: Boolean = false
    private var onPinStateChangedListener: ((String, Boolean) -> Unit)? = null

    init {
        val inflater = LayoutInflater.from(context)
        inflater.inflate(R.layout.item_pinnable_field, this, true)

        container = findViewById(R.id.field_container)
        pinButton = findViewById(R.id.btn_pin)

        pinButton.setOnClickListener {
            togglePinState()
        }
    }

    /**
     * Set the field to be wrapped by this view
     */
    fun setField(field: View, fieldId: String) {
        this.fieldId = fieldId
        
        // Remove the field from its parent if it has one
        val parent = field.parent as? ViewGroup
        parent?.removeView(field)
        
        // Add the field to our container
        container.addView(field)
    }

    /**
     * Set the pin state of this field
     */
    fun setPinned(isPinned: Boolean) {
        this.isPinned = isPinned
        updatePinButtonAppearance()
    }

    /**
     * Toggle the pin state of this field
     */
    private fun togglePinState() {
        isPinned = !isPinned
        updatePinButtonAppearance()
        onPinStateChangedListener?.invoke(fieldId, isPinned)
    }

    /**
     * Update the appearance of the pin button based on the current pin state
     */
    private fun updatePinButtonAppearance() {
        pinButton.setImageResource(
            if (isPinned) android.R.drawable.btn_star_big_on
            else android.R.drawable.btn_star_big_off
        )
    }

    /**
     * Set a listener to be notified when the pin state changes
     */
    fun setOnPinStateChangedListener(listener: (String, Boolean) -> Unit) {
        onPinStateChangedListener = listener
    }
} 