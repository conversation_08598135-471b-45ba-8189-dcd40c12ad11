package com.uniq.uniqpos.view.chooseoperator

import android.content.Intent
import android.os.Bundle
import android.view.Menu
import android.view.MenuItem
import androidx.recyclerview.widget.RecyclerView
import com.bugsnag.android.Bugsnag
import com.uniq.uniqpos.BuildConfig
import com.uniq.uniqpos.R
import com.uniq.uniqpos.data.local.entity.EmployeeEntity
import com.uniq.uniqpos.data.local.sharedpref.SharedPref
import com.uniq.uniqpos.data.local.sharedpref.getJson
import com.uniq.uniqpos.data.local.sharedpref.getLocalDataString
import com.uniq.uniqpos.data.remote.model.Outlet
import com.uniq.uniqpos.databinding.ActivityChooseOperatorBinding
import com.uniq.uniqpos.databinding.ListItemOperatorBinding
import com.uniq.uniqpos.model.Admin
import com.uniq.uniqpos.model.SubscriptionStatus
import com.uniq.uniqpos.util.*
import com.uniq.uniqpos.util.view.RecyclerItemClickListener
import com.uniq.uniqpos.view.billing.BillingActivity
import com.uniq.uniqpos.view.global.BaseActivity
import com.uniq.uniqpos.view.global.GlobalAdapter
import com.uniq.uniqpos.view.global.GlobalViewHolder
import com.uniq.uniqpos.view.verifypin.VerifyPINActivity
import com.uniq.uniqpos.viewmodel.AuthViewModel
import timber.log.Timber

class ChooseOperatorActivity : BaseActivity<AuthViewModel, ActivityChooseOperatorBinding>() {

    private var outletID: Int = 0
    private val employeeList = ArrayList<EmployeeEntity>()
    private var subscriptionUrl: String? = null

    override fun getLayoutRes() = R.layout.activity_choose_operator
    override fun getViewModel() = AuthViewModel::class.java

    override fun onCreate(savedInstanceState: Bundle?) {
        isSubscribeToSharedPref = true
        super.onCreate(savedInstanceState)
        window.setBackgroundDrawable(null)

        viewModel.refreshSubscription()
        val outlet = getJson(SharedPref.OUTLET_DATA, Outlet::class.java)
        outletID = outlet?.outletId.safe()
        title = outlet?.name ?: "Login Operator"

        binding.recView.adapter = object :
            GlobalAdapter<ListItemOperatorBinding>(R.layout.list_item_operator, employeeList) {
            override fun onBindViewHolder(
                holder: GlobalViewHolder<ListItemOperatorBinding>,
                position: Int
            ) {
                super.onBindViewHolder(holder, position)
                holder.binding.txtPositionName.text = viewModel.employeePositionList.firstOrNull {
                    it.jabatanId == employeeList.getSafe(position)?.jabatanFkid
                }?.name ?: "Kasir"
            }
        }

        binding.recView.addOnItemTouchListener(RecyclerItemClickListener(this) { _, position ->
            var intent = Intent(this, VerifyPINActivity::class.java)
            intent.putExtra("employeeName", employeeList[position].name)
            intent.putExtra("id", employeeList[position].employeeId)
            startActivity(intent)
        })

        binding.recView.addOnScrollListener(object : RecyclerView.OnScrollListener() {
            override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                if (dy > 0) {
//                    binding.fabAddEmployee.shrink()
                } else {
//                    binding.fabAddEmployee.extend()
                }
            }
        })

        binding.btnSubscribe.setOnClickListener { launchUrl(subscriptionUrl) }
        binding.imgClose.setOnClickListener { initSubscriptionWarning(false) }
        binding.fabAddEmployee.setOnClickListener {
//            launchUrl(BuildConfig.WEB_URL + "employees/crewlist")
            startActivity(Intent(this, BillingActivity::class.java))
        }

        binding.swipeRefresh.setOnRefreshListener {
            loadEmployees()
            viewModel.refreshSubscription()
        }

        binding.swipeRefresh.isRefreshing = true
        logUser()
        refreshList()
        loadEmployees()
        getSubscriptionStatus()
    }

    private fun logUser() {
        val admin = getJson(SharedPref.ADMIN_DATA, Admin::class.java)
        val outlet = outlet()
        Bugsnag.setUser(
            "${admin?.adminId}:${outlet?.outletId}:${
                getLocalDataString(
                    SharedPref.DEVICE_ID,
                    "-"
                )
            }",
            admin?.email, admin?.name
        )
    }

    override fun observeTask() {
        viewModel.subscriptionRefresh.observe(this) {
            getSubscriptionStatus()
        }
    }

    override fun onSharedPrefChanged(key: String) {
        if (key == SharedPref.SUBSCRIPTION_STATUS) {
            getSubscriptionStatus()
        }
    }

    private fun getSubscriptionStatus() {
        val subscription = getJson(SharedPref.SUBSCRIPTION_STATUS, SubscriptionStatus::class.java)
        Timber.d("subscription: $subscription")
        subscription?.takeIf { it.timeExpiredCount <= 7 && !it.subscribed }?.let { subscription ->
            var status = "Free Trial"
            subscriptionUrl = subscription.subscriptionUrl
            if (subscription.subscriptionType == "subscription") {
                binding.btnSubscribe.text = "PERPANJANG LANGGANAN"
                status = "Berlangganan"
            }
            binding.txtWarning.text = if (subscription.timeExpiredCount > 0) {
                "Masa $status anda akan segera berakhir dalam waktu ${subscription.timeExpiredCount} hari"
            } else {
                "Masa $status anda telah berakhir. Mulai berlangganan sekarang untuk terus menikmati kemudahan dalam berbisnis"
            }
            Timber.i(binding.txtWarning.text.toString())
        } ?: run { initSubscriptionWarning(false) }
    }

    private fun initSubscriptionWarning(isShow: Boolean) {
        binding.btnSubscribe.visibility = isShow.visibility()
        binding.txtWarning.visibility = isShow.visibility()
        binding.imgClose.visibility = isShow.visibility()
    }

    private fun loadEmployees() {
        viewModel.loadEmployeePosition()

        viewModel.getEmployeeList(outletID).observe(this) { resource ->
            resource?.data?.let { data ->
                data.forEach { it.name = it.name?.upperCaseWord() }
                refreshList(data)
                viewModel.storeEmployeeList(data)
                binding.swipeRefresh.isRefreshing = false
            }
        }
    }

    private fun refreshList(data: List<EmployeeEntity>? = null) {
        employeeList.clear()
        data?.let {
            employeeList.addAll(it)
        } ?: kotlin.run {
//            if (outletFeature().offlineMode)
            employeeList.addAll(employeeList())
        }
        employeeList.sortBy { it.name }
        binding.recView.adapter?.notifyDataSetChanged()
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        if (item.itemId == R.id.action_add_employee) {
            launchUrl(BuildConfig.WEB_URL + "employees/crewlist")
        }
        return super.onOptionsItemSelected(item)
    }

    override fun onCreateOptionsMenu(menu: Menu): Boolean {
        menuInflater.inflate(R.menu.menu_choose_operator, menu)
        return super.onCreateOptionsMenu(menu)
    }
}
