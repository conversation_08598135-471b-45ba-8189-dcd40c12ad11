package com.uniq.uniqpos.view.ordersales

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import com.google.gson.Gson
import com.uniq.uniqpos.R
import com.uniq.uniqpos.data.local.entity.OrderSalesEntity
import com.uniq.uniqpos.databinding.FragmentOrderSalesListBinding
import com.uniq.uniqpos.databinding.ListItemOrderSalesBinding
import com.uniq.uniqpos.model.BottomDialogModel
import com.uniq.uniqpos.model.OrderOnlineItem
import com.uniq.uniqpos.util.safe
import com.uniq.uniqpos.util.setVisible
import com.uniq.uniqpos.util.showMessage
import com.uniq.uniqpos.view.global.BottomDialogInput
import com.uniq.uniqpos.view.global.GlobalAdapter
import com.uniq.uniqpos.view.global.GlobalViewHolder
import timber.log.Timber

/**
 * A simple [Fragment] subclass.
 *
 */
class OrderSalesListFragment : Fragment() {

    companion object {
        val KEY_STATUS = "order_status"
        fun newInstance(status: String, listener: OrderSalesListener): OrderSalesListFragment {
            val bundle = Bundle()
            bundle.putString(KEY_STATUS, status.lowercase())
            val fragment = OrderSalesListFragment()
            fragment.setListener(listener)
            fragment.arguments = bundle
            return fragment
        }

    }

    private var orderStatus = ""

    private val orderList = ArrayList<OrderSalesEntity>()
    private var orderListener: OrderSalesListener? = null
    private var _binding: FragmentOrderSalesListBinding? = null
    private val binding get() = _binding!!
    private val viewModel: OrderSalesViewModel get() = (activity as OrderSalesActivity).viewModel

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?,
                              savedInstanceState: Bundle?): View {
        arguments?.apply {
            orderStatus = getString(KEY_STATUS).safe()
        }
        _binding = FragmentOrderSalesListBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        binding.recviewOrder.adapter = object : GlobalAdapter<ListItemOrderSalesBinding>(R.layout.list_item_order_sales, orderList, binding.incLayNoData.root) {
            override fun onBindViewHolder(holder: GlobalViewHolder<ListItemOrderSalesBinding>, position: Int) {
                super.onBindViewHolder(holder, position)
                val updateStatuses = viewModel.getUpdateStatusAvailable(orderStatus,  orderList[position].orderType)
                updateStatuses.forEachIndexed { index, status ->
                    if(index == 0) {
                        holder.binding.btnOne.text = status
                        holder.binding.btnOne.setVisible(true)
                    }else {
                        holder.binding.btnTwo.text = status
                        holder.binding.btnTwo.setVisible(true)
                    }
                }
                holder.binding.btnOne.setOnClickListener { updateStatus(orderList[holder.adapterPosition].orderSalesId) }
                holder.binding.btnTwo.setOnClickListener { updateStatus(orderList[holder.adapterPosition].orderSalesId,"reject") }
                holder.binding.viewParent.setOnClickListener { orderListener?.onItemSelected(orderList[holder.adapterPosition]) }
            }
        }

        refreshItem()
        observeTask()
    }

    private fun observeTask() {

    }

    private fun updateStatus(orderId: String, status: String? = null) {
        val status = status ?: when (orderStatus) {
            "pending" -> "accept"
            "accept" -> "ready"
            "payment_verified" -> "ready"
            else -> "taken"
        }
        Timber.i("current page : $orderStatus || update to -> $status")

        when (status) {
            "reject" -> {
                showRejectReasonDialog(orderId)
            }
//            "taken" -> {
////                validateTakeOrder(orderId)
//            }
            else -> {
                val msg = "apakah kamu yakin untuk mengubah status transaksi $orderId ke $status?"
                context?.showMessage(msg, getString(R.string.confirmation),
                    { _, _ ->
                        orderListener?.apply { updateStatus(orderId, status) }
                    })
            }
        }
    }

    private fun validateTakeOrder(orderId: String) {
        val bottomDialog = BottomDialogInput()
        bottomDialog.setUseBarcode(true)
        bottomDialog.setOnButtonClick { result ->
            if(result.isNotBlank()){
                orderListener?.apply {
                    val order = getOrderSales().first { it.orderSalesId == orderId }
                    val sales = Gson().fromJson(order.items, OrderOnlineItem::class.java)
                    when {
                        order.orderSalesId.isEmpty() -> { // sales.orderId.isEmpty()
                            Timber.i("this order has no order_id: ${order.items}")
                            context?.showMessage("Ops! order ini tidak valid! tidak dapat melanjutkan proses")
                        }
                        sales.orderId == result -> {
                            updateStatus(orderId, "taken", result)
                            bottomDialog.dismiss()
                        }
                        else -> {
                            bottomDialog.setError("order id tidak valid!")
                        }
                    }
                }
            }
        }
        bottomDialog.setModel(BottomDialogModel("TAKE ORDER", "SAVE",  null, "Masukan order Id untuk melanjutkan", "order id"))
        bottomDialog.show(childFragmentManager, "take-order")
    }

    private fun showRejectReasonDialog(orderId: String) {
        val bottomDialog = BottomDialogInput()
        bottomDialog.setOnButtonClick { result ->
            if(result.isNotBlank()){
                orderListener?.apply { updateStatus(orderId, "reject", result) }
                bottomDialog.dismiss()
            }
        }
        bottomDialog.setModel(BottomDialogModel("REJECT ORDER", "SAVE", "Reject Reason", "Give a reason why this order is rejected", "Reason"))
        bottomDialog.show(childFragmentManager, "reject-order")
    }

    fun refreshItem() {
        orderList.clear()
        orderListener?.apply {
            orderList.addAll(getOrderSales().filter { it.status == orderStatus })
            Timber.i("size orderSales of '$orderStatus' : ${orderList.size}")
        }
        _binding?.recviewOrder?.adapter?.notifyDataSetChanged()
    }

    fun setListener(orderListener: OrderSalesListener) {
        this.orderListener = orderListener
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
