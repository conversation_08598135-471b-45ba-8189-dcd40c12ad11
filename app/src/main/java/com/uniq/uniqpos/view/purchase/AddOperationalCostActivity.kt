package com.uniq.uniqpos.view.purchase

import android.content.Intent
import android.os.Bundle
import android.view.MenuItem
import android.view.View
import android.widget.ArrayAdapter
import androidx.lifecycle.Observer
import com.bugsnag.android.Bugsnag
import com.google.firebase.analytics.FirebaseAnalytics
import com.google.gson.Gson
import com.uniq.uniqpos.R
import com.uniq.uniqpos.data.local.entity.OperationalCostEntity
import com.uniq.uniqpos.data.local.entity.PendingPrintEntity
import com.uniq.uniqpos.data.local.sharedpref.SharedPref
import com.uniq.uniqpos.data.local.sharedpref.getJson
import com.uniq.uniqpos.data.remote.model.ShiftOpen
import com.uniq.uniqpos.databinding.ActivityAddOperationalCostBinding
import com.uniq.uniqpos.model.BottomDialogModel
import com.uniq.uniqpos.util.*
import com.uniq.uniqpos.util.lifecycle.setupSnackbar
import com.uniq.uniqpos.util.printer.PrinterSocket
import com.uniq.uniqpos.util.receipt.PrintNotaUtil
import com.uniq.uniqpos.view.global.BaseActivity
import com.uniq.uniqpos.view.global.BottomDialogInput
import com.weiwangcn.betterspinner.library.material.MaterialBetterSpinner
import timber.log.Timber

class AddOperationalCostActivity : BaseActivity<OperationalCostViewModel, ActivityAddOperationalCostBinding>() {

    private lateinit var supplierAdapter: ArrayAdapter<String>
    private lateinit var bankAdapter: ArrayAdapter<String>
    private lateinit var purchaseCategoryAdapter: ArrayAdapter<String>
    private lateinit var opCostNameAdapter: ArrayAdapter<String>
    private lateinit var addNew: String

    private val RC_ADD_SUPPLIER = 10

    override fun getLayoutRes() = R.layout.activity_add_operational_cost
    override fun getViewModel() = OperationalCostViewModel::class.java

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        viewModel.loadOperationalCostNames()
    }

    override fun initView() {
        super.initView()

        addNew = getString(R.string.add_new)

        supplierAdapter = ArrayAdapter(this, android.R.layout.simple_list_item_1, arrayListOf(addNew))
        bankAdapter = ArrayAdapter(this, android.R.layout.simple_list_item_1, arrayListOf(addNew))
        purchaseCategoryAdapter = ArrayAdapter(this, android.R.layout.simple_list_item_1, arrayListOf(addNew))
        opCostNameAdapter = ArrayAdapter(this, android.R.layout.simple_list_item_1, arrayListOf())

        binding.spinPayment.setAdapter(ArrayAdapter(this, android.R.layout.simple_list_item_1, arrayListOf("Cash", "Card")))
        binding.spinSupplier.setAdapter(supplierAdapter)
        binding.spinBank.setAdapter(bankAdapter)
        binding.spinCategory.setAdapter(purchaseCategoryAdapter)
        binding.spinPayment.setText("Cash")
        binding.edtName.setAdapter(opCostNameAdapter)

        binding.spinBank.visibility = View.GONE
        binding.spinPayment.setOnItemClickListener { _, _, position, _ ->
            binding.spinBank.visibility = if (position == 1) View.VISIBLE else View.GONE
        }

        binding.edtQty.liveToCurrencyAndWatch { countGrandTotal() }
        binding.edtPrice.liveToCurrencyAndWatch { countGrandTotal() }

        binding.btnSave.setOnClickListener { saveData() }
        binding.spinSupplier.setOnItemClickListener { _, _, _, _ ->
            if (binding.spinSupplier.value() == addNew) {
                binding.spinSupplier.setText("")
                startActivityForResult(Intent(this, AddSupplierActivity::class.java), RC_ADD_SUPPLIER)
            }
        }

        binding.spinCategory.setOnItemClickListener { _, _, _, _ ->
            if (binding.spinCategory.value() == addNew) {
                binding.spinCategory.setText("")
                addPurchaseReportCategory(binding.spinCategory)
            }
        }

        binding.edtName.setOnItemClickListener { _, _, _, _ -> applySuggestion() }
        viewModel.loadOperationalCostHistory()
    }

    private fun applySuggestion() {
        viewModel.operationalCostHistory.firstOrNull { it.opcostName.toLower().contains(binding.edtName.text.toString().toLower()) }?.let { opcost ->
            binding.spinSupplier.setText(opcost.supplierName)
            binding.spinCategory.setText(opcost.purchaseCategoryName)
            binding.edtPrice.setText(opcost.harga.toCurrency())
        }
        binding.edtQty.requestFocus()
    }

    private fun saveData() {
        //validate input
        if (!Utils.isValidField(binding.edtName, binding.edtPrice, binding.spinSupplier, binding.spinCategory, binding.spinBank)) {
            toast("Please fill all field!")
            return
        }

        showDialog(true)
        val supplierId = viewModel.supplierList.firstOrNull { it.name == binding.spinSupplier.value() }?.supplierId
        val categoryId = viewModel.purchaseCategoryList.firstOrNull { it.name == binding.spinCategory.value() }?.purchaseReportCategoryId
        val bankId = viewModel.bankList.firstOrNull { it.name == binding.spinBank.value() }?.bankId
        val qty = if (binding.edtQty.value().trim().isEmpty()) 1 else binding.edtQty.value().fromCurrency()

        if (supplierId == null || categoryId == null) {
            toast("Please fill all field!")
            Timber.i("[WEIRD] some of these ids is null -> supplierId '$supplierId' | categoryId '$categoryId'")
            showDialog(false)
            return
        }

        val oc = OperationalCostEntity()
        oc.opcostName = binding.edtName.value()
        oc.qty = qty
        oc.harga = binding.edtPrice.value().fromCurrency()
        oc.keterangan = binding.edtKet.value()
        oc.payment = binding.spinPayment.value().toLower()
        oc.supplierFkid = supplierId
        oc.prcCategoryFkid = categoryId
        oc.shiftFkid = getJson(SharedPref.SHIFT_DATA, ShiftOpen::class.java)?.shiftFkid?.toString().safe()
        oc.outletFkid = outlet()?.outletId.safe()
        oc.employeFkid = employee()?.employeeId
        oc.total = binding.txtGrandtotal.text.toString().fromCurrency()
        oc.timeCreated = System.currentTimeMillis()
        oc.supplierName = binding.spinSupplier.value()

        if (oc.payment.toLower() == "card") {
            oc.paymentBankFkid = bankId?.toString()
            oc.bankName = binding.spinBank.value()
        }

        Timber.i("saving operational cost : ${Gson().toJson(oc)}")
        Bugsnag.leaveBreadcrumb("saving operational cost, with print option checked : ${binding.cbPrint.isChecked}")

        viewModel.saveOperationalCost(oc, binding.cbPrint.isChecked)

        val outlet = outlet()
        FirebaseAnalytics.getInstance(this)
                .logEvent("app_features",
                        bundleOf("Outlet" to "AOC:${outlet?.outletId}:${outlet?.name}",
                                "Feature" to "Add Operational Cost"))
    }

    private fun countGrandTotal() {
        var qty = binding.edtQty.value().fromCurrency()
        val price = binding.edtPrice.value().fromCurrency()
        if (binding.edtQty.value().isEmpty()) {
            qty = 1
        }
        binding.txtGrandtotal.text = (price * qty).toCurrency()
    }

    override fun observeTask() {
        binding.root.setupSnackbar(this, viewModel.snackbarText)


        viewModel.taskSave.observe(this, Observer {
            Bugsnag.leaveBreadcrumb("save operational cost success")
            showDialog(false)
            resetView()
//            setResult(Activity.RESULT_OK)
//            finish()
        })

        viewModel.taskShowDialog.observe(this, { message ->
            Bugsnag.leaveBreadcrumb("show alert dialog")
            showDialog(false)
            if (message is Exception) {
                showAlert(message.readableError(this), "ERROR")
            } else if (message is String) {
                showAlert(message, "ERROR")
            }
        })

        viewModel.taskShowProgressDialog.observe(this, Observer { isShow ->
            showDialog(isShow ?: false)
        })

        viewModel.taskGeneratePrintFormat.observe(this, Observer { task ->
            task?.second?.firstOrNull()?.let { printer ->
                val printFormat = PrintNotaUtil.getOperationalCostFormat(task.first, outlet()!!, employee()!!)
                val printData = PendingPrintEntity(printer.address, printer.type, printFormat, printer.name, System.currentTimeMillis())
                toast("Printing...")
                Bugsnag.leaveBreadcrumb("print operational cost")
                managePrintWifi(listOf(printData), object : PrinterSocket.SocketListener {
                    override fun onConnectionFinish(isConnected: Boolean, message: String?) {
                        showDialog(false)
//                        setResult(Activity.RESULT_OK)
//                        Timber.i("add operational cost finish...")
//                        finish()
                    }
                })
            }
        })

        viewModel.taskRefreshOpCostNames.observe(this, Observer { data ->
            opCostNameAdapter.clear()
            data.forEach { opCostNameAdapter.add(it) }
        })
    }

    override fun observeData() {
        viewModel.supplierLive.observe(this, { resource ->
            resource?.data?.let { items ->
                viewModel.supplierList.clear()
                viewModel.supplierList.addAll(items)
                supplierAdapter.clear()
                items.forEach { supplier -> supplierAdapter.add(supplier.name) }
                supplierAdapter.add(addNew)
                binding.spinCategory.requestFocus()
            }
        })

        viewModel.getBankLive(outlet()?.outletId).observe(this, { resource ->
            resource?.data?.let { items ->
                viewModel.bankList.clear()
                viewModel.bankList.addAll(items)
                bankAdapter.clear()
                items.forEach { bank -> bankAdapter.add(bank.name) }
            }
        })

        viewModel.purcaseCategoryLive.observe(this, Observer { resource ->
            resource?.data?.let { items ->
                viewModel.purchaseCategoryList.clear()
                viewModel.purchaseCategoryList.addAll(items)
                purchaseCategoryAdapter.clear()
                items.forEach { category -> purchaseCategoryAdapter.add(category.name) }
                purchaseCategoryAdapter.add(addNew)
                binding.spinSupplier.requestFocus()
            }
        })

//        viewModel.uniqueOpCostNameLive.observe(this, Observer { items ->
//            items?.let { names ->
//                opCostNameAdapter.clear()
//                names.forEach { opCostNameAdapter.add(it) }
//            }
//        })
    }

    private fun addPurchaseReportCategory(view: MaterialBetterSpinner) {
        val bottomDialog = BottomDialogInput()
        bottomDialog.setOnButtonClick { result ->
            if (result == addNew || result.isBlank()) {
                toast("Value is not allowed!")
            } else {
                val adapter = view.adapter as ArrayAdapter<String>
                adapter.remove(addNew)
                adapter.add(result)
                adapter.add(addNew)
                view.setText(result)

                bottomDialog.dismiss()

                viewModel.addPurchaseReportCategory(result)
                binding.spinSupplier.requestFocus()
            }
        }
        bottomDialog.setModel(BottomDialogModel("ADD NEW", "SAVE", view.hint.toString(), hint = view.hint.toString()))
        bottomDialog.show(supportFragmentManager, "add-new")
    }

    private fun resetView() {
        binding.edtName.text = null
        binding.edtQty.text = null
        binding.edtPrice.text = null
        binding.edtKet.text = null
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        finish()
        return super.onOptionsItemSelected(item)
    }
}
