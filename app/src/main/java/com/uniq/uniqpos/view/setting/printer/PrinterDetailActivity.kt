package com.uniq.uniqpos.view.setting.printer

import android.os.Bundle
import android.view.*
import android.widget.ArrayAdapter
import androidx.lifecycle.Observer
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.uniq.uniqpos.BuildConfig
import com.uniq.uniqpos.R
import com.uniq.uniqpos.data.local.entity.PrinterEntity
import com.uniq.uniqpos.data.local.sharedpref.SharedPref
import com.uniq.uniqpos.data.local.sharedpref.getLocalDataString
import com.uniq.uniqpos.data.local.sharedpref.putJson
import com.uniq.uniqpos.data.local.sharedpref.sharedPref
import com.uniq.uniqpos.databinding.ListItemPrintTicketDetailBinding
import com.uniq.uniqpos.model.BottomDialogModel
import com.uniq.uniqpos.util.*
import com.uniq.uniqpos.view.global.BaseActivity
import com.uniq.uniqpos.view.global.BottomDialogInput
import com.uniq.uniqpos.view.global.GlobalAdapter
import com.uniq.uniqpos.view.global.GlobalViewHolder
import com.uniq.uniqpos.view.setting.PrinterSettingViewModel
import timber.log.Timber

class PrinterDetailActivity :
    BaseActivity<PrinterSettingViewModel, com.uniq.uniqpos.databinding.ActivityPrinterDetailBinding>() {

    override fun getLayoutRes() = R.layout.activity_printer_detail
    override fun getViewModel() = PrinterSettingViewModel::class.java

    private lateinit var printerDetailFragment: PrinterDetailFragment

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)

        val printer: PrinterEntity = intent.getParcelableExtra("data")!!
        printerDetailFragment = PrinterDetailFragment.newInstance(printer)

        supportFragmentManager.beginTransaction()
            .replace(R.id.container, printerDetailFragment)
            .commit()

        listOf(binding.txtEditOnWeb, binding.txtEditOnWebInfo).forEach { view ->
            view.setOnClickListener { launchUrl(BuildConfig.WEB_URL + "outlets/printer") }
        }
    }

    private fun showCustomPortInput(){
        val port = this.sharedPref().getString(SharedPref.PRINTER_PORT, Constant.PRINTER_PORT.toString()).safeToInt(Constant.PRINTER_PORT)
        val bottomDialog = BottomDialogInput()
        bottomDialog.setOnButtonClick { result ->
            if (result.isNumber()){
                this.sharedPref.putData(SharedPref.PRINTER_PORT, result)
                bottomDialog.dismiss()
                toast("port changed to $result")
            }else{
                toast("please type a valid number port")
            }
        }

        bottomDialog.setModel(
            BottomDialogModel(
                "Custom Printer Port",
                "SAVE",
                "change the printer port from using the default one, current port: $port",
                hint = "type port, example 9100"
            )
        )
        bottomDialog.show(supportFragmentManager, "change-port")
    }

    override fun onCreateOptionsMenu(menu: Menu): Boolean {
        menuInflater.inflate(R.menu.menu_detail_printer, menu)
        return super.onCreateOptionsMenu(menu)
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        if (item.itemId == R.id.action_save) {
            if (printerDetailFragment.saveChange()) {
                finish()
            }
        } else if (item.itemId == R.id.action_custom_port){
            showCustomPortInput()
        } else {
            finish()
        }
        return super.onOptionsItemSelected(item)
    }

}
