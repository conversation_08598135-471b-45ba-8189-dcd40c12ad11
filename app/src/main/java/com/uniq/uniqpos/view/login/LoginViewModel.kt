package com.uniq.uniqpos.view.login

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.bugsnag.android.Bugsnag
import com.uniq.uniqpos.BuildConfig
import com.uniq.uniqpos.R
import com.uniq.uniqpos.data.local.sharedpref.SharedPref
import com.uniq.uniqpos.data.local.sharedpref.putJson
import com.uniq.uniqpos.data.remote.model.AuthCodeResponse
import com.uniq.uniqpos.data.remote.model.Outlet
import com.uniq.uniqpos.data.remote.repository.AuthRepository
import com.uniq.uniqpos.model.Account
import com.uniq.uniqpos.model.AuthToken
import com.uniq.uniqpos.model.LoginAdmin
import com.uniq.uniqpos.model.MultiAccount
import com.uniq.uniqpos.util.*
import com.uniq.uniqpos.util.lifecycle.*
import kotlinx.coroutines.launch
import retrofit2.await
import timber.log.Timber
import java.util.*
import javax.inject.Inject

class LoginViewModel @Inject constructor(
    private val authRepository: AuthRepository,
    private val sharedPref: SharedPref
) : BaseViewModel() {

    var userName = MutableLiveData<String>()
    var password = MutableLiveData<String>()

    var userNameErr = MutableLiveData<String>()

    private val _checkPermission = MutableLiveData<Event<Unit>>()
    val checkPermission: LiveData<Event<Unit>>
        get() = _checkPermission

    private  val _multiAccount = MutableLiveData<Event<MultiAccount>>()
    val multiAccount: LiveData<Event<MultiAccount>>
    get() = _multiAccount

    private val _requestClearData = MutableLiveData<Event<Unit>>()
    val requestClearData: LiveData<Event<Unit>> get() = _requestClearData

    private val _authCodeSent = MutableLiveData<Event<AuthCodeResponse>>()
    val authCodeSent: LiveData<Event<AuthCodeResponse>> get() = _authCodeSent

    var firebaseToken: String? = null

    fun processLogin() {
        Timber.i("process login")
        if (userName.value.isNullOrBlank() && password.value.isNullOrBlank()) {
            Timber.i("username or password is empty")
            return
        }

//        if (!userName.value.isValidEmail()) {
//            userNameErr.value = "invalid email address"
//            return
//        }

        if (isValidInput() == null) {
            Timber.i("valid input")
            _checkPermission.value = Event(Unit)
        }
//        _checkPermission.value = Event(Unit)
    }

    private fun isValidInput(): Throwable? {
        if (userName.value.isNullOrBlank() || password.value.isNullOrBlank()) {
            return WarnException("email or password can not be blank")
        }

        if (!userName.value.isValidEmail()) {
            userNameErr.value = "invalid email address"
            return WarnException("invalid email address")
        }
        return null
    }

    fun loginAdmin() {
        Bugsnag.leaveBreadcrumb("login email as ${userName.value}")
        if(BuildConfig.DEBUG){
            _toastMsg.postValue(Event("login as ${userName.value}"))
//            return
        }
        viewModelScope.launch {
            try {
                isValidInput()?.let { throw it }
                Bugsnag.leaveBreadcrumb("time: ${Date().dateTimeFormat()}")
                _state.value = Loading(true)

//                val auth = AuthToken(token = getString(R.string.authorization).decrypt())
//                sharedPref.putJson(SharedPref.TOKEN_DATA, auth)

                val resp = authRepository.loginAdminCall(userName.value!!, password.value!!, firebaseToken).await()
                resp.data?.takeIf { resp.status }?.let { data ->
                    saveLoginSession(data)
                } ?: let {
                    _state.value = Failed(
                        WarnException(
                            resp.message
                                ?: "We encounter some unknown problem. Please try again later"
                        )
                    )
                }
            } catch (e: Exception) {
                _state.value = Failed(e)
            }
            _state.value = Loading(false)
        }
    }

    private fun saveLoginSession(data: LoginAdmin){
        sharedPref.saveJson(SharedPref.TOKEN_DATA, data.authorization)

        if(data.multiAccount?.enableMultiAccount.safe()){
            _multiAccount.postValue(Event(data.multiAccount!!))
        }else{
            sharedPref.saveJson(SharedPref.ADMIN_DATA, data.user)
            sharedPref.putData(SharedPref.LOGIN_ADMIN_STATUS, true)

            //if previous data exist, but adminId is different, clear all data
            var shouldClearData = false
            sharedPref.getJson(SharedPref.OUTLET_DATA, Outlet::class.java)
                ?.takeIf { it.outletId.safe() > 0 }?.let { outlet ->
                    Timber.i("outlet id exist (${outlet.outletId}) - adminId: ${outlet.adminFkid} | new adminId: ${data.user.adminId}")
                    if (outlet.adminFkid != data.user.adminId) {
//                                sharedPref.clearJson(SharedPref.OUTLET_DATA)
                        _requestClearData.postValue(Event(Unit))
                        shouldClearData = true
                    }
                }

            if (!shouldClearData) {
                _state.value = Success
            }
        }
    }

    fun chooseMultiAccount(account: Account) {
        viewModelScope.launch {
            _state.value = Loading(true)
            _multiAccount.value?.peekContent()?.let { multiAccount ->
                val result = authRepository.chooseMultiAccount(account.adminId, multiAccount.token).await()
                result.data?.takeIf { result.status }?.let { data ->
                    saveLoginSession(data)
                } ?: kotlin.run {
                    _toastMsg.postValue(Event("login with multiple account failed, please try again later!"))
                }
            } ?: kotlin.run {
                _toastMsg.postValue(Event("can not logged in to multi account, try to login again!"))
            }
            _state.value = Loading(false)
        }
    }

    fun sendAuthCode(email: String, deviceInfo: String = "") {
        viewModelScope.launch {
            _state.value = Loading(true)
            try {
                val result = authRepository.sendAuthCode(email, deviceInfo).await()
                result.takeIf { it.status }?.data?.let {
                    _toastMsg.postValue(Event("code sent to $email"))
                    it.email = email
                    _authCodeSent.postValue(Event(it))
                } ?: run {
                    _toastMsg.postValue(Event(result.message ?: "failed, try again later!"))
                }
            } catch (e: Exception) {
                _toastMsg.postValue(Event(e.readableError()))
            }
            _state.value = Loading(false)
        }
    }

    fun loginWithCode(authCodeResponse: AuthCodeResponse, code: String) {
        if (code.isEmpty()) {
            _toastMsg.postValue(Event("code can not be empty"))
            return
        }

        viewModelScope.launch {
            _state.value = Loading(true)
            val result = authRepository.loginWithCode(authCodeResponse.token, code).await()
            result.takeIf { it.status }?.data?.let { data ->
                saveLoginSession(data)
            } ?: let {
                _state.value = Failed(
                    WarnException(
                        result.message
                            ?: "We encounter some unknown problem. Please try again later"
                    )
                )
            }
            _state.value = Loading(false)
        }
    }
}