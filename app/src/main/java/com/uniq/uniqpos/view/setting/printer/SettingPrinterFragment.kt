package com.uniq.uniqpos.view.setting.printer


import android.Manifest
import android.app.AlertDialog
import android.bluetooth.BluetoothAdapter
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Build
import android.os.Bundle
import android.view.View
import android.widget.TextView
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.widget.PopupMenu
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import androidx.lifecycle.Observer
import androidx.lifecycle.lifecycleScope
import com.google.firebase.ktx.Firebase
import com.google.firebase.remoteconfig.FirebaseRemoteConfigSettings
import com.google.firebase.remoteconfig.ktx.remoteConfig
import com.uniq.uniqpos.BuildConfig
import com.uniq.uniqpos.R
import com.uniq.uniqpos.data.local.entity.PendingPrintEntity
import com.uniq.uniqpos.data.local.entity.PrinterEntity
import com.uniq.uniqpos.data.local.sharedpref.SharedPref
import com.uniq.uniqpos.data.local.sharedpref.getJson
import com.uniq.uniqpos.data.local.sharedpref.getLocalDataBoolean
import com.uniq.uniqpos.data.local.sharedpref.getLocalDataString
import com.uniq.uniqpos.data.local.sharedpref.putData
import com.uniq.uniqpos.data.remote.model.Outlet
import com.uniq.uniqpos.databinding.FragmentSettingPrinterBinding
import com.uniq.uniqpos.databinding.ListItemPrinterBinding
import com.uniq.uniqpos.model.RoleMobile
import com.uniq.uniqpos.util.*
import com.uniq.uniqpos.util.printer.*
import com.uniq.uniqpos.view.global.BaseFragment
import com.uniq.uniqpos.view.global.GlobalAdapter
import com.uniq.uniqpos.view.global.GlobalViewHolder
import com.uniq.uniqpos.view.setting.PrinterSettingViewModel
import kotlinx.coroutines.*
import timber.log.Timber
import javax.inject.Inject


/**
 * A simple [Fragment] subclass.
 */
class SettingPrinterFragment :
    BaseFragment<PrinterSettingViewModel, FragmentSettingPrinterBinding>() {

    private val printers = ArrayList<PrinterEntity>()
    private lateinit var roleMobile: RoleMobile

    override val layoutRes = R.layout.fragment_setting_printer
    override fun getViewModel() = PrinterSettingViewModel::class.java

    private val requestPermissionLauncher = registerForActivityResult(ActivityResultContracts.RequestPermission()) { isGranted ->

    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        roleMobile = role()

        binding.recviewPrinter.adapter =
            object : GlobalAdapter<ListItemPrinterBinding>(R.layout.list_item_printer, printers) {
                override fun onBindViewHolder(
                    holder: GlobalViewHolder<ListItemPrinterBinding>,
                    position: Int
                ) {
                    super.onBindViewHolder(holder, position)
                    val printer = printers[position]
                    holder.binding.txtPrintTest.setOnClickListener {
                        printTest(printers[holder.adapterPosition])
                    }

                    val iconType = if(printer.type == Constant.PRINTER_TYPE_BT){
                        R.drawable.baseline_bluetooth_24_orange
                    }else if(printer.type == Constant.PRINTER_TYPE_WIFI){
                        R.drawable.baseline_wifi_24_orange
                    }else{
                        R.drawable.baseline_usb_24_orange
                    }
                    holder.binding.imgIconType.setImageResource(iconType)

                    val popUpMore = PopupMenu(context!!, holder.binding.imgMore)
                    popUpMore.menu.add("Setting")
                    popUpMore.setOnMenuItemClickListener {
                        val intent = Intent(context, PrinterDetailActivity::class.java)
                        intent.putExtra("data", printers[position])
                        startActivity(intent)
                        true
                    }

                    holder.binding.viewPrinter.setOnClickListener {
                        val intent = Intent(context, PrinterDetailActivity::class.java)
                        intent.putExtra("data", printers[position])
                        startActivity(intent)
                    }
                    holder.binding.txtSetting.setOnClickListener { deletePrinter(printer) }
                    holder.binding.imgMore.setOnClickListener { popUpMore.show() }

                    adjustColor(isPrinterEnabled(printer.address),  holder.binding.textView1, R.color.text_orange, R.color.text_orange_disable)
                    adjustColor(isPrinterEnabled(printer.address),  holder.binding.textView2, R.color.grey_light, R.color.text_grey_disable)
                }
            }

//        if (!roleMobile.settingPrinter) binding.btnAddPrinter.visibility = View.GONE
        binding.btnAddPrinter.setVisible(roleMobile.settingPrinter)

        binding.btnAddPrinter.setOnClickListener {
            showAddPrinterDialog()
        }

        val outlet = context?.getJson(SharedPref.OUTLET_DATA, Outlet::class.java)
        viewModel.syncPrinter(outlet?.outletId)
        viewModel.printers.observe(viewLifecycleOwner) { items ->
            printers.clear()
            items?.let { printers.addAll(it) }
            items?.forEach { p ->
                Timber.i("Mac Address : ${p.address} | ID : ${p.printerSettingId} | Name : ${p.name}")
            }
            binding.recviewPrinter.adapter?.notifyDataSetChanged()
            binding.txtPrinterCount.text = resources.getQuantityString(
                R.plurals.printers_connected,
                printers.size,
                printers.size
            )
            Timber.i(
                "Size : " + resources.getQuantityString(
                    R.plurals.printers_connected,
                    printers.size,
                    printers.size
                )
            )
        }
    }

    private fun adjustColor(enable: Boolean, vi: TextView, colorEnable: Int, colorDisable: Int){
        context?.apply {
            vi.setTextColor(ContextCompat.getColor(this, if(enable) colorEnable else colorDisable))
        }
    }

    private fun isPrinterEnabled(ipAddress: String): Boolean {
        return context?.deactivatePrinterList()?.any { it == ipAddress } == false
    }

    private fun showAddPrinterDialog() {
        val printerOptions = arrayOf("Bluetooth", "WiFi", "USB")
        AlertDialog.Builder(context)
            .setTitle(R.string.choose_printer_type)
            .setItems(printerOptions) { _, position ->
                when (position) {
                    0 -> addPrinter(Constant.PRINTER_TYPE_BT)
                    1 -> addPrinter(Constant.PRINTER_TYPE_WIFI)
                    2 -> addPrinter(Constant.PRINTER_TYPE_USB)
                }
            }
            .show()
    }

    private fun printTest(printer: PrinterEntity) {
        if (printerManager.isDeviceAsClient()) {
            showDialog(true, "connecting to local server...")
            beginPrint(
                listOf(
                    PendingPrintEntity(
                        printer.address,
                        printer.type,
                        "## PRINT TEST UNIQ APP v${BuildConfig.VERSION_NAME} ##\n".repeat(5)
                    )
                )
            ) {
                showDialog(false)
            }
            return
        }

        if(printer.type  == Constant.PRINTER_TYPE_BT && Build.VERSION.SDK_INT >= Build.VERSION_CODES.S && ContextCompat.checkSelfPermission(requireContext(), Manifest.permission.BLUETOOTH_CONNECT) != PackageManager.PERMISSION_GRANTED){
            requestPermissionLauncher.launch(Manifest.permission.BLUETOOTH_CONNECT)
            return
        }

        lifecycleScope.launch {
            Timber.i("print test - ${printer.name} ${printer.address}")
            showDialog(true, "Connecting...")
            when (printer.type) {
                Constant.PRINTER_TYPE_BT -> {
                    try {
                        printTestBluetooth(printer)
                    } catch (e: Exception) {
                        context?.toast(e.message, level = Level.ERROR)
                    }
                }
                Constant.PRINTER_TYPE_WIFI -> {
                    val conn = NetworkUtil.getConnectivityStatus(requireContext())
                    Timber.i("connectivity status: $conn")
                    if (conn == NetworkUtil.TYPE_WIFI) {
                        printTestWiFi(printer)
                    } else {
                        showDialog(false)
                        context?.showMessage(
                            "not connected...",//getString(R.string.print_test_not_connect_wifi),
                            "WARNING",
                            { _, _ ->
                                showDialog(true, "Connecting...")
                                printTestWiFi(printer)
                            },
                            positiveMsg = "PRINT TEST"
                        )
                    }
                }
                Constant.PRINTER_TYPE_USB -> PrinterUSB(requireContext()).printTest(
                    printer.address,
                    true
                ) {
                    showDialog(false)
                    if (!it.status) showMsg(it.message)
                }
            }
        }
    }

    private fun printTestBluetooth(printer: PrinterEntity){
        val printerBT = PrinterBluetooth(requireContext())
        lifecycleScope.launch(Dispatchers.IO) {
            withTimeoutOrNull(5000L) {
                printerBT.printTest(printer.address) { response ->
                    if (response.status) context?.toast(
                        "print test success",
                        level = Level.INFO
                    )
                    else {
                        if (response.errorCode == PRINT_ERR_NOT_PAIRED) {
                            launch(Dispatchers.Main) {
                                context?.showMessage(
                                    "Printer belum di pairing, silahkan lakukan pairing terlebih dahulu",
                                    positiveAction = { _, _ ->
                                        startActivity(Intent(android.provider.Settings.ACTION_BLUETOOTH_SETTINGS))
                                    },
                                    positiveMsg = "PAIRING PRINTER"
                                )
                            }
                        } else {
                            context?.toast(
                                "print test failed! ${response.message.safe()}",
                                duration = Toast.LENGTH_LONG,
                                level = Level.ERROR
                            )
                        }
                    }
                    printerBT.disconnect()
                }
            }
            showDialog(false)
        }
    }

    private fun printTestWiFi(printer: PrinterEntity, firstAttempt: Boolean = true) {
        if(firstAttempt) context?.putData(SharedPref.PRINT_WIFI_V2, false) //reset value

        lifecycleScope.launch(Dispatchers.Main) {
            val printWifi = printerManager.getPrinterWiFi()
            var errMsg: String? = null
            val status = context?.let { context ->
                val job = lifecycleScope.async(Dispatchers.IO) {
                    printWifi.printTestWifi(context,  printer.address) {
                        Timber.i("print test wifi msg : ${it.message}")
                        errMsg = it.message
                    }
                }
                withTimeoutOrNull(15000) { job.await() }
            }

            Timber.i("print test wifi (${printer.address}) status : $status")
            if (status.safe()) {
                showDialog(false)
            } else {
                toast(errMsg)
                showDialog(true, "ping printer...")
                var isReachable = false
                lifecycleScope.launch(Dispatchers.IO){
                    isReachable = NetworkScanner.isNetworkReachable(printer.address)
                }.join()

//                launch(Dispatchers.IO) {
//                    isReachable = NetworkScanner.isNetworkReachable(printer.address)
//                }.join()

                showDialog(false)
                val pingRes = "$isReachable"
                val printerPort =  context?.getLocalDataString(SharedPref.PRINTER_PORT, Constant.PRINTER_PORT.toString()).safeToInt(Constant.PRINTER_PORT)
                if (isReachable) {
                    context?.showMessage(
                        "print test gagal (PORT $printerPort), silahkan mencoba untuk me-restart printer anda.",
                        "GAGAL",
                        positiveMsg = "Coba Lagi",
                        positiveAction = {_, _ ->
                            context?.putData(SharedPref.PRINT_WIFI_V2, true)
                            printTestWiFi(printer, firstAttempt = false)
                        })
                } else {
                    context?.showMessage(
                        msg = "print test gagal, device tidak dapat terhubung ke printer pada port $printerPort, \n" +
                                "pastikan kamu berada dalam 1 jaringan dengan printer. \n" +
                                "\n" +
                                "IP Kamu    : ${GetIpAddress()} \n" +
                                "IP Printer : ${printer.address}",
                        title = "GAGAL",
                        positiveMsg = "Coba Lagi",
                        positiveAction = {_, _ ->
                            context?.putData(SharedPref.PRINT_WIFI_V2, true)
                            printTestWiFi(printer, firstAttempt = false)
                        })
                }
                Timber.i("ping: $pingRes")
            }
        }
    }

    private fun deletePrinter(printer: PrinterEntity) {
        if (!roleMobile.settingPrinter) {
            showMsg(getString(R.string.no_permission))
            return
        }

        val outlet = context?.getJson(SharedPref.OUTLET_DATA, Outlet::class.java)
        AlertDialog.Builder(context)
            .setTitle("Delete Printer")
            .setMessage(R.string.are_you_sure)
            .setNegativeButton("Cancel", null)
            .setPositiveButton("OK") { _, _ ->
                Timber.i("Delete Printer : ${printer.name} (${printer.address})")
                viewModel.deletePrinter(printer, outlet?.outletId)
            }
            .show()
    }

    private fun hasBluetooth(): Boolean {
        val bAdapter = BluetoothAdapter.getDefaultAdapter()
        if (bAdapter != null) {
            if (!bAdapter.isEnabled) bAdapter.enable()
            return true
        } else {
            toast("Bluetooth Not Found!")
        }
        return false
    }

    private fun addPrinter(type: String) {
        val addresses = printers.map { it.address } as ArrayList<String>
        val intent = Intent(context, AddPrinterActivity::class.java)
        intent.putExtra("type", type)
        intent.putExtra("printers", addresses)
        startActivity(intent)
    }

}
