package com.uniq.uniqpos.view.transaction.dialog

import android.app.Activity
import android.app.Dialog
import android.content.Intent
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.view.*
import android.view.inputmethod.EditorInfo
import android.widget.ArrayAdapter
import android.widget.LinearLayout
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.FragmentManager
import com.bugsnag.android.Bugsnag
import com.google.firebase.analytics.FirebaseAnalytics
import com.google.firebase.analytics.ktx.analytics
import com.google.firebase.ktx.Firebase
import com.google.firebase.remoteconfig.FirebaseRemoteConfig
import com.google.firebase.remoteconfig.ktx.remoteConfig
import com.google.gson.Gson
import com.gu.toolargetool.TooLargeTool
import com.uniq.uniqpos.R
import com.uniq.uniqpos.data.local.entity.MemberEntity
import com.uniq.uniqpos.data.local.entity.SalesEntity
import com.uniq.uniqpos.data.local.sharedpref.putData
import com.uniq.uniqpos.data.remote.model.Member
import com.uniq.uniqpos.data.remote.model.ServerResponse
import com.uniq.uniqpos.databinding.ListItemChipBinding
import com.uniq.uniqpos.databinding.ListItemNamesBinding
import com.uniq.uniqpos.databinding.PopupFinishOrderBinding
import com.uniq.uniqpos.model.BottomDialogModel
import com.uniq.uniqpos.model.Feature
import com.uniq.uniqpos.util.*
import com.uniq.uniqpos.util.data.DataHolder
import com.uniq.uniqpos.util.view.DialogAuthorization
import com.uniq.uniqpos.util.view.RecyclerItemClickListener
import com.uniq.uniqpos.view.global.BottomDialogInput
import com.uniq.uniqpos.view.global.GlobalAdapter
import com.uniq.uniqpos.view.global.GlobalViewHolder
import com.uniq.uniqpos.view.help.HelpContentDialog
import com.uniq.uniqpos.view.member.MemberActivity
import com.uniq.uniqpos.view.payment.PaymentActivity
import com.uniq.uniqpos.view.payment.PaymentV2Activity
import com.uniq.uniqpos.view.pendingprint.PendingPrintActivity
import com.uniq.uniqpos.view.scanbarcode.ScanBarcodeActivity
import com.uniq.uniqpos.view.table.TableListActivity
import com.uniq.uniqpos.view.transaction.TransactionViewModel
import com.uniq.uniqpos.ui.form.DynamicFormManager
import com.uniq.uniqpos.ui.form.FormFieldConfig
import com.uniq.uniqpos.ui.form.ValidationResult
import com.uniq.uniqpos.model.CustomFields
import com.uniq.uniqpos.util.Level
import timber.log.Timber

/**
 * Created by annasblackhat on 13/07/18
 */
abstract class SaveOrderDialog(val viewModel: TransactionViewModel, val activity: Activity) :
    Dialog(activity), SaveOrderDialogContract {

    companion object {
        val RC_PAYMENT = 45
        val RC_TABLE = 46
        val RC_MEMBER_SCAN = 48
        val RC_MEMBER_SEARCH = 49
    }

    private lateinit var binding: PopupFinishOrderBinding
    private var onFocusChangeListener: View.OnFocusChangeListener? = null
    private var isResetOnNextPopUp: Boolean = false

    private lateinit var customerAdapter: ArrayAdapter<String>
    private lateinit var remoteConfig: FirebaseRemoteConfig

    // Dynamic form properties
    private lateinit var dynamicFormManager: DynamicFormManager
    private var customFieldsSection: LinearLayout? = null
    private var customFieldsContainer: LinearLayout? = null

    private val outletFeature: Feature? by lazy {
        context.outlet()?.takeIf { it.feature != null }?.let { Gson().fromJson(it.feature, Feature::class.java) }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        requestWindowFeature(Window.FEATURE_NO_TITLE)
        binding = DataBindingUtil.inflate(
            LayoutInflater.from(context),
            R.layout.popup_finish_order,
            null,
            false
        )
        setContentView(binding.root)
        window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
        if (context.resources.getBoolean(R.bool.landscape_only)) {
            window?.setLayout(
                context.resources.getDimensionPixelOffset(R.dimen.dialog_width),
                WindowManager.LayoutParams.WRAP_CONTENT
            )
        } else {
            window?.setLayout(
                WindowManager.LayoutParams.MATCH_PARENT,
                WindowManager.LayoutParams.WRAP_CONTENT
            )
        }

        binding.model = SaveOrder(R.drawable.ic_barcode)
        remoteConfig = Firebase.remoteConfig

        remoteConfig.fetchAndActivate().addOnCompleteListener {  }

        //set ROLE
        if (!context.role().pembayaran) binding.btnBayar.visibility = View.GONE
        if (!context.role().simpankeorderlist) binding.btnSave.visibility = View.GONE

        //set FEATURE
        context.outlet()?.takeIf { it.feature != null }?.let { outlet ->
            val outletFeature = Gson().fromJson(outlet.feature, Feature::class.java)
            Timber.d("outletFeature -> ${outlet.feature}")
            binding.layoutQtyCustomer.visibility = (outletFeature.numberofcustomers).visibility()
            binding.layoutPrediction.visibility = (outletFeature.waitingtime).visibility()
            binding.layoutPin.visibility = (outletFeature.employeename).visibility()
            binding.layoutTable.visibility = (outletFeature.tableinput).visibility()
            binding.btnSave.visibility = outletFeature.notelist.visibility()
            binding.txtSearchMember.visibility = outletFeature.member.visibility()
            binding.layoutNote.visibility = context.role().simpankeorderlist.visibility()
        }

        viewModel.salesEdit?.let { salesEntity ->
            binding.edtCustomer.setText(salesEntity.customer)
            binding.edtQtyCustomer.setText(salesEntity.customersQty.toString())
            binding.edtPrediction.setText(salesEntity.timePrediction.toString())
            binding.edtTable.setText(salesEntity.table)
        }

        binding.btnBayar.setOnButtonClickListener {
            validateInput {
                payOrder(getSalesEntity())
//                startActivityWithResult(Intent(context, PendingPrintActivity::class.java), 0)
            }
        }

        binding.btnSave.setOnButtonClickListener {
            Timber.i("click save btn...")
            if (Utils.isValidField(
                    binding.edtCustomer,
                    binding.edtPrediction,
                    binding.edtQtyCustomer,
                    binding.edtPin
                ) && isValid()
            ) {
                binding.btnSave.startLoading()
                saveOrder(
                    binding.edtCustomer.value().trim(),
                    binding.edtQtyCustomer.valueToInt(),
                    binding.edtPrediction.valueToInt()
                )
            }
        }

        binding.btnDone.setOnButtonClickListener {
            //why update tmp sales? still became the QUESTION
            //even when editing cart (subtracting qty / void), the data updated directly
//            viewModel.salesEdit?.let { viewModel.updateTmpSale(TmpSalesEntity(it.noNota, Gson().toJson(it), context.outlet()?.outletId!!)) }

            //if transaction contains voucher, confirm the user
            //removePromotion(promoApplied.first(), outletId)
            if (viewModel.promoApplied.isNotEmpty()) {
                context.showMessage(
                    "transaksi ini menggunakan promo deals/voucher, apakah anda ingin mereset promo tersebut?",
                    "CONFIRMATION",
                    positiveMsg = "Tidak, Reset Bill Saja",
                    negativeMsg = "Ya, Reset Bill Dan Promo",
                    positiveAction = { _, _ -> resetBill() },
                    negativeAction = { _, _ -> resetBill(true) }
                )
            } else {
                resetBill()
            }
        }

        binding.edtTable.setOnClickListener {
            var intent = Intent(context, TableListActivity::class.java)
            intent.putExtra("isChoosing", true)
            startActivityWithResult(intent, RC_TABLE)
        }

        val employees = context.employeeList()
        binding.edtPin.setOnClickListener {
            context.apply {
                DialogAuthorization(this)
                    .setAuthType(DialogAuthorization.AuthType.SALE)
                    .setAutoCheck(false)
                    .setOnAuthorizedListener { employee ->
                        binding.edtPin.setText(employee.name)
                        viewModel.choosenEmployee =
                            employees.find { it.employeeId == employee.employeeId }
                    }
                    .show()
            }
        }

        binding.txtUseSecretCode.setOnClickListener {
            if (binding.model?.isLockMode == false) {
                val bottomDialog = BottomDialogInput()
                bottomDialog.setOnButtonClick { code ->
                    if (code.isBlank()) {
                        context.toast("Value is not allowed!")
                    } else {
                        Timber.i("find member with secret code : '$code'")
                        viewModel.findMemberWithSecretCode(code)
                        bottomDialog.dismiss()
                    }
                }
                bottomDialog.setModel(
                    BottomDialogModel(
                        "SECRET CODE",
                        "NEXT",
                        hint = "type secret code",
                        message = context.getString(R.string.use_secret_code),
//                        inputType = InputType.TYPE_CLASS_NUMBER
                    )
                )
                bottomDialog.show(getFragmentManager(), "secret-code")
            }
        }

        binding.txtSearchMember.setOnClickListener {
            if (binding.model?.isLockMode == false) {
                val intent = Intent(context, MemberActivity::class.java)
                intent.putExtra(MemberActivity.INTENT_CURRENT_MEMBER, viewModel.memberId)
                startActivityWithResult(intent, RC_MEMBER_SEARCH)
            }
        }

        binding.btnScanMember.setOnClickListener {
            if (binding.model?.isLockMode == true) {
                val msg =
                    if (viewModel.isUseMemberPromo()) context.getString(R.string.warn_sales_use_member_promo) else context.getString(
                        R.string.warn_remove_member
                    )
                context.showMessage(msg, "CONFIRMATION", { _, _ ->
                    removeMember()
                    viewModel.removeMember()
                    viewModel.removeMemberPromo()
                })
            } else {
                val intent = Intent(context, ScanBarcodeActivity::class.java)
                intent.putExtra("action_type", ScanBarcodeActivity.ACTION_GET_VALUE_ONLY)
                scanMember(intent, RC_MEMBER_SCAN)
            }
        }

//        val imgRight = AppCompatResources.getDrawable(context, R.drawable.ic_barcode)
//        binding.edtQtyCustomer.setCompoundDrawablesWithIntrinsicBounds(null, null, context.resources.getDrawable(R.drawable.icon_shop), null)
//        binding.edtQtyCustomer.setCompoundDrawables(null, null, context.resources.getDrawable(R.drawable.icon_shop), null)

        Timber.d("total customer names: ${viewModel.customerNames.size}")
        customerAdapter = ArrayAdapter(
            context,
            android.R.layout.simple_list_item_1,
            arrayListOf<String>()
        )
        binding.edtCustomer.setAdapter(
            customerAdapter
        )

        onFocusChangeListener = View.OnFocusChangeListener { _, hasFocus ->
            if (hasFocus) binding.edtCustomer.selectAll()
        }
        binding.edtCustomer.onFocusChangeListener = onFocusChangeListener
        binding.edtCustomer.requestFocus()

        binding.imgClose.setOnClickListener { dismiss() }
        binding.edtCustomer.setOnEditorActionListener { vi, actionId, key ->
            var keyCode = try {
                key.keyCode
            }catch (e: Exception){
                Timber.i("can not get keyCode: $e")
                0
            }

            if (actionId == EditorInfo.IME_ACTION_DONE || keyCode == KeyEvent.KEYCODE_ENTER) {
                vi.hideKeyboard(context)
                if (binding.edtCustomer.value().length > 20 && !binding.edtCustomer.value()
                        .contains(" ")
                ) {
                    viewModel.findMember(
                        binding.edtCustomer.value(),
                        context.outlet()?.outletId.safe(),
                        isSilent = true
                    )
                } else {
                    val isValid = Utils.isValidField(
                        binding.edtCustomer,
                        binding.edtPrediction,
                        binding.edtQtyCustomer,
                        binding.edtPin
                    )

                    if (!binding.btnSave.isVisible && isValid) {
                        payOrder(getSalesEntity())
                    }
                }
            }
            false
        }

        binding.recViewTag.adapter = tagAdapter()
        binding.recViewTag.addOnItemTouchListener(RecyclerItemClickListener(context){_, position ->
            if(viewModel.salesTagList.getSafe(position)?.salesTagId == 0){
                showHelpAddOrderType()
                return@RecyclerItemClickListener
            }
            val tmpSelected = viewModel.selectedTag
            if(position == viewModel.selectedTag){
                viewModel.selectedTag = -1
            }else{
                viewModel.selectedTag = position
                binding.recViewTag.adapter?.notifyItemChanged(viewModel.selectedTag)
            }
            binding.recViewTag.adapter?.notifyItemChanged(tmpSelected)
        })

        // Initialize dynamic form
        initializeDynamicForm()
    }

    private fun isValid(): Boolean {
        if(viewModel.salesTagList.isEmpty()) return true //if not tag set, just return valid
        val requireTag = outletFeature?.transactionRequireTag.safe()
        val result = viewModel.selectedTag >= 0 || !requireTag
        if(!result)context.toast("please select order type", level = Level.ERROR)
        return result
    }

    // Dynamic form methods
    private fun initializeDynamicForm() {
        customFieldsSection = binding.customFieldsSection
        customFieldsContainer = binding.customFieldsContainer

        customFieldsContainer?.let { container ->
            dynamicFormManager = DynamicFormManager(container)
            loadCustomFields()
        }
    }

    private fun loadCustomFields() {
        try {
            // Convert TransactionSettingEntity list to FormFieldConfig list
            val configurations = viewModel.transactionSettingList.map { entity ->
                FormFieldConfig.fromEntity(entity)
            }

            if (configurations.isNotEmpty()) {
                customFieldsSection?.visibility = View.VISIBLE
                dynamicFormManager.buildForm(configurations)
                Timber.d("Loaded ${configurations.size} custom fields")
            } else {
                customFieldsSection?.visibility = View.GONE
                Timber.d("No custom fields to display")
            }
        } catch (e: Exception) {
            Timber.e(e, "Error loading custom fields")
            // Hide section if error - won't break existing flow
            customFieldsSection?.visibility = View.GONE
        }
    }

    private fun tagAdapter(): GlobalAdapter<ListItemChipBinding> {
        return object : GlobalAdapter<ListItemChipBinding>(R.layout.list_item_chip, viewModel.salesTagList.map { it.name }){
            override fun onBindViewHolder(
                holder: GlobalViewHolder<ListItemChipBinding>,
                position: Int
            ) {
                super.onBindViewHolder(holder, position)
                if (viewModel.selectedTag == position) {
                    holder.binding!!.btnName.setBackgroundResource(R.drawable.btn_round_blue)
                } else {
                    holder.binding!!.btnName.setBackgroundResource(R.drawable.btn_round)
                }

                if(viewModel.salesTagList[position].salesTagId == 0){
                    holder.binding!!.btnName.setTextColor(Color.YELLOW)
                }else{
                    holder.binding!!.btnName.setTextColor(Color.WHITE)
                }
            }
        }
    }

    private fun validateInput(doNext: () -> Unit) {
        if (viewModel.orders.isEmpty()) {
            context.toast("you have no item on your bill", level = Level.WARNING)
            return
        }

        if(!Utils.isValidField(
                binding.edtCustomer,
                binding.edtPrediction,
                binding.edtQtyCustomer,
                binding.edtPin
            ) || !isValid()){
            return
        }

        if(binding.edtQtyCustomer.valueToInt() > 100) {
            context.showMessage(
                context.getString(R.string.warn_qty_cutomer, binding.edtQtyCustomer.valueToInt()),
                context.getString(R.string.confirmation),
                positiveAction = { _, _ -> doNext() },
                negativeAction = { _, _ ->
                    binding.edtQtyCustomer.requestFocus()
                    binding.edtQtyCustomer.selectAll()
                },
                negativeMsg = context.getString(R.string.edit)
            )
            return
        }

        val unUsedPromotion = viewModel.getUnusedPromotion()
        if (unUsedPromotion.isNotEmpty()){
            val promoNames = unUsedPromotion.joinToString { it.name.safe().uppercase() }
            context.showMessage("promo berikut belum dapat digunakan karena belum memenuhi syarat: \n$promoNames",
                negativeMsg = "Hapus Promo",
                negativeAction = {_,_ ->
                    val outletId = context.outlet()?.outletId.safe()
                    unUsedPromotion.forEach { viewModel.removePromotion(it, outletId) }
                    doNext()
                },
                positiveMsg = "Close",
                positiveAction = {_,_ -> dismiss()})
            return
        }

        // Validate dynamic form fields
        if (::dynamicFormManager.isInitialized && dynamicFormManager.hasFields()) {
            when (val validationResult = dynamicFormManager.validateForm()) {
                is ValidationResult.Error -> {
                    context.toast(validationResult.message, level = Level.ERROR)
                    return
                }
                is ValidationResult.Success -> {
                    // Custom fields data will be collected in getSalesEntity()
                    Timber.d("Dynamic form validation passed")
                }
            }
        }

        doNext()
    }

    private fun resetBill(shouldResetPromo: Boolean = false) {
        if (shouldResetPromo) {
            viewModel.promoApplied.firstOrNull()?.let { promotion ->
                viewModel.removePromotion(promotion, context.outlet()?.outletId.safe())
            } ?: run { context.toast("no promo found") }
        }

        resetView()
        resetTransactionView()
        viewModel.resetTransaction(shouldResetPromo)
        viewModel.restorePromotion()
        dismiss()

        Firebase.analytics
            .logEvent(
                "app_features",
                bundleOf(
                    "Outlet" to "RB:${context.outlet()?.outletId}:${context.outlet()?.name}",
                    "Feature" to "Reset Bill"
                )
            )
    }

    private fun removeMember() {
        binding.edtCustomer.isFocusable = true
        binding.edtCustomer.isFocusableInTouchMode = true
        binding.txtUseSecretCode.setTextColor(
            ContextCompat.getColor(
                context,
                R.color.greeen_background
            )
        )
        binding.txtSearchMember.setTextColor(
            ContextCompat.getColor(
                context,
                R.color.greeen_background
            )
        )
        binding.edtCustomer.setText("")
        binding.txtMemberLevel.text = ""
        binding.model = SaveOrder(R.drawable.ic_barcode)
    }

    override fun saveOrder(customer: String, pax: Int, timePrediction: Int) {
        Bugsnag.leaveBreadcrumb("save order")
        binding.btnBayar.setButtonEnabled(false)
        binding.btnDone.setButtonEnabled(false)
        viewModel.saveToCart(
            getSalesEntity(),
            context.outlet()?.outletId.safe(),
            context.resources.getBoolean(R.bool.landscape_only)
        )
    }

    override fun payOrder(sales: SalesEntity) {
        Bugsnag.leaveBreadcrumb("pay order")
//        //for cart, save first
        if(viewModel.isSalesEdit){
            saveOrder("", 1, 0)
            context.toast("saving...")
        }

        val salesJson = Gson().toJson(sales)
        val contact = viewModel.contactSelfOrder ?: viewModel.memberDetail?.phone

        val useNewUi = true // remoteConfig.getBoolean("ui_payment")
        val intent = if(useNewUi) Intent(context, PaymentV2Activity::class.java) else Intent(context, PaymentActivity::class.java)
//        intent.putExtra("sales", sales)

        intent.putExtra("sales-json-static", true)
        DataHolder.setData(salesJson)
        Timber.d("sales json: $salesJson")

        intent.putExtra("merge_ids", viewModel.mergeIds)
        intent.putExtra("receipt_receiver", contact)

//        intent.putExtra(
//            PaymentActivity.INTENT_PAY_FLOW,
//            viewModel.salesEdit?.let { "Transaction - Pending Bill - Edit Transaction" }
//                ?: kotlin.run { "Transaction" })

        binding.edtCustomer.requestFocus()

//        Firebase.analytics
//            .logEvent(
//                FirebaseAnalytics.Event.BEGIN_CHECKOUT,
//                bundleOf(
//                    FirebaseAnalytics.Param.CURRENCY to "IDR",
//                    FirebaseAnalytics.Param.VALUE to sales.grandTotal.toString()
//                )
//            )
        dismiss()
//        viewModel.resetState()
//        activity.startActivityForResult(intent, RC_PAYMENT)
        startActivityWithResult(intent, RC_PAYMENT)
    }

    private fun getSalesEntity(): SalesEntity {
        var qtyCustomer = binding.edtQtyCustomer.valueToInt()
        if (qtyCustomer == 0) qtyCustomer = 1
        val salesEntity = viewModel.getSalesEntity(
            binding.edtCustomer.value().trim(),
            qtyCustomer,
            binding.edtPrediction.toInt(),
            binding.edtTable.value().trim(),
            binding.edtNote.value().trim(),
            isToPayment = false
        )

        // Add custom fields data if available
        if (::dynamicFormManager.isInitialized && dynamicFormManager.hasFields()) {
            val customFieldsData = dynamicFormManager.getFormData()
            val customFieldsList = ArrayList<CustomFields>()

            customFieldsData.forEach { (fieldId, value) ->
                // Find the corresponding transaction setting to get the label
                val transactionSetting = viewModel.transactionSettingList.find { it.id.toString() == fieldId }
                if (transactionSetting != null && value != null && value.toString().isNotBlank()) {
                    customFieldsList.add(
                        CustomFields(
                            settingTransactionFkid = transactionSetting.id,
                            label = transactionSetting.label,
                            value = value.toString()
                        )
                    )
                }
            }

            if (customFieldsList.isNotEmpty()) {
                salesEntity.customFields = customFieldsList
                Timber.d("Added ${customFieldsList.size} custom fields to sales entity")
            }
        }

        return salesEntity
    }

    fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        if (resultCode == Activity.RESULT_OK) {
            when (requestCode) {
                RC_TABLE -> {
                    val table = data?.getStringExtra("table")
                    binding.edtTable.setText(table)
                }
                RC_MEMBER_SCAN -> {
                    data?.getStringExtra("data")?.takeIf { it.trim().isNotEmpty() }
                        ?.let { barcode ->
                            Timber.i("find member with qr-code : '$barcode'")
                            viewModel.findMember(barcode, context.outlet()?.outletId)
                        }
                }
                RC_MEMBER_SEARCH -> {
                    val dataIntent = data?.getParcelableExtra("data") as? MemberEntity
                    dataIntent?.let {
                        val member = it.covertToMember()
                        viewModel.setMember(member)
                        viewModel.taskMemberFound.postValue(ServerResponse(status = true, data = member))
                        setMember(member)
                    }
                }
            }
        }
    }

    fun initButton(saveBtn: Boolean = true, payBtn: Boolean = true, resetBtn: Boolean = true) {
        binding.btnSave.visibility = saveBtn.visibility()
        binding.btnBayar.visibility = payBtn.visibility()
        binding.btnDone.visibility = resetBtn.visibility()
    }

    fun initField(
        customerField: Boolean = true,
        paxField: Boolean = true,
        predictionField: Boolean = true,
        pinField: Boolean = true,
        tableField: Boolean = true,
        noteField: Boolean = true
    ) {
        binding.edtCustomer.visibility = customerField.visibility()
        binding.edtQtyCustomer.visibility = paxField.visibility()
        binding.edtPrediction.visibility = predictionField.visibility()
        binding.edtPin.visibility = pinField.visibility()
        binding.edtTable.visibility = tableField.visibility()
        binding.layoutNote.visibility = (noteField && context.role().simpankeorderlist).visibility()
    }

    fun resetView() {
        if (isShowing) {
            binding.edtCustomer.setText("")
            binding.edtPin.setText("")
            binding.edtPrediction.setText("")
            binding.edtTable.setText("")
            binding.edtNote.setText("")
            binding.txtMemberLevel.text = ""
            binding.edtQtyCustomer.setText("")

            binding.edtCustomer.isFocusable = true
            binding.edtCustomer.isFocusableInTouchMode = true
            binding.txtUseSecretCode.setTextColor(
                ContextCompat.getColor(
                    context,
                    R.color.greeen_background
                )
            )
            binding.txtSearchMember.setTextColor(
                ContextCompat.getColor(
                    context,
                    R.color.greeen_background
                )
            )
            binding.model = SaveOrder(R.drawable.ic_barcode)

            // Reset dynamic form
            if (::dynamicFormManager.isInitialized) {
                dynamicFormManager.resetForm()
            }
        } else {
            isResetOnNextPopUp = true
        }
    }

    private fun setField(
        customer: String? = null,
        table: String? = null,
        qty: String? = null,
        prediction: String? = null,
        note: String? = null
    ) {
        customer?.let { binding.edtCustomer.setText(it) }
        table?.let { binding.edtTable.setText(it) }
        qty?.let { binding.edtQtyCustomer.setText(it) }
        prediction?.let { binding.edtPrediction.setText(it) }
        note?.let { binding.edtNote.setText(it) }
    }

    fun setMember(member: Member) {
        if (member.memberId.safeToInt() == 0){
            return
        }

        if (isShowing) {
            binding.edtCustomer.setText(member.name)
            binding.model = SaveOrder(R.drawable.ic_clear_white_24dp, true)
            binding.txtUseSecretCode.setTextColor(
                ContextCompat.getColor(
                    context,
                    R.color.text_grey
                )
            )
            binding.txtSearchMember.setTextColor(ContextCompat.getColor(context, R.color.text_grey))
            binding.edtCustomer.isFocusable = false
            binding.txtMemberLevel.text = "Level: ${member.customerLevel ?: member.typeName}"
        }
    }

    override fun onOrderSaved(sales: SalesEntity) {
        resetTransactionView()
    }

    abstract fun resetTransactionView()

    abstract fun getFragmentManager(): FragmentManager

    abstract fun scanMember(intent: Intent, requestCode: Int)

    abstract fun startActivityWithResult(intent: Intent, requestCode: Int)

    data class SaveOrder(
        var imgRight: Int = 0,
        var isLockMode: Boolean = false
    )

    override fun dismiss() {
        onFocusChangeListener = null
        binding.edtCustomer.onFocusChangeListener = null
        binding.btnSave.stopLoading()
        binding.btnBayar.setButtonEnabled(true)
        binding.btnDone.setButtonEnabled(true)
        currentFocus?.hideKeyboard(context)
        Timber.i("[DIALOG] SaveOrderDialog dismis...")
        super.dismiss()
    }

    override fun show() {
        super.show()
        Bugsnag.leaveBreadcrumb("show save order dialog")
        Timber.i("[DIALOG] SaveOrderDialog showing...")
        viewModel.loadSalesTag()
        if (isResetOnNextPopUp) {
            resetView()
            isResetOnNextPopUp = false
        }
        viewModel.customerName?.let { setField(it) }
        viewModel.diningTable?.let { setField(table = it) }
        viewModel.salesEdit?.let {
            setField(
                it.customer,
                it.table,
                it.customersQty.toString(),
                it.timePrediction.toString(),
                it.note
            )
            it.memberDetail?.let { member -> setMember(member) }
//            binding.btnBayar.setButtonText(context.getString(R.string.save_and_pay))
        } ?: kotlin.run {
            viewModel.memberDetail?.let { member ->
                setMember(member)
            }
//            binding.btnBayar.setButtonText(context.getString(R.string.pay))
        }

        customerAdapter.clear()
        customerAdapter.addAll(viewModel.customerNames)
        customerAdapter.notifyDataSetChanged()
        listOf(binding.recViewTag).forEach { vi ->
            vi.setVisible(viewModel.salesTagList.isNotEmpty())
        }

        // Reload custom fields when dialog is shown
        if (::dynamicFormManager.isInitialized) {
            loadCustomFields()
        }
    }

    private fun showHelpAddOrderType() {
        val sendReceiptInfo = """<p style='color:white'>
            Untuk dapat menambahkan atau mengubah Tipe Transaksi,<br/>          
            Kamu bisa melakukan nya melalui Web UNIQ, <br> <br/>           
             
            <a href='https://y.uniq.id/tutorial-order-type' style="color:white;">Klik Disini</a> untuk mengetahui caranya.
            </p>
        """
        HelpContentDialog
            .newInstance(sendReceiptInfo, "Add Order Type")
            .show(getFragmentManager(), "help")
    }
}
