package com.uniq.uniqpos.view.setting.kitchendisplay

import androidx.lifecycle.LiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.google.gson.Gson
import com.uniq.uniqpos.data.local.entity.KitchenDisplayCategory
import com.uniq.uniqpos.data.local.entity.KitchenDisplayEntity
import com.uniq.uniqpos.data.local.entity.SubCategoryEntity
import com.uniq.uniqpos.data.local.sharedpref.SharedPref
import com.uniq.uniqpos.data.local.sharedpref.getLocalDataString
import com.uniq.uniqpos.data.remote.Resource
import com.uniq.uniqpos.data.remote.model.Outlet
import com.uniq.uniqpos.data.remote.repository.ProductRepository
import com.uniq.uniqpos.data.remote.repository.SettingRepository
import com.uniq.uniqpos.model.ToastMessage
import com.uniq.uniqpos.util.Constant
import com.uniq.uniqpos.util.Level
import com.uniq.uniqpos.util.getDeviceName
import com.uniq.uniqpos.util.kds.KdsConnection
import com.uniq.uniqpos.util.lifecycle.BaseViewModel
import com.uniq.uniqpos.util.lifecycle.Event
import com.uniq.uniqpos.util.lifecycle.Success
import com.uniq.uniqpos.util.networking.sendWebSocket
import com.uniq.uniqpos.util.outlet
import com.uniq.uniqpos.util.safe
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

class KitchenDisplayViewModel @Inject constructor(
    private var productRepository: ProductRepository,
    private var settingRepository: SettingRepository,
    private val sharedPref: SharedPref,
    private val kdsConn: KdsConnection
): BaseViewModel() {
     val categories = ArrayList<SubCategoryEntity>()
    val kitchenDisplays = ArrayList<KitchenDisplayEntity>()

    val outlet: Outlet? by lazy {
        sharedPref.getJson(SharedPref.OUTLET_DATA, Outlet::class.java)
    }

    val deviceId: String? by lazy {
        sharedPref.getString(SharedPref.DEVICE_ID, "")
    }

    fun getCategories() = productRepository.loadCategories()

    fun loadKitchenDisplay(): LiveData<Resource<List<KitchenDisplayEntity>>> {
        val outlet = sharedPref.getJson(SharedPref.OUTLET_DATA, Outlet::class.java)
        return settingRepository.loadKitchenDisplay(outlet?.outletId.safe())
    }

    fun setCategories(list: List<SubCategoryEntity>?) {
        categories.clear()
        list?.let { categories.addAll(it) }
    }

    fun updateKitchenDisplay(id: Int, ipAddress: String, name: String, selectedCategories: java.util.ArrayList<KitchenDisplayCategory>){
        viewModelScope.launch {
            _loadingDialog.postValue(Event(true))
            val outlet = sharedPref.getJson(SharedPref.OUTLET_DATA, Outlet::class.java)
            settingRepository.updateKitchenDisplay(KitchenDisplayEntity(id, name, ipAddress, selectedCategories, outletFkid = outlet?.outletId.safe()))
            _loadingDialog.postValue(Event(false))
        }
    }

    fun addKitchenDisplay(ipAddress: String, name: String, selectedCategories: java.util.ArrayList<KitchenDisplayCategory>) {
        viewModelScope.launch {
            //validation - make sure no duplicate ip
            Timber.i("added kitchen ips: ${kitchenDisplays.map { it.address }}")
            if(kitchenDisplays.any { it.address == ipAddress }.safe()){
                _dialogMsg.postValue(Event("Kitchen display with IP Address '$ipAddress' already added before!"))
                return@launch
            }

            _loadingDialog.postValue(Event(true))
            val outlet = sharedPref.getJson(SharedPref.OUTLET_DATA, Outlet::class.java)

            try {
                val defaultId = ipAddress.substringAfterLast(".").toInt()
                settingRepository.addKitchenDisplay(KitchenDisplayEntity(defaultId, name, ipAddress, selectedCategories, outletFkid = outlet?.outletId.safe()))
                _state.postValue(Success)
            } catch (e: Exception) {
                _dialogMsg.postValue(Event(e.message ?: "saving failed, try again later"))
            }
            _loadingDialog.postValue(Event(false))
        }
    }

    fun removeKitchenDisplay(data: KitchenDisplayEntity?) {
        viewModelScope.launch {
            val outlet = sharedPref.getJson(SharedPref.OUTLET_DATA, Outlet::class.java)
            settingRepository.removeKitchenDisplay(data?.settingKitchenDisplayId.safe(), outlet?.outletId)
        }
    }

    fun addKds(ip: String, name: String, categories: ArrayList<KitchenDisplayCategory>){
        viewModelScope.launch{
            try {
                val data = mapOf(
                    "type" to "pairing",
                    "name" to name,
                    "ip_address" to ip,
                    "port" to Constant.KITCHEN_PORT,
                    "category" to Gson().toJson(categories),
                    "device_id" to deviceId,
                    "device_name" to getDeviceName(),
                    "client_id" to 1,
                    "outlet_id" to outlet?.outletId,
                    "outlet_name" to outlet?.name,
                )
                val dataJson = Gson().toJson(data)
//                sendWebSocket(dataJson, ip, Constant.KITCHEN_PORT, "/ws", requireContext().assets)
                kdsConn.sendData(dataJson, ip, onSent = {
                    _toastMessage.postValue(Event(ToastMessage("Kitchen Display '${name.uppercase()}' successfully added", type = Level.DEFAULT)))
                })
            } catch (e: Exception) {
                _toastMessage.postValue(Event(ToastMessage(e.message ?: "Failed", type = Level.ERROR)))
            }
        }
    }
}