package com.uniq.uniqpos.view.transaction.promotion

import android.content.Context
import android.graphics.Paint
import android.view.LayoutInflater
import androidx.databinding.DataBindingUtil
import com.google.gson.Gson
import com.uniq.uniqpos.R
import com.uniq.uniqpos.data.local.entity.ProductEntity
import com.uniq.uniqpos.data.local.entity.PromotionEntity
import com.uniq.uniqpos.databinding.DialogPromotionDetailBinding
import com.uniq.uniqpos.databinding.ListItemProductPromoBinding
import com.uniq.uniqpos.model.PromotionProductDialog
import com.uniq.uniqpos.util.*
import com.uniq.uniqpos.view.global.DynamicDialog
import com.uniq.uniqpos.view.global.GlobalAdapter
import com.uniq.uniqpos.view.global.GlobalViewHolder
import com.uniq.uniqpos.view.transaction.TransactionViewModel
import com.uniq.uniqpos.view.transaction.dialog.FreeItemListDialogFragment
import timber.log.Timber

abstract class PromotionDetailDialog(
    val context: Context,
    val viewModel: TransactionViewModel
) {
    private var erasePriceOrigin = false
    private var hidePricePromo = false
    private val promotionProductList = ArrayList<PromotionProductDialog>()

    private val binding: DialogPromotionDetailBinding by lazy {
        DataBindingUtil.inflate(
            LayoutInflater.from(context),
            R.layout.dialog_promotion_detail,
            null,
            false
        )
    }

    private val dialog: DynamicDialog by lazy {
        val vi = DynamicDialog(binding.root, context)
        initView()
        vi
    }

    private fun initView() {
        binding.recviewPromoProduct.adapter = object : GlobalAdapter<ListItemProductPromoBinding>(
            R.layout.list_item_product_promo,
            promotionProductList
        ) {
            override fun onBindViewHolder(
                holder: GlobalViewHolder<ListItemProductPromoBinding>,
                position: Int
            ) {
                super.onBindViewHolder(holder, position)
                holder.binding.txtPricePromo.setVisible(!hidePricePromo)
                holder.binding.txtPriceOriginal.paintFlags =
                    holder.binding.txtPriceOriginal.paintFlags or Paint.STRIKE_THRU_TEXT_FLAG
                holder.binding.root.setOnClickListener {
                    promotionProductList.getSafe(holder.adapterPosition)?.let { promotion ->
                        val product =
                            viewModel.productsUnique.firstOrNull { it.productDetailId == promotion.productDetailId }
                        if (product != null) {
                            viewModel.taskAddBillNoVariant.postValue(Pair(product, 1))
                            dialog.dismiss()
                        }else context.toast("item not found...")
                    } ?: run { context.toast("no product found!") }
                }
            }
        }
    }

    fun show(promotion: PromotionEntity) {
        dialog.show()

        Timber.i("promotion clicked: ${Gson().toJson(promotion)}")
        hidePricePromo = false
        binding.txtPromotionInfo.text = ""
        binding.txtPromoName.text = promotion.name
        var promoInfoTerm =
            if (promotion.minOrder > 0) "min order ${promotion.minOrder.toCurrency("Rp")}" else "tanpa minimum order"

        val productTerm = promotion.promotionProduct.filter { it.type == Constant.PROMO_PRODUCT_ORDER }
        if(productTerm.isNotEmpty()){
            promoInfoTerm += " | <b>Ada syarat item yang harus dibeli</b> "
        }
        setTextFromHtml(binding.txtMinOrder, promoInfoTerm)
        setTextFromHtml(binding.txtPromoTerms, promotion.term.safe())
        binding.txtPromoTerms.setVisible(promotion.term.removeHtmlTag().isNotBlank())

        val promoFreeList = listOf(
            Constant.PROMO_TYPE_FREE,
            Constant.PROMO_TYPE_FREE_MEMBER,
            Constant.PROMO_TYPE_FREE_VOUCHER
        )
        val isPromoFree = promoFreeList.any { it == promotion.promotionTypeId.safe() }
        if (isPromoFree) {
            viewModel.taskShowFreeItemDialog.postValue(promotion.promotionId)
            dialog.dismiss()
            return

//            promotion.promotionProduct.sortByDescending { it.type.safe() }
//            val qtyOrder =
//                promotion.promotionProduct.firstOrNull { it.type == "order_menu" }?.qty.safe()
//            val qtyFree =
//                promotion.promotionProduct.firstOrNull { it.type == "free_menu" }?.qty.safe()
//            binding.txtPromotionInfo.text =
//                "Menu Order yang harus di beli minimal $qtyOrder, item free yang bisa di peroleh $qtyFree (berlaku kelipatan)"
        }

        val promoSpecialPriceList = listOf(
            Constant.PROMO_TYPE_SPECIAL_PRICE,
            Constant.PROMO_TYPE_SPECIAL_PRICE_MEMBER,
            Constant.PROMO_TYPE_SPECIAL_PRICE_VOUCHER
        )

        val isPromoSpecialPrice =
            promoSpecialPriceList.any { it == promotion.promotionTypeId.safe() }

        val promoDiscList = listOf(
            Constant.PROMO_TYPE_DISCOUNT,
            Constant.PROMO_TYPE_DISCOUNT_MEMBER,
            Constant.PROMO_TYPE_DISCOUNT_VOUCHER
        )
        val isPromoDiscount = promoDiscList.any { it == promotion.promotionTypeId.safe() }
        if (isPromoDiscount && promotion.discountType == "nota") {
            hidePricePromo = true
            val promoAmount =
                if (promotion.usePercent == 1) "${promotion.ammount}%" else promotion.ammount.safe()
                    .toInt().toCurrency("Rp")
            val maxDisc =
                if (promotion.maximumDiscountNominal > 0) "(Maksimal ${promotion.maximumDiscountNominal.toCurrency()})" else ""
            binding.txtPromotionInfo.text =
                "Discount Total $promoAmount $maxDisc untuk semua item"
        }

        promotionProductList.clear()
        promotion.promotionProduct.filter { it.type != Constant.PROMO_PRODUCT_ORDER }.forEach { product ->
            var promoInfo = ""
            var pricePromo = 0
            val productOrigin =
                viewModel.products.firstOrNull { it.productDetailId == product.productDetailId }
            val priceOrigin = productOrigin?.priceSell.safe()
            var showPriceOrigin = true

            if (isPromoDiscount && product.isPercent == 1) {
                promoInfo = "-${product.ammount}%"
                pricePromo =
                    (priceOrigin - (priceOrigin * (product.ammount / 100.0))).toInt()
                pricePromo = pricePromo.min(0)
            } else if (isPromoDiscount && product.isPercent == 0) {
                promoInfo = "-${product.ammount.toCurrency()}"
                pricePromo = (priceOrigin - product.ammount).min(0)
            } else if (isPromoSpecialPrice) {
                pricePromo = product.price.safe()
            } else if (isPromoFree) {
                if (product.type == "free_menu") {
                    pricePromo = 0
                } else {
                    showPriceOrigin = false
                    pricePromo = priceOrigin
                    promoInfo = "Menu Order"
                }
            }

            promotionProductList.add(
                PromotionProductDialog(
                    product.productDetailId.safe(),
                    productOrigin?.name.safe(),
                    priceOrigin,
                    pricePromo,
                    promoInfo,
                    showPriceOrigin
                )
            )
        }

        //promotion.promotionTypeId?.readablePromoType()
        Timber.i("total promo products: ${promotionProductList.size} | actual list: ${promotion.promotionProduct.size}")
        binding.recviewPromoProduct.adapter?.notifyDataSetChanged()
        binding.txtPromotionInfo.setVisible(binding.txtPromotionInfo.text.toString().isNotBlank())
    }
}