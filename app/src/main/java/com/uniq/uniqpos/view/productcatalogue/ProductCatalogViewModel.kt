package com.uniq.uniqpos.view.productcatalogue

import android.graphics.Bitmap
import android.graphics.BitmapFactory
import com.bugsnag.android.Bugsnag
import com.google.gson.Gson
import com.uniq.uniqpos.BuildConfig
import com.uniq.uniqpos.data.local.entity.CategoryEntity
import com.uniq.uniqpos.data.local.entity.ProductEntity
import com.uniq.uniqpos.data.local.entity.ProductTypeEntity
import com.uniq.uniqpos.data.local.entity.PurchaseReportCategoryEntity
import com.uniq.uniqpos.data.local.entity.SubCategoryEntity
import com.uniq.uniqpos.data.local.entity.ToHashMapString
import com.uniq.uniqpos.data.local.entity.UnitEntity
import com.uniq.uniqpos.data.local.sharedpref.SharedPref
import com.uniq.uniqpos.data.remote.model.Outlet
import com.uniq.uniqpos.data.remote.model.ProductExtractionResponse
import com.uniq.uniqpos.data.remote.model.toProductEntity
import com.uniq.uniqpos.data.remote.repository.ProductRepository
import com.uniq.uniqpos.model.SnackbarEvent
import com.uniq.uniqpos.model.ToastMessage
import com.uniq.uniqpos.util.BACKGROUND
import com.uniq.uniqpos.util.Level
import com.uniq.uniqpos.util.Utils
import com.uniq.uniqpos.util.await
import com.uniq.uniqpos.util.extensions.base64ToFile
import com.uniq.uniqpos.util.image.toBase64
import com.uniq.uniqpos.util.lifecycle.BaseViewModel
import com.uniq.uniqpos.util.lifecycle.Event
import com.uniq.uniqpos.util.lifecycle.Success
import com.uniq.uniqpos.util.lifecycle.ViewModelState
import com.uniq.uniqpos.util.safe
import com.uniq.uniqpos.util.extensions.sizeInMb
import com.uniq.uniqpos.util.extensions.toBase64
import com.uniq.uniqpos.util.toRequestBody
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.joinAll
import kotlinx.coroutines.launch
import okhttp3.RequestBody
import timber.log.Timber
import java.io.ByteArrayInputStream
import java.io.ByteArrayOutputStream
import java.io.File
import javax.inject.Inject
import kotlin.collections.set
import kotlin.coroutines.resume
import kotlin.coroutines.suspendCoroutine

/**
 * Created by annasblackhat on 26/10/18
 */
class ProductCatalogViewModel @Inject constructor(val productRepository: ProductRepository, val sharedPref: SharedPref) : BaseViewModel() {

    val productTypeList = ArrayList<ProductTypeEntity>()
    val subCategoryList = ArrayList<SubCategoryEntity>()
    val categoryList = ArrayList<CategoryEntity>()
    val unitList = ArrayList<UnitEntity>()
    val purchaseReportCategoryList = ArrayList<PurchaseReportCategoryEntity>()

    val subCategories = productRepository.subCategories

    private val viewModelJob = Job()
    private val uiScope = CoroutineScope(Dispatchers.Main + viewModelJob)

    data class SuccessExtractMenu(val data: List<ProductExtractionResponse>): ViewModelState()

    fun loadProductType() = productRepository.loadProductType()

    fun loadCategory() = productRepository.loadCategory()

    fun loadPurchaseReportCategory() = productRepository.loadPurchaseReportCategory()

    fun loadUnit() = productRepository.loadUnit()

    fun addProduct(product: ProductEntity) {
        uiScope.launch {
            _loadingDialog.postValue(Event(true))

            val outlet = sharedPref.getJson(SharedPref.OUTLET_DATA, Outlet::class.java)
            product.active = convertActiveStatus(product.active ?: "all")
            product.outletFkid = outlet?.outletId.safe()

            //set default value
            if(product.unitName.isNullOrBlank() && product.unitFkid == null){
                product.unitName = "pcs"
            }

            //convert input (product) to map
            val productMap = product.ToHashMapString()
            Timber.i("addProduct: ${Gson().toJson(productMap)}")

            val productMapReqBody = HashMap<String, RequestBody>()
            for (item in productMap){
                productMapReqBody[item.key] = item.value.safe().toRequestBody()
            }

            Timber.i("photo path: ${product.photo}")
            val imgPart = product.photo?.let {
                Utils.requestBodyFile(product.photo, Utils.MediaTypes.IMAGE, "image")
            } ?: kotlin.run { null }

            try {
//                if(imgPart == null && BuildConfig.DEBUG) throw Exception("photo is null")
                productRepository.addProductMultipartAsync(productMapReqBody, imgPart)
                _snackbarText.postValue(Event(SnackbarEvent("${product.name?.uppercase()} berhasil ditambahkan!")))
                _state.postValue(Success)
            } catch (e: Exception) {
                _dialogMsg.postValue(Event("failed adding product\n${e.message}"))
                Timber.i("failed adding product: $e")
                Bugsnag.notify(e)
            }
            _loadingDialog.postValue(Event(false))
        }
    }

    fun addProductAsyn(product: ProductEntity, trySecondAttempt: Boolean = true, listener: (Boolean, Exception?) -> Unit) {
//        productRepository.addProductAsync(product, listener)
        Timber.i("Outlet ID : >> ${product.outletFkid}")
        val data = HashMap<String, RequestBody>()
        data["name"] = product.name.safe().toRequestBody()
        data["product_type_fkid"] = product.productTypeFkid.toString().toRequestBody()
        data["product_type_name"] = product.productcategoryName.safe().toRequestBody()
        data["product_category_fkid"] = product.productCategoryFkid.toString().toRequestBody()
        data["product_category_name"] = product.productcategoryName.safe().toRequestBody()
        data["product_subcategory_name"] = product.productSubCategoryName.safe().toRequestBody()
        data["purchase_report_category_fkid"] = product.purchaseReportCategoryFkid.toString().toRequestBody()
        data["purchase_report_category_name"] = product.purchaseReportCategoryName.safe().toRequestBody()
        data["unit_fkid"] = product.unitFkid.toString().toRequestBody()
        data["unit_name"] = product.unitName.safe().toRequestBody()
        data["stock_management"] = product.stockManagement.toString().toRequestBody()
        data["barcode"] = product.barcode.safe().toRequestBody()
        data["sku"] = (product.sku ?: "").toRequestBody()
        data["data_created"] = System.currentTimeMillis().toString().toRequestBody()
        data["outlet_fkid"] = product.outletFkid.toString().toRequestBody()
        data["price_buy_start"] = product.priceBuyStart.toString().toRequestBody()
        data["price_buy"] = product.priceBuy.toString().toRequestBody()
        data["price_sell"] = product.priceSell.toString().toRequestBody()
        data["active"] = product.active.safe().toRequestBody()

        val imgPart = product.photo?.let {
            Timber.i("photo path: ${product.photo}")
            Utils.requestBodyFile(product.photo, Utils.MediaTypes.IMAGE, "image")
        } ?: kotlin.run { null }

        uiScope.launch {
            try {
                productRepository.addProductMultipartAsync(data, imgPart)
                listener(true, null)
            } catch (e: Exception) {
                Timber.i("adding product error: $e - ${Gson().toJson(product)}")
                //if file not found, add product without photo
                //e is java.io.FileNotFoundException &&
                if (trySecondAttempt) {
                    Timber.i("try second attempt...")
                    addProductAsyn(product.copy(photo = null), false, listener)
                } else {
                    listener(false, Exception(e.message))
                }
            }
        }
    }

    fun compressBitmap(bitmap: Bitmap, externalDir: File?): Bitmap {
        val file = File(externalDir, "uniq_product_" + System.currentTimeMillis() + ".jpeg")
        val bitmapResult: Bitmap
        var quality = 80
        val byteArray = ByteArrayOutputStream()
        do {
            byteArray.reset()
            bitmap.compress(Bitmap.CompressFormat.JPEG, quality, byteArray)
            file.writeBytes(byteArray.toByteArray())
            Timber.i("file size: ${file.sizeInMb()} mb")

            quality -= 10
        }while(file.sizeInMb() >= 1.0 && quality > 40)

        Timber.i("file path: ${file.path}")
        bitmapResult = BitmapFactory.decodeStream(ByteArrayInputStream(byteArray.toByteArray()))
        byteArray.close()
        return bitmapResult
    }

    suspend fun largeBitmapToBase64(bitmap: Bitmap): String {
        return suspendCoroutine { continuation ->
            BACKGROUND.submit {
                continuation.resume(bitmap.toBase64())
            }
        }
    }

    fun extractMenu(bitmap: Bitmap? = null, externalDir: File? = null, imageFile: File? = null){
        if(bitmap == null && imageFile == null){
            _toastMessage.postValue(Event(ToastMessage("image not found", type = Level.ERROR)))
            return
        }

        uiScope.launch {
            _loadingDialogMessage.postValue(Event(Pair("please wait, AI doing its magic...", true)))
           var imgBitmap = bitmap

            //compress image
            Timber.i("externalDir exist: ${externalDir?.exists()} | imageFile: ${imageFile?.path}")
            externalDir?.takeIf { it.exists() && imgBitmap != null }?.let { file ->
                try {
                    Timber.i("compressing image...")
                    _toastMessage.postValue(Event( ToastMessage("compressing image...", type = Level.INFO)))
                    imgBitmap = compressBitmap(imgBitmap!!, file)
                } catch (e: Exception) {
                    _toastMessage.postValue(Event( ToastMessage("failed compressing menu", type = Level.ERROR)))
                }
            }
            try {
                val imgBase64 = imgBitmap?.let { largeBitmapToBase64(it) } ?: run { imageFile?.toBase64() }

                externalDir?.let { baseDir ->
                    val sizeMb = imgBase64?.base64ToFile(baseDir)?.sizeInMb()
                    Timber.i("image size: $sizeMb mb")
                    if(sizeMb != null && sizeMb >= 1.0){
                        throw Exception("image size is too large, should be less than 1mb")
                    }
                }

                val image = imageFile?.let { Utils.requestBodyFile(imageFile.path, Utils.MediaTypes.IMAGE, "file") }

//                val response = productRepository.extractMenu(imgBase64!!).await()
                val response = productRepository.extractMenuFile(image!!).await()
                if(response.status.safe()){
                    _toastMsg.postValue(Event("Menu berhasil di ekstrak"))
                    _state.postValue(SuccessExtractMenu(response.data ?: listOf()))
                }else{
                    _toastMsg.postValue(Event(response.message ?: "failed extracting menu"))
                }
            } catch (e: Exception) {
                _toastMessage.postValue(Event( ToastMessage("failed extracting menu! \n${e.message}", type = Level.ERROR)))
            }
            _loadingDialog.postValue(Event(false))
        }
    }

    fun saveProductExtraction(productList: ArrayList<ProductEntity>){
        uiScope.launch {
            _loadingDialogMessage.postValue(Event(Pair("saving...", true)))
            try {
                val outlet = sharedPref.getJson(SharedPref.OUTLET_DATA, Outlet::class.java)
                productRepository.saveProductExtraction(productList, outlet?.outletId.safe())
            } catch (e: Exception) {
                Timber.i("failed saving: ${Gson().toJson(productList)} | err: $e")
                _dialogMsg.postValue(Event("failed saving product"))
                _toastMessage.postValue(Event(ToastMessage(e.message, type = Level.ERROR)))
            }
            _loadingDialogMessage.postValue(Event(Pair("saving...", false)))
            _state.postValue(Success)
        }
    }

    fun saveProductLoop(productList: java.util.ArrayList<ProductExtractionResponse>) {
        val jobList = ArrayList<Job>()
        uiScope.launch {
            _loadingDialogMessage.postValue(Event(Pair("saving...", true)))
            val outlet = sharedPref.getJson(SharedPref.OUTLET_DATA, Outlet::class.java)
            for (product in productList){
                val productMap = product.toProductEntity("item", outlet?.outletId.safe()).ToHashMapString()
                Timber.i("addProduct: ${Gson().toJson(productMap)}")
                val productMapReqBody = HashMap<String, RequestBody>()
                for (item in productMap){
                    productMapReqBody[item.key] = item.value.safe().toRequestBody()
                }

                val job = this.launch {
                     try {
                         productRepository.addProductMultipartAsync(productMapReqBody, null)
                         Timber.d("product added ${product.name}")
                     } catch (e: Exception) {
                         Timber.i("saving product '${product.name}' err $e")
                         _toastMessage.postValue(Event( ToastMessage("failed saving product ${product.name}", type = Level.ERROR)))
                     }
                }
                jobList.add(job)
                Timber.d("${jobList.size} job added")
                if (jobList.size >= 3){
                    Timber.d("waiting ${jobList.size} jobs...")
                    jobList.joinAll()
                    jobList.clear()
                }
            }

            Timber.d("waiting all jobs...")
            jobList.joinAll()
            _loadingDialogMessage.postValue(Event(Pair("saving...", false)))
            _state.postValue(Success)
        }

    }

    private fun convertActiveStatus(status: String) = when (status.lowercase()) {
        "active on sales" -> "on_sales"
        "active on link" -> "on_link"
        "no active" -> "off"
        else -> "on_all"
    }

    override fun onCleared() {
        super.onCleared()
        viewModelJob.cancel()
    }
}