package com.uniq.uniqpos.view.reservation

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import androidx.lifecycle.Observer
import com.uniq.uniqpos.R
import com.uniq.uniqpos.data.local.entity.ReservationEntity
import com.uniq.uniqpos.data.local.sharedpref.SharedPref
import com.uniq.uniqpos.data.local.sharedpref.getJson
import com.uniq.uniqpos.data.remote.model.Outlet
import com.uniq.uniqpos.databinding.ActivityReservationBinding
import com.uniq.uniqpos.databinding.ListItemReservationBinding
import com.uniq.uniqpos.util.Utils
import com.uniq.uniqpos.view.global.BaseActivity
import com.uniq.uniqpos.view.global.GlobalAdapter
import com.uniq.uniqpos.view.global.GlobalViewHolder
import com.uniq.uniqpos.viewmodel.ReservationViewModel

class ReservationActivity : BaseActivity<ReservationViewModel, ActivityReservationBinding>() {

    private val reservationList = ArrayList<ReservationEntity>()

    override fun getLayoutRes() = R.layout.activity_reservation
    override fun getViewModel() = ReservationViewModel::class.java

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        observeData()

        binding.recviewReservation.adapter = object : GlobalAdapter<ListItemReservationBinding>(
            R.layout.list_item_reservation,
            reservationList
        ) {
            override fun onBindViewHolder(
                holder: GlobalViewHolder<ListItemReservationBinding>,
                position: Int
            ) {
                super.onBindViewHolder(holder, position)
                holder.binding.txtTransaction.setOnClickListener {
                    val intent = Intent()
                    intent.putExtra("data", reservationList[position])
                    setResult(Activity.RESULT_OK, intent)
                    finish()
                }
                holder.binding.txtEdit.setOnClickListener {
                    val intent =
                        Intent(this@ReservationActivity, AddReservationActivity::class.java)
                    intent.putExtra("data", reservationList[position])
                    startActivity(intent)
                }
            }
        }
        binding.fabAdd.setOnClickListener {
            startActivity(
                Intent(
                    this,
                    AddReservationActivity::class.java
                )
            )
        }
    }

    override fun observeData() {
        val outlet = getJson(SharedPref.OUTLET_DATA, Outlet::class.java)
        viewModel.getReservation(outlet?.outletId)
            .observe(this, Observer {
                reservationList.clear()
                it?.data?.filter { (it.timeStart ?: 0) >= Utils.getMinTimeMillisToday() }
                    ?.let { reservationList.addAll(it) }
                binding.recviewReservation.adapter?.notifyDataSetChanged()
            })
    }
}
