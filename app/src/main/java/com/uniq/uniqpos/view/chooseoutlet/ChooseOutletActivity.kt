package com.uniq.uniqpos.view.chooseoutlet

import android.content.DialogInterface
import android.content.Intent
import android.os.Bundle
import android.view.Menu
import android.view.MenuItem
import com.uniq.uniqpos.R
import com.uniq.uniqpos.data.local.sharedpref.SharedPref
import com.uniq.uniqpos.data.local.sharedpref.clearAllData
import com.uniq.uniqpos.data.local.sharedpref.getJson
import com.uniq.uniqpos.data.local.sharedpref.putJson
import com.uniq.uniqpos.data.remote.model.Outlet
import com.uniq.uniqpos.databinding.ActivityChooseOutletBinding
import com.uniq.uniqpos.databinding.ListItemOutletBinding
import com.uniq.uniqpos.model.Admin
import com.uniq.uniqpos.model.LoginAdmin
import com.uniq.uniqpos.util.awaitAsync
import com.uniq.uniqpos.util.showMessage
import com.uniq.uniqpos.util.view.RecyclerItemClickListener
import com.uniq.uniqpos.view.chooseoperator.ChooseOperatorActivity
import com.uniq.uniqpos.view.global.BaseActivity
import com.uniq.uniqpos.view.global.GlobalAdapter
import com.uniq.uniqpos.view.login.LoginAdminActivity
import com.uniq.uniqpos.viewmodel.AuthViewModel

class ChooseOutletActivity : BaseActivity<AuthViewModel, ActivityChooseOutletBinding>() {

    private val outletList = arrayListOf<Outlet>()

    override fun getLayoutRes() = R.layout.activity_choose_outlet
    override fun getViewModel() = AuthViewModel::class.java

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        window.setBackgroundDrawable(null)

        val admin = getJson(SharedPref.ADMIN_DATA, Admin::class.java)
        title = admin?.businessName ?: "Outlet List"

        binding.recviewOutlet.adapter =
            object : GlobalAdapter<ListItemOutletBinding>(R.layout.list_item_outlet, outletList) {}

        binding.recviewOutlet.addOnItemTouchListener(RecyclerItemClickListener(
            this
        ) { _, position ->
            putJson(SharedPref.OUTLET_DATA, outletList[position])
            var intent = Intent(this, ChooseOperatorActivity::class.java)
            intent.putExtra("outletId", outletList[position].outletId)
            startActivity(intent)
        })

        binding.swipeRefresh.setOnRefreshListener { loadData() }
        loadData()
    }

    private fun loadData() {
        viewModel.getOutlet()
            .awaitAsync({
                outletList.clear()
                it.body()?.data?.let { outletList.addAll(it) }
                binding.recviewOutlet.adapter?.notifyDataSetChanged()
            }, {}, this, binding.swipeRefresh, false)
    }

    private fun logout() {
        showMessage(
            getString(R.string.are_you_sure),
            getString(R.string.logout).uppercase(),
            { _, _ ->
                clearAllData()
                startActivity(Intent(this, LoginAdminActivity::class.java))
                finish()
            })
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        when (item.itemId) {
            R.id.action_logout -> logout()
        }
        return super.onOptionsItemSelected(item)
    }

    override fun onCreateOptionsMenu(menu: Menu): Boolean {
        menuInflater.inflate(R.menu.menu_outlet, menu)
        return super.onCreateOptionsMenu(menu)
    }
}
