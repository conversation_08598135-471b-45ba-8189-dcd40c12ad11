package com.uniq.uniqpos.view.billing

import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import android.os.Bundle
import android.view.MenuItem
import androidx.appcompat.app.AppCompatActivity
import androidx.databinding.DataBindingUtil
import com.uniq.uniqpos.R
import com.uniq.uniqpos.databinding.ActivityBillingInformationBinding
import com.uniq.uniqpos.model.Billing
import com.uniq.uniqpos.util.*
import timber.log.Timber
import java.text.SimpleDateFormat


class BillingInformationActivity : AppCompatActivity() {

    private lateinit var binding: ActivityBillingInformationBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = DataBindingUtil.setContentView(this, R.layout.activity_billing_information)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        supportActionBar?.setHomeAsUpIndicator(R.drawable.ic_clear_white_24dp)

        val billing: Billing? = intent.getParcelableExtra("billing")
        val serviceName: String = intent.getStringExtra("service_name") ?: ""
        val period: String = intent.getStringExtra("period") ?: ""
        val slot: String = intent.getStringExtra("slot") ?: ""

        val dateExpired = try {
            val sdf = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSXXX")
            sdf.parse(billing?.expirationDate)?.dateFormat("EEEE, dd MMM yyyy HH:mm")
        } catch (e: Exception) {
            Timber.i("converting date error, $e")
            billing?.expirationDate
        }

        binding.txtDateExpired.text = dateExpired
        binding.txtBank.text = "${billing?.bankCode} Virtual Account"
        binding.txtVirtualAccount.text = billing?.accountNumber
        binding.txtServiceName.text = serviceName
        binding.txtPeriod.text = period
        binding.txtSlot.text = "$slot slot"
        setTextCurrency(binding.txtTotal, billing?.expectedAmount.safe(), "Rp")

        binding.txtCopy.setOnClickListener {
            val clipboard: ClipboardManager = getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
            val clip: ClipData = ClipData.newPlainText("va_number", billing?.accountNumber)
            clipboard.setPrimaryClip(clip)
            toast("nomor virtual account berhasi disalin", level = Level.INFO)
        }
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        onBackPressed()
        return super.onOptionsItemSelected(item)
    }

    override fun onBackPressed() {
        showMessage("Detail Invoice Telah Dikirimkan Ke Nomor WhatsApp Anda", "CONFIRMATION", positiveAction = { _, _ ->
            super.onBackPressed()
        })
    }
}