package com.uniq.uniqpos.view.transaction.dialog

import android.content.DialogInterface
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewTreeObserver.OnGlobalLayoutListener
import android.widget.FrameLayout
import androidx.fragment.app.FragmentManager
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.uniq.uniqpos.R
import com.uniq.uniqpos.databinding.DialogFreeItemBinding
import com.uniq.uniqpos.databinding.DialogFreeItemListBinding
import com.uniq.uniqpos.databinding.ListItemPromoFreeBinding
import com.uniq.uniqpos.databinding.ListItemPromoFreeHeaderBinding
import com.uniq.uniqpos.model.PromotionFree
import com.uniq.uniqpos.util.*
import com.uniq.uniqpos.view.global.GlobalAdapterMultiView
import com.uniq.uniqpos.view.transaction.TransactionViewModel
import timber.log.Timber


class FreeItemListDialogFragment : BottomSheetDialogFragment() {

    private var viewModel: TransactionViewModel? = null
    private val promotionFreeList = ArrayList<PromotionFree>()
    private var isFreeItemChanged = false
    private var _binding: DialogFreeItemListBinding? = null
    private val binding: DialogFreeItemListBinding get() = _binding!!
    private var filterPromoId = 0

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.apply {
            filterPromoId = getInt(FILTER_PROMO_ID, 0)
            Timber.i(">> Filter Promo (get): $filterPromoId")
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = DialogFreeItemListBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        binding.imgClose.setOnClickListener { dismiss() }

        setupView()
        viewModel?.apply {
            promotionFreeList.clear()
            promotionFreeList.addAll(createFreeItemList(filterPromoId))
            binding.recViewFree.adapter?.notifyDataSetChanged()
        } ?: run { context?.toast("vm not initialized...") }

        if (resources.getBoolean(R.bool.landscape_only)) {
            view.viewTreeObserver.addOnGlobalLayoutListener(object : OnGlobalLayoutListener {
                override fun onGlobalLayout() {
                    view.viewTreeObserver.removeOnGlobalLayoutListener(this)
                    val dialog = dialog as BottomSheetDialog
                    val bottomSheet =
                        dialog.findViewById(com.google.android.material.R.id.design_bottom_sheet) as FrameLayout?
                    val behavior = BottomSheetBehavior.from(bottomSheet!!)
                    behavior.state = BottomSheetBehavior.STATE_EXPANDED
                }
            })
        }

        binding.swAutoShow.setVisible(resources.getBoolean(R.bool.landscape_only))
        binding.swAutoShow.isChecked = viewModel?.getAutoShowPromoFreeSetting().safe(true)
        binding.swAutoShow.setOnCheckedChangeListener { _, selected ->
            viewModel?.setAutoShowPromoFree(selected)
        }
    }

    private fun setupView() {
        binding.recViewFree.adapter = object : GlobalAdapterMultiView(
            R.layout.list_item_promo_free_header,
            R.layout.list_item_promo_free,
            promotionFreeList,
            { position -> if (promotionFreeList[position].isHeader) ViewType.TYPE_HEADER else ViewType.TYPE_ITEM },
            { bindingHolder, position ->
                when (bindingHolder) {
                    is ListItemPromoFreeHeaderBinding -> {
                        bindingHolder.root.setOnClickListener {
                            if (bindingHolder.txtPromoDetail.visibility == View.GONE) {
                                bindingHolder.txtPromoDetail.visibility = View.VISIBLE
                                bindingHolder.root.slideUp()
                                bindingHolder.txtPromoDetail.post {
                                    bindingHolder.imgArrow.rotation = 90f
                                }
                            } else {
                                bindingHolder.txtPromoDetail.visibility = View.GONE
                                bindingHolder.txtPromoDetail.post {
                                    bindingHolder.imgArrow.rotation = 0f
                                }
                            }
                        }
                    }
                    is ListItemPromoFreeBinding -> {
                        bindingHolder.imgMin.setOnClickListener {
                            changeQtyFreeItem(
                                -1,
                                position,
                                bindingHolder
                            )
                        }
                        bindingHolder.imgAdd.setOnClickListener {
                            changeQtyFreeItem(
                                1,
                                position,
                                bindingHolder
                            )
                        }
                        bindingHolder.txtAddAll.setOnClickListener {
                            val freeItemAvailable = getMaxFreeItem(position)
                            val qtyNew = getMaxFreeItem(position) - getTotalItemAdded(position)
                            if(freeItemAvailable == 0){
                                context?.toast("no free item available", level = Level.WARNING)
                            }else if (qtyNew <= 0){
                                context?.toast("all free item has been added", level = Level.WARNING)
                            }else{
                                changeQtyFreeItem(
                                    qtyNew,
                                    position,
                                    bindingHolder
                                )
                                dismiss()
                            }
                        }
                    }

                }
            }
        ) {}
    }

    private fun changeQtyFreeItem(
        qty: Int,
        position: Int,
        holderBinding: ListItemPromoFreeBinding,
    ) {
        val result = holderBinding.txtQty.text.toString().safeToInt() + qty
        if (result < 0) return

        var maxFree = getMaxFreeItem(position)
        if (getTotalItemAdded(position) + qty > maxFree) {
            context?.toast("Qty free melebihi batas maksimal!")
        } else {
            isFreeItemChanged = true
            holderBinding.txtQty.text = (result).toString()
            promotionFreeList[position].qtySelected = result
        }
    }

    private fun getMaxFreeItem(position: Int): Int {
        for (i in position downTo 0) {
            if (promotionFreeList[i].isHeader) {
                return promotionFreeList[i].qtyMax
            }
        }
        return 0
    }

    private  fun getTotalItemAdded(position: Int): Int{
        return promotionFreeList.filter { it.promotionId == promotionFreeList[position].promotionId }
            .sumOf { it.qtySelected }
    }

    fun setViewModel(viewModel: TransactionViewModel) {
        this.viewModel = viewModel
    }

    private fun addFreeItemToBill() {
        if (isFreeItemChanged) {
            viewModel?.applyFreeItem(promotionFreeList)
            context?.toast("updating free item...")
        }
    }

    override fun show(manager: FragmentManager, tag: String?) {
        Timber.i("[promo] open FreeItemListDialog")
        if (!isAdded) {
            super.show(manager, tag)
        }
    }

    override fun onDismiss(dialog: DialogInterface) {
        addFreeItemToBill()
        super.onDismiss(dialog)
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

    companion object {
        const val FILTER_PROMO_ID = "filter_promo_id"

        fun newInstance(filterPromoId: Int = 0): FreeItemListDialogFragment {
            Timber.i(">> Filter Promo (set): $filterPromoId")
            val args = Bundle()
            args.putInt(FILTER_PROMO_ID, filterPromoId)
            val fragment = FreeItemListDialogFragment()
            fragment.arguments = args
            return fragment
        }
    }
}