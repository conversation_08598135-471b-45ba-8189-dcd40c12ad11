package com.uniq.uniqpos.view.transactionhistory

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.uniq.uniqpos.data.local.entity.EmployeeEntity
import com.uniq.uniqpos.data.local.entity.PiutangEntity
import com.uniq.uniqpos.data.local.entity.RefundEntity
import com.uniq.uniqpos.data.local.entity.SalesEntity
import com.uniq.uniqpos.data.local.entity.ShiftEntity
import com.uniq.uniqpos.data.local.sharedpref.SharedPref
import com.uniq.uniqpos.data.remote.model.Employee
import com.uniq.uniqpos.data.remote.repository.OutletRepository
import com.uniq.uniqpos.data.remote.repository.SalesRepository
import com.uniq.uniqpos.data.remote.repository.SettingRepository
import com.uniq.uniqpos.model.Order
import com.uniq.uniqpos.model.SnackbarEvent
import com.uniq.uniqpos.model.ToastMessage
import com.uniq.uniqpos.util.employeeList
import com.uniq.uniqpos.util.lifecycle.BaseViewModel
import com.uniq.uniqpos.util.lifecycle.Event
import com.uniq.uniqpos.util.readableError
import com.uniq.uniqpos.util.safe
import kotlinx.coroutines.*
import timber.log.Timber
import java.util.ArrayList
import javax.inject.Inject

/**
 * Created by annasblackhat on 16/05/18
 */

class TransactionHistoryViewModel @Inject constructor(
    private val salesRepository: SalesRepository,
    private val outletRepository: OutletRepository,
    private val settingRepository: SettingRepository,
    private val sharedPref: SharedPref
) : BaseViewModel() {

    var enableRefund = true
    val salesList = ArrayList<SalesEntity>()
    val shiftList = ArrayList<ShiftEntity>()
    private val salesListTmp = ArrayList<SalesEntity>()

    private val mDialog = MutableLiveData<String>()

    val dialog: LiveData<String>
        get() = mDialog

    private val _piutangNavigation = MutableLiveData<Event<PiutangEntity>>()
    val piutangNavigation: LiveData<Event<PiutangEntity>>
        get() = _piutangNavigation

    val refreshList = MutableLiveData<Unit>()

    fun getShifts() = outletRepository.getShiftSync()

    fun getOpenShift(outletId: Int) = outletRepository.getOpenShiftSync(outletId)

    fun getTransactionHistoryByOpenShift(openShiftFkid: Long) =
        salesRepository.getSalesByOpenShift(openShiftFkid)

    fun getPrinterSetting() = settingRepository.getPrinters()

    fun saveRefund(sales: SalesEntity, reason: String, employeeId: Int) {
        Timber.i("saveRefund ${sales.noNota} by $employeeId, reason: $reason")
        if(employeeId == 0){
            _toastMessage.postValue(Event(ToastMessage("Gagal Refund, Silahkan Login Kembali!")))
            return
        }
        val salesRefund = RefundEntity(
            salesId = sales.noNota,
            grandTotal = sales.grandTotal,
            employeeId = employeeId,
            reason = reason.trim()
        )

        val tmpSales = sales.copy(
            synced = false, status = "Refund",
            timeModified = System.currentTimeMillis(), refundReason = reason.trim(),
            salesRefund = salesRefund
        )

        salesRepository.saveRefund(salesRefund)

        //tmpSales.grandTotal = tmpSales.grandTotal * -1
        updateSales(tmpSales)
        _toastMessage.postValue(Event(ToastMessage("Transaksi ${sales.displayNota} berhasil di Refund!")))
    }

    fun updateSales(salesEntity: SalesEntity) {
        viewModelScope.launch { salesRepository.updateSales(salesEntity) }
    }

    fun getPiutangBySaleId(salesId: String) {
        viewModelScope.launch {
            try {
                val piutang =
                    withContext(Dispatchers.IO) { salesRepository.getPiutangBySalesId(salesId) }
                piutang?.let {
                    _piutangNavigation.postValue(Event(it))
                } ?: run {
                    _snackbarText.postValue(Event(SnackbarEvent("data not found!")))
                }
            } catch (e: Exception) {
                _snackbarText.postValue(Event(SnackbarEvent(e.readableError())))
            }
        }
    }

    fun search(q: String?) {
        if (q.isNullOrBlank()) {
            salesList.clear()
            salesList.addAll(salesListTmp)
            salesListTmp.clear()
        } else {
            if (salesListTmp.isEmpty()) salesListTmp.addAll(salesList)
            salesList.clear()

            fun matchId(s: SalesEntity) = s.displayNota.lowercase().contains(q.lowercase())
            fun matchCustomer(s: SalesEntity) =
                s.customer.safe().lowercase().contains(q.lowercase())

            fun matchProduct(s: SalesEntity) = s.items.safe().lowercase().contains(q.lowercase())
            fun matchPayment(s: SalesEntity) = s.payment.lowercase().contains(q.lowercase())

            salesList.addAll(salesListTmp.filter {
                matchId(it) || matchCustomer(it) || matchProduct(
                    it
                ) || matchPayment(it)
            })
        }
        refreshList.postValue(Unit)
    }

    fun isSalesRefund(salesId: String): Boolean {
        Timber.d(
            "salesId : $salesId | total Sales list: ${salesList.size} | ${
                salesList.filter { it.noNota == salesId }.map { "${it.noNota} | ${it.status}" }
            }"
        )
        return salesList.any { it.noNota == salesId && it.status.safe().lowercase() == "refund" }
    }

    fun setData(
        items: List<SalesEntity>?, paymentMethod: String = "",
        isOnlyDiscVouc: Boolean = false,
        isPromotionOnly: Boolean = false
    ) {
        salesList.clear()
        var itemCount = 0
        items?.forEach {
            var isAddData = true
            if (paymentMethod.isNotEmpty() && !it.payment.lowercase()
                    .contains(paymentMethod.lowercase())
            ) {
                isAddData = false
            }
            if (isOnlyDiscVouc) {
                if ((it.discount?.discount ?: 0) <= 0 && (it.discount?.voucher
                        ?: 0) <= 0
                ) isAddData = false
                if (!isAddData && it.orderList?.any { p -> p.discount.discount > 0 || p.discount.discountNominal > 0 } == true) isAddData =
                    true

            }

            if (isPromotionOnly) {
                isAddData =
                    !it.promotions.isNullOrEmpty() || it.orderList?.any { order -> order.promotion != null }
                        .safe()
            }

            if (isAddData) {
                if (it.salesTag?.salesTagId == 0) {
                    it.salesTag = null
                }
                itemCount++
                if (it.status?.lowercase() == "refund") {
                    val saleRefund = it.copy(
                        grandTotal = it.grandTotal * -1,
                        orderList = ArrayList(),
                        timeCreated = it.timeModified
                    )
                    val tmpOrder = ArrayList<Order>()
                    it.orderList?.forEach { order ->
                        val extraOrder = ArrayList<Order>()
                        order.extra.forEach { extraOrder.add(it.copy()) }
                        tmpOrder.add(order.copy(extra = extraOrder)) //we should use copy, otherwise everything we change in here will effect the main variable
                    }

                    tmpOrder.forEach { item ->
                        item.subTotal *= -1
                        item.extra.forEach { it.subTotal = it.subTotal * -1 }
                        saleRefund.orderList?.add(item)
                    }
                    salesList.add(saleRefund)
                    salesList.add(it.copy(status = "Success"))
                } else {
                    salesList.add(it)
                }
            }
        }
        Timber.i("total transaction: $itemCount")
    }

    fun getEmployeeById(id: Int): EmployeeEntity? {
        return sharedPref.employeeList().firstOrNull { p -> p.employeeId == id }
    }
}