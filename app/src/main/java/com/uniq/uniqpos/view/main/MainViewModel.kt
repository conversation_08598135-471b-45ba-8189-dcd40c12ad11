package com.uniq.uniqpos.view.main

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.uniq.uniqpos.BuildConfig
import com.uniq.uniqpos.data.local.entity.ShiftEntity
import com.uniq.uniqpos.data.local.sharedpref.SharedPref
import com.uniq.uniqpos.data.local.sharedpref.getJson
import com.uniq.uniqpos.data.remote.model.AppRelease
import com.uniq.uniqpos.data.remote.model.ShiftOpen
import com.uniq.uniqpos.data.remote.repository.OutletRepository
import com.uniq.uniqpos.data.remote.repository.SystemRepository
import kotlinx.coroutines.launch
import retrofit2.await
import timber.log.Timber
import javax.inject.Inject


/**
 * Created by annasblackhat on 1/11/21.
 */

class MainViewModel @Inject constructor(
    private val repoSystem: SystemRepository,
    private val repoOutlet: OutletRepository,
    private val sp: SharedPref
) : ViewModel() {

    private val _eventNewRelease = MutableLiveData<AppRelease>()
    val eventNewRelease: LiveData<AppRelease> = _eventNewRelease

    private val shiftList = ArrayList<ShiftEntity>()

    private val _shiftName = MutableLiveData<String>()
    val shiftNameLive:  LiveData<String> = _shiftName

    fun checkLatestVersion() {
        if (BuildConfig.FLAVOR != "development") {
            return
        }

        viewModelScope.launch {
            try {
                val result = repoSystem.getLatestVersion().await()
                result.data?.let { appRelease ->
                    if (appRelease.versionCode > BuildConfig.VERSION_CODE) {
                        Timber.i("should update to version: ${appRelease.versionName} --> $appRelease")
                        _eventNewRelease.postValue(appRelease)
                    }
                }
            } catch (e: Exception) {
                Timber.i("get latest version err: $e")
            }
        }
    }

    fun loadCurrentShift(){
        viewModelScope.launch {
            if (shiftList.isEmpty()){
                shiftList.addAll(repoOutlet.getAllShift())
            }
            sp.getJson(SharedPref.SHIFT_DATA, ShiftOpen::class.java)?.takeIf { it.openShiftId > 0  }?.let { openShift ->
                shiftList.firstOrNull { it.shiftId == openShift.shiftFkid }?.let { shift ->
                    _shiftName.postValue(shift.name)
                }
            }
        }
    }
}