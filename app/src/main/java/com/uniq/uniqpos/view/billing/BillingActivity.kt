package com.uniq.uniqpos.view.billing

import android.content.Intent
import android.os.Bundle
import android.view.MenuItem
import android.widget.ArrayAdapter
import com.uniq.uniqpos.R
import com.uniq.uniqpos.databinding.ActivityBillingBinding
import com.uniq.uniqpos.model.BillingService
import com.uniq.uniqpos.util.*
import com.uniq.uniqpos.util.lifecycle.Failed
import com.uniq.uniqpos.util.lifecycle.Loading
import com.uniq.uniqpos.view.global.BaseActivity

class BillingActivity : BaseActivity<BillingViewModel, ActivityBillingBinding>() {

    private lateinit var serviceAdapter: ArrayAdapter<String>
    private lateinit var billingPeriodAdapter: ArrayAdapter<String>
    private lateinit var paymentMethodAdapter: ArrayAdapter<String>

    private var serviceSelected: Int = -1
    private var periodSelected: Int = -1
    private var paymentSelected: Int = -1

    override fun getLayoutRes() = R.layout.activity_billing
    override fun getViewModel() = BillingViewModel::class.java

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)

        serviceAdapter = ArrayAdapter(this, android.R.layout.simple_list_item_1, arrayListOf())
        billingPeriodAdapter = ArrayAdapter(this, android.R.layout.simple_list_item_1, arrayListOf())
        paymentMethodAdapter = ArrayAdapter(this, android.R.layout.simple_list_item_1, arrayListOf())

        binding.spinService.setAdapter(serviceAdapter)
        binding.spinPeriod.setAdapter(billingPeriodAdapter)
        binding.spinPaymentMethod.setAdapter(paymentMethodAdapter)

        binding.spinService.setOnItemClickListener { _, _, position, _ ->
            binding.spinPeriod.text = null
            viewModel.billingServiceList.value?.get(position)?.let { updatePeriodUi(it) }
            serviceSelected = position
            updateSubTotal()
        }

        binding.spinPeriod.setOnItemClickListener { _, _, position, _ ->
            periodSelected = position
            updateSubTotal()
        }

        binding.spinPaymentMethod.setOnItemClickListener { _, _, position, _ ->
            paymentSelected = position
        }

        binding.edtQtySlot.liveToCurrencyAndWatch { updateSubTotal() }
        binding.btnPay.setOnClickListener { validateInput() }

        viewModel.loadBillingServices()
        viewModel.loadPaymentMedia()
    }

    private fun validateInput() {
        if (Utils.isValidField(binding.spinService, binding.spinPeriod, binding.edtQtySlot, binding.spinPaymentMethod)) {
            val service = viewModel.billingServiceList.value?.get(serviceSelected)
            val period = periodSelected + 1
            val payment = viewModel.bankCodes[paymentSelected]
            val slot = binding.edtQtySlot.text.toString().fromCurrency()

            viewModel.createSubscription(service, period, slot, payment)
        }
    }

    override fun observeData() {
        viewModel.billingServiceList.observe(this, { data ->
            serviceAdapter.clear()
            billingPeriodAdapter.clear()

            data.forEach {
                serviceAdapter.add(it.name)
            }
        })

        viewModel.paymentMediaList.observe(this, { data ->
            paymentMethodAdapter.clear()
            data.forEach { paymentMethodAdapter.add(it) }
        })
    }

    override fun observeTask() {
        viewModel.state.observe(this, { state ->
            when (state) {
                is Loading -> showDialog(state.isLoading)
                is Failed -> showMessage(state.exception.readableError(this))
                else -> print("")
            }
        })

        viewModel.billingCreated.observe(this, { billing ->
            val intent = Intent(this, BillingInformationActivity::class.java)
            intent.putExtra("billing", billing)
            intent.putExtra("service_name", binding.spinService.text.toString())
            intent.putExtra("period", binding.spinPeriod.text.toString())
            intent.putExtra("slot", binding.edtQtySlot.text.toString())
            startActivity(intent)
            finish()
        })
    }

    private fun updateSubTotal() {
        viewModel.billingServiceList.value?.getSafe(serviceSelected)?.let { billingService ->
            val period = if (periodSelected < 0 || binding.spinPeriod.value().isEmpty()) 1 else periodSelected + 1
            var slot = binding.edtQtySlot.text.toString().fromCurrency()
            if (slot == 0) slot = 1
            setTextCurrency(binding.txtSubtotal, billingService.price.toInt() * period * slot, "Rp")
        }
    }

    private fun updatePeriodUi(service: BillingService) {
        billingPeriodAdapter.clear()

        val suffix = if (service.serviceLengthDay.toInt() == 30) "bulan" else if (service.serviceLengthDay.toInt() == 365) "tahun" else "X ${service.serviceLengthDay}"
        for (i in 1..service.servicePeriodMax.toInt()) {
            billingPeriodAdapter.add("$i $suffix")
        }
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        finish()
        return super.onOptionsItemSelected(item)
    }
}