package com.uniq.uniqpos.view.piutang

import android.os.Bundle
import android.view.MenuItem
import com.uniq.uniqpos.R
import com.uniq.uniqpos.databinding.ActivityPiutangBinding
import com.uniq.uniqpos.view.global.BaseActivity

class PiutangDetailActivity : BaseActivity<PiutangViewModel, ActivityPiutangBinding>() {

    override fun getLayoutRes() = R.layout.activity_piutang_detail
    override fun getViewModel() = PiutangViewModel::class.java

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)

        val fragment = PiutangDetailFragment()
        fragment.arguments = intent.getBundleExtra("data")

        supportFragmentManager.beginTransaction()
                .replace(R.id.container, fragment)
                .commit()
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        finish()
        return super.onOptionsItemSelected(item)
    }
}
