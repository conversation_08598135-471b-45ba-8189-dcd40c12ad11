package com.uniq.uniqpos.view.ordersales

import androidx.appcompat.app.AppCompatActivity
import android.os.Bundle
import android.view.MenuItem
import androidx.fragment.app.commit
import com.uniq.uniqpos.R
import com.uniq.uniqpos.data.local.entity.OrderSalesEntity
import com.uniq.uniqpos.data.local.entity.SalesEntity
import com.uniq.uniqpos.util.toast
import com.uniq.uniqpos.view.transactionhistory.HistoryDetailFragment

class OrderSalesDetailActivity : AppCompatActivity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_order_sales_detail)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)

        if(savedInstanceState == null){
            val args = Bundle()
            val salesJson = intent.getStringExtra("data")
            args.putString("data", salesJson)
//            val sales = intent.getParcelableExtra<OrderSalesEntity>("data")
//            args.putParcelable("data", sales)

            val fragment = OrderSalesDetailFragment()
            fragment.arguments = args

            supportFragmentManager.commit {
                replace(R.id.fragment_detail, fragment)
            }
        }
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        finish()
        return super.onOptionsItemSelected(item)
    }
}
