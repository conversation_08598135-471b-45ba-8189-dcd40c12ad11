package com.uniq.uniqpos.view.setting.printerserver


import android.content.DialogInterface
import android.graphics.Color
import android.os.Bundle
import android.view.View
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import com.uniq.uniqpos.R
import com.uniq.uniqpos.data.local.sharedpref.SharedPref
import com.uniq.uniqpos.data.local.sharedpref.getLocalDataBoolean
import com.uniq.uniqpos.data.local.sharedpref.getLocalDataString
import com.uniq.uniqpos.data.local.sharedpref.putData
import com.uniq.uniqpos.data.local.sharedpref.sharedPref
import com.uniq.uniqpos.databinding.FragmentPrinterServerBinding
import com.uniq.uniqpos.util.GetIpAddress
import com.uniq.uniqpos.util.Utility
import com.uniq.uniqpos.util.isValidIpAddress
import com.uniq.uniqpos.util.safe
import com.uniq.uniqpos.util.setVisible
import com.uniq.uniqpos.util.showMessage
import com.uniq.uniqpos.util.simpleOnTextChanged
import com.uniq.uniqpos.util.toast
import com.uniq.uniqpos.view.global.BaseFragment
import com.uniq.uniqpos.view.setting.PrinterSettingViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import timber.log.Timber
import java.util.regex.Pattern

/**
 * A simple [Fragment] subclass.
 */
class PrinterServerFragment : BaseFragment<PrinterSettingViewModel, FragmentPrinterServerBinding>() {

    override val layoutRes = R.layout.fragment_printer_server
    override fun getViewModel() = PrinterSettingViewModel::class.java

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        val ip = context?.getLocalDataString(SharedPref.IP_SERVER, "")
        binding.txtLocalIp.text = GetIpAddress()

        binding.swtServer.setOnCheckedChangeListener { _, isChecked ->
            if (isChecked && context?.getLocalDataString(SharedPref.IP_SERVER, "") != "") {
                binding.swtServer.isChecked = false
                context?.showMessage("disconnet dari server dan jadikan device ini sebagai server?", "WARNING", DialogInterface.OnClickListener { _, _ ->
                    binding.edtIpServer.text = null
                    pingServer()
                    binding.swtServer.isChecked = true
                })
            } else {
                setAsServer(isChecked)
            }
        }

        binding.swtServer.isChecked = context?.sharedPref()?.getBoolean(SharedPref.PRINTER_SERVER_STATUS).safe()
        binding.edtIpServer.setText(ip)

//        ip?.takeIf { it.isNotEmpty() }?.let { v.swt_server.isEnabled = false }
//        if (isServer) binding.swtServer.isEnabled = true

        binding.edtIpServer.simpleOnTextChanged {
            binding.btnPing.text = if (it.isNotEmpty()) "Connect To Server" else "Disconnect From Server"
            binding.imgClearIp.setVisible(it.isNotEmpty())
        }

        binding.edtIpServer.setOnClickListener {
            if (context?.getLocalDataBoolean(SharedPref.PRINTER_SERVER_STATUS).safe()) {
                context?.showMessage("device ini saat ini diaktifkan sebagai server, nonaktifkan device ini sebagai server agar dapat terhubung dengan server baru?", "WARNING", DialogInterface.OnClickListener { _, _ ->
                    binding.swtServer.isChecked = false
                })
            }
        }

        binding.imgClearIp.setOnClickListener {
            if (context?.getLocalDataString(SharedPref.IP_SERVER, "") != "") {
                context?.showMessage("disconnect dari server?", "WARNING", DialogInterface.OnClickListener { _, _ ->
                    binding.edtIpServer.setText("")
                    pingServer()
                })
            } else {
                binding.edtIpServer.setText("")
            }
        }

        binding.btnPing.setOnClickListener { pingServer() }
    }

    private fun pingServer() {
        val ip = binding.edtIpServer.text.toString()
        if (ip.isEmpty()) {
            context?.putData(SharedPref.IP_SERVER, ip)
            printerSocket.onDestroy()
//            binding.swtServer.isEnabled = true
            toast("Disconnected!")
            return
        }

//        val ipPattern = "^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\$"
//        val pattern = Pattern.compile(ipPattern)
//        if (!pattern.matcher(ip).matches()) {
//            context?.toast("Invalid IP address Format")
//            return
//        }
        if (isValidIpAddress(ip)){
            context?.toast("Invalid IP address Format")
            return
        }

        showDialog(true, "Connecting...")
        printerSocket.connectToServer(ip) { isConnected, message ->
            showDialog(false)
//                binding.swtServer.isEnabled = !isConnected
            if (isConnected) showMsg("Connection to $ip success", "SUCCESS")
            else {
                var pingRes = Utility.ping(ip)
                Timber.i(">> Auto Ping Result : $pingRes")
                context?.showMessage((message
                        ?: "Connection failed!") + "\nTry to ping server?", null, { _, _ ->
                    showDialog(true)
                    lifecycleScope.launch(Dispatchers.Main) {
                        for (i in 1..2) {
                            pingRes += "\n\n" + Utility.ping(ip)
                            delay(1000)
                        }
                        showDialog(false)
                        showMsg(pingRes, "PING RESULT")
                    }
                })
            }
        }

        context?.putData(SharedPref.IP_SERVER, ip)
    }

    private fun setAsServer(isServer: Boolean) {
        if (isServer) {
            printerSocket.start()
            binding.btnPing.setTextColor(Color.parseColor("#22718E"))
        } else {
            printerSocket.onDestroy()
            binding.btnPing.setTextColor(Color.WHITE)
        }

        binding.edtIpServer.isFocusable = !isServer
        binding.edtIpServer.isFocusableInTouchMode = !isServer
        binding.btnPing.isEnabled = !isServer
        context?.putData(SharedPref.PRINTER_SERVER_STATUS, isServer)
    }
}
