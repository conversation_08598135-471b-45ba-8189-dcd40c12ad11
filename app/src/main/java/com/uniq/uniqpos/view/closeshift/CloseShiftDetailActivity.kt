package com.uniq.uniqpos.view.closeshift

import android.os.Bundle
import android.view.MenuItem
import com.uniq.uniqpos.R
import com.uniq.uniqpos.binding.closeshift.CloseShiftDetailFragment
import com.uniq.uniqpos.databinding.ActivityCloseShiftDetailBinding
import com.uniq.uniqpos.view.global.BaseActivity

class CloseShiftDetailActivity : BaseActivity<CloseShiftViewModel, ActivityCloseShiftDetailBinding>() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)

        if (savedInstanceState == null) {
            val bundle = intent.getBundleExtra("bundle")
            val fragment = CloseShiftDetailFragment()
            fragment.arguments = bundle
            supportFragmentManager.beginTransaction().replace(R.id.container, fragment).commit()
        }
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        finish()
        return super.onOptionsItemSelected(item)
    }

    override fun getLayoutRes() = R.layout.activity_close_shift_detail
    override fun getViewModel() = CloseShiftViewModel::class.java
}