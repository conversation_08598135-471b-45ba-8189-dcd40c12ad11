package com.uniq.uniqpos.view.pendingprint

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.uniq.uniqpos.data.local.entity.PendingPrintEntity
import com.uniq.uniqpos.data.remote.repository.SalesRepository
import com.uniq.uniqpos.util.safe
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

/**
 * Created by annasblackhat on 07/02/18.
 */
class PendingPrintViewModel @Inject constructor(val salesRepository: SalesRepository) : ViewModel() {

    private val _pendingPrintLive = MutableLiveData<List<PendingPrintEntity>>()
    val pendingPrintLive: LiveData<List<PendingPrintEntity>> = _pendingPrintLive

    fun deletePendingPrint(pendingPrintEntity: PendingPrintEntity) {
        viewModelScope.launch {
            salesRepository.deletePendingPrint(pendingPrintEntity)
            loadPendingPrint()
        }
    }

    fun loadPendingPrint() {
        viewModelScope.launch {
            val pendingPrint = salesRepository.getPendingPrint()
            pendingPrint.forEach { print ->
                val pattern = when {
                    print.dataString.safe().contains("CAPTAIN ORDER") -> {
                        "Pelanggan[\\d\\D]+Tanggal.+[\r\n]"
                    }
                    print.dataString.safe().contains("No Nota") -> {
                        "No Nota[\\d\\D]+(Meja|Pembayaran).+[\r\n]"
                    }
                    print.dataString.safe().contains("LAPORAN") -> {
                        "LAPORAN[\\d\\D]+Tanggal.+[\r\n]"
                    }
                    else -> {
                        ".+"
                    }
                }
                print.beautifyDisplay = pattern.toRegex().find(print.dataString.safe())?.value?.trim()
                        ?: print.dataPrintDisplay.safe()
            }
            _pendingPrintLive.postValue(pendingPrint)
        }
    }
}