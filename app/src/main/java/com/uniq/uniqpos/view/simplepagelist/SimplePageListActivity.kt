package com.uniq.uniqpos.view.simplepagelist

import android.app.Activity
import android.app.SearchManager
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.Menu
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import android.widget.ArrayAdapter
import android.widget.CheckedTextView
import android.widget.SearchView
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import com.uniq.uniqpos.R
import com.uniq.uniqpos.databinding.ActivitySimplePageListBinding
import com.uniq.uniqpos.model.BottomDialogModel
import com.uniq.uniqpos.model.SimplePageListData
import com.uniq.uniqpos.util.Utils
import com.uniq.uniqpos.util.safe
import com.uniq.uniqpos.util.setVisible
import com.uniq.uniqpos.util.toast
import com.uniq.uniqpos.view.global.BottomDialogInput

class SimplePageListActivity : AppCompatActivity() {

    private val data = ArrayList<SimplePageListData>()
    private val items = ArrayList<String>()
    private var dataType: String = "None"

    private lateinit var binding: ActivitySimplePageListBinding
    private lateinit var searchView: SearchView
    private lateinit var searchListener: SearchView.OnQueryTextListener
    private lateinit var listAdapter: ArrayAdapter<String>
    private var itemSelectedList = ArrayList<String>()
    private var isMultiple = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivitySimplePageListBinding.inflate(layoutInflater)
        setContentView(binding.root)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)

        dataType = intent.getStringExtra("type").safe()
        data.clear()
        data.addAll(intent.getParcelableArrayListExtra("data")!!)
        items.clear()
        items.addAll(data.map { it.title })

        val isShowAdd = intent.getBooleanExtra("show_add", true)
        isMultiple = intent.getBooleanExtra("is_multiple", false)

        title = dataType
        binding.layoutAddNew.setVisible(isShowAdd)

        if (isMultiple){
            listAdapter = object :
                ArrayAdapter<String>(this, android.R.layout.simple_list_item_multiple_choice, items) {
                override fun getView(position: Int, convertView: View?, parent: ViewGroup): View {
                    val text = super.getView(position, convertView, parent) as CheckedTextView
                    text.setTextColor(Utils.getColor(this@SimplePageListActivity, R.color.grey_light))
                    text.isChecked = itemSelectedList.contains(items[position])
                    return text
                }
            }
        }else{
            listAdapter = object :
                ArrayAdapter<String>(this, android.R.layout.simple_dropdown_item_1line, items) {
                override fun getView(position: Int, convertView: View?, parent: ViewGroup): View {
                    val text = super.getView(position, convertView, parent) as TextView
                    text.setTextColor(Utils.getColor(this@SimplePageListActivity, R.color.grey_light))
                    return text
                }
            }
        }
        binding.listItem.adapter = listAdapter
        binding.listItem.setOnItemClickListener { _, _, position, _ ->
            if(isMultiple){
                if(itemSelectedList.contains(items[position])){
                    itemSelectedList.remove(items[position])
                }else{
                    itemSelectedList.add(items[position])
                }
                listAdapter.notifyDataSetChanged()
            }else{
                val intent = Intent()
                intent.putExtra("source", "existing")
                intent.putExtra("data", data.first { it.title == items[position] })
                setResult(Activity.RESULT_OK, intent)
                finish()
            }
        }

        binding.layoutAddNew.setOnClickListener {
            addNewItem()
        }

        searchListener = object : SearchView.OnQueryTextListener {
            override fun onQueryTextSubmit(query: String?): Boolean {
                return false
            }

            override fun onQueryTextChange(q: String?): Boolean {
                searchData(q)
                return true
            }
        }
    }

    private fun searchData(query: String?) {
        items.clear()
        query?.let { q ->
            items.addAll(data.filter { it.title.lowercase().contains(q.lowercase()) }
                .map { it.title })
        } ?: kotlin.run { items.addAll(data.map { it.title }) }
        listAdapter.notifyDataSetChanged()
    }

    private fun addNewItem() {
        val bottomDialog = BottomDialogInput()
        bottomDialog.setOnButtonClick { result ->
            if (result.isBlank()) {
                toast("Value is not allowed!")
            } else {
                bottomDialog.dismiss()
                val intent = Intent()
                intent.putExtra("source", "new")
                intent.putExtra("data", SimplePageListData(result, 0))
                setResult(Activity.RESULT_OK, intent)
                finish()
            }
        }
        bottomDialog.setModel(
            BottomDialogModel(
                "ADD NEW",
                "SAVE",
                dataType,
                hint = dataType.lowercase()
            )
        )
        bottomDialog.show(supportFragmentManager, "add-new")
    }

    override fun onCreateOptionsMenu(menu: Menu): Boolean {
        menuInflater.inflate(R.menu.menu_simple_page_list, menu)
        val searchManager = getSystemService(Context.SEARCH_SERVICE) as SearchManager
        searchView = menu?.findItem(R.id.action_search)?.actionView as SearchView
        searchView.setSearchableInfo(searchManager.getSearchableInfo(componentName))
        searchView.setOnQueryTextListener(searchListener)
        return super.onCreateOptionsMenu(menu)
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        if(isMultiple){
            val intent = Intent()
            intent.putExtra("source", "existing")
            val result = ArrayList<SimplePageListData>()
            result.addAll(data.filter { it.title in itemSelectedList })
//            data.forEachIndexed { index, simplePageListData ->
//                if(itemSelectedList.contains(simplePageListData.title)){
//                    result.add(simplePageListData)
//                }
//            }
            intent.putExtra("data", result)
            setResult(Activity.RESULT_OK, intent)
        }
        finish()
        return super.onOptionsItemSelected(item)
    }

    companion object {

        @JvmStatic
        fun newInstance(context: Context, title: String, items: ArrayList<SimplePageListData>,
                        isMultiple: Boolean = false,
                        showAdd: Boolean=true): Intent {
            val intent = Intent(context, SimplePageListActivity::class.java)
            intent.putExtra("type", title)
            intent.putExtra("is_multiple", isMultiple)
            intent.putExtra("show_add", showAdd)
            intent.putParcelableArrayListExtra("data", items)
            return intent
        }
    }
}