package com.uniq.uniqpos.view.member

import android.app.Activity
import android.app.SearchManager
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.Menu
import android.view.MenuItem
import android.view.View
import android.view.animation.AnimationUtils
import android.widget.SearchView
import com.uniq.uniqpos.R
import com.uniq.uniqpos.data.local.entity.MemberEntity
import com.uniq.uniqpos.data.local.entity.ProductEntity
import com.uniq.uniqpos.databinding.ActivityMemberBinding
import com.uniq.uniqpos.databinding.DialogAddMemberBinding
import com.uniq.uniqpos.databinding.ListItemMemberBinding
import com.uniq.uniqpos.util.Utils
import com.uniq.uniqpos.util.lifecycle.Loading
import com.uniq.uniqpos.util.lifecycle.setupDialogMessage
import com.uniq.uniqpos.util.value
import com.uniq.uniqpos.util.view.RecyclerItemClickListener
import com.uniq.uniqpos.view.global.BaseActivity
import com.uniq.uniqpos.view.global.DynamicDialog
import com.uniq.uniqpos.view.global.GlobalAdapter
import timber.log.Timber

class MemberActivity : BaseActivity<MemberViewModel, ActivityMemberBinding>() {

    private val REQ_CODE_MEMBER = 1
    private val memberList = ArrayList<MemberEntity>()
    private val productMember = ArrayList<ProductEntity>()
    private var currentMemberId: String? = null //current selected member id

    private lateinit var searchView: SearchView
    private lateinit var searchListener: SearchView.OnQueryTextListener
    private var bindingDialog: DialogAddMemberBinding? = null
    private var dialogAddMember: DynamicDialog? = null

    override fun getLayoutRes() = R.layout.activity_member
    override fun getViewModel() = MemberViewModel::class.java

    companion object {
        const val INTENT_CURRENT_MEMBER = "current_member_id"
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)

        currentMemberId = intent.getStringExtra(INTENT_CURRENT_MEMBER)

        binding.recviewMember.adapter =
            object : GlobalAdapter<ListItemMemberBinding>(R.layout.list_item_member, memberList) {}
        binding.recviewMember.layoutAnimation =
            AnimationUtils.loadLayoutAnimation(this, R.anim.layout_animation_fall_down)
        binding.recviewMember.addOnItemTouchListener(RecyclerItemClickListener(this) { _, position ->
            selectMember(memberList[position])
        })

        observeData()

        binding.fabAddMember.setOnClickListener {
//            val intent = Intent(this, AddMemberActivity::class.java)
//            intent.putExtra("type", productMember)
//            startActivityForResult(intent, REQ_CODE_MEMBER)
            showInputNewMemberDialog()
        }

        binding.btnRemoveMember.setOnClickListener {
            Timber.i("remove member $currentMemberId from transaction")
            val intent = Intent()
            intent.putExtra("action", "remove")
            setResult(Activity.RESULT_OK, intent)
            finish()
        }

        searchListener = object : SearchView.OnQueryTextListener {
            override fun onQueryTextSubmit(query: String?): Boolean {
                return false
            }

            override fun onQueryTextChange(q: String?): Boolean {
                viewModel.searchMember(q)
                return true
            }
        }

        setupDialogMessage(this, viewModel.dialogMsg)
    }

    private fun selectMember(member: MemberEntity) {
        val intent = Intent()
        intent.putExtra("data", member)
        setResult(Activity.RESULT_OK, intent)
        Timber.i("choose member: $member")
        finish()
    }

    private fun showInputNewMemberDialog() {
        if (bindingDialog == null) {
            bindingDialog = DialogAddMemberBinding.inflate(layoutInflater, null, false).apply {
                btnSave.setOnButtonClickListener {
                    if (Utils.isValidField(edtName, edtPhone)) {
                        viewModel.addMember(
                            edtName.value(),
                            edtPhone.value()
                        )
                    }
                }

                dialogAddMember = DynamicDialog(root, this@MemberActivity)
            }
        }

        dialogAddMember?.show()
    }

    override fun observeData() {
        viewModel.getMembers()
            .observe(this, { response ->
                viewModel.setMemberList(response.data)
                currentMemberId?.apply {
                    binding.txtRemoveInfo.text = getString(
                        R.string.remove_current_member,
                        response.data?.firstOrNull { it.memberId == this }?.name
                    )
                    binding.groupRemoveMember.visibility = View.VISIBLE
                }
            })

        viewModel.getMemberProduct()
            .observe(this, {
                productMember.clear()
                it?.let { productMember.addAll(it) }
                reInitMemberData()
            })

        viewModel.memberData.observe(this, {
            memberList.clear()
            memberList.addAll(it)
            reInitMemberData()
        })

        viewModel.addMemberState.observe(this, { event ->
            event.getContentIfNotHandled()?.let { state ->
                when (state) {
                    is Loading -> {
                        bindingDialog?.let { vi ->
                            vi.btnSave.setLoading(state.isLoading)
                        }
                    }
                    else -> {
                    }
                }
            }
        })

        viewModel.addMemberSuccess.observe(this, { member ->
            selectMember(member)
        })
    }

    private fun reInitMemberData() {
//        memberList.forEach { member ->
//            member.typeName = productMember.firstOrNull { it.productId == member.productFkid?.toLong() }?.name
//                    ?: ""
//        }
        binding.recviewMember.adapter?.notifyDataSetChanged()
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (resultCode == Activity.RESULT_OK) {
            if (requestCode == REQ_CODE_MEMBER) {
                setResult(Activity.RESULT_OK, data)
                finish()
            }
        }
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        finish()
        return super.onOptionsItemSelected(item)
    }

    override fun onCreateOptionsMenu(menu: Menu): Boolean {
        menuInflater.inflate(R.menu.menu_search, menu)
        val searchManager = getSystemService(Context.SEARCH_SERVICE) as SearchManager
        searchView = menu?.findItem(R.id.action_search)?.actionView as SearchView
        searchView.setSearchableInfo(searchManager.getSearchableInfo(componentName))
        searchView.setOnQueryTextListener(searchListener)
        return super.onCreateOptionsMenu(menu)
    }
}
