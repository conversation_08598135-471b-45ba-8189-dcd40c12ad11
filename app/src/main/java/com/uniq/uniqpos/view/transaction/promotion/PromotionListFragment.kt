package com.uniq.uniqpos.view.transaction.promotion

import android.os.Bundle
import androidx.fragment.app.Fragment
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.uniq.uniqpos.R
import com.uniq.uniqpos.data.local.entity.EmployeeEntity
import com.uniq.uniqpos.data.local.entity.PromotionEntity
import com.uniq.uniqpos.data.local.entity.PromotionProduct
import com.uniq.uniqpos.databinding.FragmentPromotionListBinding
import com.uniq.uniqpos.databinding.ListItemPromoAllBinding
import com.uniq.uniqpos.model.PromotionProductDialog
import com.uniq.uniqpos.util.data.DataHolder
import com.uniq.uniqpos.util.dateFormat
import com.uniq.uniqpos.util.getSafe
import com.uniq.uniqpos.util.takeMax
import com.uniq.uniqpos.util.toast
import com.uniq.uniqpos.view.global.GlobalAdapter
import com.uniq.uniqpos.view.global.GlobalViewHolder
import com.uniq.uniqpos.view.main.MainActivity
import com.uniq.uniqpos.view.transaction.TransactionViewModel
import timber.log.Timber


private const val ARG_PARAM1 = "param1"
private const val ARG_PROMOTION = "promotion_data"

/**
 * A simple [Fragment] subclass.
 * Use the [PromotionListFragment.newInstance] factory method to
 * create an instance of this fragment.
 */
class PromotionListFragment : Fragment() {
    private var promotionJson: String? = null
    private var promotionList = ArrayList<PromotionEntity>()

    private var _binding: FragmentPromotionListBinding? = null
    private val binding: FragmentPromotionListBinding get() = _binding!!

    private lateinit var dialogDetail: PromotionDetailDialog
    private var viewModel: TransactionViewModel? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.let {
            promotionJson = it.getString(ARG_PROMOTION)
            setDataPromotion(promotionJson)
        }

        if (promotionList.isEmpty() && DataHolder.hasData()){
            setDataPromotion(DataHolder.getData())
        }
    }

    private  fun setDataPromotion(promotionJson: String?){
        if(promotionJson.isNullOrBlank()){
            return
        }

        val type = object : TypeToken<List<PromotionEntity>>() {}.type
        val data = Gson().fromJson<List<PromotionEntity>>(promotionJson?.trim(), type)

        promotionList.clear()
        promotionList.addAll(data.sortedBy { it.name })
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentPromotionListBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        viewModel = (activity as MainActivity).getTransactionMain()!!.viewModel
        dialogDetail = object: PromotionDetailDialog(requireContext(), viewModel!!){}

        if(promotionList.isEmpty()){
            promotionList.addAll(viewModel!!.promotionList)
        }

        Timber.i("[promo] total promo: ${promotionList.size} -> ${promotionList.map { "${it.name}(${it.promotionId})," }}")
        if(promotionList.isEmpty()) context?.toast("no promotion available")

        binding.recviewList.adapter =
            object : GlobalAdapter<ListItemPromoAllBinding>(
                R.layout.list_item_promo_all,
                promotionList
            ) {
                override fun onBindViewHolder(
                    holder: GlobalViewHolder<ListItemPromoAllBinding>,
                    position: Int
                ) {
                    super.onBindViewHolder(holder, position)

                    promotionList.getSafe(holder.adapterPosition)?.let { promotion ->
                        val start = promotion.startPromotionDate.dateFormat("dd MMM")
                        val end = promotion.endPromotionDate.dateFormat("dd MMM")
//                        val timeStart = promotion.startPromotionTime.takeMax(5)
//                        val timeEnd = promotion.endPromotionTime.takeMax(5)
                        var timeInfo = "${promotion.startPromotionTime.takeMax(5, suffix = "")}-${
                            promotion.endPromotionTime.takeMax(5, suffix = "")
                        }"
                        if (timeInfo == "00:00-23:59") {
                            timeInfo = "24 jam"
                        }

                        holder.binding.txtDate.text =
                            "$start - $end ($timeInfo)"
                    }

                    holder.binding.root.setOnClickListener { showDetail(promotionList.getSafe(holder.adapterPosition)) }
                }
            }
    }

    private fun showDetail(promotion: PromotionEntity?) {
        promotion?.let { promo ->
            dialogDetail.show(promo)
        }
    }

    companion object {
        @JvmStatic
        fun newInstance(promo: ArrayList<PromotionEntity>): PromotionListFragment {
//            return PromotionListFragment().apply {
//                Timber.i("total product promo: [1] ${promo.firstOrNull()?.promotionProduct?.size}")
//                arguments = Bundle().apply {
//                    putString(ARG_PROMOTION, Gson().toJson(promo))
//                }
//            }
            DataHolder.setData(Gson().toJson(promo))
            return PromotionListFragment()
        }

    }
}