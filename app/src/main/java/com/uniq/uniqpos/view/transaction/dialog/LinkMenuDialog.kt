package com.uniq.uniqpos.view.transaction.dialog

import android.app.Dialog
import android.content.Context
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.view.LayoutInflater
import android.view.Window
import android.view.WindowManager
import android.widget.Button
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatDialog
import androidx.core.content.ContextCompat
import androidx.databinding.DataBindingUtil
import com.uniq.uniqpos.R
import com.uniq.uniqpos.data.local.entity.LinkMenuEntity
import com.uniq.uniqpos.data.local.entity.ProductEntity
import com.uniq.uniqpos.databinding.DialogLinkMenuBinding
import com.uniq.uniqpos.databinding.ListItemLinkMenuBinding
import com.uniq.uniqpos.model.LinkMenuProduct
import com.uniq.uniqpos.util.safe
import com.uniq.uniqpos.util.safeToInt
import com.uniq.uniqpos.util.setVisible
import com.uniq.uniqpos.util.toast
import com.uniq.uniqpos.util.view.RecyclerItemClickListener
import com.uniq.uniqpos.view.global.GlobalAdapter
import com.uniq.uniqpos.view.global.GlobalViewHolder
import com.uniq.uniqpos.view.transaction.TransactionViewModel
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.async
import kotlinx.coroutines.delay
import kotlinx.coroutines.runBlocking
import timber.log.Timber

abstract class LinkMenuDialog(context: Context,
                              private val viewModel: TransactionViewModel) : Dialog(context) {

    private lateinit var binding: DialogLinkMenuBinding
    private var currentPosition = 0
    private val currentLinkMenu = ArrayList<LinkMenuEntity>()
    private lateinit var currentProductEntity: ProductEntity

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        requestWindowFeature(Window.FEATURE_NO_TITLE)
        binding = DataBindingUtil.inflate(LayoutInflater.from(context), R.layout.dialog_link_menu, null, false)
        setContentView(binding.root)
        window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
        if (context.resources.getBoolean(R.bool.landscape_only)) {
            window?.setLayout(
                context.resources.getDimensionPixelOffset(R.dimen.dialog_width),
                WindowManager.LayoutParams.WRAP_CONTENT
            )
        } else {
            window?.setLayout(
                WindowManager.LayoutParams.MATCH_PARENT,
                WindowManager.LayoutParams.WRAP_CONTENT
            )
        }

        initView()
    }

    private fun initView() {
        binding.recviewLinkmenu.adapter = object : GlobalAdapter<ListItemLinkMenuBinding>(R.layout.list_item_link_menu, viewModel.linkMenuProductList) {
            override fun onBindViewHolder(holder: GlobalViewHolder<ListItemLinkMenuBinding>, position: Int) {
                super.onBindViewHolder(holder, position)
                val linkMenu = viewModel.linkMenuProductList[position]
                holder.binding.checkBox.isChecked = viewModel.linkMenuSelected.any { it.linkMenuDetail.linkmenuDetailId == linkMenu.linkMenuDetail.linkmenuDetailId }
            }
        }

        binding.recviewLinkmenu.addOnItemTouchListener(RecyclerItemClickListener(context) { _, position ->
            val linkMenu = viewModel.linkMenuProductList[position]
            val isChecked = !viewModel.linkMenuSelected.any { it.linkMenuDetail.linkmenuDetailId == linkMenu.linkMenuDetail.linkmenuDetailId }
            viewModel.linkMenuProductList[position].isChecked = isChecked

            if (isChecked) {
                if (viewModel.linkMenuCurrent?.multiplechoice == 0) {
                    viewModel.linkMenuSelected.firstOrNull { it.linkMenuDetail.linkmenuFkid == viewModel.linkMenuProductList[position].linkMenuDetail.linkmenuFkid }?.let { linkMenu ->
                        viewModel.linkMenuSelected.remove(linkMenu)
                    }
                            ?: run { Timber.i("NOT FOUND - to uncheck link menu | total selected : ${viewModel.linkMenuSelected.size}") }
                }
                viewModel.linkMenuSelected.add(viewModel.linkMenuProductList[position])
                context.toast("${viewModel.linkMenuProductList[position].product.name} checked")
                binding.recviewLinkmenu.adapter?.notifyItemChanged(position)

                Timber.d("[LINK-MENU] total item: ${viewModel.linkMenuProductList.size}")
                if(viewModel.linkMenuProductList.size == 1 || viewModel.linkMenuCurrent?.multiplechoice.safe() == 0 ){
                    next()
                }
            } else {
                viewModel.linkMenuSelected.remove(viewModel.linkMenuSelected.first { it.linkMenuDetail.linkmenuDetailId == linkMenu.linkMenuDetail.linkmenuDetailId })
                binding.recviewLinkmenu.adapter?.notifyItemChanged(position)
            }

//            binding.recviewLinkmenu.adapter?.notifyDataSetChanged()
            // binding.recviewLinkmenu.scheduleLayoutAnimation()
        })

        binding.imgAdd.setOnClickListener {
            val qty = binding.edtQty.text.toString().safeToInt() + 1
            binding.edtQty.setText(qty.toString())
        }

        binding.imgMinus.setOnClickListener {
            val qty = binding.edtQty.text.toString().safeToInt() - 1
            if (qty > 0) {
                binding.edtQty.setText(qty.toString())
            }
        }

        binding.btnNext.setOnClickListener {
            next()
        }

        binding.btnPrevious.setOnClickListener {
            if (currentPosition > 0) {
                changePosition(--currentPosition)
                changeButton(binding.btnNext, true)
            }

            if (currentPosition == 0) {
                changeButton(binding.btnPrevious, false)
            }
            binding.btnAddToBill.setTextColor(ContextCompat.getColor(context, R.color.text_green_disable))
        }

        binding.btnAddToBill.setOnClickListener {
            addToBill(currentProductEntity, viewModel.linkMenuSelected, binding.edtQty.text.toString().safeToInt())
            dismiss()
        }

        binding.txtSelectAll.setOnClickListener {
            viewModel.linkMenuProductList.forEach { it.isChecked = true }
            viewModel.linkMenuProductList.forEach { linkMenu ->
                if (!viewModel.linkMenuSelected.any { it.linkMenuDetail.linkmenuDetailId == linkMenu.linkMenuDetail.linkmenuDetailId }) {
                    viewModel.linkMenuSelected.add(linkMenu)
                }
            }

            binding.recviewLinkmenu.adapter?.notifyDataSetChanged()
            // binding.recviewLinkmenu.scheduleLayoutAnimation()
        }
    }

    private  fun next(){
        if (currentLinkMenu.size > (currentPosition + 1)) {
            changePosition(++currentPosition)
            changeButton(binding.btnPrevious, true)
        }

        if (currentLinkMenu.size == (currentPosition + 1)) {
            changeButton(binding.btnNext, false)
            binding.btnAddToBill.setTextColor(Color.WHITE)
        }
    }

    private fun changePosition(position: Int) {
        runBlocking {
            viewModel.linkMenuCurrent = currentLinkMenu[position]
            val ids = currentLinkMenu[position].linkMenuDetail.map { it.productDetailFkid }
            viewModel.linkMenuProductList.clear()

            val products = GlobalScope.async { viewModel.getProductsByIds(ids) }.await()
            products.forEach { product ->
                var variantName = ""
                product.variantFkid?.let { id -> viewModel.variants.firstOrNull { it.variantId == id }?.let { variant ->
                    variantName = "(${variant.variantName})"
                } }
                Timber.d("${product.name} - ${product.variantFkid}")
                viewModel.linkMenuProductList.add(LinkMenuProduct(product.copy(name = "${product.name} $variantName"), currentLinkMenu[position].linkMenuDetail.first { it.productDetailFkid == product.productDetailId }))
            }

            binding.recviewLinkmenu.adapter?.notifyDataSetChanged()
            binding.recviewLinkmenu.scheduleLayoutAnimation()
            binding.txtLinkname.text = currentLinkMenu[position].name            
            binding.txtLinkCount.text = "(${(currentPosition + 1)}/${currentLinkMenu.size})"

            val strResource = if (viewModel.linkMenuCurrent?.multiplechoice.safe() == 1) R.string.select_one_or_more else R.string.select_one_only
            binding.txtSelectAll.setVisible(viewModel.linkMenuCurrent?.multiplechoice.safe() == 1)
            binding.txtInfoSelect.text = String.format(context.getString(strResource), viewModel.linkMenuProductList.size)
        }
    }

    fun showLinkMenu(productEntity: ProductEntity) {
        currentProductEntity = productEntity
        show()

        binding.txtMenu.text = productEntity.name
        currentLinkMenu.clear()
        currentLinkMenu.addAll(viewModel.linkMenuList.filter { it.productDetailFkid == productEntity.productDetailId })
        changePosition(0)
        changeButton(binding.btnPrevious, false)

        if (currentLinkMenu.size == 1) {
            changeButton(binding.btnNext, false)
            binding.btnAddToBill.setTextColor(Color.WHITE)
        }
    }

    private fun changeButton(btn: Button, isEnable: Boolean = true) {
        val colorCode = if (isEnable) "#C8CFD2" else "#454754"
        btn.setTextColor(Color.parseColor(colorCode))
    }

    override fun show() {
        super.show()
        currentPosition = 0
        binding.edtQty.setText("1")
    }

    override fun dismiss() {
        viewModel.linkMenuSelected.clear()
        super.dismiss()
    }

    abstract fun addToBill(productEntity: ProductEntity, linkMenuSelected: List<LinkMenuProduct>, qty: Int)
}