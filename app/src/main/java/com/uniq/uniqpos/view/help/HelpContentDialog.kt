package com.uniq.uniqpos.view.help

import android.graphics.Color
import android.os.Build
import android.os.Bundle
import android.util.Base64
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewTreeObserver
import android.webkit.WebResourceRequest
import android.webkit.WebView
import android.webkit.WebViewClient
import android.widget.FrameLayout
import androidx.annotation.RequiresApi
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.google.gson.Gson
import com.uniq.uniqpos.databinding.DialogHelpContentBinding
import com.uniq.uniqpos.util.launchUrl
import com.uniq.uniqpos.util.safe
import timber.log.Timber


/**
 * Created by annas<PERSON><PERSON><PERSON> on 18/09/20.
 */

class HelpContentDialog : BottomSheetDialogFragment() {

    private lateinit var binding: DialogHelpContentBinding
    private var content: String = ""
    private var title: String = "HELP"

    companion object {
        fun newInstance(content: String, title: String = "HELP"): HelpContentDialog {
            val args = Bundle()
            args.putString("content", content)
            args.putString("title", title)
            val fragment = HelpContentDialog()
            fragment.arguments = args
            return fragment
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.apply {
            content = getString("content").safe()
            title = getString("title").safe("HELP")
        }
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        binding = DialogHelpContentBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        binding.imgClose.setOnClickListener { dismiss() }

        binding.txtTitle.text = title
        binding.webView.setBackgroundColor(Color.TRANSPARENT)
        binding.webView.webViewClient = if(Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) getWebClient() else getWebClientOld()

        val encodeHtml = Base64.encodeToString(content.toByteArray(), Base64.NO_PADDING)
        binding.webView.loadData(encodeHtml, "text/html", "base64")

        view.viewTreeObserver.addOnGlobalLayoutListener(object : ViewTreeObserver.OnGlobalLayoutListener {
            override fun onGlobalLayout() {
                view.viewTreeObserver.removeOnGlobalLayoutListener(this)
                val dialog = dialog as BottomSheetDialog
                val bottomSheet = dialog.findViewById(com.google.android.material.R.id.design_bottom_sheet) as FrameLayout?
                val behavior = BottomSheetBehavior.from(bottomSheet!!)
                behavior.state = BottomSheetBehavior.STATE_EXPANDED
            }
        })
    }

    @RequiresApi(api = Build.VERSION_CODES.N)
    fun getWebClient(): WebViewClient {
        return object : WebViewClient() {
            override fun shouldOverrideUrlLoading(view: WebView?, request: WebResourceRequest?): Boolean {
                Timber.i("request : ${Gson().toJson(request)}")
                context?.launchUrl(request?.url.toString())
                return true
            }
        }
    }

    @RequiresApi(api = Build.VERSION_CODES.ICE_CREAM_SANDWICH)
    fun getWebClientOld(): WebViewClient {
        return object : WebViewClient() {
            override fun shouldOverrideUrlLoading(view: WebView?, url: String?): Boolean {
                context?.launchUrl(url)
                return true
            }
        }
    }
}