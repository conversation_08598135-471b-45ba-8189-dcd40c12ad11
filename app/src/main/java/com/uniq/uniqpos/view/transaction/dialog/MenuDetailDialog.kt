package com.uniq.uniqpos.view.transaction.dialog

import android.content.Context
import android.content.DialogInterface
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.widget.ArrayAdapter
import android.widget.LinearLayout
import androidx.appcompat.app.AlertDialog
import androidx.databinding.DataBindingUtil
import com.google.android.material.textfield.TextInputEditText
import com.google.android.material.textfield.TextInputLayout
import com.google.firebase.analytics.FirebaseAnalytics
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.uniq.uniqpos.BuildConfig
import com.uniq.uniqpos.R
import com.uniq.uniqpos.data.local.sharedpref.SharedPref
import com.uniq.uniqpos.data.local.sharedpref.getLocalDataString
import com.uniq.uniqpos.data.local.sharedpref.putJson
import com.uniq.uniqpos.databinding.DialogFreeItemBinding
import com.uniq.uniqpos.databinding.DialogMenuDetailBinding
import com.uniq.uniqpos.databinding.ListItemInfoBinding
import com.uniq.uniqpos.util.*
import com.uniq.uniqpos.util.view.DialogAuthorization
import com.uniq.uniqpos.view.global.DynamicDialog
import com.uniq.uniqpos.view.global.GlobalAdapter
import com.uniq.uniqpos.view.global.GlobalViewHolder
import com.uniq.uniqpos.view.transaction.TransactionViewModel
import timber.log.Timber

/**
 * Created by annasblackhat on 23/06/18
 */
abstract class MenuDetailDialog(
    val context: Context,
    val viewModel: TransactionViewModel
) {

    private var position = 0
    private var discType = Constant.TYPE_PERSEN //default is nominal, as per UI shown

    private val binding: DialogMenuDetailBinding by lazy {
        Timber.d("init menu detail dialog binding...")
        DataBindingUtil.inflate(
            LayoutInflater.from(context),
            R.layout.dialog_menu_detail,
            null,
            false
        )
    }

    private val dialog: DynamicDialog by lazy {
        Timber.d("init dynamic dialog...")
        val vi = DynamicDialog(binding.root, context)
        initView()
        vi
    }

//    override fun onCreate(savedInstanceState: Bundle?) {
//        super.onCreate(savedInstanceState)
//        binding = DataBindingUtil.inflate(LayoutInflater.from(context), R.layout.dialog_menu_detail, null, false)
////        requestWindowFeature(Window.FEATURE_NO_TITLE)
//
////        if (context.resources.getBoolean(R.bool.landscape_only)) {
////            window.setLayout(WindowManager.LayoutParams.WRAP_CONTENT,
////                    WindowManager.LayoutParams.WRAP_CONTENT)
////        } else {
////            window.setLayout(WindowManager.LayoutParams.WRAP_CONTENT,
////                    WindowManager.LayoutParams.WRAP_CONTENT)
////        }
//
//        window?.clearFlags(WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or WindowManager.LayoutParams.FLAG_ALT_FOCUSABLE_IM)
//        setContentView(binding.root)
//        window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
//
//        Timber.i("MenuDetail -> position $position | ${viewModel.orders[position].product?.name}")
//        initView()
//    }

    fun initView() {
        Timber.d("init view...")

        Utils.registerToCurrency(binding.edtPrice, binding.edtQty)

        binding.edtDiscount.setOnClickListener {
            if (viewModel.orders[position].product?.discount == "off") {
                showWarningDiscountDisable()
            }
        }

        if (!context.role().gantiharga || viewModel.orders[position].promotion != null) {
            binding.edtPrice.isFocusable = false
            binding.edtPrice.isLongClickable = false
            binding.edtPrice.setOnClickListener {
                if (viewModel.orders[position].promotion == null)
                    context.toast(context.getString(R.string.no_permission))
            }
        }

        if (!context.role().changeDiscountItem) {
            binding.edtDiscount.isFocusable = false

            var isAuthorizedDiscItem = false
            binding.edtDiscount.setOnClickListener {
                if (!isAuthorizedDiscItem) {
                    DialogAuthorization(context)
                        .setAuthType(DialogAuthorization.AuthType.AUTH_FREE)
                        .setOnAuthorizedListener {
                            binding.edtDiscount.isFocusableInTouchMode = true
                            binding.edtDiscount.isFocusable = true
                            binding.edtDiscount.selectAll()
                            isAuthorizedDiscItem = true
                        }
                        .show()
                }
            }
        }

//        val adapterType = ArrayAdapter(context, android.R.layout.simple_dropdown_item_1line, types)
//        binding.spinType.setAdapter(adapterType)
//        binding.spinType.setText(if (viewModel.orders[position].discount.discountType == Constant.TYPE_PERSEN) types[1] else types[0])

        binding.btnExtra.setOnClickListener {
            context.showMessage("Fitur ini sedang dalam tahap pengembangan!")
        }

        binding.btnSave.setOnClickListener {
            if (binding.edtDiscount.fromCurrency() > 0 && binding.edtInfo.text.toString().trim()
                    .isEmpty()
            ) {
                context.toast(context.getString(R.string.disc_info_empty))
                binding.edtInfo.requestFocus()
            } else if (discType == Constant.TYPE_PERSEN && binding.edtDiscount.value()
                    .fromCurrency() > 100
            ) {
                context.toast(context.getString(R.string.disc_out_range) + " persen")
            } else if (discType == Constant.TYPE_NOMINAL && binding.edtDiscount.value()
                    .fromCurrency() > viewModel.orders[position].subTotal
            ) {
                context.toast(context.getString(R.string.disc_out_range) + " nominal")

            } else {
                if (binding.edtQty.text.toString()
                        .fromCurrency() < viewModel.orders[position].qty && viewModel.isParentFromPromo(
                        viewModel.orders[position]
                    )
                ) {
                    context.showMessage(
                        "Free item akan di hapus dari daftar bill jika anda melanjutkan proses ini",
                        "REMOVE FREE ITEM",
                        { _, _ ->
                            saveUpdate(true)
                        })
                } else if(viewModel.orders[position].promotion != null){
                    if(viewModel.enableUpdateQty(viewModel.orders[position], binding.edtQty.text.toString().safeToInt())){
                        saveUpdate()
                    }
                } else {
                    saveUpdate()
                }
            }
        }

        binding.txtFree.setOnClickListener {
            if (viewModel.orders[position].product?.discount == "off") {
                showWarningDiscountDisable()
            } else {
                if (context.role().changeDiscountItem) {
                    showFreeItemDialog {}
                } else {
                    DialogAuthorization(context)
                        .setAuthType(DialogAuthorization.AuthType.AUTH_FREE)
                        .setOnAuthorizedListener {
                            showFreeItemDialog {}
                        }
                        .show()
                }
            }
        }

        binding.layoutRemove.setOnClickListener {
            context.showMessage(
                context.getString(R.string.are_you_sure),
                context.getString(R.string.delete_item),
                { _, _ ->
                    viewModel.orders.getSafe(position)?.let { order ->
                        Timber.i("removed from bill : ${order.product?.name} (${order.product?.productDetailId}) on position $position")
                        viewModel.removeBill(order)
                        onMenuDetailSaved(position)
//                    dismiss()
                        dialog.dismiss()
                    }
                })
        }

        binding.btnNote.setOnClickListener {
            val edt = TextInputEditText(context)
            val layout = TextInputLayout(context)
            val params = LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.WRAP_CONTENT,
                LinearLayout.LayoutParams.WRAP_CONTENT
            )

            params.setMargins(16, 16, 16, 16)
            layout.layoutParams = params
            layout.setPadding(19, 19, 19, 19)
            edt.hint = "note..."
            layout.addView(edt)
            AlertDialog.Builder(context)
                .setView(layout)
                .setTitle("NOTE")
                .setNegativeButton(R.string.cancel, null)
                .setPositiveButton(R.string.save) { _, _ ->
                    viewModel.orders[position].note = edt.text.toString()
                }
                .show()
        }

        binding.btnDiscNominal.setOnClickListener {
            discType = Constant.TYPE_NOMINAL; changeSwitchUI(
            binding.btnDiscNominal,
            binding.btnDiscPercent,
            Gravity.LEFT,
            context
        )
        }
        binding.btnDiscPercent.setOnClickListener {
            discType = Constant.TYPE_PERSEN; changeSwitchUI(
            binding.btnDiscNominal,
            binding.btnDiscPercent,
            Gravity.RIGHT,
            context
        )
        }

        binding.edtDiscount.liveToCurrencyAndWatch { total ->
            if (total.fromCurrency() > 100 && discType == Constant.TYPE_PERSEN) {
                discType = Constant.TYPE_NOMINAL
                changeSwitchUI(
                    binding.btnDiscNominal,
                    binding.btnDiscPercent,
                    Gravity.LEFT,
                    context
                )
            } else if (total.fromCurrency() <= 100 && discType == Constant.TYPE_NOMINAL) {
                discType = Constant.TYPE_PERSEN
                changeSwitchUI(
                    binding.btnDiscNominal,
                    binding.btnDiscPercent,
                    Gravity.RIGHT,
                    context
                )
            }
        }

        binding.btnQtyAdd.setOnClickListener { updateQty(1) }
        binding.btnQtyMin.setOnClickListener { updateQty(-1) }

//        binding.inputNote.setAdapter(
//            ArrayAdapter(
//                context,
//                android.R.layout.simple_dropdown_item_1line,
//                arrayOf("Take Away", "Pakai Cabai")
//            )
//        )
    }

    fun show(position: Int) {
        this.position = position
        dialog.show()

        binding.edtPrice.setText(viewModel.orders[position].price.toCurrency())
        binding.menuName.text = viewModel.orders[position].product?.name
        binding.edtQty.setText(viewModel.orders[position].qty.toString())
        binding.edtPrice.setText(viewModel.orders[position].price.toCurrency())
        binding.materialInputNote.setText(viewModel.orders[position].note)

        binding.edtDiscount.setText(viewModel.orders[position].discount.discount.toString())
        binding.edtInfo.setText(viewModel.orders[position].discount.discountInfo)

        val isDiscEnable = viewModel.orders[position].product?.discount == "on"
        binding.edtDiscount.isFocusable = isDiscEnable
        binding.edtDiscount.isLongClickable = isDiscEnable
        binding.edtDiscount.isFocusableInTouchMode = isDiscEnable
        binding.edtInfo.isFocusable = isDiscEnable
        binding.edtInfo.isFocusableInTouchMode = isDiscEnable

        Timber.d("menu detail, isFocusable: ${binding.edtDiscount.isFocusable}")

        discType = when (viewModel.orders[position].discount.discountType) {
            Constant.TYPE_PERSEN -> {
                changeSwitchUI(
                    binding.btnDiscNominal,
                    binding.btnDiscPercent,
                    Gravity.RIGHT,
                    context
                )
                Constant.TYPE_PERSEN
            }
            Constant.TYPE_NOMINAL -> {
                changeSwitchUI(
                    binding.btnDiscNominal,
                    binding.btnDiscPercent,
                    Gravity.LEFT,
                    context
                )
                Constant.TYPE_NOMINAL
            }
            else -> {
                Constant.TYPE_PERSEN
            }
        }

        if (viewModel.orders[position].isHold) {
//            binding.btnDelete.isEnabled = false
//            binding.layoutMin.isEnabled = false
//            binding.layoutAdd.isEnabled = false
            binding.btnQtyMin.isEnabled = false
            binding.btnQtyAdd.isEnabled = false
            binding.edtQty.isEnabled = false
            binding.txtDelete.isEnabled = false
//            binding.btnDelete.setTextColor(Color.parseColor("#FFF19EA8"))
        } else {
            binding.edtQty.isEnabled = viewModel.orders[position].enableAddQty
        }

        viewModel.orders[position].promotion?.let { promotion ->
            binding.txtPromoInfo.visibility = View.VISIBLE
            binding.txtPromoInfo.text = context.getString(
                R.string.promo_applied_info,
                promotion.pomotionTypeFkid.readablePromoType(context),
                promotion.name,
                promotion.minOrder.toCurrency()
            )
        }

        val selectedProduct = viewModel.orders[position].product
        binding.materialInputNote.setAdapter(
            ArrayAdapter(
                context,
                android.R.layout.simple_dropdown_item_1line,
                viewModel.getNoteHistoryByProduct(selectedProduct?.productId.safe().toInt())
            )
        )

        binding.materialInputNote.setOnItemClickListener { _, _, _, _ ->
            Timber.d("[note] note clicked...")
            FirebaseAnalytics.getInstance(context)
                .logEvent(
                    "note_suggestion",
                    bundleOf(
                        "Outlet" to "${context.outlet()?.outletId}:${context.outlet()?.name}",
                        "Feature" to "note suggestion"
                    )
                )
        }

        binding.edtInfo.setAdapter(
            ArrayAdapter(
                context,
                android.R.layout.simple_dropdown_item_1line,
                viewModel.discAndVoucherInfoHistory
            )
        )
    }

    private fun showWarningDiscountDisable() {
        context.showMessage(
            context.getString(R.string.discount_is_disable),
            context.getString(
                R.string.discount_disabled
            ),
            negativeAction = { _, _ ->
                val productName = viewModel.orders[position].product?.name
                context.launchUrl("${BuildConfig.WEB_URL}/products/catalogue?search=$productName")
            },
            negativeMsg = "OPEN WEB",
        )
    }

    private fun updateQty(qty: Int) {
        (binding.edtQty.value().fromCurrency() + qty).takeIf { it > 0 }?.apply {
            binding.edtQty.setText(toCurrency())
        }
    }

    private fun saveUpdate(isRemoveFreeItem: Boolean = false) {
        viewModel.orders[position].discount.discount = binding.edtDiscount.value().fromCurrency()
        viewModel.orders[position].discount.discountInfo = binding.edtInfo.text.toString().trim()
//        viewModel.orders[position].discount.discountType = if (binding.spinType.text.toString() == types[0]) Constant.TYPE_NOMINAL else Constant.TYPE_PERSEN
        viewModel.orders[position].discount.discountType = discType
        viewModel.orders[position].discount.discountNominal =
            viewModel.calculateDiscount(viewModel.orders[position])
        if (binding.materialInputNote.text.toString().isNotBlank()) {
            viewModel.orders[position].note = binding.materialInputNote.text.toString().trim()
        } else {
            viewModel.orders[position].note = null
        }

        if (binding.edtQty.text.toString().isNotEmpty()) viewModel.orders[position].qty =
            binding.edtQty.value().fromCurrency()
        if (context.role().gantiharga && binding.edtPrice.text.toString()
                .isNotEmpty() && viewModel.orders[position].promotion == null
        ) {
            viewModel.orders[position].priceAdd = binding.edtPrice.value()
                .fromCurrency() - (viewModel.orders[position].product?.priceSell
                ?: 0)
            viewModel.orders[position].product?.priceSell = binding.edtPrice.value().fromCurrency()
        }
        viewModel.updatePrice(viewModel.orders[position])
        viewModel.reCalculateTax()
        viewModel.updateMenuDetail(position)

        Timber.i(">>> Discount Saved! => ${Gson().toJson(viewModel.orders[position].discount)}")
        if (binding.edtQty.value().fromCurrency() == 0) {
            context.toast("item removed '${viewModel.orders[position].product?.name}'")
            viewModel.orders.remove(viewModel.orders[position])
        }

        if (isRemoveFreeItem) {
            viewModel.removeFreeItemByParentId(viewModel.orders[position].tmpId)
        }
        viewModel.runGrandTotalWatcher()
        onMenuDetailSaved(position)
//        dismiss()
        dialog.dismiss()
    }

    private fun showFreeItemDialog(func: (String) -> Unit) {
//        val v = LayoutInflater.from(context).inflate(R.layout.dialog_free_item, null)
        val bindingDialog = DialogFreeItemBinding.inflate(LayoutInflater.from(context), null, false)
        bindingDialog.edtFreeInfo.setText(viewModel.orders[position].discount.discountInfo)
        val suggestions = ArrayList<String>(getSuggestion())
        Timber.i("Suggestion : $suggestions")

        bindingDialog.recviewSuggestion.adapter =
            object : GlobalAdapter<ListItemInfoBinding>(R.layout.list_item_info, suggestions) {
                override fun onBindViewHolder(
                    holder: GlobalViewHolder<ListItemInfoBinding>,
                    position: Int
                ) {
                    super.onBindViewHolder(holder, position)
                    holder.binding.btn1.setOnClickListener {
                        context.toast("free item info : '${suggestions[position]}'")
                        bindingDialog.edtFreeInfo.setText(suggestions[position])

                        FirebaseAnalytics.getInstance(context)
                            .logEvent(
                                "app_features",
                                bundleOf(
                                    "Outlet" to "FIS:${context.outlet()?.outletId}:${context.outlet()?.name}",
                                    "Feature" to "Free Item Suggestion"
                                )
                            )
                    }
                }
            }


        var builder = AlertDialog.Builder(context, R.style.custom_dialog_dark)
            .setView(bindingDialog.root)
            .setCancelable(false)
            .setTitle("SET FREE ITEM")
            .setNegativeButton(R.string.cancel, null)
            .setPositiveButton(R.string.save) { _, _ ->
                if (bindingDialog.edtFreeInfo.value().isNotBlank()) {
                    viewModel.orders[position].discount.discount = 100
                    viewModel.orders[position].discount.discountInfo =
                        bindingDialog.edtFreeInfo.value()
                    viewModel.orders[position].discount.discountType = Constant.TYPE_PERSEN
                    viewModel.reCalculateTax()

                    //save info as history
                    if (!suggestions.any { it == bindingDialog.edtFreeInfo.value() }) {
                        suggestions.add(0, bindingDialog.edtFreeInfo.value())
                        context.putJson(SharedPref.FREE_INFO_HISTORY, suggestions)
                    }

                    onMenuDetailSaved(position)
//                    dismiss()
                    dialog.dismiss()
                    func(bindingDialog.edtFreeInfo.value())
                } else {
                    context.showMessage("info free item wajib diisi!")
                }
            }
        val dialog = builder.create()
        dialog.show()
    }

    private fun getSuggestion(): List<String> {
        val suggestions =
            listOf("Buy 1 Get 1 Free", "Promo Special", "Promo Akhir Pekan", "Promo Akhir Tahun")
        val type = object : TypeToken<ArrayList<String>>() {}.type
        var infoHistoryList: ArrayList<String>? = Gson().fromJson<ArrayList<String>>(
            context.getLocalDataString(SharedPref.FREE_INFO_HISTORY),
            type
        )
        if (infoHistoryList == null) infoHistoryList = ArrayList()

        suggestions.forEach { suggest ->
            if (!infoHistoryList.any { it == suggest }) {
                infoHistoryList.add(suggest)
            }
        }

//        return formatSuggestion(suggestions)
        return if (infoHistoryList.size > 8) infoHistoryList.subList(0, 8) else infoHistoryList
    }

//    override fun dismiss() {
//        currentFocus?.hideKeyboard(context)
//        super.dismiss()
//    }

    abstract fun onMenuDetailSaved(position: Int)
}