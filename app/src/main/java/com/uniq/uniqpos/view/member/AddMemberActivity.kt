package com.uniq.uniqpos.view.member

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.view.MenuItem
import android.widget.ArrayAdapter
import androidx.appcompat.app.AppCompatActivity
import com.uniq.uniqpos.data.local.entity.MemberEntity
import com.uniq.uniqpos.data.local.entity.ProductEntity
import com.uniq.uniqpos.databinding.ActivityAddMemberBinding
import com.uniq.uniqpos.model.Order
import com.uniq.uniqpos.util.*
import java.util.*

class AddMemberActivity : AppCompatActivity() {

    private var productMember = ArrayList<ProductEntity>()
    private lateinit var binding: ActivityAddMemberBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityAddMemberBinding.inflate(layoutInflater)
        setContentView(binding.root)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)

        productMember = intent.getSerializableExtra("type") as ArrayList<ProductEntity>

        binding.spinType.setAdapter(ArrayAdapter<String>(this, android.R.layout.simple_expandable_list_item_1, productMember.map { it.name }))

        val cal = Calendar.getInstance()
        cal.add(Calendar.YEAR, -16)
        binding.edtBirthday.setAsDateinput(supportFragmentManager)

        binding.btnNext.setOnClickListener { next() }
    }

    private fun next() {
        if(!Utils.isValidField(binding.edtName, binding.edtPhone, binding.edtEmail, binding.edtBirthday, binding.edtAddress, binding.spinType)){
            toast("Please fill all fields!")
            return
        }

        val product = productMember.first { it.name == binding.spinType.text.toString()}
        val member = MemberEntity(System.currentTimeMillis().toString(), name = binding.edtName.value(), phone = binding.edtPhone.value(), email = binding.edtEmail.value(),
                dateOfBirth = binding.edtBirthday.value().dateToLong(), address = binding.edtAddress.value(), productFkid = product.productFkid)
        val order = Order(product, 1, member = member)
        val intent = Intent()
        intent.putExtra("data", order)
        setResult(Activity.RESULT_OK, intent)
        finish()
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        finish()
        return super.onOptionsItemSelected(item)
    }
}
