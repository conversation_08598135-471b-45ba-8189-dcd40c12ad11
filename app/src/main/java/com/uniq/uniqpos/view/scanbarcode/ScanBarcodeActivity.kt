package com.uniq.uniqpos.view.scanbarcode

import android.Manifest
import android.app.Activity
import android.content.Context
import android.content.DialogInterface
import android.content.Intent
import android.content.IntentFilter
import android.content.pm.PackageManager
import android.hardware.Camera
import android.media.MediaPlayer
import android.os.Build
import android.os.Bundle
import android.view.SurfaceHolder
import android.view.View
import androidx.appcompat.app.AppCompatActivity
import androidx.core.app.ActivityCompat
import com.google.android.gms.common.ConnectionResult
import com.google.android.gms.common.GoogleApiAvailability
import com.google.android.gms.vision.CameraSource
import com.google.android.gms.vision.Detector
import com.google.android.gms.vision.barcode.Barcode
import com.google.android.gms.vision.barcode.BarcodeDetector
import com.uniq.uniqpos.R
import com.uniq.uniqpos.data.local.entity.ProductEntity
import com.uniq.uniqpos.databinding.ActivityScanBarcodeBinding
import com.uniq.uniqpos.model.Order
import com.uniq.uniqpos.util.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import timber.log.Timber
import java.io.IOException
import java.lang.ref.WeakReference


class ScanBarcodeActivity : AppCompatActivity() {

    private val MAX_RESCAN = 10
    private val RC_CAMERA = 20
    private var scanLoop = 0
    private var lastScanDetected = ""
    private lateinit var cameraSource: CameraSource
    private var mp: MediaPlayer? = null
    private val productList = ArrayList<ProductEntity>()
    private val orderList = ArrayList<Order>()
    private var actionType: String? = null
    private var cameraCallback: CameraCallBack? = null
    private lateinit var binding: ActivityScanBarcodeBinding

    companion object {
        const val ACTION_GET_VALUE_ONLY = "scan_only_for_getting_value"
    }

    private var barcodeDetector: BarcodeDetector? = null


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityScanBarcodeBinding.inflate(layoutInflater)
        setContentView(binding.root)

        actionType = intent.getStringExtra("action_type")
        intent.getParcelableArrayListExtra<ProductEntity>("product")?.let { productList.addAll(it) }

        val barcode = productList.filter { !it.barcode.isNullOrBlank() }.map { it.barcode }
        Timber.i("scan actionType : $actionType")
        Timber.i("Products Size : ${productList.size} | has barcode : ${barcode.size}")

        binding.incCart.txtKetGrandtotal.text = "Grand Total (Sebelum Pajak)"
        binding.incCart.root.setOnClickListener {
            val bundle = Bundle()
            bundle.putParcelableArrayList("order", orderList)
            val intent = Intent()
            intent.putExtras(bundle)
            setResult(Activity.RESULT_OK, intent)
            finish()
        }

        val hasCamera = checkCameraHardware(this)
        if(!hasCamera){
            toast("no camera detected", level = Level.ERROR)
        }

        initCamera()
        checkGooglePlayService()
        Utils.hideKeyboard(this)
    }

    private fun checkGooglePlayService() {
        val code = GoogleApiAvailability.getInstance().isGooglePlayServicesAvailable(
                applicationContext
        )
        if (code != ConnectionResult.SUCCESS) {
            val dlg = GoogleApiAvailability.getInstance().getErrorDialog(this, code, 1)
            dlg?.show()
            toast("Scan Failed! Google play service is not available")
        }
    }

    private fun startScan(isScanning: Boolean = true) {
        Timber.d("startScan")
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            if (ActivityCompat.checkSelfPermission(
                            this,
                            Manifest.permission.CAMERA
                    ) != PackageManager.PERMISSION_GRANTED
            ) {
                ActivityCompat.requestPermissions(
                        this,
                        arrayOf(Manifest.permission.CAMERA),
                        RC_CAMERA
                )
                return
            }
        }

        try {
            if (isScanning) {
                cameraSource.start(binding.cameraView.holder)
                binding.cameraView.requestFocus()
            } else cameraSource.stop()
        } catch (e: IOException) {
            showMessage(
                    "Sorry, your device is not supported for using camera, or no camera detected",
                    onDismissListener =  {
                        finish()
                    })
            Timber.i("<<< ERROR >>> Start camera error (IOException) : $e")
        } catch (e: Exception) {
            showMessage("Start camera error \n$e")
            Timber.i("<<< ERROR >>> Start camera error : $e")
        }
    }

    class CameraCallBack(scanBarcodeActivity: ScanBarcodeActivity) : SurfaceHolder.Callback {
        var activity = WeakReference(scanBarcodeActivity)
        override fun surfaceChanged(holder: SurfaceHolder, format: Int, width: Int, height: Int) {}
        override fun surfaceDestroyed(holder: SurfaceHolder) {
            activity.get()?.cameraSource?.stop()
        }

        override fun surfaceCreated(holder: SurfaceHolder) {
            Timber.d("surfaceCreated")
            activity.get()?.startScan()
        }
    }

    private fun initCamera() {
        try {
            barcodeDetector = BarcodeDetector.Builder(this)
//                    .setBarcodeFormats(
//                            Barcode.EAN_13 or Barcode.QR_CODE or Barcode.EAN_8 or Barcode.CODE_39 or
//                                    Barcode.CODE_128 or Barcode.ITF or Barcode.ISBN or
//                                    Barcode.CODABAR or Barcode.DATA_MATRIX or Barcode.UPC_A) // or : Barcode.ALL_FORMATS
                    .setBarcodeFormats(Barcode.ALL_FORMATS)
                    .build()

            //check if the required native libraries are currently available
            barcodeDetector?.let { barcodeDetector ->
                if (!barcodeDetector.isOperational) {
                    val lowStorageFilter = IntentFilter(Intent.ACTION_DEVICE_STORAGE_LOW)
                    val hasLowStorage = registerReceiver(null, lowStorageFilter) != null
                    if (hasLowStorage) {
                        showMessage(
                                "your device has low storage! barcode detector would not work unless you free up your storage",
                                onDismissListener = DialogInterface.OnDismissListener { finish() })
                    } else {
                        showMessage(
                                "some libraries still need to download in background to make barcode reader work! Please make sure you have internet connection and Google Play Service available",
                                onDismissListener = DialogInterface.OnDismissListener { finish() })
                    }
                }
            }

            val hasBackCamera = hasBackCamera()
            Timber.i("has back camera: $hasBackCamera")
            val cameraFacing = if(hasBackCamera) CameraSource.CAMERA_FACING_BACK else CameraSource.CAMERA_FACING_FRONT

            cameraSource = CameraSource.Builder(this, barcodeDetector)
                    .setAutoFocusEnabled(true)
                    .setRequestedPreviewSize(640, 480)
                    .setFacing(cameraFacing)
                    .build()

            binding.cameraView.isFocusable = true
            binding.cameraView.isFocusableInTouchMode = true
//            cameraCallback = object : SurfaceHolder.Callback {
//                override fun surfaceChanged(holder: SurfaceHolder?, format: Int, width: Int, height: Int) {}
//                override fun surfaceCreated(holder: SurfaceHolder?) {
//                    startScan()
//                }
//
//                override fun surfaceDestroyed(holder: SurfaceHolder?) {
//                    cameraSource.stop()
//                }
//            }

            cameraCallback = CameraCallBack(this)

            binding.cameraView.holder.addCallback(cameraCallback)
            barcodeDetector?.setProcessor(object : Detector.Processor<Barcode> {
                override fun release() {}
                override fun receiveDetections(detection: Detector.Detections<Barcode>?) {
                    onReceiveBarcodeDetection(detection)
                }
            })

            if (barcodeDetector == null) {
                toast("init barcode detector failed...")
            }

//            binding.cameraView.holder.setFormat(PixelFormat.TRANSPARENT)
            Timber.d("camera initialized")
        } catch (e: Exception) {
            Timber.i("<<< ERROR >>> Init camera error : $e")
            showMessage("we can not start camera \n$e")
        }
    }

    private fun onReceiveBarcodeDetection(detection: Detector.Detections<Barcode>?) {
        detection?.detectedItems?.takeIf { it.size() > 0 }?.let { barcode ->
            val barcodeValue = barcode.valueAt(0).displayValue
            if (actionType == Constant.ACTION_GET_VALUE_ONLY) {
                Timber.i("barcode : $barcodeValue")
                val intent = Intent()
                intent.putExtra("data", barcodeValue)
                setResult(Activity.RESULT_OK, intent)
                finish()
            } else {
                if (lastScanDetected != barcodeValue || scanLoop > MAX_RESCAN) {
                    scanLoop = 0

                    var sound = R.raw.beep
                    var sleepDuration = 1500L
                    Timber.i(">>> BARCODE : $barcodeValue")
                    var productFound: ProductEntity? = null
                    productList.firstOrNull { it.barcode == barcodeValue }?.let { product ->
                        lastScanDetected = barcodeValue
                        productFound = product
                    } ?: kotlin.run {
                        sound = R.raw.error
                        sleepDuration = 1000L
                    }
                    showProduct(productFound)

                    mp?.let {
                        if (it.isPlaying) it.stop()
                        it.release()
                    }

                    MediaPlayer.create(this, sound)?.apply {
                        mp = this
                        start()
                    }

                    GlobalScope.launch(context = Dispatchers.Main) {
                        startScan(false)
                        delay(sleepDuration)
                        startScan()
                    }
                } else {
                    toast("found same barcode - $scanLoop")
                    scanLoop++
                }
            }
        }
    }

    private fun showProduct(productEntity: ProductEntity? = null) {
        runOnUiThread {
            binding.layoutProduct.apply {
                dataNotFound(productEntity == null)
                productEntity?.let {
                    binding.txtItem.text = it.name
                    binding.txtPrice.text = it.priceSell?.toCurrency()
                    orderList.add(Order(it, 1, it.priceSell ?: 0))
                    refreshCart()
                }
//                alpha = 0f
                visibility = View.VISIBLE
//                animate()
//                        .setDuration(500)
//                        .alpha(1f)

                GlobalScope.launch(context = Dispatchers.Main) {
                    delay(1500)
//                    animate()
//                            .setDuration(300)
//                            .alpha(0f)
                    visibility = View.GONE
                }

            }
        }
    }

    private fun refreshCart() {
        if (binding.incCart.root.visibility == View.GONE) {
            binding.incCart.root.visibility = View.VISIBLE
        }
        binding.incCart.txtQtyOrder.text = orderList.sumBy { it.qty }.toString()
        binding.incCart.txtGrandtotal.text = orderList.sumBy { it.price }.toCurrency()
    }

    private fun dataNotFound(isNotFound: Boolean) {
        binding.imgError.visibility = if (isNotFound) View.VISIBLE else View.GONE
        binding.txtError.visibility = if (isNotFound) View.VISIBLE else View.GONE
        binding.txtItem.visibility = if (isNotFound) View.GONE else View.VISIBLE
        binding.txtPrice.visibility = if (isNotFound) View.GONE else View.VISIBLE
    }

    /** Check if this device has a camera */
    private fun checkCameraHardware(context: Context): Boolean {
        return context.packageManager.hasSystemFeature(PackageManager.FEATURE_CAMERA_ANY)
    }

    private fun hasBackCamera(): Boolean{
        for (i in 0 until Camera.getNumberOfCameras()) {
            val cameraInfo = Camera.CameraInfo()
            Camera.getCameraInfo(i, cameraInfo)
            if (cameraInfo.facing === Camera.CameraInfo.CAMERA_FACING_BACK) {
                return true
            }
        }
        return false
    }

    override fun onDestroy() {
        mp?.let {
            if (it.isPlaying) it.stop()
            it.release()
        }
        startScan(false)
        binding.cameraView.holder.removeCallback(cameraCallback)
        cameraCallback = null
        super.onDestroy()
        cameraSource.release()
        barcodeDetector?.release()
    }

    override fun onRequestPermissionsResult(
            requestCode: Int,
            permissions: Array<out String>,
            grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        if (grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
            startScan()
        } else {
            showMessage(
                    "scan tidak dapat dilakukan jika aplikasi tidak memiliki akses untuk membuka kamera",
                    "SCAN FAILED",
                    onDismissListener = {
                        finish()
                    })
        }
    }
}
