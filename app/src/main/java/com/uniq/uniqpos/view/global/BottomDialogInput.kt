package com.uniq.uniqpos.view.global

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.FragmentManager
import android.widget.FrameLayout
import com.uniq.uniqpos.R
import com.uniq.uniqpos.databinding.LayoutBottomDialogInputBinding
import com.uniq.uniqpos.model.BottomDialogModel
import com.uniq.uniqpos.util.simpleOnTextChanged
import com.uniq.uniqpos.util.value
import com.uniq.uniqpos.view.scanbarcode.ScanBarcodeActivity
import android.os.Handler
import android.view.*
import android.view.inputmethod.EditorInfo
import timber.log.Timber

class BottomDialogInput : BottomSheetDialogFragment() {

    private lateinit var listener: (String) -> Unit
    private lateinit var binding: LayoutBottomDialogInputBinding
    private var model: BottomDialogModel? = null
    private var isUseBarcode = false

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = LayoutBottomDialogInputBinding.inflate(inflater, container, false)

        model?.apply {
            binding.model = this
        } ?: run {
            binding.model = BottomDialogModel("DIALOG", "NEXT")
        }

        binding.edtInput.simpleOnTextChanged {
            if (it.isNotBlank() && binding.txtErrMsg.text.toString()
                    .isNotEmpty()
            ) binding.txtErrMsg.text = ""
        }
        binding.imgClose.setOnClickListener { dismiss() }
        binding.btnNext.setOnButtonClickListener { next() }

        binding.edtInput.setOnEditorActionListener { _, actionId, key ->
            if (actionId == EditorInfo.IME_ACTION_DONE) next()
            else if (key.keyCode == KeyEvent.KEYCODE_ENTER) next()
//            Timber.i("[${model?.title}] setOnEditorActionListener : $actionId | keyAction: ${key.action} | keyCode: ${key.keyCode} | enterIs : ${KeyEvent.KEYCODE_ENTER}")
            false
        }

        if (isUseBarcode) {
            binding.edtInput.setCompoundDrawablesWithIntrinsicBounds(0, 0, R.drawable.ic_barcode, 0)
            binding.btnScan.visibility = View.VISIBLE

            binding.btnScan.setOnClickListener {
                val intent = Intent(context, ScanBarcodeActivity::class.java)
                intent.putExtra("action_type", ScanBarcodeActivity.ACTION_GET_VALUE_ONLY)
                startActivityForResult(intent, 10)
            }
        }

        return binding.root
    }

    private fun next() {
        if (binding.edtInput.value().isBlank()) {
            binding.txtErrMsg.text = "field can not be empty"
        } else {
            binding.btnNext.startLoading()
            listener(binding.edtInput.value())
            binding.btnNext.stopLoading()
        }
    }

    fun setError(errMsg: String) {
        binding.txtErrMsg.text = errMsg
    }

//    override fun show(manager: FragmentManager, tag: String?) {
//        setStyle(DialogFragment.STYLE_NORMAL, R.style.DialogStyle)
//        super.show(manager, tag)
//    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        view.viewTreeObserver.addOnGlobalLayoutListener(object :
            ViewTreeObserver.OnGlobalLayoutListener {
            override fun onGlobalLayout() {
                view.viewTreeObserver.removeOnGlobalLayoutListener(this)
                val dialog = dialog as BottomSheetDialog
                val bottomSheet =
                    dialog.findViewById(com.google.android.material.R.id.design_bottom_sheet) as FrameLayout?
                val behavior = BottomSheetBehavior.from(bottomSheet!!)
                behavior.state = BottomSheetBehavior.STATE_EXPANDED
            }

        })

        Handler().postDelayed({
            binding.edtInput.requestFocus()
        }, 700)
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (resultCode == Activity.RESULT_OK) {
            data?.getStringExtra("data")?.takeIf { it.trim().isNotEmpty() }?.let { barcode ->
                binding.edtInput.setText(barcode)
                next()
            }
        }
    }

    fun setModel(model: BottomDialogModel) {
        this.model = model
    }

    fun setOnButtonClick(action: (String) -> Unit) {
        this.listener = action
    }

    fun setUseBarcode(useBarcode: Boolean) {
        isUseBarcode = useBarcode
    }

    override fun show(manager: FragmentManager, tag: String?) {
        super.show(manager, tag)
        Timber.i("show BottomDialogInput for $tag")
    }
}