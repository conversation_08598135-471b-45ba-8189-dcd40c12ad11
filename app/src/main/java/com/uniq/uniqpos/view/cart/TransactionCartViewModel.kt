package com.uniq.uniqpos.view.cart

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.bugsnag.android.Bugsnag
import com.google.firebase.analytics.FirebaseAnalytics
import com.google.gson.Gson
import com.uniq.uniqpos.data.local.converter.SalesConverter
import com.uniq.uniqpos.data.local.entity.SalesEntity
import com.uniq.uniqpos.data.local.entity.TmpSalesEntity
import com.uniq.uniqpos.data.local.sharedpref.SharedPref
import com.uniq.uniqpos.data.local.sharedpref.sharedPref
import com.uniq.uniqpos.data.remote.repository.SalesRepository
import com.uniq.uniqpos.data.remote.repository.SettingRepository
import com.uniq.uniqpos.model.Device
import com.uniq.uniqpos.model.Order
import com.uniq.uniqpos.util.*
import com.uniq.uniqpos.util.lifecycle.BaseViewModel
import com.uniq.uniqpos.util.lifecycle.Event
import kotlinx.coroutines.*
import retrofit2.await
import timber.log.Timber
import javax.inject.Inject

/**
 * Created by ANNASBlackHat on 11/11/17.
 */
class TransactionCartViewModel @Inject
constructor(private val salesRepository: SalesRepository,
            private val settingRepository: SettingRepository) : BaseViewModel() {

    val saleList = ArrayList<SalesEntity>()
    val saleListTmp = ArrayList<SalesEntity>()
    val taskRefreshSales = MutableLiveData<Event<Unit>>()
    val taskContinueEdit = MutableLiveData<Event<SalesEntity>>()
    val taskContinueEditWithMerge = MutableLiveData<Event<Bundle>>()
    val availableDevices = ArrayList<Device>()
    val printers = settingRepository.printers

    fun loadActiveDevice(outletId: Int?) {
        salesRepository.getAvailableDevice(outletId)
                .awaitAsync({
                    it.body()?.data?.let { devices ->
                        availableDevices.clear()
                        availableDevices.addAll(devices)
                    }
                }, {})
    }

    fun updateTmpSales(tmpSalesEntity: TmpSalesEntity) {
        viewModelScope.launch {
            salesRepository.updateTmpSale(tmpSalesEntity)
        }
    }

    fun convertSalesJson(listJson: List<String>) {
        saleList.clear()
        saleListTmp.clear()
        viewModelScope.launch(Dispatchers.IO) {
            listJson.filter { it.isNotEmpty() }.forEach { sales ->
                try {
                    val salesEntity = SalesConverter().fromStringToObject(sales)
                    if (!saleListTmp.any { it.noNota == salesEntity.noNota }) {
                        saleListTmp.add(salesEntity)
                    }
//                    if (saleListTmp.size % 10 == 0) taskRefreshSales.postValue(Event(Unit))
                } catch (e: Exception) {
                    Timber.i("Cannot convert to salesEntity $e \n --> $sales")
                    Bugsnag.notify(e)
                }
            }

            Timber.i("cart ids : ${saleListTmp.map { it.noNota }} | actual size: ${listJson.size}")

            //try to find duplicate item, and remove it
            val double = saleListTmp.groupBy { it.noNota }.filter { it.value.size > 1 }
            for ((salesId, _) in double) {
                saleListTmp.firstOrNull { it.noNota == salesId }?.let { saleListTmp.remove(it) }
            }

            if (double.isNotEmpty()) {
                Bugsnag.notify(Exception("cart double detected"))
            }

//            val uniqueSales = saleListTmp.distinctBy { it.noNota }.toMutableList()
//            saleList.addAll(uniqueSales)

            saleList.addAll(saleListTmp)
            taskRefreshSales.postValue(Event(Unit))
        }
    }

    //to edit sales, we have to check if it is currently being edited by other device or not,
    //and also set to server db that current cart is being edited
    //cart is taken from server, so it's must be a fresh data
    fun editSalesCart(sales: SalesEntity) {
        viewModelScope.launch {
            try {
                _loadingDialog.postValue(Event(true))
                withTimeout(5000) {
                    val result = salesRepository.getEditableStatus(sales.noNota).await()
                    if (result.status) {
                        result.data?.let { freshData ->
                            val freshSales: SalesEntity? = try {
                                SalesConverter().fromStringToObject(freshData.sales)
                            } catch (e: java.lang.Exception) {
                                Bugsnag.notify(e)
                                Timber.i("[ERROR] converting to sales error : $e \ndata : ${Gson().toJson(freshData.sales)}")
                                sales
                            }
                            freshSales?.let { taskContinueEdit.postValue(Event(it)) }
                        } ?: kotlin.run {
                            _toastMsg.postValue(Event("server not returning any data, use from local instead"))
                            taskContinueEdit.postValue(Event(sales))
                        }
                    } else {
                        _dialogMsg.postValue(Event(result.message ?: "Error, please try again"))
                    }
                }
            } catch (e: Exception) {
                _toastMsg.postValue(Event(e.readableError()))
                taskContinueEdit.postValue(Event(sales))
            } finally {
                _loadingDialog.postValue(Event(false))
            }
        }
    }

    fun mergeBill(salesEntity: SalesEntity, mergeIds: java.util.ArrayList<String>) {
        viewModelScope.launch {
            _loadingDialog.postValue(Event(true))
            val salesUsePromoMember = ArrayList<String>()
            mergeIds.forEach { id ->
                saleList.firstOrNull { it.noNota == id }?.let { sales ->
                    if (!isUseMemberPromo(sales)) {
                        sales.orderList?.let { order -> salesEntity.orderList?.addAll(order) }
                    } else {
                        salesUsePromoMember.add(sales.customer.safe())
                    }
                }
            }

            if (salesUsePromoMember.isNotEmpty()) {
                _loadingDialog.postValue(Event(false))
                _dialogMsg.postValue(Event("transaksi tidak dapat digabungkan dengan bill : ${salesUsePromoMember.joinToString()} \n\nkarena transaksi tersebut menggunakan promo member"))
            } else {
                _loadingDialog.postValue(Event(false))
                Timber.i(">>> [MERGE BILL] Chosen IDs: $mergeIds | Merge With : ${salesEntity.noNota}")
                val bundle = Bundle()
                bundle.putParcelable("sales", salesEntity)
                bundle.putBoolean("is_merge", true)
                bundle.putSerializable("merge_ids", mergeIds)
                taskContinueEditWithMerge.postValue(Event(bundle))
            }
        }
    }

    private fun isUseMemberPromo(sales: SalesEntity): Boolean {
        val promoMemberTypes = listOf(Constant.PROMO_TYPE_FREE_MEMBER, Constant.PROMO_TYPE_SPECIAL_PRICE_MEMBER, Constant.PROMO_TYPE_DISCOUNT_MEMBER)
        return sales.memberDetail != null && (sales.orderList?.any { order -> promoMemberTypes.any { order.promotion?.pomotionTypeFkid == it } }.safe() ||
                sales.promotions?.any { it.typeId == Constant.PROMO_TYPE_DEALS || it.typeId == Constant.PROMO_TYPE_DISCOUNT_MEMBER }.safe())
    }

    fun getTransactionCartLive(outletId: Int?) = salesRepository.getTransactionCartLive(outletId)
}