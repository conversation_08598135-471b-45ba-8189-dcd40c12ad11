package com.uniq.uniqpos.view.verifypin

import android.Manifest
import android.content.Context
import android.content.DialogInterface
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Build
import android.os.Bundle
import android.provider.Settings
import android.telephony.TelephonyManager
import android.view.View
import androidx.core.app.ActivityCompat
import com.bugsnag.android.Bugsnag
import com.google.firebase.analytics.ktx.analytics
import com.google.firebase.analytics.ktx.logEvent
import com.google.firebase.ktx.Firebase
import com.google.firebase.messaging.FirebaseMessaging
import com.uniq.uniqpos.R
import com.uniq.uniqpos.data.local.sharedpref.*
import com.uniq.uniqpos.databinding.ActivityVerifyPinBinding
import com.uniq.uniqpos.util.*
import com.uniq.uniqpos.util.lifecycle.Failed
import com.uniq.uniqpos.util.lifecycle.Loading
import com.uniq.uniqpos.view.billing.BillingActivity
import com.uniq.uniqpos.view.global.BaseActivity
import com.uniq.uniqpos.view.login.LoginAdminActivity
import com.uniq.uniqpos.view.main.MainActivity
import com.uniq.uniqpos.viewmodel.AuthViewModel
import timber.log.Timber

class VerifyPINActivity : BaseActivity<AuthViewModel, ActivityVerifyPinBinding>() {

    private var employeeID: Int = 0
    private var firebaseToken: String = ""
    private var loginAttempt =  0

    override fun getLayoutRes() = R.layout.activity_verify_pin
    override fun getViewModel() = AuthViewModel::class.java

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        window.setBackgroundDrawable(null)

        binding.viewModel = viewModel
        binding.verify = this
        title = intent.getStringExtra("employeeName")
        employeeID = intent.getIntExtra("id", 0)

        Timber.i("login employee for $title ($employeeID)")

        binding.imgBackspace.setOnClickListener {
            var currentPin = binding.edtPin.text.toString()
            if (currentPin.isNotEmpty()) {
                binding.edtPin.setText(currentPin.substring(0, currentPin.length - 1))
                binding.edtPin.setSelection(binding.edtPin.text.toString().length)
            }
        }

        if (isGooglePlayServicesAvailable()) {
            FirebaseMessaging.getInstance().token.addOnCompleteListener { task ->
                if (task.isSuccessful) {
                    val token = task.result
                    firebaseToken = token
                    Timber.i("firebase token: $token")
                }
            }.addOnFailureListener { toast(it.readableError()) }
        } else {
            toast("Google Play Service is unavailable. Some function might not works properly")
        }

        employeeList().takeIf { it.size == 1 }?.firstOrNull()?.let { employee ->
            if (employee.name.equalIgnoreCase("admin") && employee.pin == "12345") {
                binding.txtDefaultInfo.visibility = View.VISIBLE
                Timber.i(">>> showing default PIN info")
            }
        }
    }

    override fun observeTask() {
        viewModel.state.observe(this) { state ->
            when (state) {
                is Loading -> {
                    showDialog(state.isLoading)
                }
                is Failed -> {
                    when (state.exception) {
                        is AuthViewModel.UnAvailableSlot -> {
                            showMessage(state.exception.msg, "UNAVAILABLE SLOT", { _, _ ->
//                                launchUrl("${BuildConfig.WEB_URL}settings/subscription-add")
                                startActivity(Intent(this, BillingActivity::class.java))
                            }, positiveMsg = "BELI SLOT DEVICE")
                        }
                        is AuthViewModel.SubscriptionEnded -> {
                            showMessage(state.exception.msg, "SUBSCRIPTION ENDED", { _, _ ->
//                                launchUrl("${BuildConfig.WEB_URL}settings/subscription-add")
                                startActivity(Intent(this, BillingActivity::class.java))
                            }, positiveMsg = "BELI LANGGANAN")
                        }
                        is AuthViewModel.UnAuthorize -> {
                            showMessage(
                                "Invalid Authorization, you will be redirect to Login Page",
                                "LOGIN FAILED",
                                onDismissListener = DialogInterface.OnDismissListener {
                                    putData(SharedPref.LOGIN_ADMIN_STATUS, false)
                                    clearJson(SharedPref.ADMIN_DATA)

                                    val intent = Intent(this, LoginAdminActivity::class.java)
                                    intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                                    startActivity(intent)
                                    finish()
                                })
                        }
                        is AuthViewModel.InvalidTime -> {
                            Firebase.analytics.logEvent("fraud_detection") {
                                param("type", "Date Changed")
                                param("info", "Diff Minute : ${state.exception.diffMinute}")
                                param("outlet", outlet()?.name.safe())
                                param(
                                    "adminId_outletId",
                                    "${outlet()?.adminFkid}_${outlet()?.outletId}"
                                )
                                param("device", "${Build.BRAND} ${Build.MODEL} - ${Build.SERIAL}")
                            }

                            showMessage("Jam/Tanggal pada device anda sepertinya belum di setting dengan benar. \n" +
                                    "Silahkah buka Pengaturan dan ganti tanggal terlebih dahulu!",
                                "JAM/TANGAL BERBEDA",
                                onDismissListener = DialogInterface.OnDismissListener {
                                    startActivity(Intent(Settings.ACTION_DATE_SETTINGS))
                                })
                        }
                        else -> {
                            showMessage(state.exception.readableError(this, false))
                        }
                    }
                }
            }
        }

        viewModel.loginSuccess.observe(this) { warningMsg ->
            Bugsnag.leaveBreadcrumb("login pin")
            val intent = Intent(this@VerifyPINActivity, MainActivity::class.java)
            intent.putExtra("isFromLogin", true)
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                .addFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK)
            intent.putExtra("warning_message", warningMsg)
            startActivity(intent)
            finish()
        }

        viewModel.pin.observe(this) {
            if (it.length == 5) {
                binding.edtPin.isEnabled = false
                checkPIN()
            }
        }
    }

    fun keypadClicked(vi: View, num: String) {
        var currentPin = binding.edtPin.text.toString()
        if (currentPin.length < 5)
            binding.edtPin.append(num)
//        if (edt_pin.text.toString().length == 5) checkPIN()
    }

    private fun checkPIN() {
        loginAttempt += 1
        if(!isPermissionsGranted()){
            return
        }

        val deviceID = getCurrentDeviceId()
        Timber.i("Login with Employee $title [ID : $employeeID] | device ID $deviceID")
        viewModel.loginPin(employeeID, outlet()?.outletId.safe(), firebaseToken, deviceID)

        binding.edtPin.isEnabled = true
        binding.edtPin.setText("")
        loginAttempt = 0
    }

    private fun isPermissionsGranted(): Boolean {
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.M || Build.VERSION.SDK_INT >= 30) {
            return true
        }

        val isGrantedReadPhoneState = ActivityCompat.checkSelfPermission(
            this,
            Manifest.permission.READ_PHONE_STATE
        ) == PackageManager.PERMISSION_GRANTED
Build.VERSION.SDK_INT
        val permissionWriteExternal =  ActivityCompat.checkSelfPermission(
            this,
            Manifest.permission.WRITE_EXTERNAL_STORAGE
        ) == PackageManager.PERMISSION_GRANTED

        if ( !isGrantedReadPhoneState) {
            if(!isGrantedReadPhoneState)toast("permissions are required to run the app, can not read phone state")
//            if(!permissionWriteExternal)toast("permissions are required to run the app, can not write storage")

            if (loginAttempt >= 3) {
//                binding.edtPin.text = null
//                showMessage("silahkan coba lakukan Clear Data atau Install Ulang kembali aplikasi ini", onDismissListener = {
//                    finish()
//                })
                return true
            }else{
                ActivityCompat.requestPermissions(
                    this,
                    arrayOf(
                        Manifest.permission.READ_PHONE_STATE,
//                        Manifest.permission.WRITE_EXTERNAL_STORAGE
                    ),
                    111
                )
            }
            return false
        }
        return true
    }

    //get device identifier id
    private fun getCurrentDeviceId(): String {
        var deviceID = try {
            val tm = getSystemService(Context.TELEPHONY_SERVICE) as TelephonyManager
            if (Build.VERSION.SDK_INT >= 26) {
                tm.imei
            } else {
                tm.deviceId
            }
        } catch (e: Exception) {
            ""
        }

        if (deviceID == null || deviceID.isEmpty()) {
            deviceID = Settings.Secure.getString(contentResolver, Settings.Secure.ANDROID_ID)
        }

        if (deviceID == null || deviceID.isEmpty()) {
            deviceID = Build.SERIAL
        }

        if (deviceID == null || deviceID.isEmpty()) deviceID =
            "unknown ${Build.MODEL} - " + System.currentTimeMillis()

        val currentDeviceId = getLocalDataString(SharedPref.DEVICE_ID, "")
        if (currentDeviceId.isEmpty()) {
            putData(SharedPref.DEVICE_ID, deviceID)
        } else {
            if (currentDeviceId != deviceID) {
                Timber.i(">>> Difference Device ID. Before : $currentDeviceId | Now : $deviceID \n >>> Device : ${Build.BRAND} ${Build.MODEL}")
            }
            deviceID = currentDeviceId
        }

        if ((isDeviceHasNoImei() && !deviceID.startsWith("iq")) || deviceID == "012345678912345") {
            deviceID = "iq${System.currentTimeMillis()}_$deviceID"
            putData(SharedPref.DEVICE_ID, deviceID)
        }
        return deviceID
    }

    //device has no imei for some cases, like china tablet or androidx86 installed on PC
    private fun isDeviceHasNoImei(): Boolean {
        return (Constant.DEVICE_NAME == "STA ZH960" || Constant.DEVICE_NAME == "Lenovo 5067A1H")
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        if(grantResults.all { it == PackageManager.PERMISSION_GRANTED  }){
            checkPIN()
        }else{
            binding.edtPin.text = null
//            toast("permissions are required for the app to run properly", level = Level.ERROR)
        }
    }
}
