package com.uniq.uniqpos.view.transaction

import android.content.res.AssetManager
import androidx.lifecycle.viewModelScope
import com.google.gson.Gson
import com.uniq.uniqpos.data.local.entity.KitchenDisplayEntity
import com.uniq.uniqpos.data.local.entity.SalesEntity
import com.uniq.uniqpos.data.local.sharedpref.SharedPref
import com.uniq.uniqpos.data.remote.model.Outlet
import com.uniq.uniqpos.data.remote.repository.SettingRepository
import com.uniq.uniqpos.model.KitchenDisplayData
import com.uniq.uniqpos.model.Order
import com.uniq.uniqpos.util.Constant
import com.uniq.uniqpos.util.lifecycle.BaseViewModel
import com.uniq.uniqpos.util.networking.sendWebSocket
import com.uniq.uniqpos.util.safe
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import timber.log.Timber
import java.net.Socket

class TransactionViewModelKitchen(
    var settingRepository: SettingRepository,
    private val sharedPref: SharedPref,
    private val viewModel: BaseViewModel,
    private val transactionVm: TransactionViewModel,
    private val assetManager: AssetManager
) {

    val kitchenList = ArrayList<KitchenDisplayEntity>()

    val outletId: Int by lazy {
        sharedPref.getJson(
            SharedPref.OUTLET_DATA,
            Outlet::class.java
        )?.outletId ?: 0
    }

    fun loadKitchen() = settingRepository.loadKitchenDisplay(outletId)

    fun updateKitchenList(kitchen: List<KitchenDisplayEntity>) {
        kitchenList.clear()
        kitchenList.addAll(kitchen)
        Timber.i("kitchen list size : ${kitchenList.size}")
    }

    private val ioScope = CoroutineScope(Dispatchers.IO)

    fun sendToKitchen(sales: SalesEntity) {
        viewModel.viewModelScope.launch {
            Timber.i("send to kitchen.. ${kitchenList.size} device found!")
            kitchenList.forEach { kitchen ->
                arrangeKitchenData(kitchen, sales)?.let { kitchenData ->
                    val kitchenDataJson = Gson().toJson(kitchenData)
                    Timber.i("sending to kitchen '${kitchen.address}' (${kitchen.name}) -> $kitchenDataJson")
                    ioScope.launch {
                        try {
//                            val socket = Socket(kitchen.address, Constant.KITCHEN_PORT)
//                            val output = socket.getOutputStream()
//                            output.write(kitchenDataJson.toByteArray())
//                            output.flush()
//                            output.close()
//                            socket.close()
//
                            sendWebSocket(kitchenDataJson, kitchen.address, Constant.KITCHEN_PORT, "/ws", assetManager)

                            Timber.i("send to kitchen '${kitchen.address}:${Constant.KITCHEN_PORT}' success")
                        } catch (e: Exception) {
                            Timber.i("send to kitchen '${kitchen.address}:${Constant.KITCHEN_PORT}' failed : ${e.message}")
                        }
                    }.join()
                }
            }
        }
    }

    private fun arrangeKitchenData(
        kitchen: KitchenDisplayEntity,
        sales: SalesEntity
    ): KitchenDisplayData? {
        Timber.i("${kitchen.address}, categoryIds:  ${kitchen.categories.map { it.categoryId }}")
        Timber.i("order categoryIds: ${sales.orderList?.map { it.product?.productSubcategoryFkid }}")
        val orderDetail = ArrayList<Map<String, Any>>()
        kitchen.categories.forEach { category ->
            val orders = ArrayList<Order>()
            sales.orderList?.filter { it.product?.productSubcategoryFkid == category.categoryId }
                ?.let { orderList ->
                    orders.addAll(orderList)
                    orders.forEach { o -> o.holdQty = 1 }
                }
            if (orders.isNotEmpty())
                orderDetail.add(
                    mapOf(
                        "subcategory_id" to category.categoryId,
                        "subcategory_name" to category.categoryName,
                        "products" to orders
                    )
                )
        }

        if (orderDetail.isEmpty()) {
            Timber.i("order detail is empty for kitchen ${kitchen.name} (${kitchen.address})")
            return null
        }

        return KitchenDisplayData(
            type = "order",
            orderId = sales.noNota,
            clientId = 1,
            displayNota = sales.displayNota,
            customerName = sales.customer.safe(),
            dataStatus = "active",
            dateCreated = System.currentTimeMillis(),
            diningTable = sales.table,
            orderType = sales.salesTag?.name.safe(),
            orderDetails = Gson().toJson(orderDetail),
            isTmpSales = if(sales.displayNota.isEmpty()) 1 else 0,
        )
    }
}