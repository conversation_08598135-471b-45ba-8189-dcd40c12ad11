package com.uniq.uniqpos.view.purchase

import android.app.Activity
import android.os.Bundle
import android.view.MenuItem
import android.view.View
import androidx.lifecycle.Observer
import com.uniq.uniqpos.R
import com.uniq.uniqpos.data.local.entity.SupplierEntity
import com.uniq.uniqpos.databinding.ActivityAddSupplierBinding
import com.uniq.uniqpos.util.*
import com.uniq.uniqpos.view.global.BaseActivity

class AddSupplierActivity : BaseActivity<OperationalCostViewModel, ActivityAddSupplierBinding>() {

    private var isAdvanceVisible = true

    override fun getLayoutRes() = R.layout.activity_add_supplier
    override fun getViewModel() = OperationalCostViewModel::class.java

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        supportActionBar?.setHomeAsUpIndicator(R.drawable.ic_clear_white_24dp)

        binding.btnSave.setOnClickListener { saveSupplier() }
        listOf(binding.txtAdvanceMode, binding.txtAdvanceDetail).forEach { view ->
            view.setOnClickListener {
                setAdvanceVisibility()
            }
        }

        setAdvanceVisibility()
        observeTask()
    }

    private fun setAdvanceVisibility() {
        val visible = if(isAdvanceVisible) View.GONE else View.VISIBLE
        val views =  listOf(binding.layoutAddress, binding.layoutCity, binding.layoutEmail, binding.layoutNpwp)

        views.forEach { view ->
            view.visibility = visible
        }

        isAdvanceVisible = !isAdvanceVisible
        binding.txtAdvanceDetail.text = getString(if(isAdvanceVisible)R.string.advance_mode_detail_hide else R.string.advance_mode_detail_show)
    }

    override fun observeTask() {
        viewModel.taskSave.observe(this, Observer {
            showDialog(false)
            setResult(Activity.RESULT_OK)
            finish()
        })

        viewModel.taskShowDialog.observe(this, Observer { message ->
            showDialog(false)
            if(message is Exception){
                showAlert(message?.readableError(this))
            }else if(message is String){
                showAlert(message)
            }
        })
    }

    private fun saveSupplier() {
        if(!Utils.isValidField(binding.edtName))return
        if(binding.edtEmail.value().trim().isNotEmpty() && !binding.edtEmail.value().isValidEmail()){
            showMessage("Email address not valid!")
            return
        }

        showDialog(true)
        val supplier = SupplierEntity()
        supplier.name = binding.edtName.value()
        supplier.address = binding.edtAddress.value()
        supplier.city = binding.edtCity.value()
        supplier.email = binding.edtEmail.value()
        supplier.npwp = binding.edtNpwp.value()

        viewModel.saveSupplier(supplier)
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        finish()
        return super.onOptionsItemSelected(item)
    }
}
