package com.uniq.uniqpos.binding.closeshift

import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.util.Base64
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.webkit.WebView
import android.widget.ArrayAdapter
import android.widget.Toast
import androidx.appcompat.app.AlertDialog
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import com.bugsnag.android.Bugsnag
import com.google.gson.Gson
import com.uniq.uniqpos.BuildConfig
import com.uniq.uniqpos.R
import com.uniq.uniqpos.data.local.entity.CashRecapEntity
import com.uniq.uniqpos.data.local.entity.EmployeeEntity
import com.uniq.uniqpos.data.local.entity.PendingPrintEntity
import com.uniq.uniqpos.data.local.entity.PrinterEntity
import com.uniq.uniqpos.data.local.sharedpref.sharedPref
import com.uniq.uniqpos.databinding.DialogFilterCloseshiftBinding
import com.uniq.uniqpos.databinding.FragmentCloseShiftDetailBinding
import com.uniq.uniqpos.model.CashRecapData
import com.uniq.uniqpos.util.Level
import com.uniq.uniqpos.util.receipt.PrintNotaUtil
import com.uniq.uniqpos.util.displayNotaFormat
import com.uniq.uniqpos.util.employee
import com.uniq.uniqpos.util.employeeList
import com.uniq.uniqpos.util.outlet
import com.uniq.uniqpos.util.outletFeature
import com.uniq.uniqpos.util.safe
import com.uniq.uniqpos.util.setVisible
import com.uniq.uniqpos.util.showMessage
import com.uniq.uniqpos.util.toast
import com.uniq.uniqpos.view.closeshift.CloseShiftDetailActivity
import com.uniq.uniqpos.view.closeshift.CloseShiftViewModel
import com.uniq.uniqpos.view.main.MainActivity
import com.uniq.uniqpos.view.setting.printer.PrinterDetailActivity
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import timber.log.Timber
import kotlin.random.Random


class CloseShiftDetailFragment : Fragment() {

    private var report = ""
    private var viewModel: CloseShiftViewModel? = null
    private lateinit var dialogFilter: AlertDialog
    private var _dialogFilterView: DialogFilterCloseshiftBinding? = null
    private val dialogFilterView get() = _dialogFilterView!!
    private lateinit var filterCashierAdapter: ArrayAdapter<String>
    private var cashRecapData: CashRecapData? = null
    private val cashRecapList = ArrayList<CashRecapEntity>()
    private var allCashierChoice = "All Cashiers"
    private val employeeList = ArrayList<EmployeeEntity>()
    private val openShiftIds = ArrayList<Long>()
    private var isTabMode = true

    private var _binding: FragmentCloseShiftDetailBinding? = null
    private val binding get() = _binding!!

    companion object {
        val disableReportKey: String get() = "disable-report-${BuildConfig.VERSION_CODE}"
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.apply {
            report = getString("report").safe()
            cashRecapData = getParcelable("data")
            getString("data-json")?.takeIf { cashRecapData == null && it.isNotEmpty() }
                ?.let { dataJson ->
                    cashRecapData = Gson().fromJson(dataJson, CashRecapData::class.java)
                }
            getString("open_shift_ids")?.takeIf { it.isNotBlank() }?.let { id ->
                openShiftIds.clear()
                try {
                    openShiftIds.addAll(id.split(",").map { it.toLong() })
                } catch (e: Exception) {
                    Timber.i("adding openshift err: $e")
                }
            }
            getParcelableArrayList<CashRecapEntity>("cash_recap")?.let { data ->
                cashRecapList.clear()
                cashRecapList.addAll(data)
            }
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentCloseShiftDetailBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        Bugsnag.leaveBreadcrumb(this::class.simpleName.safe())

//        binding.webReport.setBackgroundColor(Color.TRANSPARENT)
        allCashierChoice = getString(R.string.all_cashiers)

        context?.employeeList()?.let { list ->
            employeeList.clear()
            employeeList.addAll(list)
        }

        viewModel = when (activity) {
            is CloseShiftDetailActivity -> {
                isTabMode = false
                (activity as CloseShiftDetailActivity).viewModel
            }

            is MainActivity -> (activity as MainActivity).getCloseShiftFragment()?.viewModel
            else -> null
        }

        viewModel?.taskCashRecap?.observe(this) {
            cashRecapData = it
        }

        if (cashRecapData == null && openShiftIds.isNotEmpty()) {
            viewModel?.getCashRecapData(openShiftIds)
        }

        viewModel?.initCloseShiftPrinter()

        val employeeIds = cashRecapData?.sales?.map { it.employeeID }?.distinct()
        val employeeNames = arrayListOf(allCashierChoice)
        employeeList.filter { e -> employeeIds?.any { it == e.employeeId } == true }.map {
            it.name ?: "-"
        }.let { employeeNames.addAll(it) }

        Timber.i("[CLOSE] employee Names : $employeeNames")

        _dialogFilterView = DialogFilterCloseshiftBinding.inflate(
            layoutInflater,
            null,
            false
        ) //LayoutInflater.from(context).inflate(R.layout.dialog_filter_closeshift, null)
        dialogFilter = AlertDialog.Builder(requireContext())
            .setView(dialogFilterView.root)
            .create()
        dialogFilter.setContentView(dialogFilterView.root)
        filterCashierAdapter =
            ArrayAdapter(requireContext(), android.R.layout.simple_list_item_1, employeeNames)
        dialogFilterView.spinFilter.setAdapter(filterCashierAdapter)

        dialogFilterView.btnApplyFilter.setOnClickListener {
            filterByEmployee(dialogFilterView.spinFilter.text.toString())
            dialogFilter.dismiss()
        }

        dialogFilterView.btnReset.setOnClickListener {
            filterByEmployee(null)
            dialogFilterView.spinFilter.setText("")
            dialogFilter.dismiss()
        }

        binding.btnPrint.setOnClickListener { printReport() }
        binding.btnFilter.setOnClickListener { dialogFilter.show() }
        binding.btnPrint.setOnButtonClickListener { printReport() }
        displayReport(report.displayNotaFormat())
        initFeature()
    }

    fun reInitCashRecapData(report: String) {
        this.report = report
        displayReport(report)
    }

    private fun filterByEmployee(employeeName: String?) {
        Timber.i("[CLOSE] filter by : $employeeName")
        var addWatermark = cashRecapData?.watermark
        val sales = if (employeeName.isNullOrEmpty() || employeeName == allCashierChoice) {
            cashRecapData?.sales
        } else {
            addWatermark += "\n#FILTER CASHIER : $employeeName"
            val employeeId = employeeList.firstOrNull { it.name == employeeName }?.employeeId ?: 0
            cashRecapData?.sales?.filter { it.employeeID == employeeId }
        }

        val outlet = context?.outlet()
        val employee = context?.employee()

        if (outlet == null || employee == null || sales == null || cashRecapData == null) {
            context?.toast("we can not process the filter, some of data are missing")
            return
        }

        PrintNotaUtil.getCashRecapFormat(
            cashRecapList,
            outlet,
            employee,
            sales,
            cashRecapData!!.tmpSales,
            cashRecapData!!.operationalCost,
            viewModel?.printerSettingCloseShift,
            cashRecapData!!.subcategory,
            { nota ->
                displayReport(nota)
                report = nota
            },
            openShifts = cashRecapData!!.openShifts.toTypedArray(),
            paperSize = viewModel?.printerCloseShift?.firstOrNull()?.settingPrintpapersize,
            watermark = addWatermark
        )
    }

    private fun printReport() {
        Bugsnag.leaveBreadcrumb("reprint close shift report")
        viewModel?.apply {
            Timber.i("[CLOSE] print report...")

            lifecycleScope.launch(Dispatchers.Main) {
                //printer should get from previous activity (get from intent)
                val allPrinter = withContext(Dispatchers.IO) { getPrinterSetting() }
                val printers = allPrinter.filter { it.settingClosingshift == "1" }
                if (printers.isEmpty()) {
                    context?.toast(
                        getString(R.string.no_printer_for_close_shift),
                        Toast.LENGTH_LONG
                    )
                    if (allPrinter.size == 1) {
                        context?.showMessage(
                            "${getString(R.string.no_printer_for_close_shift)}\n\n${
                                getString(
                                    R.string.update_printer_question
                                )
                            }",
                            "INFO", { _, _ -> openPrinterSetting(allPrinter.first()) }
                        )
                    }
                } else {
                    if (printers.size == 1) {
                        startPrinting(printers[0])
                    } else {
                        showPrinterOption(printers)
                    }
                }
            }
        }
    }

    private fun openPrinterSetting(printer: PrinterEntity) {
        val intent = Intent(context, PrinterDetailActivity::class.java)
        intent.putExtra("data", printer)
        startActivity(intent)
    }

    private fun startPrinting(printer: PrinterEntity) {
        binding.btnPrint.startLoading()
        lifecycleScope.launch(Dispatchers.Main) {
            val printerData = ArrayList<PendingPrintEntity>()
            printerData.add(
                PendingPrintEntity(
                    printer.address,
                    printer.type,
                    report,
                    printer.name,
                    System.currentTimeMillis() + Random.nextInt()
                )
            )

            when (activity) {
                is CloseShiftDetailActivity -> (activity as CloseShiftDetailActivity).managePrintWifi(
                    printerData
                )

                is MainActivity -> (activity as MainActivity).getCloseShiftFragment()
                    ?.managePrintWifi(printerData)
            }

            val manager = when (activity) {
                is CloseShiftDetailActivity -> (activity as CloseShiftDetailActivity).printerManager()
                is MainActivity -> (activity as MainActivity).getCloseShiftFragment()
                    ?.printerManager()

                else -> null
            }

            val ids = printerData.map { it.id }
            do {
                delay(500)
            } while (!manager?.isPrinted(ids).safe())
            binding.btnPrint.stopLoading()
            Bugsnag.leaveBreadcrumb("finish re-print close shift")
        }
    }

    private fun showPrinterOption(printerList: List<PrinterEntity>) {
        val optionList = ArrayList<String?>()
        printerList.forEach { optionList.add(it.name) }
        val options = optionList.toTypedArray()
        AlertDialog.Builder(requireContext())
            .setItems(options) { _, position ->
                startPrinting(printerList[position])
            }
            .setTitle("Choose Printer")
            .setNegativeButton(R.string.cancel, null)
            .show()
    }

    private fun displayReport(report: String) {
        if (report.isBlank()) {
            return
        }

        binding.txtReport.text = report.displayNotaFormat()

//        val disableReport = context?.sharedPref()?.getString(disableReportKey, "-")
//        if (disableReport == "-" || disableReport.isNullOrBlank()) {
//            context?.showMessage(
//                "Jika saat menampilkan laporan sistem keluar secara otomatis, silahkan di coba untuk ke 2 kalinya!\n\nPastikan Android WebView Sudah Terupdate!",
//                "WARNING",
//                negativeMsg = "Next",
//                positiveMsg = "Check WebView",
//                positiveAction = { _, _ ->
//                    val packageName = getWebViewPackage()
//                    if (packageName != null) openGooglePLay(packageName)
//                    else context?.toast("Silahkan buka Google Play, kemudian cari Android WebView")
//                },
//                negativeAction = { _, _ ->
//                    displayReportSafe(report)
//                })
//        } else {
//            displayReportSafe(report)
//        }
    }


    private fun displayReportSafe(cashRecapDetail: String) {
        var report = cashRecapDetail
        if (report.isBlank()) {
            report = "<br/><br/><b><i>${getString(R.string.no_close_shift_selected)}</i></b>"
            Timber.i("blank cash recap report...")
        }

        val disableReport = context?.sharedPref()?.getString(disableReportKey, "-")
        if (disableReport == "yes") {
            context?.toast(
                "Tidak dapat menampilkan laporan, pastikan aplikasi Android WebView sudah terupdate!",
                level = Level.ERROR,
                duration = Toast.LENGTH_LONG
            )

            val webViewPackageName = getWebViewPackage()
            context?.showMessage(
                report,
                "LAPORAN",
                negativeMsg = if (webViewPackageName != null) "UPDATE WEBVIEW" else "PRINT",
                positiveMsg = "Paksa Tampilkan",
                positiveAction = { _, _ ->
                    context?.sharedPref()?.putData(disableReportKey, "no")
                    context?.showMessage("silahkan restart aplikasi!", positiveAction = { _, _ ->
                        activity?.finish()
                    })
                },
                negativeAction = { _, _ ->
                    if (webViewPackageName != null) openGooglePLay(webViewPackageName)
                    else printReport()
                })
            return
        }

        context?.sharedPref()?.putData(disableReportKey, "yes")
        try {
            Bugsnag.leaveBreadcrumb("render close-shift html report")
//            binding.webReport.loadData(convertToHtml(report.displayNotaFormat()), "text/html", "base64")
            binding.txtReport.text = report.displayNotaFormat()
            Handler(Looper.getMainLooper()).postDelayed({
                try {
                    context?.sharedPref()?.putData(disableReportKey, "no")
                    Timber.i("set disableReportKey to no")
                } catch (e: Exception) {
                    Bugsnag.notify(e)
                }
            }, 1300)
        } catch (e: Exception) {
            context?.showMessage("tidak dapat menampilkan data", "ERROR")
            Bugsnag.notify(e)
        }
    }

    private fun openGooglePLay(appPackage: String) {
        // Create an intent to open the Google Play Store.
        val intent = Intent(Intent.ACTION_VIEW)
        // Set the data of the intent to be the package name of the app that you want to open in the Google Play Store.
        intent.data = Uri.parse("market://details?id=$appPackage")
        // Start the activity.
        startActivity(intent)
    }

    private fun getWebViewPackage(): String? {
        if (Build.VERSION.SDK_INT >= 26) {
            try {
                val webviewPackage = WebView.getCurrentWebViewPackage()
                Timber.i("webview package (26+): ${Gson().toJson(webviewPackage)}")
                return webviewPackage?.packageName
            } catch (e: Exception) {
                Bugsnag.notify(e)
                Timber.i("error get webview (26+) package: $e")
            }
        }

        try {
            val packageManager: PackageManager? = context?.packageManager
            val packageInfo =
                packageManager?.getPackageInfo("com.android.webview", PackageManager.GET_META_DATA)
            Timber.i("webview package: ${Gson().toJson(packageInfo)}")
            return packageInfo?.packageName
        } catch (e: Exception) {
            Bugsnag.notify(e)
            Timber.i("error get webview package: $e")
        }
        return null
    }

    private fun initFeature() {
        context?.outletFeature()?.let { feature ->
            binding.btnPrint.setVisible(feature.reprintCloseregister)
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        // Dismiss dialog to prevent window leak
        if (dialogFilter.isShowing) {
            dialogFilter.dismiss()
        }
        _binding = null
        _dialogFilterView = null  // Clear dialog binding
        // Clear lists
        cashRecapList.clear()
        employeeList.clear()
        openShiftIds.clear()
    }

    private fun convertToHtml(report: String): String {
        val fontSize = if (isTabMode) "small" else "medium"
        val reportHtml = template.replace("[[CONTENT]]", report)
            .replace("[[FONT]]", fontSize)
        return Base64.encodeToString(reportHtml.toByteArray(), Base64.NO_PADDING)
    }

    private val template = "<!DOCTYPE html>\n" +
            "<html\n" +
            "        lang=\"en\"\n" +
            "        dir=\"ltr\">\n" +
            "    <head>\n" +
            "        <meta charset=\"utf-8\">\n" +
            "        <title>Nota</title>\n" +
            "        <style>\n" +
            "      p {\n" +
            "        word-wrap: break-word;      /* IE 5.5-7 */\n" +
            "        white-space: -moz-pre-wrap; /* Firefox 1.0-2.0 */\n" +
            "        white-space: pre-wrap;      /* current browsers */\n" +
            "        font-family: \"Lucida Console\", Courier, monospace;\n" +
            "        font-size: [[FONT]]; \n" +
            "        color: #ffffff; \n" +
            "        text-align: center; \n" +
            "      }\n" +
            "\n" +
            "        </style>\n" +
            "    </head>\n" +
            "    <body>\n" +
            "        <p>\n" +
            "            [[CONTENT]]\n" +
            "        </p>\n" +
            "    </body>\n" +
            "</html>\n"
}
