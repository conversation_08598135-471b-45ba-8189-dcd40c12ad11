package com.uniq.uniqpos.view.splashscreen

import android.content.Intent
import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import com.uniq.uniqpos.data.local.sharedpref.SharedPref
import com.uniq.uniqpos.data.local.sharedpref.getJson
import com.uniq.uniqpos.data.local.sharedpref.sharedPref
import com.uniq.uniqpos.data.remote.model.Outlet
import com.uniq.uniqpos.view.chooseoperator.ChooseOperatorActivity
import com.uniq.uniqpos.view.chooseoutlet.ChooseOutletActivity
import com.uniq.uniqpos.view.login.LoginAdminActivity
import com.uniq.uniqpos.view.main.MainActivity

/**
 * Created by ANNASBlackHat on 19/10/2017.
 */
class SplashScreen : AppCompatActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        if(sharedPref().getBoolean(SharedPref.LOGIN_ADMIN_STATUS)){
            when {
                sharedPref().getBoolean(SharedPref.LOGIN_EMPLOYEE_STATUS) -> startActivity(Intent(this, MainActivity::class.java))
                getJson(SharedPref.OUTLET_DATA, Outlet::class.java)?.outletId != null -> startActivity(Intent(this, ChooseOperatorActivity::class.java))
                else -> startActivity(Intent(this, ChooseOutletActivity::class.java))
            }
        }else{
            startActivity(Intent(this, LoginAdminActivity::class.java))
        }
        finish()
    }
}