package com.uniq.uniqpos.view.reservation

import android.os.Bundle
import com.uniq.uniqpos.R
import com.uniq.uniqpos.data.local.entity.ReservationEntity
import com.uniq.uniqpos.data.local.sharedpref.SharedPref
import com.uniq.uniqpos.data.local.sharedpref.getJson
import com.uniq.uniqpos.data.remote.model.Outlet
import com.uniq.uniqpos.databinding.ActivityAddReservationBinding
import com.uniq.uniqpos.util.Utils
import com.uniq.uniqpos.util.setAsDateinput
import com.uniq.uniqpos.util.value
import com.uniq.uniqpos.view.global.BaseActivity
import com.uniq.uniqpos.viewmodel.ReservationViewModel
import java.text.SimpleDateFormat
import java.util.*


class AddReservationActivity : BaseActivity<ReservationViewModel, ActivityAddReservationBinding>() {

    private var isNewReservation = true

    override fun getLayoutRes() = R.layout.activity_add_reservation
    override fun getViewModel() = ReservationViewModel::class.java

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        intent.getParcelableExtra<ReservationEntity>("data")?.apply {
            binding.model = this
            isNewReservation = false
        }

        viewModel.saveTaskCommand.observe(this, androidx.lifecycle.Observer {
            finish()
        })

        binding.edtDate.setAsDateinput(supportFragmentManager, minDate = Calendar.getInstance())
        binding.edtTimeStart.setAsDateinput(supportFragmentManager, dateFormat = "HH:mm")
        binding.edtTimeEnd.setAsDateinput(supportFragmentManager, dateFormat = "HH:mm")

        binding.btnSave.setOnClickListener { saveReservation() }
    }

    private fun saveReservation() {
        if(!Utils.isValidField(binding.edtName, binding.edtPhone, binding.edtDate, binding.edtTimeStart, binding.edtTimeEnd))return

        val outlet = getJson(SharedPref.OUTLET_DATA, Outlet::class.java)
        val sdfTime = SimpleDateFormat("dd-MM-yyyy HH:mm", Locale.getDefault())

        val id = binding.model?.reservationId ?: System.currentTimeMillis()
        val reservation = ReservationEntity(id, 0, binding.edtPhone.value(),
                sdfTime.parse(binding.edtDate.value()+" "+binding.edtTimeStart.value()).time, binding.edtName.value(), 0,
                sdfTime.parse(binding.edtDate.value()+" "+binding.edtTimeEnd.value()).time, outlet?.outletId, binding.edtTable.value())
        viewModel.saveReservation(reservation)
    }
}
