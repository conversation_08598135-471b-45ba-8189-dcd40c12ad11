package com.uniq.uniqpos.view.global;

import android.view.View;

import androidx.databinding.DataBindingUtil;
import androidx.databinding.ViewDataBinding;
import androidx.recyclerview.widget.RecyclerView;

import timber.log.Timber;

/**
 * Created by ANNASBlackHat on 13/09/2017.
 */

public class GlobalViewHolderNoBinding extends RecyclerView.ViewHolder {

    private ViewDataBinding binding;

    public GlobalViewHolderNoBinding(View itemView) {
        super(itemView);
        try {
            binding = DataBindingUtil.bind(itemView);
        } catch (Exception e) {
            Timber.i(e);
        }
    }

    public void onBind(int variable, Object value) {
        if (value == null) return;
        binding.setVariable(variable, value);
        binding.executePendingBindings();
    }

}
