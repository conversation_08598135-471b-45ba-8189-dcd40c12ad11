package com.uniq.uniqpos.view.setting.kitchendisplay

import android.app.Activity.RESULT_OK
import android.os.Bundle
import android.view.View
import android.widget.ArrayAdapter
import androidx.activity.result.ActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.widget.PopupMenu
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import com.google.gson.Gson
import com.uniq.uniqpos.R
import com.uniq.uniqpos.data.local.entity.KitchenDisplayCategory
import com.uniq.uniqpos.data.local.entity.KitchenDisplayEntity
import com.uniq.uniqpos.data.local.sharedpref.SharedPref
import com.uniq.uniqpos.data.local.sharedpref.getLocalDataString
import com.uniq.uniqpos.databinding.DialogAddKitchenDisplayBinding
import com.uniq.uniqpos.databinding.FragmentKitchenDisplaySettingBinding
import com.uniq.uniqpos.databinding.ListItemKitchenDisplayBinding
import com.uniq.uniqpos.model.SimplePageListData
import com.uniq.uniqpos.util.Constant
import com.uniq.uniqpos.util.GetIpAddress
import com.uniq.uniqpos.util.Utils
import com.uniq.uniqpos.util.getDeviceName
import com.uniq.uniqpos.util.getWifiName
import com.uniq.uniqpos.util.isValidIpAddress
import com.uniq.uniqpos.util.lifecycle.Success
import com.uniq.uniqpos.util.lifecycle.setupDialogMessage
import com.uniq.uniqpos.util.lifecycle.setupLoadingDialog
import com.uniq.uniqpos.util.lifecycle.setupToastMessage
import com.uniq.uniqpos.util.networking.sendWebSocket
import com.uniq.uniqpos.util.outlet
import com.uniq.uniqpos.util.safe
import com.uniq.uniqpos.util.showMessage
import com.uniq.uniqpos.util.toast
import com.uniq.uniqpos.util.value
import com.uniq.uniqpos.view.global.BaseFragment
import com.uniq.uniqpos.view.global.DynamicDialog
import com.uniq.uniqpos.view.global.GlobalRecViewAdapter
import com.uniq.uniqpos.view.global.GlobalViewHolder
import com.uniq.uniqpos.view.simplepagelist.SimplePageListActivity
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.net.ConnectException
import java.net.NoRouteToHostException

// TODO: Rename parameter arguments, choose names that match
// the fragment initialization parameters, e.g. ARG_ITEM_NUMBER
private const val ARG_PARAM1 = "param1"
private const val ARG_PARAM2 = "param2"

/**
 * A simple [Fragment] subclass.
 * Use the [KitchenDisplaySettingFragment.newInstance] factory method to
 * create an instance of this fragment.
 */
class KitchenDisplaySettingFragment : BaseFragment<KitchenDisplayViewModel, FragmentKitchenDisplaySettingBinding>() {
    // TODO: Rename and change types of parameters
    private var param1: String? = null
    private var param2: String? = null

    private var dialogAddBinding: DialogAddKitchenDisplayBinding? = null
    private var dynamicDialogAdd: DynamicDialog? = null
    private var localIp: String? = null
    private val selectedCategories = ArrayList<KitchenDisplayCategory>()

    override val layoutRes: Int
        get() = R.layout.fragment_kitchen_display_setting
    override fun getViewModel(): Class<KitchenDisplayViewModel> = KitchenDisplayViewModel::class.java

    private val startForResultCategory = registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result: ActivityResult ->
        if (result.resultCode == RESULT_OK) {
            val data = result.data
            val simpleData: ArrayList<SimplePageListData>? = data?.getParcelableArrayListExtra("data")
//            context?.toast(Gson().toJson(simpleData))
            context?.toast("${simpleData?.size} categories selected")
            val names = simpleData?.map { it.title }
            dialogAddBinding?.spinCategory?.setText(names?.joinToString())
            selectedCategories.clear()
            simpleData?.forEach { selectedCategories.add(KitchenDisplayCategory(it.id, it.title)) }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.let {
            param1 = it.getString(ARG_PARAM1)
            param2 = it.getString(ARG_PARAM2)
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        binding.btnAdd.setOnClickListener {
            showAddInput()
        }

        binding.recviewKitchenDisplay.adapter = object : GlobalRecViewAdapter<KitchenDisplayEntity, ListItemKitchenDisplayBinding>(
            R.layout.list_item_kitchen_display,
            viewModel.loadKitchenDisplay(),
            this
        ){
            override fun onBindViewHolder(
                holder: GlobalViewHolder<ListItemKitchenDisplayBinding>,
                position: Int
            ) {
                super.onBindViewHolder(holder, position)
                holder.binding.txtRemove.setOnClickListener { removeKitchenDisplay(getItem(position)) }
                holder.binding.txtTestConnection.setOnClickListener { testConnection(getItem(position)) }
                holder.binding.viewPrinter.setOnClickListener { showAddInput(getItem(position)) }

                val popUpMore = PopupMenu(requireContext()  , holder.binding.imgMore)
                popUpMore.menu.add("Edit")
                popUpMore.setOnMenuItemClickListener {
                    showAddInput(getItem(position))
                    true
                }
                holder.binding.imgMore.setOnClickListener { popUpMore.show() }
            }
        }

        localIp = GetIpAddress()
        observerTask()
    }

    private fun testConnection(item: KitchenDisplayEntity?) {
        //val dataJson = "{\"type\":\"order\",\"order_id\":\"IA2024091159\",\"client_id\":4,\"display_nota\":\"IA2024091159\",\"customer_name\":\"Mark\",\"data_status\":\"active\",\"date_created\":1726042983022,\"dining_table\":\"Meja 04\",\"order_type\":\"Take Away\",\"order_details\":\"[{\\\"subcategory_id\\\":52,\\\"subcategory_name\\\":\\\"A La Carte\\\",\\\"products\\\":[{\\\"product\\\":{\\\"discount\\\":\\\"on\\\",\\\"name\\\":\\\"AProduct With Variant\\\",\\\"outlet_fkid\\\":29,\\\"price_buy\\\":0,\\\"price_sell\\\":700,\\\"product_category_fkid\\\":122,\\\"product_detail_id\\\":12834,\\\"product_id\\\":5715,\\\"product_subcategory_fkid\\\":52,\\\"product_type_fkid\\\":null,\\\"purchase_report_category_fkid\\\":15,\\\"stock_management\\\":1,\\\"voucher\\\":\\\"on\\\",\\\"stock\\\":\\\"available\\\"},\\\"tmpId\\\":1726042983030,\\\"price\\\":700,\\\"qty\\\":1,\\\"subTotal\\\":700,\\\"taxes\\\":[{\\\"category\\\":\\\"tax\\\",\\\"id\\\":110,\\\"name\\\":\\\"Tax PB\\\",\\\"total\\\":70,\\\"type\\\":\\\"percentage\\\",\\\"value\\\":10}],\\\"status\\\":\\\"active\\\"},{\\\"product\\\":{\\\"discount\\\":\\\"on\\\",\\\"name\\\":\\\"Jagung Bakar Original\\\",\\\"outlet_fkid\\\":29,\\\"price_buy\\\":0,\\\"price_sell\\\":8000,\\\"product_category_fkid\\\":26,\\\"product_detail_id\\\":8395,\\\"product_id\\\":2487,\\\"product_subcategory_fkid\\\":52,\\\"product_type_fkid\\\":null,\\\"purchase_report_category_fkid\\\":15,\\\"stock_management\\\":1,\\\"voucher\\\":\\\"on\\\",\\\"stock\\\":\\\"available\\\"},\\\"tmpId\\\":1726042983030,\\\"price\\\":8000,\\\"qty\\\":1,\\\"subTotal\\\":8000,\\\"taxes\\\":[{\\\"category\\\":\\\"tax\\\",\\\"id\\\":110,\\\"name\\\":\\\"Tax PB\\\",\\\"total\\\":800,\\\"type\\\":\\\"percentage\\\",\\\"value\\\":10}],\\\"status\\\":\\\"active\\\"},{\\\"product\\\":{\\\"discount\\\":\\\"on\\\",\\\"name\\\":\\\"Jagung Bakar Manis\\\",\\\"outlet_fkid\\\":29,\\\"price_buy\\\":50000,\\\"price_sell\\\":8000,\\\"product_category_fkid\\\":26,\\\"product_detail_id\\\":8393,\\\"product_id\\\":2483,\\\"product_subcategory_fkid\\\":52,\\\"product_type_fkid\\\":null,\\\"purchase_report_category_fkid\\\":15,\\\"stock_management\\\":1,\\\"voucher\\\":\\\"on\\\",\\\"stock\\\":\\\"available\\\"},\\\"tmpId\\\":1726042983030,\\\"price\\\":8000,\\\"qty\\\":1,\\\"subTotal\\\":8000,\\\"taxes\\\":[{\\\"category\\\":\\\"tax\\\",\\\"id\\\":110,\\\"name\\\":\\\"Tax PB\\\",\\\"total\\\":800,\\\"type\\\":\\\"percentage\\\",\\\"value\\\":10}],\\\"status\\\":\\\"active\\\"},{\\\"product\\\":{\\\"discount\\\":\\\"on\\\",\\\"name\\\":\\\"Jagung Bakar Original\\\",\\\"outlet_fkid\\\":29,\\\"price_buy\\\":0,\\\"price_sell\\\":8000,\\\"product_category_fkid\\\":26,\\\"product_detail_id\\\":8395,\\\"product_id\\\":2487,\\\"product_subcategory_fkid\\\":52,\\\"product_type_fkid\\\":null,\\\"purchase_report_category_fkid\\\":15,\\\"stock_management\\\":1,\\\"voucher\\\":\\\"on\\\",\\\"stock\\\":\\\"available\\\"},\\\"tmpId\\\":1726042983030,\\\"price\\\":8000,\\\"qty\\\":1,\\\"subTotal\\\":8000,\\\"taxes\\\":[{\\\"category\\\":\\\"tax\\\",\\\"id\\\":110,\\\"name\\\":\\\"Tax PB\\\",\\\"total\\\":800,\\\"type\\\":\\\"percentage\\\",\\\"value\\\":10}],\\\"status\\\":\\\"active\\\"}]}]\"}"
        val outlet = requireContext().outlet()

        val data = mapOf(
                "type" to "pairing",
                "name" to item?.name,
                "ip_address" to item?.address,
                "port" to Constant.KITCHEN_PORT,
                "category" to Gson().toJson(item?.categories),
                "device_id" to requireContext().getLocalDataString(SharedPref.DEVICE_ID, ""),
                "device_name" to getDeviceName(),
                "client_id" to 1,
                "outlet_id" to outlet?.outletId,
                "outlet_name" to outlet?.name,
        )
        val dataJson = Gson().toJson(data)

        lifecycleScope.launch {
            withContext(Dispatchers.IO){
                try {

//                    sendWebSocket(dataJson, item?.address.safe(), Constant.KITCHEN_PORT, "/ws", requireContext().assets)
//                    addKds(item?.address.safe(), item?.name.safe(), item?.categories ?: arrayListOf())
                    viewModel.addKds(item?.address.safe(), item?.name.safe(), item?.categories ?: arrayListOf())

                    context?.toast("Connected")
                } catch (e: Exception) {
                    context?.toast("Failed $e")
                }
            }
        }
    }

    private fun removeKitchenDisplay(data: KitchenDisplayEntity?, requireConfirmation: Boolean = true) {
        if(requireConfirmation){
            context?.showMessage("are you sure you want to remove '${data?.name?.uppercase()}'?",
                "CONFIRMATION",
                positiveAction = {_, _ ->
                    removeKitchenDisplay(data, false)
                })
            return
        }

        viewModel.removeKitchenDisplay(data)
    }

    private fun observerTask(){
        context?.setupLoadingDialog(this, this, viewModel.loadingDialog)
        context?.setupDialogMessage(this, viewModel.dialogMsg)
        context?.setupToastMessage(this, viewModel.toastMessage)

        viewModel.getCategories().observe(viewLifecycleOwner){
            viewModel.setCategories(it.data)
        }

        viewModel.loadKitchenDisplay().observe(viewLifecycleOwner){
            binding.txtDataCount.text = "${it.data?.size} kitchen display"
            viewModel.kitchenDisplays.clear()
            viewModel.kitchenDisplays.addAll(it.data ?: emptyList())
        }

        viewModel.state.observe(viewLifecycleOwner){ state ->
            when(state){
                is Success -> {
                    context?.toast("Success")
                    resetInput()
                }
            }
        }

    }

    //provide data for edit, otherwise considered as new
    private fun showAddInput(data: KitchenDisplayEntity? = null){
        dialogAddBinding = dialogAddBinding ?: DialogAddKitchenDisplayBinding.inflate(layoutInflater)
        dynamicDialogAdd = dynamicDialogAdd ?: DynamicDialog(dialogAddBinding!!.root, context, childFragmentManager)

        if(dialogAddBinding?.edtIp?.value().isNullOrBlank()) {
            dialogAddBinding?.edtIp?.setText(localIp?.substringBeforeLast("."))
        }

        dialogAddBinding?.txtLocalIp?.text = localIp
        dialogAddBinding?.txtWifiName?.text =  "${getWifiName(requireContext())} |"
        dialogAddBinding?.spinCategory?.setAdapter( ArrayAdapter(
            requireContext(),
            android.R.layout.simple_dropdown_item_1line,
            ArrayList<String>()
        )
        )

        dialogAddBinding?.spinCategory?.setOnClickListener {
            val data = ArrayList<SimplePageListData>()
            viewModel.categories.forEach {
                data.add(
                    SimplePageListData(
                        it.name.safe(),
                        it.productCategoryId
                    )
                )
            }
            val intent = SimplePageListActivity.newInstance(requireContext(), "Pick Category", data, isMultiple = true, showAdd = false)
//            startActivityForResult(intent, 1)
            startForResultCategory.launch(intent)
        }

        dialogAddBinding?.btnSave?.setOnButtonClickListener {
            saveKitchenDisplay(data?.settingKitchenDisplayId)
        }

        data?.let { update ->
            dialogAddBinding?.edtIp?.setText(update.address)
            dialogAddBinding?.edtName?.setText(update.name)
            dialogAddBinding?.spinCategory?.setText(update.categories.map { it.categoryName }.joinToString())
        }

        dynamicDialogAdd?.show()
    }

    private fun saveKitchenDisplay(id: Int? = null){
        if (!Utils.isValidField(dialogAddBinding?.edtIp, dialogAddBinding?.edtName, dialogAddBinding?.spinCategory)){
            return
        }

        val ip = dialogAddBinding?.edtIp?.text.toString()
        if(!isValidIpAddress(ip)){
            context?.toast("Invalid IP address Format")
            dialogAddBinding?.edtIp?.error = "invalid ip address format"
            return
        }

        dialogAddBinding?.btnSave?.startLoading()
        lifecycleScope.launch {
            withContext(Dispatchers.IO){
                try {
//                    addKds(ip, dialogAddBinding?.edtName?.value().safe(), selectedCategories)
                    viewModel.addKds(ip, dialogAddBinding?.edtName?.value().safe(), selectedCategories)

                    context?.toast("Connection Success")
                    withContext(Dispatchers.Main){
                        dynamicDialogAdd?.dismiss()
                        id?.let{ kitchenId ->
                            viewModel.updateKitchenDisplay(
                                kitchenId,
                                dialogAddBinding?.edtIp?.value().safe(),
                                dialogAddBinding?.edtName?.value().safe(),
                                selectedCategories
                            )
                        } ?: run {
                            viewModel.addKitchenDisplay(
                                dialogAddBinding?.edtIp?.value().safe(),
                                dialogAddBinding?.edtName?.value().safe(),
                                selectedCategories
                            )
                        }
                    }
                } catch (e: NoRouteToHostException){
                    withContext(Dispatchers.Main){
                        context?.showMessage("can not establish connection, make sure you are on the same connection, or try to restart the router")
                    }
                } catch (e: ConnectException){
                    withContext(Dispatchers.Main){
                        context?.showMessage("client refused to connect, make sure app is running properly")
                    }
                } catch (e: Exception) {
                    context?.toast("Failed $e")
                }finally {
                    withContext(Dispatchers.Main){
                        dialogAddBinding?.btnSave?.stopLoading()
                    }
                }
            }
        }
    }

    private suspend fun addKds(ip: String, name: String, categories: ArrayList<KitchenDisplayCategory>){
        try {
            val data = mapOf(
                "type" to "pairing",
                "name" to name,
                "ip_address" to ip,
                "port" to Constant.KITCHEN_PORT,
                "category" to Gson().toJson(categories),
                "device_id" to requireContext().getLocalDataString(SharedPref.DEVICE_ID, ""),
                "device_name" to getDeviceName(),
                "client_id" to 1,
                "outlet_id" to requireContext().outlet()?.outletId,
                "outlet_name" to requireContext().outlet()?.name,
            )
            val dataJson = Gson().toJson(data)
            sendWebSocket(dataJson, ip, Constant.KITCHEN_PORT, "/ws", requireContext().assets)
        } catch (e: Exception) {
            toast("adding kds failed, $e")
            throw e
        }
    }

    private fun sendSocketData(){

    }

    private fun resetInput(){
        dialogAddBinding?.edtIp?.setText("")
        dialogAddBinding?.edtName?.setText("")
        dialogAddBinding?.spinCategory?.setText("")
        selectedCategories.clear()
    }

    companion object {
        /**
         * Use this factory method to create a new instance of
         * this fragment using the provided parameters.
         *
         * @param param1 Parameter 1.
         * @param param2 Parameter 2.
         * @return A new instance of fragment KitchenDisplaySettingFragment.
         */
        // TODO: Rename and change types and number of parameters
        @JvmStatic
        fun newInstance(param1: String, param2: String) =
            KitchenDisplaySettingFragment().apply {
                arguments = Bundle().apply {
                    putString(ARG_PARAM1, param1)
                    putString(ARG_PARAM2, param2)
                }
            }
    }
}