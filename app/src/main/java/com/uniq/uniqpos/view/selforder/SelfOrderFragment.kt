package com.uniq.uniqpos.view.selforder

import android.app.Activity
import android.app.SearchManager
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.Menu
import android.view.MenuInflater
import android.view.View
import android.widget.SearchView
import androidx.appcompat.app.AlertDialog
import com.uniq.uniqpos.R
import com.uniq.uniqpos.data.remote.model.SelfOrder
import com.uniq.uniqpos.databinding.FragmentSelfOrderBinding
import com.uniq.uniqpos.databinding.ListItemSelfOrderBinding
import com.uniq.uniqpos.util.lifecycle.Loading
import com.uniq.uniqpos.util.lifecycle.setupToast
import com.uniq.uniqpos.util.safe
import com.uniq.uniqpos.view.global.BaseFragment
import com.uniq.uniqpos.view.global.GlobalAdapter
import com.uniq.uniqpos.view.global.GlobalViewHolder

class SelfOrderFragment : BaseFragment<SelfOrderViewModel, FragmentSelfOrderBinding>() {

    //init search
    private lateinit var searchView: SearchView
    private var searchListener: SearchView.OnQueryTextListener? = null

    private val orderListTmp = ArrayList<SelfOrder>()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setHasOptionsMenu(true)

        searchListener = object : SearchView.OnQueryTextListener {
            override fun onQueryTextSubmit(query: String?) = false
            override fun onQueryTextChange(q: String?): Boolean {
                viewModel.search(q)
                return true
            }
        }

    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        binding.recviewSelfOrder.adapter =
            object : GlobalAdapter<ListItemSelfOrderBinding>(R.layout.list_item_self_order, orderListTmp) {
                override fun onBindViewHolder(holder: GlobalViewHolder<ListItemSelfOrderBinding>, position: Int) {
                    super.onBindViewHolder(holder, position)
                    holder.itemView.setOnClickListener { showItem(orderListTmp[position]) }
                }
            }

        binding.swipeRefresh.setOnRefreshListener { viewModel.fetchSelfOrder(outlet.outletId.safe()) }

        viewModel.fetchSelfOrder(outlet.outletId.safe())
        viewModel.selfOrder.observe(viewLifecycleOwner) {
            if (it != null) {
                orderListTmp.clear()
                orderListTmp.addAll(it)
                binding.recviewSelfOrder.adapter?.notifyDataSetChanged()
            }
        }

        viewModel.state.observe(viewLifecycleOwner) { state ->
            when (state) {
                is Loading -> binding.swipeRefresh.isRefreshing = state.isLoading
                else -> {
                }
            }
        }

        context?.setupToast(viewLifecycleOwner, viewModel.toastMsg)
    }

    private fun showItem(selfOrder: SelfOrder) {
        val items = viewModel.generateItemDetail(selfOrder)
        AlertDialog.Builder(requireContext())
            .setItems(items.toTypedArray(), null)
//            .setMessage(msg)
            .setNegativeButton("Close", null)
            .setPositiveButton("NEXT") { _, _ ->
                val data = Intent()
                data.putExtra("code", selfOrder.orderCode)
                requireActivity().setResult(Activity.RESULT_OK, data)
                requireActivity().finish()
            }
            .setTitle("ITEM ORDER")
            .setCancelable(false)
            .show()
    }


    override fun onCreateOptionsMenu(menu: Menu, inflater: MenuInflater) {
        inflater.inflate(R.menu.menu_search, menu)
        val searchManager =
            requireContext().getSystemService(Context.SEARCH_SERVICE) as SearchManager
        searchView = menu.findItem(R.id.action_search)?.actionView as SearchView
//        searchView.setSearchableInfo(searchManager.getSearchableInfo(componentName))
        searchListener?.let { searchView.setOnQueryTextListener(it) }
        super.onCreateOptionsMenu(menu, inflater)
    }

    override val layoutRes = R.layout.fragment_self_order
    override fun getViewModel() = SelfOrderViewModel::class.java
}