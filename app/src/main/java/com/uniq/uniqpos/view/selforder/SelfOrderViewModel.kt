package com.uniq.uniqpos.view.selforder

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.google.gson.Gson
import com.uniq.uniqpos.data.local.entity.ProductEntity
import com.uniq.uniqpos.data.remote.model.SelfOrder
import com.uniq.uniqpos.data.remote.model.SelfOrderItem
import com.uniq.uniqpos.data.remote.repository.SelfOrderRepository
import com.uniq.uniqpos.util.lifecycle.BaseViewModel
import com.uniq.uniqpos.util.lifecycle.Event
import com.uniq.uniqpos.util.lifecycle.Failed
import com.uniq.uniqpos.util.lifecycle.Loading
import com.uniq.uniqpos.util.readableError
import com.uniq.uniqpos.util.safe
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import retrofit2.await
import timber.log.Timber
import javax.inject.Inject

class SelfOrderViewModel @Inject constructor(private val repo: SelfOrderRepository) :
    BaseViewModel() {

    private val productList = ArrayList<ProductEntity>()
    private val orderList = ArrayList<SelfOrder>()

    private val _selfOrder = MutableLiveData<List<SelfOrder>>()
    val selfOrder: LiveData<List<SelfOrder>> = _selfOrder

    private fun loadProduct() {
        viewModelScope.launch(Dispatchers.IO) {
            productList.clear()
            productList.addAll(repo.getProducts())
            initSelfOrderItems()
        }
    }

    fun fetchSelfOrder(outletId: Int) {
        if (productList.isEmpty()) loadProduct()

        viewModelScope.launch {
            _state.postValue(Loading(true))
            try {
                val result = repo.getSelfOrder(outletId).await()
                if (result.status.safe()) {
                    result.data?.let {
                        _selfOrder.postValue(it)
                        orderList.clear()
                        orderList.addAll(it)
                        initSelfOrderItems()
                    }
                } else {
                    _state.postValue(Failed(java.lang.Exception(result.message)))
                    _toastMsg.postValue(Event(result.message.safe()))
                }
            } catch (e: Exception) {
                Timber.i("loading self order err: $e")
                _toastMsg.postValue(Event(e.readableError()))
                _state.postValue(Failed(e))
            }
            _state.postValue(Loading(false))
        }
    }

    private fun initSelfOrderItems() {
        if (productList.isEmpty()) return

        orderList.filter { !it.orderItem.isNullOrBlank() }.forEach { selfOrder ->
            val items = Gson().fromJson(selfOrder.orderItem, SelfOrderItem::class.java)
            var itemNames = arrayListOf<String>()
            items.orderList?.forEach { list ->
                productList.firstOrNull { it.productDetailId == list.productDetailId }?.let {
                    itemNames.add(it.name.safe())
                }
            }
            selfOrder.itemNames = itemNames.joinToString()
        }

        _selfOrder.postValue(orderList)
    }

    fun search(q: String?) {
        if (q.isNullOrBlank()) {
            _selfOrder.postValue(orderList)
            return
        }

        fun nameMatch(data: SelfOrder) =
            data.itemNames.safe().lowercase().contains(q.safe().lowercase())

        fun codeMatch(data: SelfOrder) = data.orderCode.safe().contains(q.safe().lowercase())

        fun customerMatch(data: SelfOrder) = data.customerName.safe().lowercase().contains(q.safe().lowercase())

        _selfOrder.postValue(orderList.filter { nameMatch(it) || codeMatch(it) || customerMatch(it) })
    }

    fun generateItemDetail(selfOrder: SelfOrder): List<String> {
        var result = arrayListOf<String>()
        val items = Gson().fromJson(selfOrder.orderItem, SelfOrderItem::class.java)
        items.orderList?.forEach { item ->
            productList.firstOrNull { it.productDetailId == item.productDetailId }?.let { product ->
                result.add("${item.qty}x   ${product.name}")
            }
        }
        return result
    }

}