package com.uniq.uniqpos.view.pendingprint


import android.content.DialogInterface
import android.content.pm.ActivityInfo
import android.os.Bundle
import android.text.TextUtils
import android.view.Menu
import android.view.MenuItem
import com.bugsnag.android.Bugsnag
import com.google.gson.Gson
import com.gu.toolargetool.TooLargeTool
import com.uniq.uniqpos.R
import com.uniq.uniqpos.data.local.entity.PendingPrintEntity
import com.uniq.uniqpos.databinding.ActivityPendingPrintBinding
import com.uniq.uniqpos.databinding.ListItemPendingPrintBinding
import com.uniq.uniqpos.util.*
import com.uniq.uniqpos.view.global.BaseActivity
import com.uniq.uniqpos.view.global.GlobalAdapter
import com.uniq.uniqpos.view.global.GlobalViewHolder
import timber.log.Timber
import java.util.*
import kotlin.collections.ArrayList

class PendingPrintActivity : BaseActivity<PendingPrintViewModel, ActivityPendingPrintBinding>() {

    private val pendingPrintList = ArrayList<PendingPrintEntity>()

    override fun getLayoutRes() = R.layout.activity_pending_print
    override fun getViewModel() = PendingPrintViewModel::class.java

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        if (resources.getBoolean(R.bool.landscape_only)) {
            requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_SENSOR_LANDSCAPE
        }

        savedInstanceState?.let { state ->
            val report =  TooLargeTool.bundleBreakdown(state)
            Timber.d("bundleBreakdown: $report")
        }
        
        binding.recViewPendingprint.adapter = object : GlobalAdapter<ListItemPendingPrintBinding>(R.layout.list_item_pending_print, pendingPrintList, binding.incLayNoData.root) {
            override fun onBindViewHolder(holder: GlobalViewHolder<ListItemPendingPrintBinding>, position: Int) {
                super.onBindViewHolder(holder, position)
                holder.binding.btnReprint.setOnClickListener { reprint(position) }
                holder.binding.btnDelete.setOnClickListener { deletePrinter(pendingPrintList[position]) }
                holder.binding.txtPrint.setOnClickListener {
//                    showAlert(pendingPrintList[position].dataPrintDisplay)
                    PendingPrintDetailDialog
                            .newInstance(pendingPrintList[position].dataPrintDisplay.safe())
                            .setPrintListener(object : PendingPrintDetailDialog.PendingPrintListener {
                                override fun print() {
                                    reprint(position)
                                }
                            })
                            .show(supportFragmentManager, "pending-print")
                }
            }
        }

        viewModel.loadPendingPrint()
        viewModel.pendingPrintLive.observe(this, { items ->
            pendingPrintList.clear()
            items.let { pendingPrintList.addAll(it) }
//            pendingPrintList.forEach {
//                it.dataString = it.dataString?.replace(Constant.PRINTER_CODE_CUT, "")?.replace(Constant.PRINTER_CODE_LOGO, "")
//            }
            Timber.i("Pending Print Size : ${pendingPrintList.size} | Printer Names : ${TextUtils.join(", ", pendingPrintList.map { it.printerName })}")
            
            binding.incLayNoData.root.visibility = pendingPrintList.isEmpty().visibility()
            binding.recViewPendingprint.adapter?.notifyDataSetChanged()
        })
    }

    private fun deletePrinter(data: PendingPrintEntity) {
        showMessage(getString(R.string.are_you_sure), "Confirmation", DialogInterface.OnClickListener { _, _ ->
            viewModel.deletePendingPrint(data)
        })
    }

    private fun reprint(index: Int = 0, isLoop: Boolean = false) {
        Bugsnag.leaveBreadcrumb("reprint")
        showDialog(true)

        val data = if (isLoop) pendingPrintList else arrayListOf(pendingPrintList[index])
        data.forEach { it.isOpenCashDrawer = false }
        Timber.i(">>> Reprint pending print : \n${Gson().toJson(data)}")
        val reprintWatermark = "# REPRINT: ${Date().dateTimeFormat("HH:mm")} #"
        data.forEach {
            if(it.dataString?.contains(Constant.PRINTER_CODE_CUT).safe()){
                it.dataString = it.dataString?.replace(Constant.PRINTER_CODE_CUT, "$reprintWatermark \n\n ${Constant.PRINTER_CODE_CUT}")
            }else {
                it.dataString = it.dataString?.replace(" BILL INFORMATION ", reprintWatermark)
            }
        }

        managePrintWifi(data
        ) { isConnected, message ->
            if (isConnected) data.forEach { viewModel.deletePendingPrint(it) }
            viewModel.loadPendingPrint()
            showDialog(false)
        }
    }

    override fun onCreateOptionsMenu(menu: Menu): Boolean {
        menuInflater.inflate(R.menu.menu_pending_print, menu)
        return super.onCreateOptionsMenu(menu)
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        when {
            item?.itemId == R.id.action_print_all -> reprint(0, true)
            item?.itemId == R.id.action_delete_all -> deleteAllPendingPrint()
            else -> finish()
        }
        return super.onOptionsItemSelected(item)
    }

    private fun deleteAllPendingPrint() {
        showMessage(getString(R.string.are_you_sure), "Confirmation", DialogInterface.OnClickListener { _, _ ->
            pendingPrintList.forEach {
                viewModel.deletePendingPrint(it)
            }
        })
    }
}
