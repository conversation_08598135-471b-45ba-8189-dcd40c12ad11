package com.uniq.uniqpos.view.runoutstock

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.uniq.uniqpos.data.local.entity.ProductEntity
import com.uniq.uniqpos.data.remote.repository.ProductRepository
import com.uniq.uniqpos.util.lifecycle.BaseViewModel
import com.uniq.uniqpos.util.lifecycle.Loading
import com.uniq.uniqpos.util.lifecycle.ViewModelState
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

/**
 * Created by annasblackhat on 15/03/18.
 */
class RunOutOfStockViewModel @Inject constructor(private val productRepository: ProductRepository) : BaseViewModel() {

    private val productList = ArrayList<ProductEntity>()
    val productsAvailable = java.util.ArrayList<ProductEntity>()
    val productsUnAvailable = java.util.ArrayList<ProductEntity>()

    fun updateProduct(productEntity: ProductEntity) {
        viewModelScope.launch {
            productRepository.updateProduct(productEntity)
        }
    }

    fun getProduct() = productRepository.getProduct()

    fun loadAllProduct() {
        viewModelScope.launch(Dispatchers.IO) {
            _state.postValue(Loading(true))
            val products = productRepository.getProduct()
            val variants = productRepository.getAllVariant()

            products.filter { it.variantFkid != null }.let { productWithVariant ->
                val group = productWithVariant.groupBy { it.productId }
                for ((id, prodList) in group) {
                    prodList.forEachIndexed { pIndex, productEntity ->
                        variants.filter { it.productFkid.toLong() == id }.forEachIndexed { vIndex, productVariantEntity ->
                            if (pIndex == vIndex) {
                                productEntity.name = "${productEntity.name} (${productVariantEntity.variantName})"
                            }
                        }
                    }
                }
            }

            productList.clear()
            productList.addAll(products)

            //divide product
            productsAvailable.clear()
            productsUnAvailable.clear()
            products.forEach { product ->
                if (product.stock == "available") {
                    productsAvailable.add(product)
                } else {
                    productsUnAvailable.add(product)
                }
            }

            _state.postValue(Loading(false))
        }
    }

    fun performSearch(query: String?) {
        viewModelScope.launch {
            _state.value = Loading(true)
            productsAvailable.clear()
            productsUnAvailable.clear()

            val filtered = java.util.ArrayList<ProductEntity>()
            query?.takeIf { it.isNotEmpty() }?.let { search ->
                filtered.addAll(productList.filter { it.name?.lowercase()?.contains(search.lowercase()) == true })
            } ?: kotlin.run {
                filtered.addAll(productList)
            }

            filtered.forEach { product ->
                if (product.stock == "available") {
                    productsAvailable.add(product)
                } else {
                    productsUnAvailable.add(product)
                }
            }

            _state.value = Loading(false)
        }
    }

    fun updateStock(productDetailId: Int, stockStatus: String) {
        viewModelScope.launch(Dispatchers.IO) {
            //get the latest data product from db, then update
            productRepository.getProductById(productDetailId)?.let { product ->
                product.stock = stockStatus
                productRepository.updateProduct(product)
            }
        }
    }

}