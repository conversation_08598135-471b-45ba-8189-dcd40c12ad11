package com.uniq.uniqpos.view.setting.printer

import android.Manifest
import android.bluetooth.BluetoothAdapter
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Build
import android.os.Bundle
import android.text.InputType
import android.view.Menu
import android.view.MenuItem
import android.view.View
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.lifecycle.lifecycleScope
import com.uniq.uniqpos.R
import com.uniq.uniqpos.data.local.entity.PrinterEntity
import com.uniq.uniqpos.data.local.sharedpref.SharedPref
import com.uniq.uniqpos.data.local.sharedpref.getJson
import com.uniq.uniqpos.data.remote.model.Outlet
import com.uniq.uniqpos.databinding.ActivityAddPrinterBinding
import com.uniq.uniqpos.databinding.ListItemPrinterBinding
import com.uniq.uniqpos.model.Admin
import com.uniq.uniqpos.model.BottomDialogModel
import com.uniq.uniqpos.util.Constant
import com.uniq.uniqpos.util.Level
import com.uniq.uniqpos.util.NetworkScanner
import com.uniq.uniqpos.util.printer.PrinterBluetooth
import com.uniq.uniqpos.util.printer.PrinterUSB
import com.uniq.uniqpos.util.printer.PrinterWifi
import com.uniq.uniqpos.util.safe
import com.uniq.uniqpos.util.setVisible
import com.uniq.uniqpos.util.showMessage
import com.uniq.uniqpos.util.toast
import com.uniq.uniqpos.view.global.BaseActivity
import com.uniq.uniqpos.view.global.BottomDialogInput
import com.uniq.uniqpos.view.global.GlobalAdapter
import com.uniq.uniqpos.view.global.GlobalViewHolder
import com.uniq.uniqpos.view.setting.PrinterSettingViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withTimeoutOrNull
import timber.log.Timber


class AddPrinterActivity : BaseActivity<PrinterSettingViewModel, ActivityAddPrinterBinding>() {

    private var bAdapter: BluetoothAdapter? = null
    private lateinit var printerType: String
    private lateinit var outlet: Outlet
    private lateinit var admin: Admin
    private lateinit var printerUSB: PrinterUSB
    private var networkScanner: NetworkScanner? = null
    private val listedPrinter = ArrayList<String>()
    private val STATUS_SUPPORTED = "supported"
    private val STATUS_TESTED = "tested"
    private val STATUS_UNKNOWN = "unknown"

    private val RC_PERMISSION_BT = 1

    override fun getLayoutRes() = R.layout.activity_add_printer
    override fun getViewModel() = PrinterSettingViewModel::class.java

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)

        printerUSB = PrinterUSB(this)

        printerType = intent.getStringExtra("type").safe()
        listedPrinter.addAll(intent.getStringArrayListExtra("printers")!!)
        outlet = getJson(SharedPref.OUTLET_DATA, Outlet::class.java) ?: Outlet()
        admin = getJson(SharedPref.ADMIN_DATA, Admin::class.java) ?: Admin()

        binding.txtPairing.setOnClickListener { startActivity(Intent(android.provider.Settings.ACTION_BLUETOOTH_SETTINGS)) }
        binding.imgClose.setOnClickListener { binding.layoutWarning.visibility = View.GONE }
        binding.layoutWarning.setVisible(printerType == Constant.PRINTER_TYPE_BT)
        binding.btnAddManual.setVisible(printerType == Constant.PRINTER_TYPE_WIFI)

        binding.recviewPrinter.adapter = object : GlobalAdapter<ListItemPrinterBinding>(
            R.layout.list_item_printer,
            viewModel.printerList
        ) {
            override fun onBindViewHolder(
                holder: GlobalViewHolder<ListItemPrinterBinding>,
                position: Int
            ) {
                super.onBindViewHolder(holder, position)
                holder.binding.txtSetting.text = "ADD"
                holder.binding.txtPrintTest.setOnClickListener { testPrint(viewModel.printerList[position]) }
                holder.binding.txtSetting.setOnClickListener {
                    if (listedPrinter.contains(viewModel.printerList[position].address)) {
                        showMessage(getString(R.string.cannot_add_same_printer))
                    } else {
                        addPrinter(viewModel.printerList[position])
                    }
                }
            }
        }

        when (printerType) {
            Constant.PRINTER_TYPE_BT -> {
                bAdapter = BluetoothAdapter.getDefaultAdapter()
                searchBluetooth()
            }
            Constant.PRINTER_TYPE_WIFI -> {
                searching(true)

                binding.layoutProgress.visibility = View.VISIBLE
                networkScanner = NetworkScanner()
                networkScanner?.scan(this, object : NetworkScanner.NetworkScanListener {
                    override fun onJobFinish() {
                        binding.progressBar.visibility = View.GONE
                        binding.txtInfo.text = "${viewModel.printerList.size} printers found!"
                    }

                    override fun onDeviceDiscovery(data: NetworkScanner.ScannerData) {
                        if (!viewModel.printerList.any { it.address == data.ipAddress }) {
                            viewModel.lookupVendor(data)
                        }
                    }
                })
            }
            Constant.PRINTER_TYPE_USB -> searchUsbPrinter()
        }

        binding.btnAddManual.setOnClickListener { showInputManual() }

        viewModel.taskRefreshPrinter.observe(this) {
            searching(false)
            binding.recviewPrinter.adapter?.notifyDataSetChanged()
        }

        Timber.i(">> Opening AddPrinter page")
        //request enable bluetooth
//        val intent = Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE)
//        startActivityForResult(intent, 1000)
    }

    private fun addPrinter(printer: PrinterEntity) {
        viewModel.addPrinter(printer.copy(adminFkid = admin.adminId, outletFkid = outlet.outletId))
        listedPrinter.add(printer.address)
//        toast(getString(R.string.printer_added), level = Level.INFO)
        toast(getString(R.string.success), level = Level.INFO)
        PrinterDetailBottomDialog(printer).show(supportFragmentManager, "printer_detail")

//        showMessage(
//            getString(R.string.printer_added, "${printer.name} (${printer.address})"),
//            getString(R.string.success),
//            { _, _ ->
//                val intent = Intent(this, PrinterDetailActivity::class.java)
//                intent.putExtra("data", printer)
//                startActivity(intent)
//            },
//            null,
//            getString(R.string.open_setting),
//            getString(R.string.close)
//        )
    }

    private fun testPrint(printer: PrinterEntity) {
        when (printerType) {
            Constant.PRINTER_TYPE_BT -> {
                showDialog(true, "Connecting...")
                val printerBT = PrinterBluetooth(this)
                lifecycleScope.launch {
                    withTimeoutOrNull(5000L) {
                        printerBT.printTest(printer.address) { response ->
                            if (response.status) toast("Print test success")
                            else toast("Print test failed ${response.message}")
                            printerBT.disconnect()
                        }
                    }
                    showDialog(false)
                }
            }
            Constant.PRINTER_TYPE_WIFI -> {
                showDialog(true, "Connecting...")
                lifecycleScope.launch(Dispatchers.Main) {
//                    printerManager.printTestWifi(this@AddPrinterActivity, printer.address)
                    PrinterWifi(this@AddPrinterActivity).printTestWifi(
                        this@AddPrinterActivity,
                        printer.address
                    ) {
                        toast(
                            it.message
                                ?: "tidak dapat terhubung ke printer",
                            level = if (it.status) Level.INFO else Level.ERROR
                        )
                        showDialog(false)
                    }
                }
            }
            Constant.PRINTER_TYPE_USB -> {
                printerUSB.printTest(printer.address, true) {}
            }
        }
    }

    private fun searchUsbPrinter() {
        searching(true)
        toast("Searching USB printer...")
        printerUSB.getUsbDevices().forEach { usbDevice ->
            var deviceName = "${usbDevice.vendorId} - $${usbDevice.deviceName}"
            if (Build.VERSION.SDK_INT >= 21) {
                deviceName =
                    "${(usbDevice.manufacturerName ?: "").trim()} ${usbDevice.productName?.trim()}"
            }

            if (deviceName.trim() == "null" || deviceName.trim().isEmpty()) {
                deviceName = "USB Printer"
            }

            viewModel.printerList.add(
                PrinterEntity(
                    PrinterUSB.getAddressFormat(usbDevice),
                    deviceName,
                    printerType
                )
            )
        }
        binding.recviewPrinter.adapter?.notifyDataSetChanged()
        searching(false)
    }

    private fun searchBluetooth() {
        searching(true)

        if (bAdapter == null) {
            toast("No Bluetooth adapter found!")
            finish()
        }

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S && !hasPermission(Manifest.permission.BLUETOOTH_CONNECT)) {
            ActivityCompat.requestPermissions(
                this,
                arrayOf(Manifest.permission.BLUETOOTH_CONNECT),
                RC_PERMISSION_BT
            )
            toast("bluetooth permission needed")
            return
        }

        bAdapter?.apply {
            if (!isEnabled) enable()

            val pairedDevice = bondedDevices
            viewModel.printerList.clear()
            var info = ">>> Printer Bluetooth Paired List : \n"
            pairedDevice.forEach { d ->
                viewModel.printerList.add(PrinterEntity(d.address, d.name, printerType, device = d))
                info += "${d.name} - ${d.address} \n"
            }
            Timber.i(info)
            if (pairedDevice.isNotEmpty()) {
                binding.recviewPrinter.adapter?.notifyDataSetChanged()
            }
            searching(false)
        }

    }

    private fun searching(search: Boolean) {
        binding.iconPrinter.visibility = if (search) {
            binding.ripple.startRippleAnimation()
            View.VISIBLE
        } else {
            binding.ripple.stopRippleAnimation()
            View.GONE
        }
    }

    override fun onCreateOptionsMenu(menu: Menu): Boolean {
        menuInflater.inflate(R.menu.menu_add_printer, menu)
        return super.onCreateOptionsMenu(menu)
    }

    override fun onOptionsItemSelected(item: MenuItem) = when (item.itemId) {
        R.id.action_refresh -> {
            refreshList()
            true
        }
        R.id.action_input_manual -> {
            showInputManual()
            true
        }
        else -> {
            finish()
            true
        }
    }

    private fun refreshList() {
        if (printerType == Constant.PRINTER_TYPE_BT) searchBluetooth()
//        else if (printerType == Constant.PRINTER_TYPE_WIFI) restartDiscovery()
    }

    private fun showInputManual() {
        val bottomDialog = BottomDialogInput()
        bottomDialog.setOnButtonClick { result ->
            if (result.isBlank()) {
                toast("Value is not allowed!")
            } else {
                bottomDialog.dismiss()
                addPrinter(PrinterEntity(result, "WiFi Printer", Constant.PRINTER_TYPE_WIFI))
            }
        }
        bottomDialog.setModel(
            BottomDialogModel(
                "ADD NEW PRINTER",
                "SAVE",
                "Wi-FI Printer",
                hint = "IP Address",
                inputType = InputType.TYPE_CLASS_PHONE
            )
        )
        bottomDialog.show(supportFragmentManager, "add-new")
    }

    private fun hasPermission(permission: String): Boolean {
        return ContextCompat.checkSelfPermission(
            this,
            permission
        ) == PackageManager.PERMISSION_GRANTED
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        if (requestCode == RC_PERMISSION_BT) {
            if (grantResults.any { it != PackageManager.PERMISSION_GRANTED }) {
                toast("permission to bluetooh is required to connect with printer")
                finish()
            } else {
                searchBluetooth()
            }
        }
    }

    public override fun onDestroy() {
        networkScanner?.stop()
        if (printerType == Constant.PRINTER_TYPE_WIFI) {
            binding.ripple.stopRippleAnimation()
        }
        super.onDestroy()
    }
}
