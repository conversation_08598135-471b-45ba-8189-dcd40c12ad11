package com.uniq.uniqpos.view.runoutstock

import android.app.SearchManager
import android.content.Context
import android.content.pm.ActivityInfo
import android.os.Bundle
import android.view.Menu
import android.view.MenuItem
import android.view.View
import android.widget.SearchView
import com.google.firebase.analytics.FirebaseAnalytics
import com.uniq.uniqpos.R
import com.uniq.uniqpos.data.remote.model.Outlet
import com.uniq.uniqpos.databinding.ActivityRunOutOfStockBinding
import com.uniq.uniqpos.databinding.ListItemMenuCardBinding
import com.uniq.uniqpos.util.Constant
import com.uniq.uniqpos.util.bundleOf
import com.uniq.uniqpos.util.lifecycle.Loading
import com.uniq.uniqpos.util.outlet
import com.uniq.uniqpos.util.view.RecyclerItemClickListener
import com.uniq.uniqpos.view.global.BaseActivity
import com.uniq.uniqpos.view.global.GlobalAdapter

class RunOutOfStockActivity : BaseActivity<RunOutOfStockViewModel, ActivityRunOutOfStockBinding>(),
    SearchView.OnQueryTextListener {

    private var outlet: Outlet? = null

    //    private val productsOriginal = ArrayList<ProductEntity>()
//    private val productsAvailable = ArrayList<ProductEntity>()
//    private val productsUnAvailable = ArrayList<ProductEntity>()
    private lateinit var searchView: SearchView

    override fun getLayoutRes() = R.layout.activity_run_out_of_stock
    override fun getViewModel() = RunOutOfStockViewModel::class.java

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
//        if (resources.getBoolean(R.bool.landscape_only)) {
//            requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_SENSOR_LANDSCAPE
//        }
        //force to landscape for all kind of devices
        requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_SENSOR_LANDSCAPE

        outlet = outlet()
        viewModel.loadAllProduct()

        //log
        FirebaseAnalytics.getInstance(this)
            .logEvent(
                "app_features",
                bundleOf(
                    "Outlet" to "ROS:${outlet?.outletId}:${outlet?.name}",
                    "Feature" to "Run Out Stock"
                )
            )
    }

    override fun initView() {
        super.initView()
        val productsAvailableAdapter: GlobalAdapter<ListItemMenuCardBinding> = object :
            GlobalAdapter<ListItemMenuCardBinding>(
                R.layout.list_item_menu_card,
                viewModel.productsAvailable
            ) {}
        binding.recviewAvailable.adapter = productsAvailableAdapter

        val productsNotAvailableAdapter = object : GlobalAdapter<ListItemMenuCardBinding>(
            R.layout.list_item_menu_card,
            viewModel.productsUnAvailable
        ) {}
        binding.recviewUnavailable.adapter = productsNotAvailableAdapter

        binding.recviewAvailable.addOnItemTouchListener(RecyclerItemClickListener(this) { _, position ->
            val product = viewModel.productsAvailable[position]
            product.stock = Constant.STOCK_UNAVAILABLE
//            product.sync = false
//            viewModel.updateProduct(product)
            viewModel.updateStock(product.productDetailId, Constant.STOCK_UNAVAILABLE)
            viewModel.productsAvailable.removeAt(position)
            viewModel.productsUnAvailable.add(product)
            binding.recviewAvailable.adapter?.notifyItemRemoved(position)
            binding.recviewUnavailable.adapter?.notifyItemInserted(viewModel.productsUnAvailable.size - 1)
            binding.recviewUnavailable.scrollToPosition(viewModel.productsUnAvailable.size - 1)
            countItems()
        })

        binding.recviewUnavailable.addOnItemTouchListener(RecyclerItemClickListener(this) { _, position ->
            val product = viewModel.productsUnAvailable[position]
            product.stock = Constant.STOCK_AVAILABLE
//            viewModel.updateProduct(product)
            viewModel.updateStock(product.productDetailId, Constant.STOCK_AVAILABLE)
            viewModel.productsUnAvailable.removeAt(position)
            viewModel.productsAvailable.add(product)
            binding.recviewUnavailable.adapter?.notifyItemRemoved(position)
            binding.recviewAvailable.adapter?.notifyItemInserted(viewModel.productsAvailable.size - 1)
            binding.recviewAvailable.scrollToPosition(viewModel.productsAvailable.size - 1)
            countItems()
        })
    }

    override fun observeData() {
        viewModel.state.observe(this, androidx.lifecycle.Observer { state ->
            when (state) {
                is Loading -> {
                    if (state.isLoading) {
                        binding.progressAvailable.visibility = View.VISIBLE
                        binding.progressRunout.visibility = View.VISIBLE
                    } else {
                        binding.progressAvailable.visibility = View.GONE
                        binding.progressRunout.visibility = View.GONE

                        binding.recviewAvailable.adapter?.notifyDataSetChanged()
                        binding.recviewUnavailable.adapter?.notifyDataSetChanged()
                        countItems()
                    }
                }
            }
        })

        /*progress_available.visibility = View.VISIBLE
        progress_runout.visibility = View.VISIBLE
        productsOriginal.clear()
        productsAvailable.clear()
        productsUnAvailable.clear()
        recview_available.adapter?.notifyDataSetChanged()
        recview_unavailable.adapter?.notifyDataSetChanged()

        runBlocking {
            delay(500)
            val products = GlobalScope.async { viewModel.getProduct() }.await()
            productsOriginal.addAll(products)
            products.forEach { product ->
                if(product.stock == "available"){
                    productsAvailable.add(product)
                }else{
                    productsUnAvailable.add(product)
                }
            }

            recview_unavailable.adapter?.notifyDataSetChanged()
            recview_available.adapter?.notifyDataSetChanged()

            progress_available.visibility = View.GONE
            progress_runout.visibility = View.GONE
            countItems()
        }*/


    }

    private fun countItems() {
        binding.textView2.text = "Out Of Stock - ${viewModel.productsUnAvailable.size} Items"
        binding.textView1.text = "Available Products - ${viewModel.productsAvailable.size} Items"
    }

    override fun onQueryTextSubmit(p0: String?): Boolean {
        return true
    }

    override fun onQueryTextChange(query: String?): Boolean {
        viewModel.performSearch(query)

//        productsAvailable.clear()
//        productsUnAvailable.clear()
//
//        val filtered = ArrayList<ProductEntity>()
//        query?.takeIf { it.isNotEmpty() }?.let { search ->
//            filtered.addAll(productsOriginal.filter { it.name?.toLowerCase()?.contains(search.toLowerCase()) == true })
//        } ?: kotlin.run {
//            filtered.addAll(productsOriginal)
//        }
//
//        filtered.forEach {product ->
//            if(product.stock == "available"){
//                productsAvailable.add(product)
//            }else{
//                productsUnAvailable.add(product)
//            }
//        }
//        recview_available.adapter?.notifyDataSetChanged()
//        recview_unavailable.adapter?.notifyDataSetChanged()
//        countItems()
        return true
    }

    override fun onCreateOptionsMenu(menu: Menu): Boolean {
        menuInflater.inflate(R.menu.menu_runout_stock, menu)
        val searchManager = getSystemService(Context.SEARCH_SERVICE) as SearchManager
        searchView = menu?.findItem(R.id.action_search)?.actionView as SearchView
        searchView.setSearchableInfo(searchManager.getSearchableInfo(componentName))
        searchView.setOnQueryTextListener(this)
        return super.onCreateOptionsMenu(menu)
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        finish()
        return super.onOptionsItemSelected(item)
    }
}
