package com.uniq.uniqpos.view.payment

import android.graphics.Color
import android.os.Bundle
import android.view.LayoutInflater
import android.view.Menu
import android.view.MenuInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ArrayAdapter
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import com.uniq.uniqpos.BuildConfig
import com.uniq.uniqpos.R
import com.uniq.uniqpos.data.local.entity.BankEntity
import com.uniq.uniqpos.databinding.DialogCardPaymentSimplifyBinding
import com.uniq.uniqpos.databinding.FragmentPaymentListBinding
import com.uniq.uniqpos.databinding.ListItemCardNoCheckboxBinding
import com.uniq.uniqpos.databinding.ListItemPaymentV2Binding
import com.uniq.uniqpos.model.RoleMobile
import com.uniq.uniqpos.util.*
import com.uniq.uniqpos.util.view.RecyclerItemClickListener
import com.uniq.uniqpos.view.global.DynamicDialog
import com.uniq.uniqpos.view.global.GlobalAdapter
import com.uniq.uniqpos.view.global.GlobalViewHolder
import com.uniq.uniqpos.view.help.HelpContentDialog
import timber.log.Timber

// TODO: Rename parameter arguments, choose names that match
// the fragment initialization parameters, e.g. ARG_ITEM_NUMBER
private const val ARG_PARAM1 = "param1"
private const val ARG_PARAM2 = "param2"

/**
 * A simple [Fragment] subclass.
 * Use the [PaymentListFragment.newInstance] factory method to
 * create an instance of this fragment.
 */
class PaymentListFragment : Fragment() {
    // TODO: Rename and change types of parameters
    private var param1: String? = null
    private var param2: String? = null

    private var viewModel: PaymentViewModel? = null
    private var _binding: FragmentPaymentListBinding? = null
    private val binding: FragmentPaymentListBinding get() = _binding!!
    private lateinit var role: RoleMobile

    private val cardOptionDialog: DynamicDialog by lazy { setupPaymentCard() }
    private val instantPaymentDialog: DynamicDialog by lazy { setupInstantPaymentDialog() }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setHasOptionsMenu(true)
        Timber.d("[LIFE] #onCreate")
//        arguments?.let {
//            param1 = it.getString(ARG_PARAM1)
//            param2 = it.getString(ARG_PARAM2)
//        }
    }

    override fun onCreateOptionsMenu(menu: Menu, inflater: MenuInflater) {
        Timber.d("[LIFE] #onCreateOptionsMenu")
        super.onCreateOptionsMenu(menu, inflater)
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        // Inflate the layout for this fragment
        _binding = FragmentPaymentListBinding.inflate(inflater, container, false)
        return _binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        viewModel = (activity as PaymentV2Activity).getPaymentViewModel()
        role = requireContext().role()

        binding.viewmodel = viewModel
        binding.recViewPaymnet.adapter = getPaymentListAdapter()
        binding.txtSettingPayment.setOnClickListener { requireContext().launchUrl(BuildConfig.WEB_URL + "outlets/paymentmedia") }
        binding.imgHelpReceipt.setOnClickListener { showHelpReceipt() }
        binding.imgClearReceipt.setOnClickListener { clearReceipt() }

        observeTask()
    }

    private fun clearReceipt() {
        binding.edtReceiptAddress.setText("")
    }

    private fun getPaymentListAdapter(): GlobalAdapter<ListItemPaymentV2Binding> {
        return object : GlobalAdapter<ListItemPaymentV2Binding>(
            R.layout.list_item_payment_v2,
            viewModel!!.paymentListSelected
        ) {
            override fun onBindViewHolder(
                holder: GlobalViewHolder<ListItemPaymentV2Binding>,
                position: Int
            ) {
                super.onBindViewHolder(holder, position)
                if (holder.adapterPosition == viewModel?.selectedPaymentIdx) {
                    holder.binding.layoutItemPayment.background = ContextCompat.getDrawable(
                        requireContext(),
                        R.drawable.basic_background_selected_darker
                    )
                } else {
                    holder.binding.layoutItemPayment.background =
                        ContextCompat.getDrawable(requireContext(), R.drawable.basic_background)
                    holder.binding.imgPaymentCheck.setColorFilter(0)
                }

                if (viewModel?.paymentListSelected?.get(holder.adapterPosition)?.total.safe() > 0) {
                    holder.binding.imgPaymentCheck.setColorFilter(0)
                } else {
                    holder.binding.imgPaymentCheck.setColorFilter(Color.parseColor("#515A61"))
                }

                holder.itemView.setOnClickListener { changePaymentSelected(holder.adapterPosition) }
            }
        }
    }

    private fun changePaymentSelected(position: Int) {
        val tmpSelected = viewModel!!.selectedPaymentIdx
        //validation will also set the current position index
        if (!viewModel?.validatePaymentChange(position).safe()) {
            return
        }

        binding.recViewPaymnet.adapter?.notifyItemChanged(tmpSelected)
        binding.recViewPaymnet.adapter?.notifyItemChanged(position)

        val payment = viewModel?.paymentListSelected?.get(viewModel?.selectedPaymentIdx.safe())
        if (payment?.method == PaymentViewModel.PaymentType.CARD.toString() ) {
            if(payment.selectedBankIdx < 0){ //if user didnt choose the bank, show the dialog
                cardOptionDialog.show()
            }else{
                viewModel?.selectBank(payment.selectedBankIdx) //will set total pay
                (activity as PaymentV2Activity).updatePaymentDetailUi()
            }
        } else if(payment?.method  == PaymentViewModel.PaymentType.INSTANT_PAYMENT.toString()){
            if(viewModel?.isInstantPaymentEnable.safe()){
                instantPaymentDialog.show()
            }else{
                context?.showMessage("Instant Payment Belum di Aktifkan untuk akun anda")
            }
        } else {
            (activity as PaymentV2Activity).updatePaymentDetailUi()
        }
    }

    private fun observeTask() {
        viewModel?.receiptListRefresh?.observe(this) {
            if (viewModel?.receiptReceiverList?.isNotEmpty().safe())
                binding.edtReceiptAddress.setAdapter(
                    ArrayAdapter(
                        requireContext(),
                        android.R.layout.simple_list_item_1,
                        viewModel!!.receiptReceiverList
                    )
                )
        }

        viewModel?.taskRefreshPaymentList?.observe(this) { position ->
            if (position >= 0) {
                binding.recViewPaymnet.adapter?.notifyItemChanged(position)
            } else {
                binding.recViewPaymnet.adapter?.notifyDataSetChanged()
            }
        }

        viewModel?.taskShowCardDialog?.observe(this) {
            cardOptionDialog.show()
        }
    }

    private fun setupPaymentCard(): DynamicDialog {
        val bindingCardDialog =
            DialogCardPaymentSimplifyBinding.inflate(layoutInflater, null, false)
        val cardDialog = DynamicDialog(bindingCardDialog.root, requireContext())
        bindingCardDialog.recViewCard.adapter = getCardAdapter()
        bindingCardDialog.recViewCard.addOnItemTouchListener(
            RecyclerItemClickListener(
                requireContext()
            ) { _, position ->
                viewModel?.selectBank(position)
                (activity as PaymentV2Activity).updatePaymentDetailUi()
                //add card options
                binding.recViewPaymnet.adapter?.notifyItemChanged(viewModel?.addPaymentCardOption().safe())
                binding.recViewPaymnet.adapter?.notifyItemChanged(viewModel?.selectedPaymentIdx.safe())
                binding.recViewPaymnet.adapter?.notifyDataSetChanged()
                cardDialog.dismiss()
            })
        bindingCardDialog.txtAddBank.setOnClickListener {
            context?.launchUrl(BuildConfig.WEB_URL + "outlets/bankaccount")
        }

        if(viewModel?.isTabletMode.safe()){
            cardDialog.setOnDismissListener {

            }
        }

        bindingCardDialog.txtTitleBank.text = buildString {
            append(getString(R.string.choose_bank_account))
            append(" (${viewModel?.bankList?.size} banks)")
        }
        return cardDialog
    }

    private fun setupInstantPaymentDialog(): DynamicDialog {
        val bindingCardDialog =
            DialogCardPaymentSimplifyBinding.inflate(layoutInflater, null, false)
        val cardDialog = DynamicDialog(bindingCardDialog.root, requireContext())
        bindingCardDialog.recViewCard.adapter = object :
            GlobalAdapter<ListItemCardNoCheckboxBinding>(
                R.layout.list_item_card_no_checkbox,
                listOf(BankEntity(name = "QRIS", bankId = 0))
            ) {}
        bindingCardDialog.recViewCard.addOnItemTouchListener(
                RecyclerItemClickListener(
                    requireContext()
                ) { _, _ ->
                    cardDialog.dismiss()
                    viewModel?.createInstantPayment()
                })
        bindingCardDialog.txtAddBank.setVisible(false)
        return cardDialog
    }

    private fun getCardAdapter(): GlobalAdapter<ListItemCardNoCheckboxBinding> {
        return object :
            GlobalAdapter<ListItemCardNoCheckboxBinding>(
                R.layout.list_item_card_no_checkbox,
                viewModel!!.bankList
            ) {}
    }

    private fun showHelpReceipt() {
        val sendReceiptInfo = """<p style='color:white'>
            Kamu bisa mengirimkan Nota Elektronik kepada pelanggan melalui WhatsApp atau Email.<br/><br/>
            
            Untuk meningkatkan <i>Engagement</i>, kamu bisa juga loo mengirimkan nya menggunakan nomor WhatsApp kamu,<br>
            kamu tinggal menghubungkan WhatsApp kamu dengan Social Connect di Web UNIQ.<br/><br/>
             
            <a href='https://y.uniq.id/tutorial-social-connect' style="color:white;">Klik Disini</a> untuk mengetahui caranya.
            </p>
        """
        HelpContentDialog
            .newInstance(sendReceiptInfo, getString(R.string.send_receipt))
            .show(childFragmentManager, "help")
    }



    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

    companion object {
        /**
         * Use this factory method to create a new instance of
         * this fragment using the provided parameters.
         *
         * @param param1 Parameter 1.
         * @param param2 Parameter 2.
         * @return A new instance of fragment PaymentListFragment.
         */
        // TODO: Rename and change types and number of parameters
        @JvmStatic
        fun newInstance(param1: String, param2: String) =
            PaymentListFragment().apply {
                arguments = Bundle().apply {
                    putString(ARG_PARAM1, param1)
                    putString(ARG_PARAM2, param2)
                }
            }
    }
}