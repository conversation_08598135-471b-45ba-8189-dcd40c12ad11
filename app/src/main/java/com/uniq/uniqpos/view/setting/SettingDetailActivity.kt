package com.uniq.uniqpos.view.setting

import android.os.Bundle
import androidx.fragment.app.Fragment
import androidx.appcompat.app.AppCompatActivity
import android.view.MenuItem
import com.uniq.uniqpos.R
import com.uniq.uniqpos.util.toast
import com.uniq.uniqpos.view.setting.kitchendisplay.KitchenDisplaySettingFragment
import com.uniq.uniqpos.view.setting.printer.SettingPrinterFragment
import com.uniq.uniqpos.view.setting.printerserver.PrinterServerFragment
import dagger.android.AndroidInjection
import dagger.android.DispatchingAndroidInjector
import dagger.android.support.HasSupportFragmentInjector
import javax.inject.Inject

class SettingDetailActivity : AppCompatActivity(), HasSupportFragmentInjector {

    @Inject
    lateinit var fragmentDispatchingInjector: DispatchingAndroidInjector<Fragment>

    override fun supportFragmentInjector() = fragmentDispatchingInjector

    override fun onCreate(savedInstanceState: Bundle?) {
        AndroidInjection.inject(this)
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_setting_detail)

        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        title = intent.getStringExtra("title")

        val position = intent.getIntExtra("position", 0)
        loadFragment(position)
    }

    private fun loadFragment(position: Int) {
        val fragment = when(position){
            0 -> SettingPrinterFragment()
            1 -> PrinterServerFragment()
            2 -> KitchenDisplaySettingFragment()
            else -> {
                toast("Fragment not found")
                null
            }
        }

        fragment?.let { supportFragmentManager.beginTransaction()
                .replace(R.id.container, fragment)
                .commit()}
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        finish()
        return super.onOptionsItemSelected(item)
    }
}
