package com.uniq.uniqpos.view.global

import android.app.ProgressDialog
import android.content.SharedPreferences
import android.os.Build
import android.os.Bundle
import android.view.MenuItem
import androidx.annotation.LayoutRes
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.databinding.DataBindingUtil
import androidx.databinding.ViewDataBinding
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import com.google.gson.Gson
import com.uniq.uniqpos.data.local.entity.PendingPrintEntity
import com.uniq.uniqpos.data.local.entity.PrinterEntity
import com.uniq.uniqpos.data.local.sharedpref.SharedPref
import com.uniq.uniqpos.data.local.sharedpref.getLocalDataBoolean
import com.uniq.uniqpos.data.local.sharedpref.getLocalDataString
import com.uniq.uniqpos.model.SocketMessage
import com.uniq.uniqpos.util.decrypt
import com.uniq.uniqpos.util.printer.PrintResponse
import com.uniq.uniqpos.util.printer.PrinterManager
import com.uniq.uniqpos.util.printer.PrinterSocket
import com.uniq.uniqpos.util.toast
import dagger.android.AndroidInjection
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject


/**
 * Created by AnnasBlackHat on 12/10/2017.
 */

abstract class BaseActivity<VM : ViewModel, DB : ViewDataBinding> : AppCompatActivity(), ViewContract, SharedPrefListener {

    @Inject
    lateinit var viewModelFactory: ViewModelProvider.Factory

    @Inject
    lateinit var printerManager: PrinterManager

    @Inject
    lateinit var sharedPref: SharedPref

    @Inject
    lateinit var printerSocket: PrinterSocket

    lateinit var viewModel: VM

    private var _binding: DB? = null
    val binding get() = _binding!!

    lateinit var pDialog: ProgressDialog

    var isSubscribeToSharedPref = false
    private var sharedPrefChangeListener: SharedPreferences.OnSharedPreferenceChangeListener? = null

    @LayoutRes
    abstract fun getLayoutRes(): Int
    abstract fun getViewModel(): Class<VM>

    override fun onCreate(savedInstanceState: Bundle?) {
        AndroidInjection.inject(this)
        super.onCreate(savedInstanceState)
        supportActionBar?.setDisplayHomeAsUpEnabled(getDisplayHomeAsUpEnabled())
        _binding = DataBindingUtil.setContentView(this, getLayoutRes())

//        viewModel = ViewModelProviders.of(this, viewModelFactory).get(getViewModel())
        viewModel = ViewModelProvider(this, viewModelFactory)[getViewModel()]

//        if (resources.getBoolean(R.bool.landscape_only)) {
//            requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_SENSOR_LANDSCAPE
//
//            if(resources.configuration.orientation == Configuration.ORIENTATION_PORTRAIT){
//                return
//            }
//        }

        initView()
        observeTask()
        observeData()

        if (isSubscribeToSharedPref) {
            sharedPrefChangeListener = SharedPreferences.OnSharedPreferenceChangeListener { _, key ->
                onSharedPrefChanged(key.decrypt())
            }
        }

        Timber.i("printer manager : $printerManager")
    }

    override fun onSharedPrefChanged(key: String) {

    }

    fun showDialog(isShow: Boolean, message: String? = null) {
        if (isShow) {
            if (!pDialog.isShowing) {
                pDialog.setMessage(message ?: "Loading...")
                pDialog.show()
            } else {
                pDialog.setMessage(message)
            }
            Timber.i("[[PROGRESS DIALOG]] ${message ?: "Loading..."}")
        } else {
            if (pDialog.isShowing) {
                pDialog.dismiss()
            }
        }
    }

    fun setDialogMessage(msg: String) {
        pDialog.setMessage(msg)
    }


    fun isProgressDialogShowing() = pDialog.isShowing

    fun showAlert(msg: String?, title: String? = null) {

        val alert = AlertDialog.Builder(this)
                .setTitle(title)
                .setMessage(msg)
                .setPositiveButton("OK", null)
        msg?.let { alert.show() }
    }

    fun openCashDrawer(list: List<PrinterEntity>, callback: (PrintResponse) -> Unit) {
        lifecycleScope.launch (Dispatchers.IO) {
            printerManager.openCashDrawer(list, callback)
        }
    }

    fun beginPrint(list: List<PendingPrintEntity>, listener: PrinterManager.PrintListener? = null) {
        printerManager.managePrint(list, printerSocket, listener)
    }

    fun managePrintWifi(list: List<PendingPrintEntity>, listener: PrinterSocket.SocketListener? = null) {
        if (list.isEmpty()) {
            Timber.i(">> List print is empty")
            listener?.onConnectionFinish(true, null)
            return
        }

        val isServer = getLocalDataBoolean(SharedPref.PRINTER_SERVER_STATUS)
        val ipServer = getLocalDataString(SharedPref.IP_SERVER, "")

        if (!isServer && ipServer.isNotEmpty()) {
            val req = Gson().toJson(SocketMessage("print_list", Gson().toJson(list.filter { it.dataString?.length ?: 0 > 10 }), "${Build.BRAND} ${Build.MODEL} - ${Build.SERIAL}"))
            printerSocket.sendToServer(req, listener)
        } else {
            printerManager.addAllToQueue(list)
            listener?.let {
                lifecycleScope.launch(Dispatchers.Main) {
                    do {
                        delay(500)
                    } while (!printerManager.isPrinted(list.map { it.id }))

                    listener.onConnectionFinish(false, null)
                }
            }
        }
    }

    fun printerManager() = printerManager

    override fun initView() {
        pDialog = ProgressDialog(this)
        pDialog.setCancelable(false)
        pDialog.setMessage("Loading...")
    }

    override fun observeData() {}
    override fun observeTask() {}
    override fun getDisplayHomeAsUpEnabled() = false

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        if(item.itemId == android.R.id.home) {
            finish()
        }
        return super.onOptionsItemSelected(item)
    }

    override fun onResume() {
        super.onResume()
        sharedPref.getSharedPreference().registerOnSharedPreferenceChangeListener(sharedPrefChangeListener)
    }

    override fun onPause() {
        sharedPref.getSharedPreference().unregisterOnSharedPreferenceChangeListener(sharedPrefChangeListener)
        super.onPause()
    }

    override fun onDestroy() {
        if (pDialog.isShowing) {
            pDialog.dismiss()
        }
        _binding = null
        super.onDestroy()
    }
}