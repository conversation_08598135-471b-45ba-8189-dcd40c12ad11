package com.uniq.uniqpos.view.transaction.utils

import com.uniq.uniqpos.data.local.entity.ProductEntity
import com.uniq.uniqpos.util.safe

fun searchProductByKeyword(query: String, product: ProductEntity): Boolean{
    val q = query.lowercase()
    val searchableParams = listOf(product.name.safe(), product.sku.safe(), product.barcode.safe())
    return searchableParams.any { p -> p.lowercase().contains(q)  }
}