package com.uniq.uniqpos.view.transaction.dialog

import android.app.Activity
import android.content.DialogInterface
import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.view.*
import android.view.inputmethod.EditorInfo
import android.widget.FrameLayout
import androidx.appcompat.content.res.AppCompatResources
import androidx.core.content.ContextCompat
import androidx.fragment.app.FragmentManager
import androidx.lifecycle.Observer
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.uniq.uniqpos.R
import com.uniq.uniqpos.data.remote.model.Promotion
import com.uniq.uniqpos.data.remote.model.ServerResponse
import com.uniq.uniqpos.databinding.DialogApplyVoucherBinding
import com.uniq.uniqpos.util.*
import com.uniq.uniqpos.view.scanbarcode.ScanBarcodeActivity
import com.uniq.uniqpos.view.transaction.TransactionViewModel
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import timber.log.Timber


/**
 * Created by annasblackhat on 2019-08-06
 */
class ApplyVoucherDialogFragment : BottomSheetDialogFragment() {

    private val RC_SCAN = 12
    private var outletId = 0
    private var viewModel: TransactionViewModel? = null
    private var _binding: DialogApplyVoucherBinding? = null
    private val binding get() = _binding!!

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = DialogApplyVoucherBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        context?.apply {
            val scanDrawable = AppCompatResources.getDrawable(this, R.drawable.ic_barcode)
            scanDrawable?.setColorFilter(
                ContextCompat.getColor(this, R.color.greeen_background),
                android.graphics.PorterDuff.Mode.MULTIPLY
            )
            binding.edtVoucher.setCompoundDrawablesWithIntrinsicBounds(
                null,
                null,
                scanDrawable,
                null
            )
            outletId = outlet()?.outletId ?: 0
        }

        binding.imgClose.setOnClickListener { dismiss() }
        binding.edtVoucher.setOnTouchListener { _, event ->
            if (event.action == MotionEvent.ACTION_UP) {
                if (event.rawX >= (_binding!!.edtVoucher.right - _binding!!.edtVoucher.compoundDrawables[2].bounds.width())) {
                    scanVoucher()
                    true
                }
            }
            false
        }

        binding.edtVoucher.simpleOnTextChanged {
            if (_binding!!.txtErrMsg.text.isNotEmpty())
                _binding!!.txtErrMsg.text = ""
        }

        binding.btnApply.setOnButtonClickListener {
            checkVoucher()
        }

        observeTask()

        view.viewTreeObserver.addOnGlobalLayoutListener(object :
            ViewTreeObserver.OnGlobalLayoutListener {
            override fun onGlobalLayout() {
                view.viewTreeObserver.removeOnGlobalLayoutListener(this)
                val dialog = dialog as BottomSheetDialog
                val bottomSheet =
                    dialog.findViewById(com.google.android.material.R.id.design_bottom_sheet) as FrameLayout?
                val behavior = BottomSheetBehavior.from(bottomSheet!!)
                behavior.state = BottomSheetBehavior.STATE_EXPANDED
            }
        })

        binding.edtVoucher.setOnEditorActionListener { _, actionId, key ->
            if (actionId == EditorInfo.IME_ACTION_DONE) checkVoucher()
            else if(key.keyCode == KeyEvent.KEYCODE_ENTER)  checkVoucher()
//            Timber.i("[voucher] setOnEditorActionListener : $actionId | keyAction: ${key.action} | keyCode: ${key.keyCode} | enterIs : ${KeyEvent.KEYCODE_ENTER}")
            false
        }

//        binding.edtVoucher.requestFocus()
        Handler().postDelayed({
            //after a few seconds, the dialog can be closed, and binding is already null
            _binding?.edtVoucher?.requestFocus()
        }, 700)
    }

    private fun observeTask() {
        viewModel?.taskPromotionResult?.observe(viewLifecycleOwner) { err ->
            _binding?.btnApply?.stopLoading()
            err?.let {
                _binding?.txtErrMsg?.text = it.readableError(context, false)
                _binding?.edtVoucher?.requestFocus()
                _binding?.edtVoucher?.selectAll()
            }
                ?: run { dismiss() }
        }
    }

    fun setViewModel(viewModel: TransactionViewModel) {
        this.viewModel = viewModel
    }

    private fun checkVoucher() {
        if (binding.edtVoucher.value().isBlank()) {
            binding.txtErrMsg.text = "please enter voucher code"
            return
        }

        binding.btnApply.startLoading()
        viewModel?.checkPromotionCode(_binding?.edtVoucher?.value().safe(), outletId)
    }

    private fun scanVoucher() {
        val intent = Intent(context, ScanBarcodeActivity::class.java)
        intent.putExtra("action_type", Constant.ACTION_GET_VALUE_ONLY)
        startActivityForResult(intent, RC_SCAN)
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (resultCode == Activity.RESULT_OK && requestCode == RC_SCAN) {
            data?.getStringExtra("data")?.takeIf { it.trim().isNotEmpty() }?.let { barcode ->
                _binding?.edtVoucher?.setText(barcode)
                checkVoucher()
            }
        }
    }

    fun onCheckVoucherFinish(task: ServerResponse<Promotion>?): String? {
        _binding?.btnApply?.stopLoading()
        task?.takeIf { it.status }?.data?.let {
            context?.toast("voucher found!")
            dismiss()
            return it.displayVoucher
        } ?: kotlin.run {
            _binding?.txtErrMsg?.text = task?.message ?: "Voucher not found or expired!"
        }
        return null
    }

    override fun onDismiss(dialog: DialogInterface) {
        _binding?.edtVoucher?.setText("")
        super.onDismiss(dialog)
        _binding = null
    }

    override fun show(manager: FragmentManager, tag: String?) {
        if (!isAdded) {
            super.show(manager, tag)
        }
    }
}