package com.uniq.uniqpos.view.login.code

import android.content.Intent
import android.os.Bundle
import android.view.Menu
import android.view.MenuItem
import com.uniq.uniqpos.R
import com.uniq.uniqpos.data.remote.model.AuthCodeResponse
import com.uniq.uniqpos.databinding.ActivityLoginWithCodeBinding
import com.uniq.uniqpos.util.lifecycle.Failed
import com.uniq.uniqpos.util.lifecycle.Loading
import com.uniq.uniqpos.util.lifecycle.Success
import com.uniq.uniqpos.util.lifecycle.setupToast
import com.uniq.uniqpos.util.outlet
import com.uniq.uniqpos.util.readableError
import com.uniq.uniqpos.util.safe
import com.uniq.uniqpos.util.showMessage
import com.uniq.uniqpos.view.chooseoperator.ChooseOperatorActivity
import com.uniq.uniqpos.view.chooseoutlet.ChooseOutletActivity
import com.uniq.uniqpos.view.global.BaseActivity
import com.uniq.uniqpos.view.login.LoginViewModel

class LoginWithCodeActivity : BaseActivity<LoginViewModel, ActivityLoginWithCodeBinding>(){

    private var authCode: AuthCodeResponse? = null

    override fun getLayoutRes() = R.layout.activity_login_with_code
    override fun getViewModel() = LoginViewModel::class.java

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        supportActionBar?.setHomeAsUpIndicator(R.drawable.ic_close)

        authCode = intent.getParcelableExtra("data")

        binding.txtEmail.text = authCode?.email
    }

    override fun initView() {
        super.initView()
        this.setupToast(this, viewModel.toastMsg)

        viewModel.state.observe(this) { state ->
            when (state) {
                is Loading -> showDialog(state.isLoading)
                is Failed -> showMessage(state.exception.readableError(this), "Failed")
                is Success -> nextPage()
            }
        }

        binding.btnLogin.setOnButtonClickListener {
            authCode?.let {
                viewModel.loginWithCode(it, binding.edtCode.text.toString())
            }
        }
    }

    private fun nextPage() {
        var destination: Class<*> = ChooseOutletActivity::class.java
        //if somehow outlet is exist, which mean user already logged in before,
        //then continue with that outlet
        outlet()?.takeIf { it.outletId.safe() > 0 }?.let { outlet ->
            destination = ChooseOperatorActivity::class.java
        }

        startActivity(Intent(this, destination))
        finish()
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        finish()
        return super.onOptionsItemSelected(item)
    }
}