package com.uniq.uniqpos.view.transaction

import android.app.Activity
import android.content.Intent
import android.graphics.Color
import android.os.Bundle
import android.view.MenuItem
import android.view.View
import androidx.appcompat.app.AppCompatActivity
import com.uniq.uniqpos.R
import com.uniq.uniqpos.data.local.entity.ProductEntity
import com.uniq.uniqpos.data.local.entity.SubCategoryEntity
import com.uniq.uniqpos.databinding.ActivityMenuExtraBinding
import com.uniq.uniqpos.databinding.ListItemCategoryBinding
import com.uniq.uniqpos.databinding.ListItemMenuExtraBinding
import com.uniq.uniqpos.model.Order
import com.uniq.uniqpos.view.global.GlobalAdapter
import com.uniq.uniqpos.view.global.GlobalViewHolder

class MenuExtraActivity : AppCompatActivity() {

    private var tmpProduct = ArrayList<ProductEntity>()
    private lateinit var binding: ActivityMenuExtraBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityMenuExtraBinding.inflate(layoutInflater)
        setContentView(binding.root)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)

        val categories = intent.getSerializableExtra("category") as List<SubCategoryEntity>
        val product = intent.getSerializableExtra("product") as List<ProductEntity>
        val extra = intent.getSerializableExtra("extra") as ArrayList<Order>
        val position = intent.getIntExtra("position", 0)

        binding.recViewCategory.adapter =
            object : GlobalAdapter<ListItemCategoryBinding>(R.layout.list_item_category, categories) {
                var selected = 0
                override fun onBindViewHolder(holder: GlobalViewHolder<ListItemCategoryBinding>, position: Int) {
                    super.onBindViewHolder(holder, position)
                    if (position == selected) {
                        holder.binding.layoutList.setBackgroundColor(Color.parseColor("#C2C3C4"))

                        var selectedCategoryId = categories[position].productCategoryId
                        tmpProduct.clear()
                        if (selectedCategoryId < 0) tmpProduct.addAll(product)
                        else tmpProduct.addAll(product.filter { p -> p.productSubcategoryFkid == selectedCategoryId })
                        binding.recViewExtra.adapter?.notifyDataSetChanged()
                    } else {
                        holder.binding.layoutList.setBackgroundColor(Color.TRANSPARENT)
                    }
                    holder.binding.root.setOnClickListener { _ ->
                        val tmpSelected = selected
                        selected = position
                        notifyItemChanged(tmpSelected)
                        notifyItemChanged(position)
                    }
                }
            }

        binding.recViewExtra.adapter = object :
            GlobalAdapter<ListItemMenuExtraBinding>(R.layout.list_item_menu_extra, tmpProduct) {
            override fun onBindViewHolder(
                holder: GlobalViewHolder<ListItemMenuExtraBinding>,
                position: Int
            ) {
                super.onBindViewHolder(holder, position)
                holder.binding.layoutAdd.setOnClickListener {
                    addQty(
                        holder,
                        tmpProduct[position],
                        1
                    )
                }
                holder.binding.addQty.setOnClickListener {
                    addQty(
                        holder,
                        tmpProduct[position],
                        1
                    )
                }
                holder.binding.minQty.setOnClickListener {
                    addQty(
                        holder,
                        tmpProduct[position],
                        -1
                    )
                }
                setQty(holder, tmpProduct[position])
            }

            fun setQty(
                holder: GlobalViewHolder<ListItemMenuExtraBinding>,
                tmpProduct: ProductEntity
            ) {
                val tmp = extra.firstOrNull { it.product?.productId == tmpProduct.productId }
                tmp?.let {
                    setVisibility(holder, it.qty)
                } ?: kotlin.run { setVisibility(holder, 0) }
            }

            fun setVisibility(holder: GlobalViewHolder<ListItemMenuExtraBinding>, qty: Int) {
                holder.binding.txtQty.text = qty.toString()
                if (qty > 0 && holder.binding.layoutEditQty.visibility == View.GONE) {
                    holder.binding.layoutEditQty.visibility = View.VISIBLE
                    holder.binding.txtQty.visibility = View.VISIBLE
                    holder.binding.layoutAdd.visibility = View.GONE
                    holder.binding.layoutColor.setBackgroundColor(Color.parseColor("#99424242"))
                } else if (qty <= 0 && holder.binding.layoutAdd.visibility == View.GONE) {
                    holder.binding.layoutEditQty.visibility = View.GONE
                    holder.binding.txtQty.visibility = View.GONE
                    holder.binding.layoutAdd.visibility = View.VISIBLE
                    holder.binding.layoutColor.setBackgroundColor(Color.TRANSPARENT)
                }
            }

            fun addQty(
                holder: GlobalViewHolder<ListItemMenuExtraBinding>,
                tmpProduct: ProductEntity,
                value: Int
            ) {
                var qty = holder.binding.txtQty.text.toString().toInt() + value
                holder.binding.txtQty.text = qty.toString()
                if (qty > 0 && holder.binding.layoutEditQty.visibility == View.GONE) {
                    holder.binding.layoutEditQty.visibility = View.VISIBLE
                    holder.binding.txtQty.visibility = View.VISIBLE
                    holder.binding.layoutAdd.visibility = View.GONE
                    holder.binding.layoutColor.setBackgroundColor(Color.parseColor("#99424242"))
                } else if (qty <= 0 && holder.binding.layoutAdd.visibility == View.GONE) {
                    holder.binding.layoutEditQty.visibility = View.GONE
                    holder.binding.txtQty.visibility = View.GONE
                    holder.binding.layoutAdd.visibility = View.VISIBLE
                    holder.binding.layoutColor.setBackgroundColor(Color.TRANSPARENT)
                }
                extra.firstOrNull { it.product?.productId == tmpProduct.productId }
                    ?.let { it.qty = qty } ?: kotlin.run { extra.add(Order(tmpProduct.copy())) }
            }
        }

        binding.btnSave.setOnClickListener {
            val intent = Intent()
            intent.putExtra("position", position)
            intent.putExtra("extra", extra)
            setResult(Activity.RESULT_OK, intent)
            finish()
        }
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        finish()
        return super.onOptionsItemSelected(item)
    }
}
