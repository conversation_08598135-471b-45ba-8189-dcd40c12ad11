package com.uniq.uniqpos.view.transaction


import android.Manifest
import android.annotation.SuppressLint
import android.app.Activity.RESULT_OK
import android.content.Intent
import android.content.pm.PackageManager
import android.content.res.ColorStateList
import android.graphics.Color
import android.media.MediaPlayer
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.text.InputType
import android.view.LayoutInflater
import android.view.MenuInflater
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.activity.result.ActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatDelegate
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.core.widget.ImageViewCompat
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.commit
import androidx.fragment.app.replace
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.ItemTouchHelper
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.bugsnag.android.Bugsnag
import com.google.android.gms.vision.CameraSource
import com.google.android.gms.vision.Detector
import com.google.android.gms.vision.barcode.Barcode
import com.google.android.gms.vision.barcode.BarcodeDetector
import com.google.firebase.analytics.ktx.analytics
import com.google.firebase.ktx.Firebase
import com.google.gson.Gson
import com.uniq.uniqpos.R
import com.uniq.uniqpos.data.local.entity.MemberEntity
import com.uniq.uniqpos.data.local.entity.ProductEntity
import com.uniq.uniqpos.data.local.entity.ProductVariantEntity
import com.uniq.uniqpos.data.local.entity.ReservationEntity
import com.uniq.uniqpos.data.local.entity.SalesEntity
import com.uniq.uniqpos.data.local.entity.TmpSalesEntity
import com.uniq.uniqpos.data.local.sharedpref.SharedPref
import com.uniq.uniqpos.data.local.sharedpref.getLocalDataBoolean
import com.uniq.uniqpos.data.local.sharedpref.putData
import com.uniq.uniqpos.data.local.sharedpref.sharedPref
import com.uniq.uniqpos.data.remote.model.Employee
import com.uniq.uniqpos.data.remote.model.ServerResponse
import com.uniq.uniqpos.databinding.FragmentTransactionV2Binding
import com.uniq.uniqpos.databinding.ListItemCategoryBinding
import com.uniq.uniqpos.databinding.ListItemMenuBinding
import com.uniq.uniqpos.databinding.ListItemTaxBinding
import com.uniq.uniqpos.model.BottomDialogModel
import com.uniq.uniqpos.model.Discount
import com.uniq.uniqpos.model.LinkMenuProduct
import com.uniq.uniqpos.model.Order
import com.uniq.uniqpos.model.ShowCaseData
import com.uniq.uniqpos.util.Constant
import com.uniq.uniqpos.util.Level
import com.uniq.uniqpos.util.ShowCase
import com.uniq.uniqpos.util.Utils
import com.uniq.uniqpos.util.bundleOf
import com.uniq.uniqpos.util.covertToMember
import com.uniq.uniqpos.util.employee
import com.uniq.uniqpos.util.getSafe
import com.uniq.uniqpos.util.outlet
import com.uniq.uniqpos.util.outletFeature
import com.uniq.uniqpos.util.safe
import com.uniq.uniqpos.util.setVisible
import com.uniq.uniqpos.util.showMessage
import com.uniq.uniqpos.util.takeMax
import com.uniq.uniqpos.util.toCurrency
import com.uniq.uniqpos.util.toast
import com.uniq.uniqpos.util.view.ItemTouchHelperContract
import com.uniq.uniqpos.util.view.ItemTouchHelperMove
import com.uniq.uniqpos.util.view.RecyclerItemClickListener
import com.uniq.uniqpos.util.view.RecyclerItemMoveCallback
import com.uniq.uniqpos.util.view.SwipeController
import com.uniq.uniqpos.util.visibility
import com.uniq.uniqpos.view.global.BottomDialogInput
import com.uniq.uniqpos.view.global.GlobalAdapter
import com.uniq.uniqpos.view.global.GlobalAdapterNoBinding
import com.uniq.uniqpos.view.global.GlobalViewHolder
import com.uniq.uniqpos.view.global.GlobalViewHolderNoBinding
import com.uniq.uniqpos.view.main.MainActivity
import com.uniq.uniqpos.view.member.MemberActivity
import com.uniq.uniqpos.view.ordersales.OrderSalesActivity
import com.uniq.uniqpos.view.pendingprint.PendingPrintActivity
import com.uniq.uniqpos.view.scanbarcode.ScanBarcodeActivity
import com.uniq.uniqpos.view.transaction.adapter.BillAdapter
import com.uniq.uniqpos.view.transaction.dialog.ApplyVoucherDialogFragment
import com.uniq.uniqpos.view.transaction.dialog.ChooseVariantDialog
import com.uniq.uniqpos.view.transaction.dialog.FreeItemListDialogFragment
import com.uniq.uniqpos.view.transaction.dialog.LinkMenuDialog
import com.uniq.uniqpos.view.transaction.dialog.MenuDetailDialog
import com.uniq.uniqpos.view.transaction.dialog.SaveOrderDialog
import com.uniq.uniqpos.view.transaction.dialog.TaxDetailDialog
import com.uniq.uniqpos.view.transaction.dialog.VoidDialog
import com.uniq.uniqpos.view.transaction.dialog.VoucherDetailDialogFragment
import com.uniq.uniqpos.view.transaction.promotion.PromotionListFragment
import com.uniq.uniqpos.view.transaction.utils.searchProductByKeyword
import kotlinx.coroutines.async
import kotlinx.coroutines.delay
import kotlinx.coroutines.runBlocking
import smartdevelop.ir.eram.showcaseviewlib.GuideView
import smartdevelop.ir.eram.showcaseviewlib.config.DismissType
import timber.log.Timber
import java.util.Collections


/**
 * A simple [Fragment] subclass.
 */
class TransactionFragment : Fragment() {

    private val RC_PAYMENT = 1
    private val RC_EDIT = 2
    private val RC_CAMERA = 3
    private val RC_EXTRA = 4
    private val RC_TABLE = 5
    private val RC_MEMBER = 6
    private val RC_SCAN = 7

    private lateinit var viewModel: TransactionViewModel
    private var _binding: FragmentTransactionV2Binding? = null
    private val dataBinding get() = _binding!!

    //    private lateinit var menuAdapter: GlobalAdapter<ListItemMenuBinding>
    private lateinit var menuAdapter: GlobalAdapterNoBinding
    private lateinit var menuExtraAdapter: GlobalAdapter<ListItemMenuBinding>
    private lateinit var categoryAdapter: GlobalAdapter<ListItemCategoryBinding>
    private lateinit var categoryExtraAdapter: GlobalAdapter<ListItemCategoryBinding>
    private lateinit var billAdapter: BillAdapter

    private lateinit var cameraSource: CameraSource
    private var mp: MediaPlayer? = null
    private var searchQuery: String? = null
    private var isGridLayout = true
    private var isSearchLogged = false
    private var isShowCaseShowed = false
    private var isShowCasePart2Showed = false

    private var saveOrderDialog: SaveOrderDialog? = null

    private lateinit var taxDetailDialog: TaxDetailDialog

    private val productsMainTmp = ArrayList<ProductEntity>()
    private val productsExtraTmp = ArrayList<ProductEntity>()

    private lateinit var applyVoucherDialog: ApplyVoucherDialogFragment
    private lateinit var voucherDetailDialog: VoucherDetailDialogFragment
    private lateinit var menuDetailDialog: MenuDetailDialog

    val resultLauncher = registerForActivityResult(ActivityResultContracts.StartActivityForResult()){ result -> handleActivityResult(result)}

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setHasOptionsMenu(true)
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?
    ): View {
        AppCompatDelegate.setCompatVectorFromResourcesEnabled(true)
        super.onCreateView(inflater, container, savedInstanceState)
        _binding = FragmentTransactionV2Binding.inflate(inflater, container, false)

        applyVoucherDialog = ApplyVoucherDialogFragment()
        voucherDetailDialog = VoucherDetailDialogFragment()

        return _binding!!.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        Timber.d("[LIFE] #onViewCreated, has savedInstanceState? ${savedInstanceState == null} | added: $isAdded, hidden: $isHidden")

        dataBinding.isShowExtra =
            !(context?.sharedPref()?.getBoolean(SharedPref.SETTING_HIDE_EXTRA, true).safe())
        isGridLayout =
            context?.sharedPref()?.getBoolean(SharedPref.SETTING_LAYOUT_TRANSACTION, true)
                .safe(true)

        searchQuery = null

//        try { Log.setLogSettings(context, Log.PERIOD_TEMPORARY, Log.OUTPUT_STORAGE, null, 0, 1, Log.LOGLEVEL_LOW) } catch (e: Exception) { }

        val outlet = context?.outlet()
        val layoutType = if (isGridLayout) "Grid" else "List"
        Firebase.analytics.logEvent(
                "menu_layout", bundleOf(
                    "Layout" to layoutType,
                    "Outlet" to "$layoutType:${outlet?.outletId}:${outlet?.name}"
                )
            )

        val employee = context?.employee()
        isShowCaseShowed =
            context?.getLocalDataBoolean("${ShowCase.Sales_MenuDetail} ${employee?.employeeId}")
                .safe()
        isShowCasePart2Showed =
            context?.getLocalDataBoolean("${ShowCase.Sales_Swipe_Bill_Delete} ${employee?.employeeId}")
                .safe()

        try {
            if (savedInstanceState == null && (activity as MainActivity).getTransactionMain() != null) {
                viewModel = (activity as MainActivity).getTransactionMain()!!.viewModel
                applyVoucherDialog.setViewModel(viewModel)
                voucherDetailDialog.setViewModel(viewModel)

                initLayout()
                initPopUpFinishOrder()
                initPopUpTaxDetail()
//                initCamera()
                showHideExtraMenu()
                changeMenuLayout()
                initVisibilityViewFinishOrder()

                observeData()
            }
        } catch (e: Exception) {
            context?.toast("we encounter some problem, please restart the app")
        }
    }

    private fun initLayout() {
        Timber.d(">> (main) initlayout")
        categoryExtraAdapter =
            viewModel.getAdapterCategory(productsExtraTmp, TransactionViewModel.CategoryType.EXTRA)
        categoryAdapter =
            viewModel.getAdapterCategory(productsMainTmp, TransactionViewModel.CategoryType.MAIN)

        menuExtraAdapter = object :
            GlobalAdapter<ListItemMenuBinding>(R.layout.list_item_menu, productsExtraTmp) {}

        val outletFeature = context?.outletFeature()
        val showStock = outletFeature?.showStock ?: false

//        val menuAdapterList =
        menuAdapter = object : GlobalAdapterNoBinding(R.layout.list_item_menu, productsMainTmp) {
            override fun onBindViewHolder(holder: GlobalViewHolderNoBinding, position: Int) {
                super.onBindViewHolder(holder, position)
                val imgProduct = holder.itemView.findViewById<View?>(R.id.img_product)
                imgProduct?.setOnClickListener {
                    productsMainTmp.getSafe(holder.adapterPosition)?.let { product ->
                        if (isGridLayout) {
                            checkStock(product) {
                                addToBill(product)
                            }
                        } else {
                            (activity as MainActivity).getTransactionMain()
                                ?.showProductDetail(product)
                        }
                    }
                }

                holder.itemView.findViewById<TextView?>(R.id.txt_menu)?.let { txtMenu ->
                    productsMainTmp.getSafe(holder.adapterPosition)?.let { product ->
                        val color = if(product.stock == "available") R.color.grey_light else R.color.text_grey_disable
                        txtMenu.setTextColor(ContextCompat.getColor(requireContext(), color))
                    }
                }

                imgProduct?.setOnLongClickListener {
                    if (isGridLayout) {
                        productsMainTmp.getSafe(holder.adapterPosition)?.let { product ->
                            (activity as MainActivity).getTransactionMain()
                                ?.showProductDetail(product)
                        }
                    }
                    true
                }
                holder.itemView.setOnClickListener {
                    productsMainTmp.getSafe(holder.adapterPosition)?.let { product ->
                        checkStock(product) {
                            addToBill(product)
                        }
                    }
                }

                //show/hide stock
//                holder.itemView.findViewById<View?>(R.id.txt_qty)?.visibility = if(showStock) View.VISIBLE else View.GONE
                if(showStock){
                    holder.itemView.findViewById<View?>(R.id.txt_qty)?.setVisible(productsMainTmp.getSafe(holder.adapterPosition)?.isShowStock.safe())
                }else{
                    holder.itemView.findViewById<View?>(R.id.txt_qty)?.setVisible(false)
                }
            }
        }

        billAdapter = object : BillAdapter(viewModel, requireContext()) {
            override fun showMenuDetail(position: Int) {
                <EMAIL>(position)
            }

            override fun showVoidDialog(order: Order, employee: Employee) {
                <EMAIL>(order, employee)
            }

            override fun refreshGrandTotal() {
                countGrandTotal()
            }
        }

        billAdapter.setOnQtyChangeListener { order, _, isAdd ->
            var enable = viewModel.enableChangeQty(order, isAdd)
            if (enable && isAdd){
                if (viewModel.hasPotentialPromo(order)){
                    context?.toast("order berpotensi mendapatkan promo", level = Level.INFO)
//                    enable = false
//                    addToBill(order.product!!)
                }
            }
            enable
        }

        dataBinding.recViewCategoryMain.adapter = categoryAdapter
        dataBinding.recViewCategoryExtra.adapter = categoryExtraAdapter
        dataBinding.recViewMenuMain.adapter = menuAdapter
        dataBinding.recViewMenuExtra.adapter = menuExtraAdapter
        dataBinding.recViewBill.adapter = billAdapter
        val outlet = context?.outlet()

        dataBinding.layoutTaxDisc.recviewTax.adapter = object :
            GlobalAdapter<ListItemTaxBinding>(R.layout.list_item_tax, viewModel.taxSaleList) {}
        dataBinding.layoutTaxDisc.recviewTax.layoutManager =
            LinearLayoutManager(context, LinearLayoutManager.VERTICAL, true)

        dataBinding.recViewCategoryMain.addOnItemTouchListener(RecyclerItemClickListener(context) { _, position ->
            val tmpSelected = viewModel.selectedCategoryPosition
            viewModel.selectedCategoryPosition = position
            categoryAdapter.notifyItemChanged(position)
            categoryAdapter.notifyItemChanged(tmpSelected)

            dataBinding.recViewMenuMain.setVisible(true)
            dataBinding.containerMain.setVisible(false)
            selectPromotion(false)

            dataBinding.recViewCategoryMain.post { menuAdapter.notifyDataSetChanged() }

            Firebase.analytics.logEvent(
                    "app_features", bundleOf(
                        "Feature" to "Choose Category",
                        "Outlet" to "CC:${outlet?.outletId}:${outlet?.name}"
                    )
                )
        })

        //handle move position
        val moveCallback = RecyclerItemMoveCallback(object: ItemTouchHelperContract<ListItemCategoryBinding> {
            override fun onRowMoved(fromPosition: Int, toPosition: Int) {
                Timber.d("move position from $fromPosition to $toPosition")
                if(fromPosition < toPosition){
                    for(i in fromPosition until toPosition){
                        Collections.swap(viewModel.getCategoryList(), i, i+1)
                    }
                }else{
                    for(i in fromPosition downTo toPosition + 1){
                        Collections.swap(viewModel.getCategoryList(), i, i-1)
                    }
                }
                categoryAdapter.notifyItemMoved(fromPosition, toPosition)
            }
            override fun onRowClear(viewHolder: GlobalViewHolder<ListItemCategoryBinding>) {
                Timber.d("onRowClear")
                viewHolder.binding.root.setBackgroundColor(Color.GRAY)
            }
            override fun onRowSelected(viewHolder: GlobalViewHolder<ListItemCategoryBinding>) {
                Timber.d("onRowSelected")
                viewHolder.binding.root.setBackgroundColor(Color.WHITE)
            }

        })

//        val touchHelper = ItemTouchHelper(moveCallback)
//        touchHelper.attachToRecyclerView(dataBinding.recViewCategoryMain)

        val dragMove = ItemTouchHelperMove(categoryAdapter, viewModel.getCategoryList())
        dragMove.attachToRecyclerView(dataBinding.recViewCategoryMain)
        dragMove.setOnRowClear { viewModel.saveCategoryPosition() }

        dataBinding.recViewCategoryExtra.addOnItemTouchListener(RecyclerItemClickListener(context) { _, position ->
            val tmpSelected = viewModel.selectedCategoryExtraPosition
            viewModel.selectedCategoryExtraPosition = position
            categoryExtraAdapter.notifyItemChanged(position)
            categoryExtraAdapter.notifyItemChanged(tmpSelected)

            dataBinding.recViewCategoryExtra.post { menuExtraAdapter.notifyDataSetChanged() }

            Firebase.analytics.logEvent(
                    "app_features", bundleOf(
                        "Feature" to "Choose Category Extra",
                        "Outlet" to "CCE:${outlet?.outletId}:${outlet?.name}"
                    )
                )
        })

        dataBinding.recViewMenuExtra.addOnItemTouchListener(RecyclerItemClickListener(context) { _, position ->
            if (viewModel.orders.size > 0 && !viewModel.orders[viewModel.orders.size - 1].isItemVoid) {
                Timber.i("Position $position | productSize ${productsExtraTmp.size}")
                if (position >= 0 && position < productsExtraTmp.size) {
                    if (productsExtraTmp[position].stock == Constant.STOCK_UNAVAILABLE) {
                        if(requireContext().outletFeature().transactionZeroStock){
                            context?.showMessage(getString(R.string.question_adding_no_stock_product),
                                getString(R.string.run_out),
                                { _, _ ->
                                    addExtra(productsExtraTmp[position])
                                })
                        }else{
                            context?.showMessage(getString(R.string.stock_out_warning), getString(R.string.run_out))
                        }
                    } else {
                        addExtra(productsExtraTmp[position])
                    }
                }
            }
        })

        viewModel.selectedCategoryPosition = 0
//        vi.recViewCategoryMain.post { menuAdapter.notifyDataSetChanged() }
        Handler(Looper.getMainLooper()).postDelayed({
            _binding?.recViewCategoryMain?.post { menuAdapter.notifyDataSetChanged() }
        }, 1000)

        Timber.i(">> Category size : ${viewModel.getCategoryList().size}")

        listOf(
            dataBinding.layoutTaxDisc.txtDetailTax, dataBinding.layoutTaxDisc.imgDetail
        ).forEach { view ->
            view.setOnClickListener {
                taxDetailDialog.show()
            }
        }

//        vi.layout_tax_detail.setOnClickListener { taxDetailDialog.show() }
//        vi.txt_tax.setOnClickListener { dialogtax.show() }

        dataBinding.layoutTableList.setOnClickListener {
            //startActivityForResult(Intent(context, TableListActivity::class.java), RC_EDIT)
            (activity as MainActivity).getTransactionMain()?.navigateToTableList()
        }
        dataBinding.layoutNoteList.setOnClickListener {
            //startActivityForResult(Intent(context, TransactionCartActivity::class.java), RC_EDIT)
            (activity as MainActivity).getTransactionMain()?.navigateToCart()
        }
        dataBinding.layoutPayment.setOnClickListener { if (viewModel.orders.isNotEmpty()) saveOrderDialog?.show() }
        dataBinding.layoutMember.setOnClickListener {
            val intent = Intent(context, MemberActivity::class.java)
            intent.putExtra(MemberActivity.INTENT_CURRENT_MEMBER, viewModel.memberId)
            startActivityForResult(intent, RC_MEMBER)
        }

        dataBinding.layoutTaxDisc.txtVoucher.setOnClickListener {
            if (viewModel.promoApplied.none { it.appliedByManual }) {
                applyVoucherDialog.show(childFragmentManager, "voucher")
            } else {
                Bugsnag.leaveBreadcrumb("click voucher detail")

                try {
                    val oldFragment =childFragmentManager.findFragmentByTag("voucher_detail")
                    Timber.i("oldFragment : $oldFragment | added: ${oldFragment?.isAdded}")
                    Bugsnag.leaveBreadcrumb("oldFragment : $oldFragment | added: ${oldFragment?.isAdded}")
                } catch (e: Exception) {
                    Bugsnag.notify(e)
                    Timber.i("err: $e")
                }

                try {
                    voucherDetailDialog.show(childFragmentManager, "voucher_detail")
                } catch (e: Exception) {
                    Bugsnag.notify(e)
                    context?.toast("can not show promo detail information!", level = Level.ERROR)
                }
//                showFreeItemDialog()
            }
        }

        if (!viewModel.isSalesEdit) {
            viewModel.restorePromotion()
        }

        dataBinding.layoutTaxDisc.txtAddFree.setOnClickListener {
            showFreeItemDialog()
        }

        dataBinding.layoutNoteList.visibility = View.GONE
        dataBinding.txtCheckOrder.setOnClickListener {
            startActivity(
                Intent(
                    context, OrderSalesActivity::class.java
                )
            )
        }

        //swipe bill to remove
        val itemTouchHelperBilling =
            ItemTouchHelper(SwipeController(object : SwipeController.SwipeListener {
                override fun enableSwipe(position: Int): Boolean {
                    return !viewModel.orders.getSafe(position)?.isItemVoid.safe() && !viewModel.orders.getSafe(
                        position
                    )?.isHold.safe()
                }

                override fun onSwiped(position: Int) {
                    viewModel.orders.getSafe(position)?.let { orderSwiped ->
                        context?.showMessage(getString(
                            R.string.are_you_sure_delete, orderSwiped.product?.name
                        ), getString(R.string.delete_item), { _, _ ->
                            viewModel.removeBill(orderSwiped)
                            countGrandTotal()
                            dataBinding.recViewBill.adapter?.notifyDataSetChanged()
                            dataBinding.recViewBill.post { dataBinding.layoutTaxDisc.recviewTax.adapter?.notifyDataSetChanged() }
                        })
                        dataBinding.recViewBill.adapter?.notifyItemChanged(position)
                    }
                }
            }))
        itemTouchHelperBilling.attachToRecyclerView(dataBinding.recViewBill)

        dataBinding.layoutError.setOnClickListener {
            startActivity(
                Intent(
                    context, PendingPrintActivity::class.java
                )
            )
        }

        menuDetailDialog = object : MenuDetailDialog(requireContext(), viewModel) {
            override fun onMenuDetailSaved(position: Int) {
                dataBinding.layoutTaxDisc.recviewTax.adapter?.notifyDataSetChanged()
                viewModel.orders.getSafe(position)?.let { order ->
                    viewModel.updateDiscountVoid(order)
                }

                countGrandTotal()
                dataBinding.recViewBill.adapter?.notifyDataSetChanged()
            }
        }

        dataBinding.txtPromotion.setOnClickListener { selectPromotion() }
    }

    private fun showFreeItemDialog(filterPromoId: Int = 0) {
        Timber.i(">> Filter Promo (receive) : $filterPromoId")
        val freeItemListDialog = FreeItemListDialogFragment.newInstance(filterPromoId)
        freeItemListDialog.setViewModel(viewModel)
        freeItemListDialog.show(childFragmentManager, "free_item")
    }

    private fun checkStock(product: ProductEntity, listener: () -> Unit) {
        if (!viewModel.isHasVariant(product.productId) && (product.stock == Constant.STOCK_UNAVAILABLE || (product.stockQty == 0 && product.stockManagement == 1))) {
            val isEnableAdd = context?.outletFeature()?.transactionZeroStock.safe(true)
            val msgId = if(isEnableAdd) R.string.question_adding_no_stock_product else R.string.no_stock_available;
            context?.showMessage(getString(msgId),
                getString(R.string.run_out),
                { _, _ ->
                    if(isEnableAdd) listener()
                })
        } else {
            listener()
        }
    }

    private fun addToBill(
        productEntity: ProductEntity,
        isNeedToCheckLinkMenu: Boolean = true,
        isNeedToCheckVariant: Boolean = true,
        qty: Int = 1
    ) {
        if (isNeedToCheckVariant && viewModel.isHasVariant(productEntity.productId)) {
            object : ChooseVariantDialog(requireContext(), viewModel, productEntity.productId) {
                override fun onItemSelected(productEntity: ProductEntity) {
                    addToBill(productEntity, isNeedToCheckVariant = false)
                }

//                override val lifecycle: Lifecycle
//                    get() = viewLifecycleOwner.lifecycle
            }.show()
            return
        }

        if (isNeedToCheckLinkMenu) {
            val linkMenu =
                viewModel.linkMenuList.filter { it.productDetailFkid == productEntity.productDetailId }
            if (linkMenu.isNotEmpty()) {
//                showLinkMenu(linkMenu, productEntity)
                object : LinkMenuDialog(requireContext(), viewModel) {
                    override fun addToBill(
                        productEntity: ProductEntity,
                        linkMenuSelected: List<LinkMenuProduct>,
                        qty: Int
                    ) {
                        addToBill(
                            productEntity, false, isNeedToCheckVariant = false, qty = qty
                        )
                        linkMenuSelected.forEach {
                            addExtra(
                                it.product.copy(priceSell = it.linkMenuDetail.priceAdd), "link"
                            )
                        }
                    }

//                    override val lifecycle: Lifecycle
//                        get() = viewLifecycleOwner.lifecycle
                }.showLinkMenu(productEntity)
                return
            }
        }

        Timber.i(""">> Add This Product to Bill => ${productEntity.name} | ${productEntity.priceSell} | productId: ${productEntity.productId} | productDetailId: ${productEntity.productDetailId} | QTY : ${productEntity.stockQty}""")

        val order = Order(productEntity, qty)
        viewModel.updatePrice(order)
        viewModel.orders.add(order)
        Timber.d("menu '${order.product?.name}' | info : ${order.info} | price : ${order.price}")
        dataBinding.recViewBill.adapter?.notifyItemInserted(viewModel.orders.size - 1)
        dataBinding.recViewBill.smoothScrollToPosition(viewModel.orders.size - 1)
        dataBinding.recViewBill.post {
            var isNeedToRecalculateTax = viewModel.isNeedToRecalculateTax()
            if (isNeedToRecalculateTax) {
                reCalculateTax()
            } else {
                updateTax(order, qty = qty)
            }

            countGrandTotal()
            viewModel.runGrandTotalWatcher()
            viewModel.checkFreeItemEligibility(order)
            if (!isShowCaseShowed) {
                showCase(ShowCase.Sales_MenuDetail)
            } else if (!isShowCasePart2Showed) {
                showCase(ShowCase.Sales_Swipe_Bill_Delete)
            }
        }
    }

    private fun showProductVariant(product: ProductEntity) {
        runBlocking {
            val data =
                lifecycleScope.async { viewModel.getProductVariantById(product.productId) }.await()
            if (data.isEmpty()) {
                context?.showMessage("Data varian kosong!\nPastikan anda terhubung ke internet!")
            } else {
                val variantNames = data.map { it.variantName }
                AlertDialog.Builder(requireContext()).setTitle(getString(R.string.choose_variant))
                    .setNegativeButton(R.string.cancel, null)
                    .setItems(variantNames.toTypedArray()) { _, position ->
                        selectProductByVariant(data[position])
                    }.show()
            }
        }
    }

    private fun selectProductByVariant(variant: ProductVariantEntity) {
        runBlocking {
            val product =
                lifecycleScope.async { viewModel.getProductByVariantId(variant.variantId) }.await()
            Timber.d("[product varian] ${Gson().toJson(product)}")
            addToBill(
                product.copy(name = product.name + " (${variant.variantName})"),
                isNeedToCheckVariant = false
            )
        }
    }

    //tag : can be extra or link
    private fun addExtra(productEntity: ProductEntity, tag: String = "extra") {
        Timber.i(">> Add Extra : ${productEntity.name}")
        val order = Order(
            productEntity,
            extra = ArrayList(),
            employeeId = context?.employee()?.employeeId ?: 0,
            extraType = tag
        )
        val parentBill = viewModel.orders[viewModel.orders.size - 1]
        viewModel.updatePrice(order)
        val disc =
            viewModel.calculateDiscPerItem(order, viewModel.orders[viewModel.orders.size - 1])

        updateTax(order, disc, qty = parentBill.qty)
        parentBill.extra.add(order)

        dataBinding.recViewBill.adapter?.notifyItemChanged(viewModel.orders.size - 1)
        viewModel.runGrandTotalWatcher()
    }

    private fun observeData() {
        viewModel.loadCustomerNames()

        viewModel.refreshProduct.observe(viewLifecycleOwner) {
            if (searchQuery == null) {
                categoryAdapter.notifyDataSetChanged()
                categoryExtraAdapter.notifyDataSetChanged()
            }
        }

        viewModel.refreshCategory.observe(viewLifecycleOwner) {
            if (searchQuery == null) {
                categoryAdapter?.notifyDataSetChanged()
                categoryExtraAdapter?.notifyDataSetChanged()
            }
        }

        viewModel.getPendingPrintCount().observe(viewLifecycleOwner) { count ->
                if (count == 0) {
                    dataBinding.layoutError.visibility = View.GONE
                } else {
                    showErrorDialog(String.format(getString(R.string.pending_print_count), count))
                }
            }

        viewModel.searchProduct.observe(viewLifecycleOwner) {
            onQueryTextChange(it)
        }

        viewModel.refreshTransactionCartCount.observe(viewLifecycleOwner) {
            dataBinding.txtBadge.text = it?.toString()
            dataBinding.txtBadge.visibility = if (it == 0) View.GONE else View.VISIBLE
        }

        viewModel.barcodeResult.observe(viewLifecycleOwner) {
            it?.let { result ->
                //if product is variant, we don't need to check variant
                context?.toast("${result.first.name?.uppercase()} added to bill")
                addToBill(result.first, isNeedToCheckVariant = !result.second)
            }
        }

        viewModel.refreshOrder.observe(viewLifecycleOwner) {
            dataBinding.recViewBill.adapter?.notifyDataSetChanged()
            dataBinding.layoutTaxDisc.recviewTax.adapter?.notifyDataSetChanged()
            countGrandTotal()
            Timber.d("orders now : ${Gson().toJson(viewModel.orders)}")
        }

        viewModel.taskReceiveCart.observe(viewLifecycleOwner) {
            dataBinding.recViewBill.adapter?.notifyDataSetChanged()
            dataBinding.layoutTaxDisc.recviewTax.adapter?.notifyDataSetChanged()
            countGrandTotal()

            if (viewModel.isMergeBill) {
                showCase(ShowCase.MergeBill_BillList)
                reCalculateTax()
            }
        }

        viewModel.taskRefreshPromo.observe(viewLifecycleOwner) {
            viewModel.promoApplied.firstOrNull()?.let { promo ->
                dataBinding.layoutTaxDisc.txtVoucher.text = promo.displayVoucher
            } ?: run {
                dataBinding.layoutTaxDisc.txtVoucher.text = getString(R.string.have_voucher_code)
            }
            dataBinding.layoutTaxDisc.recviewTax.adapter?.notifyDataSetChanged()
            countGrandTotal()
        }

        viewModel.taskShowVoucherDetail.observe(viewLifecycleOwner) {
            Bugsnag.leaveBreadcrumb("show voucher detail")
            Timber.d("task-show voucher-detail invoked...")

            try {
                val oldFragment =childFragmentManager.findFragmentByTag("voucher_detail")
                Timber.i("oldFragment : $oldFragment | added: ${oldFragment?.isAdded}")
                Bugsnag.leaveBreadcrumb("oldFragment : $oldFragment | added: ${oldFragment?.isAdded}")
            } catch (e: Exception) {
                Bugsnag.notify(e)
                Timber.i("err: $e")
            }

            try {
                voucherDetailDialog.show(childFragmentManager, "voucher_detail")
            } catch (e: Exception) {
                Bugsnag.notify(e)
                context?.toast("can not show promo detail information!", level = Level.ERROR)
            }
        }

        viewModel.taskRefreshBill.observe(viewLifecycleOwner) {
            dataBinding.recViewBill.adapter?.notifyDataSetChanged()
        }

        viewModel.taskRefreshTax.observe(viewLifecycleOwner) {
            dataBinding.layoutTaxDisc.recviewTax.adapter?.notifyDataSetChanged()
        }

        viewModel.taskMemberFound.observe(viewLifecycleOwner) {
            it?.takeIf { it.status }?.data?.let { member ->
                saveOrderDialog?.setMember(member)
                dataBinding.layoutMember.text = member.name?.takeMax(10)
                dataBinding.layoutMember.setTextColor(
                    ContextCompat.getColor(
                        requireContext(), R.color.text_orange
                    )
                )
            } ?: run {
                context?.showMessage(
                    it?.message
                        ?: "data member tidak ditemukan, pastikan member telah terdaftar di bisnis ini",
                    "NOT FOUND"
                )
            }
        }

        viewModel.taskFoundSelfOrder.observe(viewLifecycleOwner) {
            if (it.isNullOrEmpty()) {
                context?.showMessage("Item tidak ditemukan!")
                (activity as MainActivity).getTransactionMain()
                    ?.updateSelfOrderDialogUi("item tidak ditemukan")
            } else {
                (activity as MainActivity).getTransactionMain()?.updateSelfOrderDialogUi()
//                it.forEach { order -> addToBill(order.product!!, isNeedToCheckLinkMenu = false, isNeedToCheckVariant = false, qty = order.qty) }
            }
        }

        viewModel.getPendingOrderCountLive().observe(viewLifecycleOwner) {
            Timber.i(">>> count : $it")
            if (it.safe() > 0) {
                dataBinding.txtPendingOrder.text = getString(R.string.pending_order_count, it)
                dataBinding.txtPendingOrder.setVisible(true)
                dataBinding.txtCheckOrder.setVisible(true)
            } else {
                dataBinding.txtPendingOrder.setVisible(false)
                dataBinding.txtCheckOrder.setVisible(false)
            }
        }

        viewModel.taskShowTutorialProduct.observe(viewLifecycleOwner) {
            dataBinding.layoutTutorialProduct.root.setVisible(it)
        }

        viewModel.taskAddBillNoVariant.observe(viewLifecycleOwner) { data ->
            checkStock(data.first) {
                addToBill(data.first, qty = data.second, isNeedToCheckVariant = false)
            }
        }

        viewModel.taskCartSaved.observe(viewLifecycleOwner) { sales ->
            resetTransaction()
            saveOrderDialog?.dismiss()
            printSales(
                sales, true, isSendToServer = true
            )
        }

        viewModel.taskMemberRemoved.observe(viewLifecycleOwner) {
            dataBinding.layoutMember.text = "Member"
            dataBinding.layoutMember.setTextColor(
                ContextCompat.getColor(
                    requireContext(), R.color.text_grey_light
                )
            )
        }

        viewModel.taskPromotionListRefresh.observe(viewLifecycleOwner) {
            dataBinding.txtPromotion.setVisible(viewModel.promotionList.isNotEmpty())
        }

        viewModel.taskShowFreeItemDialog.observe(viewLifecycleOwner) { idFilter ->
            showFreeItemDialog(idFilter.safe())
        }
    }

    private fun initPopUpFinishOrder() {
        saveOrderDialog = object : SaveOrderDialog(viewModel, requireActivity()) {
            override fun getFragmentManager(): FragmentManager = fragmentManager!!
            override fun resetTransactionView() {
                resetTransaction()
            }

            override fun scanMember(intent: Intent, requestCode: Int) {
                startActivityForResult(intent, requestCode)
            }

//            override fun payOrder(sales: SalesEntity) {
//                val salesJson = Gson().toJson(sales)
//                val contact = viewModel.contactSelfOrder ?: viewModel.memberDetail?.phone
//                val useNewUi = true // Firebase.remoteConfig.getBoolean("ui_payment")
//                val intent = if(useNewUi) Intent(context, PaymentV2Activity::class.java) else Intent(context, PaymentActivity::class.java)
////                intent.putExtra("sales", sales)
////                intent.putExtra("sales-json", salesJson)
//
////                intent.putExtra("sales-json-static", true)
////                DataHolder.setData(salesJson)
//
////                intent.putExtra("merge_ids", viewModel.mergeIds)
////                intent.putExtra("receipt_receiver", contact)
////                intent.putExtra(PaymentActivity.INTENT_PAY_FLOW,
////                    viewModel.salesEdit?.let { "Transaction - Pending Bill - Edit Transaction" }
////                        ?: kotlin.run { "Transaction" })
//
////                Firebase.analytics.logEvent(
////                        FirebaseAnalytics.Event.BEGIN_CHECKOUT, bundleOf(
////                            FirebaseAnalytics.Param.CURRENCY to "IDR",
////                            FirebaseAnalytics.Param.VALUE to sales.grandTotal.toString()
////                        )
////                    )
//
//                dismiss()
//                startActivityForResult(intent, RC_PAYMENT)
//            }

            override fun startActivityWithResult(intent: Intent, requestCode: Int) {
                startActivityForResult(intent, requestCode)
//                resultLauncher.launch(intent)
            }
        }
    }

    fun initVisibilityViewFinishOrder() {
//        val role = role()
        context?.outletFeature()?.let { feature ->
            dataBinding.layoutMember.visibility = feature.member.visibility()
//            vi.layoutNoteList.visibility = (feature.notelist && role.simpankeorderlist).visibility()
            dataBinding.layoutTableList.visibility = feature.tableinput.visibility()
        }
    }

    private fun initPopUpTaxDetail() {
        viewModel.discount = Discount()

        taxDetailDialog = object : TaxDetailDialog(requireContext(), viewModel) {
            override fun startActivityWithResult(intent: Intent, requestCode: Int) {
                startActivityWithResult(intent, requestCode)
            }

            override fun onTaxSaved() {
                countGrandTotal()
                dataBinding.layoutTaxDisc.recviewTax.adapter?.notifyDataSetChanged()
            }

//            override val lifecycle: Lifecycle = <EMAIL>
        }
    }

    private fun initCamera() {
        try {
            val barcodeDetector = BarcodeDetector.Builder(context)
                .setBarcodeFormats(Barcode.EAN_13 or Barcode.QR_CODE or Barcode.EAN_8 or Barcode.CODABAR or Barcode.DATA_MATRIX or Barcode.UPC_A)
                .build()

            if (!barcodeDetector.isOperational) {
                Timber.i("[WARN] barcode detector is not operational...")
            }

            cameraSource = CameraSource.Builder(context, barcodeDetector).setAutoFocusEnabled(true)
                .setRequestedPreviewSize(1600, 1024).build()
            barcodeDetector.setProcessor(object : Detector.Processor<Barcode> {
                override fun receiveDetections(detections: Detector.Detections<Barcode>?) {
                    val barcodes = detections?.detectedItems
                    barcodes?.let {
                        if (barcodes.size() > 0) {
                            //play beep sound
                            mp?.let {
                                if (it.isPlaying) it.stop()
                                it.release()
                            }
                            mp = MediaPlayer.create(context, R.raw.beep)
                            mp?.start()

                            val product =
                                productsMainTmp.firstOrNull { it.sku == barcodes.valueAt(0).displayValue }
                            product?.let {
                                runBlocking {
                                    startCamera(false)
                                    addToBill(product)
                                    delay(1000)
                                    startCamera()
                                }
                            }
                                ?: kotlin.run { context?.toast(getString(R.string.sku_not_registered)) }
                        }
                    }
                }

                override fun release() {}
            })
//            vi.camera_preview.holder.setFormat(PixelFormat.TRANSPARENT)
        } catch (e: Exception) {
            context?.toast("init camera error $e")
        }
    }

    private fun showVoidDialog(order: Order, employee: Employee) {
        VoidDialog(requireContext(), order, employee, viewModel).setOnVoidSaved {
                Timber.i(">> Void Item ${order.product?.name} ")
                if (viewModel.isParentFromPromo(order)) {
                    viewModel.removeFreeItemByParentId(order.tmpId)
                }
                dataBinding.recViewBill.adapter?.notifyItemInserted(viewModel.orders.size - 1)
                dataBinding.recViewBill.post {
                    viewModel.runGrandTotalWatcher()
                    dataBinding.layoutTaxDisc.recviewTax.adapter?.notifyDataSetChanged()

                    val sales = viewModel.getSalesEntity()
                    val tmpSales = TmpSalesEntity(
                        sales.noNota, Gson().toJson(sales), context?.outlet()?.outletId!!
                    )
                    viewModel.salesEdit?.let { viewModel.updateTmpSale(tmpSales) }
                    dataBinding.layoutTaxDisc.recviewTax.adapter?.notifyDataSetChanged()
                    dataBinding.recViewBill.smoothScrollToPosition(viewModel.orders.size - 1)
                }
            }.show()
    }

    private fun reCalculateTax() {
        viewModel.reCalculateTax()
        Timber.i(">> reCalculateTax. TAX : ${Gson().toJson(viewModel.taxSaleList)}")
        dataBinding.layoutTaxDisc.recviewTax.adapter?.notifyDataSetChanged()
    }

    private fun updateTax(order: Order, discount: Int = 0, isAdd: Boolean = true, qty: Int = 1) {
        viewModel.updateTax(order, discount, isAdd, qty)
        viewModel.recalculateDiscAndVoucher()
        dataBinding.layoutTaxDisc.recviewTax.adapter?.notifyDataSetChanged()
    }

    private fun countGrandTotal() {
        dataBinding.txtGrandTotal.text = viewModel.calculateGrandTotal().toCurrency()
    }

    private fun showMenuDetail(position: Int) {
        if (position >= viewModel.orders.size || position < 0) {
            context?.toast("item not found!")
            return
        }

        if (viewModel.orders[position].isItemVoid) {
            context?.toast(getString(R.string.warn_show_detail_item_void))
            return
        }
//        MenuDetailDialog(context!!, viewModel.orders[position]).show()

        Timber.i(">> SHOW MENU DETAIL : ${Gson().toJson(viewModel.orders[position])}")

        menuDetailDialog.show(position)
    }

    private fun showCamera(isShow: Boolean) {
        if (isShow && Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            if (ActivityCompat.checkSelfPermission(
                    requireContext(), Manifest.permission.CAMERA
                ) != PackageManager.PERMISSION_GRANTED
            ) {
                ActivityCompat.requestPermissions(
                    activity?.parent!!, arrayOf(Manifest.permission.CAMERA), RC_CAMERA
                )
                return
            }
        }
        try {
//            vi.camera_preview.visibility = if (isShow) View.VISIBLE else View.GONE
//            vi.layout_category.visibility = if (!isShow) View.VISIBLE else View.GONE
            dataBinding.recViewMenuMain.visibility = if (!isShow) View.VISIBLE else View.GONE
            dataBinding.recViewMenuExtra.visibility = if (!isShow) View.VISIBLE else View.GONE

//            vi.camera_preview.post {
//                startCamera()
//            }
        } catch (e: Exception) {
            context?.toast("Failed to start camera ${e.message}")
        }
    }

    @SuppressLint("MissingPermission")
    private fun startCamera(isStart: Boolean = true) {
        try {
//            if (isStart) cameraSource.start(vi.camera_preview.holder)
//            else cameraSource.stop()
        } catch (e: Exception) {
            context?.toast("Failed to start camera. $e")
        }
    }

    private fun onQueryTextChange(query: String?): Boolean {
        productsMainTmp.clear()
        productsExtraTmp.clear()
        searchQuery = query
        val tmpProducts =
            if (viewModel.selectedCategoryId < 0) viewModel.productsUnique else viewModel.productsUnique.filter { p -> p.productSubcategoryFkid!! == viewModel.selectedCategoryId }
        val tmpExtra =
            if (viewModel.selectedCategoryExtraId < 0) viewModel.productsUnique else viewModel.productsUnique.filter { p -> p.productSubcategoryFkid == viewModel.selectedCategoryExtraId }
        if (!query.isNullOrEmpty()) {
            productsMainTmp.addAll(tmpProducts.filter { p -> searchProductByKeyword(query, p) })
            productsExtraTmp.addAll(tmpExtra.filter { p ->
                p.name?.lowercase()?.contains(query.lowercase()) == true
            })
            logSearch()
        } else {
            productsMainTmp.addAll(tmpProducts)
            productsExtraTmp.addAll(tmpExtra)
        }
        dataBinding.recViewMenuMain.adapter?.notifyDataSetChanged()
        dataBinding.recViewMenuExtra.adapter?.notifyDataSetChanged()
        return true
    }

    private fun logSearch() {
        if (!isSearchLogged) {
            isSearchLogged = true
            val outlet = context?.outlet()
            Firebase.analytics.logEvent(
                    "app_features", bundleOf(
                        "Feature" to "Search Menu",
                        "Outlet" to "SM:${outlet?.outletId}:${outlet?.name}"
                    )
                )
        }
    }


    override fun onCreateOptionsMenu(menu: android.view.Menu, inflater: MenuInflater) {
        Timber.i("[LIFE] onCreateOptionsMenu")
        inflater.inflate(R.menu.menu_transaction, menu)
        menu.findItem(R.id.action_change_layout)
            ?.setIcon(if (isGridLayout) R.drawable.ic_view_list_white_24dp else R.drawable.ic_view_module_white_24dp)
        menu.findItem(R.id.action_show_extra)
            ?.setTitle(if (dataBinding.isShowExtra == true) R.string.hide_extra_menu else R.string.show_extra_menu)
        super.onCreateOptionsMenu(menu, inflater)
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        when (item.itemId) {
//            item?.itemId == R.id.action_barcode -> showCamera(vi.camera_preview.visibility != View.VISIBLE)
//            startActivity(Intent(context, TecScanActivity::class.java))
            R.id.action_barcode -> {
                val intent = Intent(context, ScanBarcodeActivity::class.java)
                intent.putExtra("product", viewModel.products)
                startActivityForResult(intent, RC_SCAN)
            }
            R.id.action_change_layout -> {
                isGridLayout = !isGridLayout
                changeMenuLayout()
            }
            R.id.action_show_extra -> showHideExtraMenu()
//            R.id.action_self_order -> getSelfOrder()
        }
        return super.onOptionsItemSelected(item)
    }

    private fun getSelfOrder() {
        val bottomDialog = BottomDialogInput()
        bottomDialog.setOnButtonClick { code ->
            viewModel.findSelfOrder(code, context?.outlet()?.outletId)
            bottomDialog.dismiss()
        }
        bottomDialog.setUseBarcode(true)
        bottomDialog.setModel(
            BottomDialogModel(
                "SELF ORDER X",
                "CHECK",
                "order code",
                hint = "order code",
                inputType = InputType.TYPE_CLASS_NUMBER
            )
        )
        bottomDialog.show(childFragmentManager, "self-order")
    }

    private fun changeMenuLayout() {
//        vi.recViewMenuMain.layoutAnimation = AnimationUtils.loadLayoutAnimation(context, R.anim.layout_animation_fall_down)
        var span = context?.resources?.getInteger(R.integer.menu_span_count) ?: 2
        if (dataBinding.isShowExtra == false) span++
        if (isGridLayout) menuAdapter.changeLayout(R.layout.list_item_menu) else menuAdapter.changeLayout(
            R.layout.list_item_menu_card
        )
        dataBinding.recViewMenuMain.layoutManager =
            if (isGridLayout) GridLayoutManager(context, span) else LinearLayoutManager(context)
        dataBinding.recViewMenuMain.adapter = menuAdapter
        activity?.invalidateOptionsMenu()
        context?.putData(SharedPref.SETTING_LAYOUT_TRANSACTION, isGridLayout)
    }

    private fun showHideExtraMenu() {
        if (isGridLayout) {
            val tmpAdapter = dataBinding.recViewMenuMain.adapter
            var span =
                context?.resources?.getInteger(R.integer.menu_span_count)!! + if (dataBinding.isShowExtra == true) 1 else 0
            dataBinding.recViewMenuMain.layoutManager = GridLayoutManager(context, span)
            dataBinding.recViewMenuMain.adapter = tmpAdapter
        }
        dataBinding.isShowExtra = dataBinding.isShowExtra != true
        activity?.invalidateOptionsMenu()
        context?.sharedPref()
            ?.putData(SharedPref.SETTING_HIDE_EXTRA, (dataBinding.isShowExtra == true))
    }

    fun setReservationData(reservationEntity: ReservationEntity) {
        dataBinding.layoutMember.text = reservationEntity.name
    }

    override fun onRequestPermissionsResult(
        requestCode: Int, permissions: Array<out String>, grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        if (requestCode == RC_CAMERA) showCamera(true)
    }

    private fun handleActivityResult(result: ActivityResult){
        saveOrderDialog?.onActivityResult(RESULT_OK, result.resultCode, result.data)
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        saveOrderDialog?.onActivityResult(requestCode, resultCode, data)
        if (resultCode == RESULT_OK) {
            if (requestCode == RC_PAYMENT || requestCode == SaveOrderDialog.RC_PAYMENT) {
                resetTransaction()
                val sales = data?.extras?.getParcelable<SalesEntity>("sales")
                val mergeIds = data?.extras?.getSerializable("marge_ids") as? List<String>?
                Timber.i(">> Got data and Print... is data null? (${sales == null}) | mergeIds : $mergeIds")
                sales?.let {
                    viewModel.syncSales(sales)
                    viewModel.updateCartToPaid(mergeIds)
                    printSales(sales)
                    viewModel.restorePromotion()
                }
            } else if (requestCode == RC_EXTRA) {
                data?.let {
                    val position = data.getIntExtra("position", -1)
                    val extras = data.getSerializableExtra("extra") as ArrayList<Order>
                    viewModel.orders[position].extra.clear()
                    viewModel.orders[position].extra.addAll(extras.filter { it.qty > 0 })
                    dataBinding.recViewBill.adapter?.notifyItemChanged(position)
                    reCalculateTax()
                }
            } else if (requestCode == RC_TABLE) {
                val table = data?.getStringExtra("table")
//                saveOrderDialog?.setField(table.toString())
                Timber.i("onActivityResult request code is RC_TABLE $RC_TABLE | table : $table")
            } else if (requestCode == RC_MEMBER) {
                val dataIntent = data?.getParcelableExtra("data") as? Any
                val action = data?.getStringExtra("action")
                if (dataIntent is Order) {
                    viewModel.orders.add(dataIntent)
                    dataBinding.recViewBill.adapter?.notifyItemInserted(viewModel.orders.size - 1)
                    dataBinding.recViewBill.post { countGrandTotal() }
                } else if (dataIntent is MemberEntity) {
                    Timber.d("got member: $dataIntent")
                    val member = dataIntent.covertToMember()
                    if (viewModel.memberDetail != null && viewModel.memberDetail?.memberId != member.memberId && viewModel.isUseMemberPromo()) {
                        context?.showMessage("apakah anda akan mengubah member dari ${viewModel.memberDetail?.name} ke ${member.name}?\n\n" + "promo yang telah digunakan akan otomatis terhapus",
                            "KONFIRMASI",
                            positiveAction = { _, _ ->
                                viewModel.removeMember()
                                viewModel.removeMemberPromo()
                                viewModel.setMember(member)
                                viewModel.taskMemberFound.postValue(
                                    ServerResponse(
                                        status = true, data = member
                                    )
                                )
                            })
                    } else {
                        viewModel.setMember(member)
                        viewModel.taskMemberFound.postValue(
                            ServerResponse(
                                status = true, data = member
                            )
                        )
                    }
                    Timber.i("onActivityResult request code is RC_MEMBER $RC_MEMBER | name : ${dataIntent.name}")
                } else if (action == "remove") {
                    val msg =
                        if (viewModel.isUseMemberPromo()) context?.getString(R.string.warn_sales_use_member_promo) else context?.getString(
                            R.string.warn_remove_member
                        )
                    context?.showMessage(msg, "CONFIRMATION", { _, _ ->
                        viewModel.removeMember()
                        viewModel.removeMemberPromo()
                    })
                }
            } else if (requestCode == RC_SCAN) {
                data?.extras?.getParcelableArrayList<Order>("order")?.let { order ->
                    viewModel.orders.addAll(order)
                    viewModel.orders.forEach { viewModel.updatePrice(it) }
                    viewModel.reCalculateTax()

                    dataBinding.recViewBill.adapter?.notifyDataSetChanged()
                    dataBinding.layoutTaxDisc.recviewTax.adapter?.notifyDataSetChanged()
                    countGrandTotal()
                }
            }
        }
    }

    private fun showCase(showCaseType: String = "") {
        val employee = context.employee()
        val key = "$showCaseType ${employee?.employeeId}"
        val showCaseData = when (showCaseType) {
            ShowCase.MergeBill_BillList -> {
                ShowCaseData(
                    "Merge Bill Item", "Data item yang sudah di Merge", dataBinding.recViewBill
                ) { showCase(ShowCase.MergeBill_SaveBill) }
            }
            ShowCase.MergeBill_SaveBill -> {
                ShowCaseData(
                    "Save Merge Bill",
                    "Proses Merge Bill belum disimpan sampai " + "kamu mengklik tombol Simpan/Bayar pada Pop Up yang muncul saat kamu mengklik tombol Pembayaran",
                    dataBinding.layoutPayment
                ) { }
            }
            ShowCase.Sales_MenuDetail -> {
                ShowCaseData(
                    "Menu Detail",
                    "Untuk bisa menambahkan Discount per item ataupun " + "Merubah Harga dengan mengklik salah satu item ini",
                    dataBinding.recViewBill
                ) { showCase(ShowCase.Sales_TaxAndGratuityDetail) }
            }
            ShowCase.Sales_TaxAndGratuityDetail -> {
                ShowCaseData(
                    "Tax & Gratuity",
                    "Kamu bisa menambahkan Discount, Voucher " + "ataupun mengaktifkan tax & gratutity dengan mengklik di sini",
                    dataBinding.layoutTaxDisc.txtDetailTax
                ) { }
            }

            ShowCase.Sales_Swipe_Bill_Delete -> {
                ShowCaseData(
                    "Hapus Item",
                    "Untuk menghapus item dari bill, cukup geser ke kanan pada item yang ingin di hapus",
                    dataBinding.recViewBill
                ) { }
            }

            else -> null
        }

        showCaseData?.let {
            if (!requireContext().getLocalDataBoolean(key)) {
                GuideView.Builder(context).setTitle(showCaseData.title)
                    .setContentText(showCaseData.message).setTargetView(showCaseData.view)
                    .setTitleTextSize(17).setDismissType(DismissType.anywhere)
                    .setGuideListener { showCaseData.next.invoke() }.build().show()
                context?.putData(key, true)
            }
        }
    }

    private fun printSales(
        sales: SalesEntity, isOnlyPrintKitchen: Boolean = false, isSendToServer: Boolean = false
    ) {
        (activity as MainActivity).getTransactionMain()?.apply {
            printSales(sales, isOnlyPrintKitchen)
            if (isSendToServer) {
                sendSalesCartToLocalServer(
                    TmpSalesEntity(
                        sales.noNota, Gson().toJson(sales), outlet.outletId.safe()
                    )
                )
            }
        }
    }

    private fun showErrorDialog(msg: String) {
        dataBinding.layoutError.text = msg
        dataBinding.layoutError.visibility = View.VISIBLE
    }

    private fun resetTransaction() {
        viewModel.promoApplied.clear()
        viewModel.resetTransaction()
        saveOrderDialog?.resetView()
        dataBinding.recViewBill.adapter?.notifyDataSetChanged()
        dataBinding.layoutTaxDisc.recviewTax.adapter?.notifyDataSetChanged()
        dataBinding.txtGrandTotal.text = "0"
    }

    private fun selectPromotion(isSelected: Boolean = true) {
        dataBinding.txtPromotion.setTextColor(
            Utils.getColor(
                requireContext(), if (isSelected) R.color.text_orange else R.color.text_default
            )
        )

        dataBinding.txtPromotion.setBackgroundColor(if (isSelected) Color.parseColor("#353F4C") else 0)
        ImageViewCompat.setImageTintList(
            dataBinding.imgPromo, ColorStateList.valueOf(
                if (isSelected) Color.parseColor("#F2AE4A") else Color.parseColor("#96999F")
            )
        )

        if (!isSelected) {
            return
        }

        val tmpPosition = viewModel.selectedCategoryPosition
        viewModel.selectedCategoryPosition = -1
        categoryAdapter.notifyItemChanged(tmpPosition)

        dataBinding.recViewMenuMain.setVisible(false)
        dataBinding.containerMain.setVisible(true)

        childFragmentManager.commit {
            replace(R.id.container_main, PromotionListFragment.newInstance(viewModel.promotionList))
        }
//        childFragmentManager.beginTransaction().replace(
//                R.id.container_main, PromotionListFragment.newInstance(viewModel.promotionList)
//            ).commit()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

}

