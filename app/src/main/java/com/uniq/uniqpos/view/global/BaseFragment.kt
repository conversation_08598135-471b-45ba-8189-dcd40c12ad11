package com.uniq.uniqpos.view.global

import android.app.AlertDialog
import android.app.ProgressDialog
import android.content.Context
import android.content.SharedPreferences
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.LayoutInflater
import android.view.Menu
import android.view.MenuInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.annotation.LayoutRes
import androidx.databinding.DataBindingUtil
import androidx.databinding.ViewDataBinding
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import com.bugsnag.android.Bugsnag
import com.google.gson.Gson
import com.gu.toolargetool.TooLargeTool
import com.uniq.uniqpos.BuildConfig
import com.uniq.uniqpos.data.local.entity.PendingPrintEntity
import com.uniq.uniqpos.data.local.entity.TmpSalesEntity
import com.uniq.uniqpos.data.local.sharedpref.SharedPref
import com.uniq.uniqpos.data.local.sharedpref.getJson
import com.uniq.uniqpos.data.local.sharedpref.getLocalDataBoolean
import com.uniq.uniqpos.data.local.sharedpref.getLocalDataString
import com.uniq.uniqpos.data.local.sharedpref.sharedPref
import com.uniq.uniqpos.data.remote.model.Employee
import com.uniq.uniqpos.data.remote.model.Outlet
import com.uniq.uniqpos.model.SocketMessage
import com.uniq.uniqpos.util.decrypt
import com.uniq.uniqpos.util.printer.PrinterManager
import com.uniq.uniqpos.util.printer.PrinterSocket
import com.uniq.uniqpos.util.safe
import dagger.android.support.AndroidSupportInjection
import timber.log.Timber
import javax.inject.Inject

/**
 * Created by ANNASBlackHat on 12/10/2017.
 */

abstract class BaseFragment<VM : ViewModel, DB : ViewDataBinding> : Fragment(), SharedPrefListener {

//    private val lifecycleRegistry = LifecycleRegistry(this)

    @Inject
    lateinit var viewModelFactory: ViewModelProvider.Factory

    @Inject
    lateinit var printerManager: PrinterManager

    @Inject
    lateinit var printerSocket: PrinterSocket

    var mContext: Context? = null

    lateinit var viewModel: VM

    var _binding: DB? = null
    val binding get() = _binding!!

    private lateinit var pDialog: ProgressDialog
    lateinit var outlet: Outlet
        private set
    lateinit var employee: Employee
        private set

    private var sharedPref: SharedPreferences? = null
    private var sharedPrefChangeListener: SharedPreferences.OnSharedPreferenceChangeListener? = null

    @get:LayoutRes
    abstract val layoutRes: Int

    abstract fun getViewModel(): Class<VM>

    var isSubscribeToSharedPref = false

    override fun onCreate(savedInstanceState: Bundle?) {
        AndroidSupportInjection.inject(this)
        super.onCreate(savedInstanceState)
        viewModel = ViewModelProvider(this, viewModelFactory).get(getViewModel())
//        viewModel = ViewModelProviders.of(this, viewModelFactory).get(getViewModel())
        savedInstanceState?.let { state ->
            val report =  TooLargeTool.bundleBreakdown(state)
            Timber.d("bundleBreakdown: $report")
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        Bugsnag.leaveBreadcrumb("create view: $layoutRes")
        _binding = DataBindingUtil.inflate(inflater, layoutRes, container, false)

        outlet = context?.getJson(SharedPref.OUTLET_DATA, Outlet::class.java) ?: Outlet()
        employee = context?.getJson(SharedPref.EMPLOYEE_DATA, Employee::class.java) ?: Employee()

        pDialog = ProgressDialog(context)
        pDialog.setMessage("Loading...")
        pDialog.setCancelable(false)


        Timber.i("printer manager : $printerManager")
        return _binding!!.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        isSubscribeToSharedPref = false //force to false, just temporary, check if this causing force close
        Timber.i("isSubscribeToSharedPref: $isSubscribeToSharedPref")
        context?.takeIf { isSubscribeToSharedPref }?.apply {
//            context?.sharedPref()?.getSharedPreference()?.registerOnSharedPreferenceChangeListener { _, key ->
//                onSharedPrefChanged(key.decrypt())
//            }

            sharedPref = context?.sharedPref()?.getSharedPreference()
            sharedPrefChangeListener =
                SharedPreferences.OnSharedPreferenceChangeListener { _, key ->
                    onSharedPrefChanged(key.decrypt())
                }
            sharedPref?.registerOnSharedPreferenceChangeListener(sharedPrefChangeListener)
        }
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        mContext = context
    }

    override fun onSharedPrefChanged(key: String) {
        Timber.i("main ... shared pref changed : $key")
    }

    fun toast(
        msg: String?,
        duration: Int = Toast.LENGTH_SHORT,
        isShowOnProduction: Boolean = false
    ) {
        if (msg == null) return
        if (!BuildConfig.DEBUG && !isShowOnProduction) return
        if (Looper.myLooper() == Looper.getMainLooper()) {
            Toast.makeText(context?.applicationContext, msg.safe(), duration).show()
        } else {
            Handler(Looper.getMainLooper()).post {
                Toast.makeText(context?.applicationContext, msg.safe(), duration).show()
            }
        }
        Timber.i("[[TOAST]] '$msg'")
    }

    fun showDialog(isShow: Boolean, message: String? = "Loading...") {
        if (isShow) {
            if (pDialog.isShowing) {
                pDialog.setMessage(message)
            } else {
                Bugsnag.leaveBreadcrumb("activity finishing: ${activity?.isFinishing}")
                pDialog.setMessage(message ?: "Loading...")
                pDialog.show()
                Timber.i("[PROGRESS_DIALOG] $message")
            }
        } else {
            if (pDialog.isShowing) {
                pDialog.dismiss()
            }
        }
    }

    fun isProgressDialogShowing() = pDialog.isShowing

    fun setDialogMessage(msg: String? = "Loading...") {
        pDialog.setMessage(msg)
    }

    fun showMsg(msg: String?, title: String? = null): AlertDialog? {
        Timber.i("[DIALOG] : $msg")
        return context?.let {
            AlertDialog.Builder(it)
                .setTitle(title)
                .setMessage(msg ?: "Error")
                .setPositiveButton("OK", null)
                .show()
        } ?: run { null }
    }

    fun beginPrint(list: List<PendingPrintEntity>, listener: PrinterManager.PrintListener? = null) {
        printerManager.managePrint(list, printerSocket, listener)
    }

    fun managePrintWifi(
        list: List<PendingPrintEntity>,
        listener: PrinterSocket.SocketListener? = null
    ) {
        if (list.isEmpty()) return

        val isServer = context?.getLocalDataBoolean(SharedPref.PRINTER_SERVER_STATUS) ?: false
        val ipServer = context?.getLocalDataString(SharedPref.IP_SERVER, "")

        if (!isServer && ipServer?.isNotEmpty() == true) {
            val req = Gson().toJson(
                SocketMessage(
                    "print_list",
                    Gson().toJson(list.filter { (it.dataString?.length ?: 0) > 10 }),
                    "${Build.BRAND} ${Build.MODEL} - ${Build.SERIAL}"
                )
            )
            printerSocket.sendToServer(req, listener)
        } else {
            Timber.i("Add printer to queue. Size : ${list.size}")
            printerManager.addAllToQueue(list.filter { (it.dataString?.trim()?.length ?: 0) > 5 })
        }
    }

    fun isPrintingInProcess() = printerManager.isPrintingInProcess()

    fun printerManager() = printerManager

    protected fun sendSalesToLocalServer(sales: TmpSalesEntity) {
        val isServer = context?.getLocalDataBoolean(SharedPref.PRINTER_SERVER_STATUS) ?: false
        val ipServer = context?.getLocalDataString(SharedPref.IP_SERVER, "")

        if (!isServer && ipServer?.isNotEmpty() == true) {
            val req = Gson().toJson(
                SocketMessage(
                    "sales_cart",
                    Gson().toJson(sales),
                    "${Build.BRAND} ${Build.MODEL} - ${Build.SERIAL}"
                )
            )
            printerSocket.sendToServer(req)
        }
    }

    fun getBaseFragment(): BaseFragment<VM, DB> {
        return this
    }

    override fun onCreateOptionsMenu(menu: Menu, inflater: MenuInflater) {
        Timber.d("[LIFE] #onCreateOptionsMenu")
        super.onCreateOptionsMenu(menu, inflater)
    }

    override fun onResume() {
        super.onResume()
        sharedPref?.registerOnSharedPreferenceChangeListener(sharedPrefChangeListener)
    }

    override fun onPause() {
        sharedPref?.unregisterOnSharedPreferenceChangeListener(sharedPrefChangeListener)
        super.onPause()
    }

    override fun onDestroy() {
        if (pDialog.isShowing) pDialog.dismiss()
        super.onDestroy()
        Timber.d("[LIFE] #onDestroy")
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
//        clearFindViewByIdCache()
    }
}
