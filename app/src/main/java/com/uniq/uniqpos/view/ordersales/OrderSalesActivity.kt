package com.uniq.uniqpos.view.ordersales

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.Build
import android.os.Bundle
import android.view.Menu
import android.view.MenuItem
import androidx.lifecycle.Observer
import androidx.viewpager.widget.ViewPager
import com.google.gson.Gson
import com.uniq.uniqpos.R
import com.uniq.uniqpos.data.local.entity.OrderSalesEntity
import com.uniq.uniqpos.data.local.entity.SalesEntity
import com.uniq.uniqpos.databinding.ActivityOrderSalesBinding
import com.uniq.uniqpos.util.*
import com.uniq.uniqpos.util.lifecycle.setupDialogMessage
import com.uniq.uniqpos.util.lifecycle.setupToastMessage
import com.uniq.uniqpos.util.printer.PrinterSocket
import com.uniq.uniqpos.view.global.BaseActivity
import com.uniq.uniqpos.view.transactionhistory.HistoryDetailActivity
import timber.log.Timber

class OrderSalesActivity : BaseActivity<OrderSalesViewModel, ActivityOrderSalesBinding>() {

    override fun getLayoutRes() = R.layout.activity_order_sales
    override fun getViewModel() = OrderSalesViewModel::class.java

    private lateinit var adapter: ViewPagerAdapter

    //private lateinit var orderSalesPending: OrderSalesListFragment
//    private lateinit var orderSalesAccept: OrderSalesListFragment
//    private lateinit var orderSalesReady: OrderSalesListFragment
    private val orderSalesPages = ArrayList<OrderSalesListFragment>()
    private lateinit var listener: OrderSalesListener
    private lateinit var broadcastReceiver: BroadcastReceiver
    private var selectedPage = 0
    private var isUseTabLayout = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        supportActionBar?.elevation = 0.0f
        title = "Order Online"

        viewModel.loadProduct()
        viewModel.loadGratuity()
        val outletId = outlet()?.outletId ?: 0

        broadcastReceiver = object : BroadcastReceiver() {
            override fun onReceive(context: Context?, intent: Intent?) {
                when (intent?.getStringExtra("sync")) {
                    "order_sales" -> viewModel.syncOrderSales(outletId)
                }
            }
        }
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                registerReceiver(broadcastReceiver, IntentFilter(Constant.INTENT_SYNC_REQUEST), RECEIVER_NOT_EXPORTED)
            } else {
                @Suppress("UnspecifiedRegisterReceiverFlag") 
                registerReceiver(broadcastReceiver, IntentFilter(Constant.INTENT_SYNC_REQUEST))
            }
        } catch (e: Exception) {
            Timber.i("register receiver error $e")
        }

//        isUseTabLayout = resources.getBoolean(R.bool.landscape_only)
    }

    override fun initView() {
        super.initView()

        listener = object : OrderSalesListener {
            override fun getOrderSales() = viewModel.orderSalesList
            override fun onItemSelected(orderSales: OrderSalesEntity) {
                viewModel.fetchSalesEntity(orderSales)
            }

            override fun updateStatus(orderId: String, status: String, info: String?) {
                Timber.i("update to : $status")
                viewModel.updateOrderSalesById(
                    orderId,
                    status,
                    info,
                    outlet()!!,
                    employee()!!,
                    shiftOpen()!!
                )
            }
        }

        adapter = ViewPagerAdapter(supportFragmentManager)

        val statusFilter = intent.getStringExtra("status_filter").safe()
        val statusList = mapOf("reject" to "Reject")
        val pageFilter = HashMap<String, String>()
        if(statusFilter.isNotBlank()){
            statusFilter.split(",").forEach { key ->
                statusList[key]?.let { v ->
                    pageFilter[key] = v
                }
            }
        }

        employee()
        //key: status (based on db), value: title displayed in app
        val pages = if(pageFilter.isEmpty()) mapOf("pending" to "Pending", "payment_verified" to "Paid", "ready" to "Ready") else pageFilter
        for (page in pages) {
            val fragmentPage = OrderSalesListFragment.newInstance(page.key, listener)
            orderSalesPages.add(fragmentPage)
            adapter.addFragment(fragmentPage, page.value)
        }

        binding.viewPager.adapter = adapter
        binding.tablayout.post { binding.tablayout.setupWithViewPager(binding.viewPager) }

        binding.viewPager.addOnPageChangeListener(object : ViewPager.OnPageChangeListener {
            override fun onPageScrollStateChanged(state: Int) {}
            override fun onPageScrolled(
                position: Int,
                positionOffset: Float,
                positionOffsetPixels: Int
            ) {
            }

            override fun onPageSelected(position: Int) {
                selectedPage = position
                refreshPage(position)
            }
        })
    }

    override fun observeTask() {
        setupToastMessage(this, viewModel.toastMessage)
        setupDialogMessage(this, viewModel.dialogMsg)

        viewModel.dialogTaskCommand.observe(this) {
            showMessage(it)
        }

        viewModel.taskNavigateSalesDetail.observe(this) { sales ->
            val intent =
                Intent(this, HistoryDetailActivity::class.java)
            intent.putExtra("data", sales)
            startActivity(intent)
        }

        viewModel.taskNavigateOrderSalesDetail.observe(this) { orderSales ->
            val intent =
                Intent(this, OrderSalesDetailActivity::class.java)
            val orderSalesJson = Gson().toJson(orderSales)
//            intent.putExtra("data", orderSales)
            intent.putExtra("data", orderSalesJson)
            Timber.i("taskNavigateOrderSalesDetail: $orderSalesJson")
            startActivity(intent)
        }
    }

    override fun observeData() {
        viewModel.getOrderSalesLive(outlet()?.outletId ?: 0)
            .observe(this) { resource ->
                Timber.d("#ordersales : $resource")
                resource?.data?.let { items ->
                    viewModel.setData(items)
                    refreshPage(selectedPage)
                }
            }

        viewModel.printTask.observe(this) {
            it?.let { printList ->
                managePrintWifi(
                    printList
                ) { isConnected, message ->
                    if (!isConnected) {
                        viewModel.savePendingPrint(printList)
                    }
                }
            }
        }
    }

    private fun refreshPage(page: Int) {
        orderSalesPages.getSafe(page)?.refreshItem()
//        when(page) {
//            0 -> orderSalesPending.refreshItem()
//            1 -> orderSalesAccept.refreshItem()
//            2 -> orderSalesReady.refreshItem()
//        }
    }

    override fun onCreateOptionsMenu(menu: Menu?): Boolean {
        menuInflater.inflate(R.menu.menu_order_sales, menu)
        return super.onCreateOptionsMenu(menu)
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        when (item.itemId) {
            R.id.action_reject -> {
//                startActivity(Intent(this, OrderSalesRejectActivity::class.java))
                val intent = Intent(this, OrderSalesActivity::class.java)
                intent.putExtra("status_filter", "reject")
                startActivity(intent)
            }
            else -> finish()
        }
        return super.onOptionsItemSelected(item)
    }

}
