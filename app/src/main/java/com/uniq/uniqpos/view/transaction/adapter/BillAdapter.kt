package com.uniq.uniqpos.view.transaction.adapter

import android.content.Context
import android.content.DialogInterface
import android.util.TypedValue
import android.view.View
import android.widget.TextView
import com.google.gson.Gson
import com.uniq.uniqpos.R
import com.uniq.uniqpos.data.remote.model.Employee
import com.uniq.uniqpos.databinding.ListItemBillBinding
import com.uniq.uniqpos.model.Order
import com.uniq.uniqpos.util.*
import com.uniq.uniqpos.util.view.DialogAuthorization
import com.uniq.uniqpos.view.global.GlobalAdapter
import com.uniq.uniqpos.view.global.GlobalViewHolder
import com.uniq.uniqpos.view.transaction.TransactionViewModel
import timber.log.Timber

/**
 * Created by annasblackhat on 11/07/18
 */
abstract class BillAdapter(private val viewModel: TransactionViewModel,
                           private val context: Context) : GlobalAdapter<ListItemBillBinding>(R.layout.list_item_bill, viewModel.orders) {

    private var listener: ((Order, Int, Boolean) -> Boolean)? = null

    override fun onBindViewHolder(holder: GlobalViewHolder<ListItemBillBinding>, position: Int) {
        super.onBindViewHolder(holder, position)

        holder.binding.imgAdd.setOnClickListener {
            viewModel.orders.getSafe(holder.adapterPosition)?.let { order ->
                var qty = order.qty
                if (listener?.invoke(order, qty, true) != false) {
                    order.product?.takeIf { it.productId > 0 }?.let {
                        holder.binding.txtQty.text = (++qty).toString()
                        if (viewModel.orders.size > 0) {
                            order.qty = qty

                            var isNeedToRecalculateTax = viewModel.updatePrice(order, holder)
                            updateExtraPrice(holder, order, -1)

                            val disc = calculateDiscount(order)
                            order.extra.forEachIndexed { _, _ ->
                                order.discount.discountNominal = disc
                            }

                            updateDiscount(order, holder)
                            order.promotion?.takeIf { holder.binding.txtInfo.text.toString() != order.info }?.let {
                                holder.binding.txtInfo.text = order.info
                                holder.binding.txtInfo.visibility = View.VISIBLE
                                isNeedToRecalculateTax = true
                            }

                            if (!isNeedToRecalculateTax) {
                                viewModel.promoApplied.firstOrNull { it.minOrder > 0 }?.let { promo ->
                                    isNeedToRecalculateTax = (viewModel.calculateGrandTotal(false) >= promo.minOrder)
                                }
                            }

                            if (!isNeedToRecalculateTax) {
                                isNeedToRecalculateTax = viewModel.isNeedToRecalculateTax()
                            }

                            if (isNeedToRecalculateTax) {
                                viewModel.reCalculateTax()
                                viewModel.taskRefreshTax.call()
                            } else {
                                viewModel.updateTax(order, viewModel.calculateDiscPerItem(order, order, true))
                                order.extra.forEachIndexed { _, extra ->
                                    viewModel.updateTax(extra, viewModel.calculateDiscPerItem(extra, order, true))
                                }
                                viewModel.recalculateDiscAndVoucher()
                                viewModel.taskRefreshTax.call()
                            }

                            viewModel.updatePromotionValue(order)
                            refreshGrandTotal()
                            viewModel.runGrandTotalWatcher()
                            viewModel.checkFreeItemEligibility(order)
                        }
                    }
                }
            }
        }

        holder.binding.imgRemove.setOnClickListener {
            if (holder.adapterPosition >= 0) {
                val order = viewModel.orders[holder.adapterPosition]
                var qty = order.qty
                if ((qty - 1) >= 0 && listener?.invoke(order, qty, false) != false) {
                    order.product?.takeIf { it.productId > 0 }?.let {
                        if (viewModel.isParentFromPromo(order)) {
                            val isRemoveFreeItem = !order.isHold || (order.isHold && qty > order.holdQty)
                            val warnMsg = if (isRemoveFreeItem) "Promo item akan di hapus dari daftar bill jika anda melanjutkan proses ini" else "Harga Promo item akan berubah menjadi normal. \nTetap lanjutkan?"
                            holder.itemView.context.showMessage(warnMsg, "WARNING",  { _, _ ->
                                minusItem(order, holder)
                                if (isRemoveFreeItem) {
                                    viewModel.removeFreeItemByParentId(order.tmpId)
                                }
                            }, positiveMsg = context.getString(R.string.continue_text))
                        } else {
                            minusItem(order, holder)
                        }
                    }
                }
            }
        }

//        viewModel.updatePrice(viewModel.orders[holder.adapterPosition], holder)

        addExtra(holder, viewModel.orders[holder.adapterPosition])
        updateDiscount(viewModel.orders[holder.adapterPosition], holder)

        holder.binding.layoutMenu.setOnClickListener { showMenuDetail(holder.adapterPosition) }
        holder.binding.layoutPrice.setOnClickListener { showMenuDetail(holder.adapterPosition) }

        Timber.d("on '${viewModel.orders[holder.adapterPosition].product?.name}' | promo : ${Gson().toJson(viewModel.orders[position].promotion)} | ${viewModel.orders[position].info}")
    }

    private fun minusItem(order: Order, holder: GlobalViewHolder<ListItemBillBinding>? = null) {
        var qty = order.qty
        if (order.isHold && qty <= order.holdQty) {
            if (order.voidedQty >= order.holdQty) {
                context.toast("Semua item sudah di void")
            } else {
                DialogAuthorization(context)
                        .setAuthType(DialogAuthorization.AuthType.VOID)
                        .setOnAuthorizedListener { employeeAuth ->
                            showVoidDialog(order, employeeAuth)
                        }
                        .show()
            }
        } else {
            holder?.binding?.txtQty?.text = (--qty).toString()
            Timber.i(">> min qty ${order.product?.name} | QTY Now : $qty")
            if (viewModel.orders.size > 0 && qty >= 0) {
                order.qty = qty
                var isNeedToRecalculateTax = viewModel.updatePrice(order, holder)
                holder?.binding?.txtInfo?.text = order.info
                updateExtraPrice(holder, order, 1)
                val disc = calculateDiscount(order)
                order.discount.discountNominal = disc
                updateDiscount(order, holder)

                if (!isNeedToRecalculateTax) {
                    viewModel.promoApplied.firstOrNull { it.minOrder > 0 }?.let { promo ->
                        isNeedToRecalculateTax = (viewModel.calculateGrandTotal(false) < promo.minOrder)
                    }
                }

                if (!isNeedToRecalculateTax) {
                    isNeedToRecalculateTax = viewModel.isNeedToRecalculateTax()
                }

                if (isNeedToRecalculateTax) {
                    viewModel.reCalculateTax()
                } else {
                    viewModel.updateTax(order, viewModel.calculateDiscPerItem(order, order, true), false)
                    order.extra.forEachIndexed { _, it ->
                        viewModel.updateTax(it, viewModel.calculateDiscPerItem(it, order, true), false)
                    }
                    viewModel.recalculateDiscAndVoucher()
                }

                viewModel.updatePromotionValue(order)
                holder?.binding?.txtInfo?.text = order.info
                refreshGrandTotal()
                viewModel.runGrandTotalWatcher()
                viewModel.taskRefreshTax.call()
            }

            if (qty == 0) removeItem(holder)
            else println("")
        }
    }

    private fun addExtra(holder: GlobalViewHolder<ListItemBillBinding>, order: Order) {
        var qty = holder.binding.txtQty.text.toString().toInt()
        holder.binding.layoutExtra.removeAllViews()
        holder.binding.layoutPriceExtra.removeAllViews()
        order.extra.forEach { extra ->
            val extraText = TextView(context)
            extraText.setTextColor(Utils.getColor(holder.itemView.context, R.color.grey_light))
            extraText.text = extra.product?.name
            extraText.setTextSize(TypedValue.COMPLEX_UNIT_SP, 12f)
            holder.binding.layoutExtra.addView(extraText)

            val extraPrice = TextView(context)
            extraPrice.setTextColor(Utils.getColor(holder.itemView.context, R.color.grey_light))
            var price = 0
            extra.product?.let { p ->
                price = p.priceSell ?: 0
                if (order.isItemVoid && price > 0) {
                    price *= -1
                }
            }

            extra.price = price
            extra.qty = qty
            extra.subTotal = (price * qty)
            extraPrice.text = (price * qty).toCurrency()
            extraPrice.setTextSize(TypedValue.COMPLEX_UNIT_SP, 12f)
            holder.binding.layoutPriceExtra.addView(extraPrice)
        }
        refreshGrandTotal()
    }

    private fun updateDiscount(order: Order, holder: GlobalViewHolder<ListItemBillBinding>? = null) {
        holder?.binding?.layoutDiscount?.removeAllViews()
        holder?.binding?.layoutDiscountPrice?.removeAllViews()
        Timber.i(">> ${order.product?.name} | ${order.discount.discount}")
        if (order.discount.discount > 0) {
            val discText = TextView(context)
            discText.setTextColor(Utils.getColor(holder?.itemView?.context, R.color.grey_light))
            discText.setTextSize(TypedValue.COMPLEX_UNIT_SP, 12f)
            var discount = calculateDiscount(order)
            if (order.isItemVoid && discount < 0) discount *= -1
            order.discount.discountNominal = discount
//                    Timber.i("updateDiscount => isItemVoid : ${order.isItemVoid}")
//                    Timber.i("updateDiscount => Disc : $discount")
            Timber.i(">> Is item void ${order.isItemVoid} | Disc : $discount")
            discText.text = (if (order.isItemVoid) "" else "-") + discount.toCurrency()
            holder?.binding?.layoutDiscountPrice?.addView(discText)

            if (order.discount.discountType == Constant.TYPE_PERSEN) {
                order.discount.discountNominal = order.discount.discount * (order.subTotal + (order.extra.sumOf { it.subTotal })) / 100
            } else {
                order.discount.discountNominal = order.discount.discount
            }

            val disc = if (order.discount.discountType == Constant.TYPE_PERSEN) "(${order.discount.discount}%)" else ""
            val text = TextView(context)
            text.setTextColor(Utils.getColor(holder?.itemView?.context, R.color.grey_light))
            text.setTextSize(TypedValue.COMPLEX_UNIT_SP, 12f)
            text.text = "  - Disc $disc"
            holder?.binding?.layoutDiscount?.addView(text)
        }
    }

    private fun updateExtraPrice(holder: GlobalViewHolder<ListItemBillBinding>? = null, order: Order, add: Int) {
        var qty = holder?.binding?.txtQty?.text.toString().safeToInt()
        for (i in 0 until order.extra.size) {
            val child = holder?.binding?.layoutPriceExtra?.getChildAt(i)
            if (child is TextView) {
//                val currentPrice = child.text.toString().fromCurrency()
//                val qty = (qty + add)
//                var originalPrice = if(qty > 0) currentPrice / qty else currentPrice
                var originalPrice = order.extra?.get(i)?.product?.priceSell ?: 0
                var total = originalPrice * qty
                if (order.isItemVoid && total > 0) {
                    originalPrice *= -1
                    total *= -1
                }
                child.text = total.toCurrency()
                order.extra[i].qty = qty
                order.extra[i].price = originalPrice
                order.extra[i].subTotal = total
            }
        }
    }

    private fun calculateDiscount(order: Order): Int {
        return if (order.discount.discount > 0) {
            if (order.discount.discountType == Constant.TYPE_PERSEN) {
                val totalExtra = order.extra?.sumOf { it.subTotal } ?: 0
                val subtotal = order.subTotal + totalExtra
                val discount = (order.discount.discount.toDouble() / 100) * subtotal
                discount.toInt()
            } else order.discount.discount
        } else 0
    }

    //Order object, Int current qty, Boolean isAdd
    fun setOnQtyChangeListener(listener: (Order, Int, Boolean) -> Boolean) {
        this.listener = listener
    }

    abstract fun showMenuDetail(position: Int)

    abstract fun showVoidDialog(order: Order, employee: Employee)

    abstract fun refreshGrandTotal()
}