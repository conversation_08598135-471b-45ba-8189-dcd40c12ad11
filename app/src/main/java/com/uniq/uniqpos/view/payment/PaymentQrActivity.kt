package com.uniq.uniqpos.view.payment

import android.app.Activity
import android.graphics.Bitmap
import android.graphics.drawable.Drawable
import android.os.Bundle
import android.os.CountDownTimer
import com.bumptech.glide.request.target.CustomTarget
import com.bumptech.glide.request.transition.Transition
import com.google.android.gms.vision.Frame
import com.google.android.gms.vision.barcode.Barcode
import com.google.android.gms.vision.barcode.BarcodeDetector
import com.uniq.uniqpos.R
import com.uniq.uniqpos.app.GlideApp
import com.uniq.uniqpos.databinding.ActivityPaymentQrBinding
import com.uniq.uniqpos.model.SalesPaymentCreate
import com.uniq.uniqpos.util.*
import com.uniq.uniqpos.util.lifecycle.setupToastMessage
import com.uniq.uniqpos.view.global.BaseActivity
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import kotlin.coroutines.resume
import kotlin.coroutines.suspendCoroutine

class PaymentQrActivity : BaseActivity<PaymentViewModel, ActivityPaymentQrBinding>() {
    override fun getLayoutRes() = R.layout.activity_payment_qr
    override fun getViewModel() = PaymentViewModel::class.java

    private var glideCustomTarget: CustomTarget<Bitmap>? = null
    private var countDownTimer: CountDownTimer? = null
    private var totalTimeInMillis: Long = 60000 * 3
    private var salesId: String? = null
    private var payment: SalesPaymentCreate? = null
    private var startTime = System.currentTimeMillis()
    private var grandTotal: Int = 0
    private var qrBitmap: Bitmap? = null
    private var qrValue: String? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        payment = intent.getParcelableExtra("data")
        grandTotal = intent.getIntExtra("total", 0)
        salesId = intent.getStringExtra("id")

        setTextCurrency(binding.txtGrandTotal, grandTotal)
        setImageUrl(binding.imgQr, payment?.qris?.url, placeHolder = R.drawable.loading_image)
        startTimer()

        binding.btnPrint.setOnClickListener { printQr() }
    }

    private fun printQr() {
        showDialog(true)
        GlobalScope.launch {
            val bitmap = qrBitmap ?: getBitmapAsync(payment?.qris?.url.safe())
            val qr = qrValue ?: readQrValue(bitmap)
            viewModel.preparePrintQr(readQrValue(bitmap), grandTotal, bitmap,  startTime + totalTimeInMillis)

            //cache
            qrBitmap = bitmap
            qrValue = qr
        }
//        getBitmap(payment?.qris?.url.safe()) { bitmap ->
//            viewModel.preparePrintQr(readQrValue(bitmap), grandTotal, bitmap,  startTime + totalTimeInMillis)
//        }
    }

    private fun readQrValue(bitmap: Bitmap): String {
        //read qr value
        val barcodeDetector = BarcodeDetector.Builder(this)
            .setBarcodeFormats(Barcode.ALL_FORMATS)
            .build()

        val frame = Frame.Builder()
            .setBitmap(bitmap)
            .build()


        val barcodes = barcodeDetector.detect(frame)
        if(barcodes.size() == 0) {
            toast("can not read qr code value", level = Level.ERROR)
        }
        return  barcodes.valueAt(0).displayValue
    }

    private suspend fun getBitmapAsync(imgUrl: String): Bitmap = suspendCoroutine { continuation ->
        glideCustomTarget = object : CustomTarget<Bitmap>() {
            override fun onResourceReady(resource: Bitmap, transition: Transition<in Bitmap>?) {
                qrBitmap = resource
                continuation.resume(resource)
            }

            override fun onLoadCleared(placeholder: Drawable?) {
            }
        }

        GlideApp.with(this)
            .asBitmap()
            .load(imgUrl)
            .into(glideCustomTarget!!)
    }


    private fun getBitmap(imgUrl: String,fn: (Bitmap) -> Unit) {
        if(qrBitmap != null){
            fn(qrBitmap!!)
        }else{
            GlideApp.with(this)
                .asBitmap()
                .load(imgUrl)
                .into(object : CustomTarget<Bitmap>() {
                    override fun onResourceReady(resource: Bitmap, transition: Transition<in Bitmap>?) {
                        qrBitmap = resource
                        fn(resource)
                    }
                    override fun onLoadCleared(placeholder: Drawable?) {}
                })
        }
    }

    override fun observeTask() {
        super.observeTask()
        setupToastMessage(this, viewModel.toastMessage)

        viewModel.taskPaymentPaid.observe(this) {
            setResult(Activity.RESULT_OK)
            finish()
        }

        viewModel.printTask.observe(this) { printTask ->
            managePrintWifi(printTask.data) { _, message ->
                toast(message, level = Level.WARNING)
                showDialog(false)
            }
        }

        viewModel.pDialogTaskCommand.observe(this) { isShow ->
            showDialog(isShow ?: false)
        }
    }

    private fun startTimer() {
        countDownTimer = object : CountDownTimer(totalTimeInMillis, 1000) {
            override fun onTick(millisUntilFinished: Long) {
                binding.txtTimer.text = formatTime(millisUntilFinished)
                if (((millisUntilFinished / 1000) % 60).toInt() % 2 == 0) {
                    viewModel.checkPaymentStatus(salesId)
                }
            }

            override fun onFinish() {
                toast(getString(R.string.time_limit_exceeded), level = Level.WARNING)
                finish()
            }
        }
        countDownTimer?.start()
    }

    private fun formatTime(timeInMillis: Long): String {
        val seconds = (timeInMillis / 1000) % 60
        val minutes = (timeInMillis / (1000 * 60)) % 60
        return String.format("%02d:%02d", minutes, seconds)
    }

    override fun onBackPressed() {
        showMessage(
            getString(R.string.cancel_payment),
            getString(R.string.confirmation),
            negativeMsg = getString(R.string.close),
            positiveAction = { _, _ ->
                countDownTimer?.cancel()
                super.onBackPressed()
            })
    }

    override fun onDestroy() {
        super.onDestroy()
        countDownTimer?.cancel()
        qrValue = null
        qrBitmap = null
        glideCustomTarget = null
    }
}