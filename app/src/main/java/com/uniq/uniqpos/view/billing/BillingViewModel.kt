package com.uniq.uniqpos.view.billing

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.uniq.uniqpos.data.local.sharedpref.SharedPref
import com.uniq.uniqpos.data.remote.repository.BillingRepository
import com.uniq.uniqpos.model.Billing
import com.uniq.uniqpos.model.BillingService
import com.uniq.uniqpos.model.Order
import com.uniq.uniqpos.util.lifecycle.BaseViewModel
import com.uniq.uniqpos.util.lifecycle.Failed
import com.uniq.uniqpos.util.lifecycle.Loading
import com.uniq.uniqpos.util.lifecycle.ViewModelState
import kotlinx.coroutines.launch
import retrofit2.await
import timber.log.Timber
import java.util.ArrayList
import javax.inject.Inject

class BillingViewModel @Inject constructor(private val billingRepository: BillingRepository,
                                           val sharedPref: SharedPref) : BaseViewModel() {

    val billingServiceList = MutableLiveData<List<BillingService>>()
    val paymentMediaList = MutableLiveData<List<String>>()
    val bankCodes = arrayOf("BCA", "BNI", "MANDIRI", "BRI")
    val billingCreated = MutableLiveData<Billing>()

    fun loadBillingServices() {
        //load from shared pref
        sharedPref.getString("billing_service")?.let { localData ->
            try {
                val type = object : TypeToken<ArrayList<BillingService>>() {}.type
                val services = Gson().fromJson<ArrayList<BillingService>>(localData, type)
                billingServiceList.postValue(services)
            } catch (e: Exception) {
                Timber.i("getting service from local err: $e")
            }
        }

        viewModelScope.launch {
            try {
                val response = billingRepository.getBillingService().await()
                response.data?.let { data ->
                    billingServiceList.postValue(data)
                    sharedPref.putData("billing_service", data)
                }
            } catch (e: Exception) {
                Timber.i("getting service err: $e")
                _state.postValue(Failed(e))
            }
        }
    }

    fun loadPaymentMedia() {
        viewModelScope.launch {
            paymentMediaList.postValue(bankCodes.map { "$it Virtual Account" })
        }
    }

    fun createSubscription(service: BillingService?, period: Int, slot: Int, payment: String) {
        if (service == null) {
            return
        }
        Timber.i("create subscription: ${service.name}, period: $period, qty: $slot, payment: $payment")
        viewModelScope.launch {
            _state.postValue(Loading(true))
            try {
                val result = billingRepository.createBilling(service.sysServiceID.toInt(), period, payment, slot).await()
                result.data?.let {
                    billingCreated.postValue(it)
                }
            } catch (e: Exception) {
                Timber.i("create billing error: $e")
                _state.postValue(Failed(e))
            }
            _state.postValue(Loading(true))
        }
    }
}