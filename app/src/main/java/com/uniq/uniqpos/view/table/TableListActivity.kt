package com.uniq.uniqpos.view.table

import android.app.Activity
import android.content.DialogInterface
import android.content.Intent
import android.content.pm.ActivityInfo
import android.graphics.Color
import android.os.Bundle
import android.view.LayoutInflater
import android.view.Menu
import android.view.MenuItem
import android.widget.ArrayAdapter
import androidx.appcompat.app.AlertDialog
import androidx.core.content.res.ResourcesCompat
import androidx.lifecycle.Observer
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.uniq.uniqpos.R
import com.uniq.uniqpos.data.local.entity.DiningTableEntity
import com.uniq.uniqpos.data.local.entity.SalesEntity
import com.uniq.uniqpos.data.local.sharedpref.SharedPref
import com.uniq.uniqpos.data.local.sharedpref.getJson
import com.uniq.uniqpos.data.remote.model.Outlet
import com.uniq.uniqpos.databinding.ActivityTableListBinding
import com.uniq.uniqpos.databinding.DialogGenerateTableBinding
import com.uniq.uniqpos.databinding.ListItemTableBinding
import com.uniq.uniqpos.model.BottomDialogModel
import com.uniq.uniqpos.util.*
import com.uniq.uniqpos.view.global.BaseActivity
import com.uniq.uniqpos.view.global.BottomDialogInput
import com.uniq.uniqpos.view.global.GlobalAdapter
import com.uniq.uniqpos.view.global.GlobalViewHolder
import timber.log.Timber

class TableListActivity : BaseActivity<TableViewModel, ActivityTableListBinding>() {

    override fun getLayoutRes() = R.layout.activity_table_list
    override fun getViewModel() = TableViewModel::class.java

    var selectedTable = 0
    val tableList = ArrayList<DiningTableEntity>()
    private var isChoosing = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setSupportActionBar(binding.incToolbar.toolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        if (resources.getBoolean(R.bool.landscape_only)) {
            requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_SENSOR_LANDSCAPE
        }

        isChoosing = intent.getBooleanExtra("isChoosing", false)
    }

    override fun initView() {
        super.initView()
        binding.swipeRefresh.setOnRefreshListener { binding.swipeRefresh.isRefreshing = false }

        binding.recview.adapter = object : GlobalAdapter<ListItemTableBinding>(R.layout.list_item_table, tableList) {
            override fun onBindViewHolder(holder: GlobalViewHolder<ListItemTableBinding>, position: Int) {
                super.onBindViewHolder(holder, position)
                val table = tableList[position]

                val drawableId = if (tableList[position].status == "used") R.drawable.round_stroke_grey_disable else if (position == selectedTable) R.drawable.round_stroke_grey_selected else R.drawable.round_stroke_grey
                val drawable = ResourcesCompat.getDrawable(resources, drawableId, null)
                val textColor = if (table.status == "used") Color.parseColor("#606266") else Color.WHITE

                holder.binding.txtTable.setTextColor(textColor)
                holder.binding.container.background = drawable
                holder.binding.root.setOnClickListener {
                    onTableClicked(holder.adapterPosition)
                }

                holder.itemView.setOnLongClickListener {
                    if (tableList[holder.adapterPosition].status == "empty")
                        showMoreMenu(tableList[holder.adapterPosition])
                    true
                }
            }
        }
    }

    private fun onTableClicked(position: Int) {
        tableList.getSafe(position)?.let { table ->
            Timber.i("Onclick.... table status : ${table.status}")
            if (table.status == "empty") {
                val tmpSelected = selectedTable
                selectedTable = position
                binding.recview.adapter?.notifyItemChanged(tmpSelected)
                binding.recview.adapter?.notifyItemChanged(selectedTable)

                if (isChoosing) {
                    chooseTableForBill(table)
                }
            } else {
                val sales = viewModel.saleList.filter { it.table == table.tableName }
                if (sales.isEmpty()) {
                    finishTable(table)
                } else {
                    if (isChoosing) {
                        chooseTableForBill(table)
                    } else {
                        if (sales.size > 1) {
                            val options = ArrayList<String>()
                            sales.forEach { p -> options.add(p.customer ?: p.items ?: "-") }
                            AlertDialog.Builder(this)
                                    .setTitle("Choose Bill")
                                    .setItems(options.toTypedArray()) { _, which ->
                                        editSale(sales[which])
                                    }
                                    .show()
                        } else {
                            editSale(sales.first())
                        }
                    }
                    val options = ArrayList<String>()
                    sales.forEach { p -> options.add(p.customer ?: p.items ?: "-") }
                }
            }
        }
    }

    private fun chooseTableForBill(table: DiningTableEntity) {
        val intent = Intent()
        intent.putExtra("table", table.tableName)
        setResult(Activity.RESULT_OK, intent)
        finish()
    }

    private fun showMoreMenu(diningTable: DiningTableEntity) {
        val choices = listOf(getString(R.string.update), getString(R.string.delete))
        AlertDialog.Builder(this)
                .setTitle("OPTIONS")
                .setItems(choices.toTypedArray()) { _, which ->
                    if (which == 0) {
                        updateTable(diningTable)
                    } else {
                        showMessage("Are you sure?", "Confirmation", DialogInterface.OnClickListener { _, _ ->
                            Timber.i("Delte Table : Name ${diningTable.tableName} | ID : ${diningTable.tableName}")
                            viewModel.deleteTable(diningTable)
                        })
                    }
                }
                .show()
    }

    private fun updateTable(diningTable: DiningTableEntity) {
        val bottomDialog = BottomDialogInput()
        bottomDialog.setOnButtonClick { result ->
            if (result.isBlank()) {
                toast("Value is not allowed!")
            } else {
                if (tableList.firstOrNull { it.tableName == result } == null) {
                    diningTable.tableName = result.trim()
                    diningTable.synced = false
                    diningTable.timeModified = System.currentTimeMillis()
                    viewModel.updateTable(diningTable)
                    bottomDialog.dismiss()
                } else {
                    showMessage("Nama tabel duplikat!")
                }
            }
        }
        bottomDialog.setModel(BottomDialogModel("UPDATE", "UPDATE", "Table", hint = "table name"))
        bottomDialog.show(supportFragmentManager, "add-new")
    }

    override fun observeData() {
//        viewModel.syncTmpSales(outlet.outletId)
        val outlet = outlet() ?: Outlet()

        viewModel.loadSalesCart(outlet.outletId.safe())

        binding.swipeRefresh.isRefreshing = true
        viewModel.taskLoadTable.observe(this) {
            it.getContentIfNotHandled()?.let {
                viewModel.getDiningTable(outlet.outletId.safe())
                    .observe(this, Observer {
                        tableList.clear()
                        it?.data?.let {
                            val tmpTable = ArrayList<DiningTableEntity>()
                            val tablemap = ArrayList<TableMap>()
                            it.forEach { data ->
                                viewModel.saleList.firstOrNull { it.table == data.tableName }
                                    ?.let { data.status = "used" }
                                if (data.tableName.isNumeric()) {
                                    tablemap.add(TableMap(data.tableName.toInt(), data))
                                } else {
                                    tmpTable.add(data)
                                }
                            }
                            tablemap.sortedBy { it.id }.forEach { tableList.add(it.table) }
                            tableList.addAll(tmpTable)
                        }
                        binding.recview.adapter?.notifyDataSetChanged()
                        binding.swipeRefresh.isRefreshing = false
                    })
            }
        }

    }

    private fun addTable() {
        val bottomDialog = BottomDialogInput()
        bottomDialog.setOnButtonClick { result ->
            if (result.isBlank()) {
                bottomDialog.setError("Value is not allowed!")
            } else if (result.length > 15) {
                bottomDialog.setError(getString(R.string.input_exceed_limit))
            } else {
                if (tableList.firstOrNull { it.tableName == result } == null) {
                    val outlet = getJson(SharedPref.OUTLET_DATA, Outlet::class.java)
                    viewModel.addTable(result, outlet)
                    bottomDialog.dismiss()
                } else {
                    showMessage("Nama tabel duplikat!")
                }
            }
        }
        bottomDialog.setModel(BottomDialogModel("ADD NEW", "SAVE", "Table", hint = "table name"))
        bottomDialog.show(supportFragmentManager, "add-new")
    }

    private fun addMultipleTable(){
        val outlet = getJson(SharedPref.OUTLET_DATA, Outlet::class.java)
        val dialogView = DialogGenerateTableBinding.inflate(layoutInflater)
        val dialog = BottomSheetDialog(this)
        dialog.setContentView(dialogView.root)

//        val view = LayoutInflater.from(this).inflate(R.layout.dialog_generate_table, null)
        dialogView.btnSave.setOnClickListener {
            if(Utils.isValidField(dialogView.edtStart, dialogView.edtEnd)){
                val start = dialogView.edtStart.valueToInt()
                val end = dialogView.edtEnd.valueToInt()
                if (end > start){
                    var totalGenerated = 0
                    for (i in start..end){
                        if (!tableList.any { it.tableName == i.toString() }){
                            viewModel.addTable(i.toString(), outlet)
                            totalGenerated += 1
                        }
                    }
                    if(totalGenerated == 0){
                        toast("no table has been generated!", level = Level.WARNING)
                    }else{
                        toast("$totalGenerated tables generated successfully", level = Level.INFO)
                    }
                }else{
                    toast("invalid input", level = Level.ERROR)
                }
                dialog.dismiss()
            }
        }
        dialog.show()
    }

    private fun editSale(salesEntity: SalesEntity) {
        val bundle = Bundle()
        bundle.putParcelable("sales", salesEntity)
        val intent = Intent()
        intent.putExtras(bundle)
        setResult(Activity.RESULT_OK, intent)
        finish()
    }

    private fun finishTable(table: DiningTableEntity) {
        val msg = if (isChoosing) "Finish order and use this table?" else "Finish order?"
        showMessage(msg, "Confirmation", { _, _ ->
            viewModel.updateTableStatus(table.copy(status = Constant.TABLE_STATUS_EMPTY))
            if (isChoosing) {
                chooseTableForBill(table)
            }
        })
    }

    private fun showCreateTableOption(){
        val optionAdapter = ArrayAdapter<String>(this, android.R.layout.simple_list_item_1).apply {
            add("Single Table")
            add("Generate Multiple Table")
        }
        AlertDialog.Builder(this)
            .setTitle("Create New Table")
            .setAdapter(optionAdapter
            ) { _, position -> if (position == 0) addTable() else addMultipleTable() }
            .create()
            .show()
    }

    override fun onCreateOptionsMenu(menu: Menu): Boolean {
        menuInflater.inflate(R.menu.menu_table, menu)
        return super.onCreateOptionsMenu(menu)
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        if (item.itemId == R.id.action_add) {
            showCreateTableOption()
        } else {
            finish()
        }
        return super.onOptionsItemSelected(item)
    }

    data class TableMap(var id: Int, var table: DiningTableEntity)
}
