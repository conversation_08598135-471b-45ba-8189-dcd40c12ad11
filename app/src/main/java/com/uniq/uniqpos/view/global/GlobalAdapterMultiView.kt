package com.uniq.uniqpos.view.global

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.annotation.LayoutRes
import androidx.databinding.ViewDataBinding
import androidx.recyclerview.widget.RecyclerView
import com.uniq.uniqpos.BR
import com.uniq.uniqpos.util.safe

abstract class GlobalAdapterMultiView(
    @field:LayoutRes private var layoutIdHeader: Int,
    @field:LayoutRes private var layoutIdItem: Int,
    private val listData: List<*>? = null,
    val itemType: (position: Int) -> ViewType,
    private val onBind: ((binding: ViewDataBinding, position: Int) -> Unit)? = null
) : RecyclerView.Adapter<GlobalRecViewHolder>() {

    companion object {
        private const val VIEW_TYPE_HEADER = 0
        private const val VIEW_TYPE_ITEM = 1
    }

    enum class ViewType {
        TYPE_HEADER {
            override fun type() = VIEW_TYPE_HEADER
        },
        TYPE_ITEM {
            override fun type() = VIEW_TYPE_ITEM
        };

        abstract fun type(): Int
    }

    override fun getItemViewType(position: Int) = itemType(position).type()
    override fun getItemCount() = listData?.size.safe()
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): GlobalRecViewHolder {
        val layoutRes = if (viewType == VIEW_TYPE_HEADER) layoutIdHeader else layoutIdItem
        return GlobalRecViewHolder(
            LayoutInflater.from(parent.context)
                .inflate(layoutRes, parent, false)
        )
    }

    override fun onBindViewHolder(holder: GlobalRecViewHolder, position: Int) {
        listData?.apply {
            holder.onBind(BR.model, listData[position])
            onBind?.invoke(holder.getBinding()!!, position)
        }
    }

}