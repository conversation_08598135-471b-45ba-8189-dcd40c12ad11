package com.uniq.uniqpos.model

import androidx.room.Ignore
import android.os.Parcelable
import kotlinx.android.parcel.Parcelize
import java.io.Serializable

/**
 * Created by ANNASBlackHat on 13/11/17.
 */

@Parcelize
data class Discount(
        var discount: Int = 0, //value of discount. Example : 50 (if 50%)
        var discountInfo: String? = "",
        var voucher: Int = 0,
        var voucherInfo: String? = "",
        var discountType: String? = "",
        var voucherType: String? = "",
        var discountNominal: Int = 0, //discount in nominal, could be same as discount if discount type is nominal
        var voucherNominal: Int = 0
):  Parcelable {
    @Ignore constructor(): this(0, "",0, "","","", 0, 0)
}