package com.uniq.uniqpos.model

import android.annotation.SuppressLint
import android.os.Parcelable
import kotlinx.android.parcel.Parcelize

/**
 * Created by ANNASBlackHat on 29/10/2017.
 */
@Parcelize
@SuppressLint("ParcelCreator")
data class TaxSales(var id: Int?,
                    var name: String?,
                    var total: Int = 0, //the total nominal after some calculation
                    var type: String?,  //percent or nominal
                    var value: Int = 0, //original tax value, if tax is 5%, then it's 5
                    var category: String?,
                    var identifierId: String? = null,
                    var identifierType: String? = null,
                    var parentIds: String? = null //tmp ids of order (separated with comma)
) : Parcelable