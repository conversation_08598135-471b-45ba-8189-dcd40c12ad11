package com.uniq.uniqpos.model

import com.google.gson.annotations.SerializedName

data class Admin(

	@field:SerializedName("phone")
	val phone: String? = null,

	@field:SerializedName("admin_id")
	val adminId: Int = 0,

	@field:SerializedName("outletName")
	val name: String? = null,

	@field:SerializedName("email")
	val email: String? = null,

	@field:SerializedName("username")
	val username: String? = null,

	@field:SerializedName("password")
	val password: String? = null,

	@field:SerializedName("business_name")
	val businessName: String? = null
)