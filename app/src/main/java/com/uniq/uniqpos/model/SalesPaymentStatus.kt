package com.uniq.uniqpos.model


import com.google.gson.annotations.SerializedName

data class SalesPaymentStatus(
    @SerializedName("id")
    val id: Int,
    @SerializedName("payload")
    val payload: String,
    @SerializedName("payment_type")
    val paymentType: String,
    @SerializedName("status")
    val status: String,
    @SerializedName("time_created")
    val timeCreated: Long,
    @SerializedName("tmp_sales_fkid")
    val tmpSalesFkid: String,
    @SerializedName("transaction_id")
    val transactionId: String
)