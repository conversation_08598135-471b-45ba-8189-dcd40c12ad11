package com.uniq.uniqpos.model

import com.google.gson.annotations.SerializedName

data class BillingService(
        @field:SerializedName("sys_service_id")
        val sysServiceID: Long,

        val name: String,
        val price: Long,
        val discount: Long,

        @field:SerializedName("service_feature")
        val serviceFeature: String,

        @field:SerializedName("service_length_day")
        val serviceLengthDay: Long,

        @field:SerializedName("service_period_max")
        val servicePeriodMax: Long,

        @field:SerializedName("service_type")
        val serviceType: String,

        @field:SerializedName("service_sortorder")
        val serviceSortorder: Long,

        @field:SerializedName("data_created")
        val dataCreated: Long,

        @field:SerializedName("data_modified")
        val dataModified: Long,

        @field:SerializedName("data_status")
        val dataStatus: Long
)