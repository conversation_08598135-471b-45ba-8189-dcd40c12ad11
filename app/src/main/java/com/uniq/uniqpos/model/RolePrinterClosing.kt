package com.uniq.uniqpos.model


import com.google.gson.annotations.SerializedName

data class RolePrinterClosing(

		@field:SerializedName("paymentmedia")
		val paymentmedia: Boolean = false,

		@field:SerializedName("actual")
		val actual: Boolean = false,

		@field:SerializedName("entertainincome")
		val entertainincome: Boolean = false,

		@field:SerializedName("fifo")
		val fifo: Boolean = false,

		@field:SerializedName("piutangdetail")
		val piutangdetail: Boolean = false,

		@field:SerializedName("voucher")
		val voucher: Boolean = false,

		@field:SerializedName("shiftname")
		val shiftname: Boolean = false,

		@field:SerializedName("itemsales")
		val itemsales: Boolean = false,

		@field:SerializedName("purchase")
		val purchase: Boolean = false,

		@field:SerializedName("discountdetail")
		val discountdetail: Boolean = false,

		@field:SerializedName("tax")
		val tax: Boolean = false,

		@field:SerializedName("promodetail")
		val promodetail: Boolean = false,

		@field:SerializedName("sales")
		val sales: Boolean = false,

		@field:SerializedName("groupsales")
		val groupsales: Boolean = false,

		@field:SerializedName("freedetail")
		val freedetail: Boolean = false,

		@field:SerializedName("opcostdetail")
		val opcostdetail: Boolean = false,

		@field:SerializedName("complimentdetail")
		val complimentdetail: Boolean = false,

		@field:SerializedName("time")
		val time: Boolean = false,

		@field:SerializedName("dutydetail")
		val dutydetail: Boolean = false,

		@field:SerializedName("debt")
		val debt: Boolean = false,

		@field:SerializedName("avgpaxbill")
		val avgpaxbill: Boolean = false,

		@field:SerializedName("refund")
		val refund: Boolean = false,

		@field:SerializedName("outlet_commission_sharing")
		val outletCommissionSharing: Boolean = false,
)