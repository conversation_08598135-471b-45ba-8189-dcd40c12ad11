package com.uniq.uniqpos.model

import android.os.Parcelable
import com.uniq.uniqpos.data.local.entity.*
import com.uniq.uniqpos.data.remote.model.OperationalCost
import kotlinx.android.parcel.Parcelize

/**
 * Created by annasblackhat on 2019-06-21
 */

@Parcelize
data class CashRecapData (
        var sales: List<SalesEntity>,
        var operationalCost: ArrayList<OperationalCost>,
        var tmpSales: List<TmpSalesEntity>,
        var subcategory: List<SubCategoryEntity>,
        var openShifts: List<OpenShiftEntity>,
        var watermark: String?,
        var debtList: List<PiutangHistoryEntity> = emptyList()
) : Parcelable