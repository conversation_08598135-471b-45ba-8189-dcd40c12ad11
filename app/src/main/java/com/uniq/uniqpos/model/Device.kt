package com.uniq.uniqpos.model

import com.google.gson.annotations.SerializedName

data class Device(

	@field:SerializedName("device_status")
	val deviceStatus: String = "off",

	@field:SerializedName("device_id")
	val deviceId: Int? = null,

	@field:SerializedName("data_modified")
	val dataModified: Long? = null,

	@field:SerializedName("name")
	val name: String? = null,

	@field:SerializedName("imei")
	val imei: String? = null,

	@field:SerializedName("data_created")
	val dataCreated: Long? = null,

	@field:SerializedName("data_status")
	val dataStatus: Int? = null,

	@field:SerializedName("admin_fkid")
	val adminFkid: Int? = null,

	@field:SerializedName("open_shift_fkid")
	val openShiftFkid: Int? = null,

	@field:SerializedName("outlet_fkid")
	val outletFkid: Int? = null,

	@field:SerializedName("outlet_name")
	val outletName: String? = null,

	@field:SerializedName("employee_name")
	val employeeName: String? = null
)