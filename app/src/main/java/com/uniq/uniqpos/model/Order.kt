package com.uniq.uniqpos.model

import android.annotation.SuppressLint
import android.os.Parcelable
import com.uniq.uniqpos.data.local.entity.MemberEntity
import com.uniq.uniqpos.data.local.entity.ProductEntity
import com.uniq.uniqpos.data.local.entity.PromotionEntity
import kotlinx.android.parcel.Parcelize
import java.io.Serializable

/**
 * Created by ANNASBlackHat on 14/10/2017.
 */
@Parcelize
@SuppressLint("ParcelCreator")
data class Order(var product: ProductEntity? = null,
                 var qty: Int = 1,
                 var price: Int = 0, var subTotal: Int = 0,
                 var discount: Discount = Discount(),
                 var extra: ArrayList<Order> = ArrayList(),
                 var isItemVoid: Boolean = false,
                 var voidInfo: String? = null,
                 var isHold: Boolean = false, // true if item saved to cart
                 var holdQty: Int = 0,
                 var voidedQty: Int = 0,
                 var printed: Int = 0,
                 var tmpId: Long = System.currentTimeMillis(),
                 var taxes: ArrayList<TaxSales> = ArrayList(),
                 var note: String? = null, //given by employee
                 var employeeId: Int = 0,
                 var employeeName: String? = null,
                 var member: MemberEntity? = null,
                 var discountDetail: Discount? = null,
                 var priceAdd: Int = 0,
                 var info: String? = null, //given by system
                 var voidEmployeeAuthId: Int = 0,
                 var promotion: PromotionSales? = null,
                 var voidParentId: Long = 0,
                 var grandTotalWatcher: GrandTotalWatcher? = null,
                 var extraType: String? = null
) : Parcelable {
    val enableAddQty: Boolean
        get() {
            return if (isItemVoid) false else promotion?.parentId == null
        }
}

