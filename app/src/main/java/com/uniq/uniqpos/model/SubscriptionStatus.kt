package com.uniq.uniqpos.model

import com.google.gson.annotations.SerializedName

data class SubscriptionStatus(

        @field:SerializedName("warning_message")
        val warningMessage: String? = null,

        @field:SerializedName("subscribed")
        val subscribed: Boolean = false,

        @field:SerializedName("time_expired")
        val timeExpired: Long = 0,

        @field:SerializedName("time_now")
        val timeNow: Long = System.currentTimeMillis(),

        @field:SerializedName("count_unit")
        val countUnit: String = "day",

        @field:SerializedName("subscription_type")
        val subscriptionType: String = "trial",

        @field:SerializedName("time_expired_count")
        val timeExpiredCount: Int = 0,

        @field:SerializedName("subscription_message")
        val subscriptionMessage: String? = null,

        @field:SerializedName("subscription_url")
        val subscriptionUrl: String? = null,

        @field:SerializedName("device_login")
        val deviceLogin: ArrayList<Device>
)