package com.uniq.uniqpos.model


import com.google.gson.annotations.SerializedName

data class RoleMobile(

		@field:SerializedName("voucher")
		val voucher: Boolean = false,

		@field:SerializedName("reprint_void")
		val reprintVoid: <PERSON>olean = false,

		@field:SerializedName("gantipajak")
		val gantipajak: Boolean = false,

		@field:SerializedName("setting_printer")
		val settingPrinter: Boolean = false,

		@field:SerializedName("inputpromo")
		val inputpromo: Boolean = false,

		@field:SerializedName("bukalaciuang")
		val bukalaciuang: Boolean = false,

		@field:SerializedName("compliment")
		val compliment: Boolean = false,

		@field:SerializedName("gantidiskonperitem")
		val changeDiscountItem: Boolean = false,

		@field:SerializedName("inputkaskeluar")
		val inputkaskeluar: Boolean = false,

		@field:SerializedName("pembayaran")
		val pembayaran: Boolean = false,

		@field:SerializedName("tutupkasir")
		val tutupkasir: Boolean = false,

		@field:SerializedName("gantiharga")
		val gantiharga: Boolean = false,

		@field:SerializedName("reprint_nota")
		val reprintNota: Boolean = false,

		@field:SerializedName("inputbaranghabis")
		val inputbaranghabis: Boolean = false,

		@field:SerializedName("reprint_order")
		val reprintOrder: Boolean = false,

		@field:SerializedName("inputpembelian")
		val inputpembelian: Boolean = false,

		@field:SerializedName("gantidiskonperbill")
		val gantidiskonperbill: Boolean = false,

		@field:SerializedName("reprint_refund")
		val reprintRefund: Boolean = false,

		@field:SerializedName("gantivoucherperitem")
		val gantivoucherperitem: Boolean = false,

		@field:SerializedName("inputbarangrusak")
		val inputbarangrusak: Boolean = false,

		@field:SerializedName("simpankeorderlist")
		val simpankeorderlist: Boolean = false,

		@field:SerializedName("duty")
		val duty: Boolean = false,

		@field:SerializedName("inputkasmasuk")
		val inputkasmasuk: Boolean = false,

		@field:SerializedName("reprint_tutupkasir")
		val reprintTutupkasir: Boolean = false,

		@field:SerializedName("refund")
		val refund: Boolean = false,

		@field:SerializedName("diskonall")
		val diskonAll: Boolean = false,

		val diskonperitem: Boolean = false,

		val freeitem: Boolean = false,

		val free: Boolean = false,

		@field:SerializedName("reprint_receipt")
		val reprintReceipt: Boolean = false,

		val closeshift: Boolean = false,

		val void: Boolean = false,

		@field:SerializedName("reprint_closeshift")
		val reprintCloseShift: Boolean = false,

		@field:SerializedName("viewtransactionhistory")
		val viewtransactionhistory: Boolean = false,

		@field:SerializedName("viewcloseregister")
		val viewcloseregister: Boolean = false,

		@field:SerializedName("viewtotalachievement")
		val viewtotalachievement: Boolean = false,

		@field:SerializedName("print_dailyrecap")
		val printDailyrecap: Boolean = false,

		@field:SerializedName("product_create")
		val productCreate: Boolean = false,

		val authorization: RoleMobile? = null
)