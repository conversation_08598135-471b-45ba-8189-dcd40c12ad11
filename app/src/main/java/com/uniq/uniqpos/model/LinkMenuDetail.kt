package com.uniq.uniqpos.model

import com.google.gson.annotations.SerializedName


data class LinkMenuDetail(

        @field:SerializedName("linkmenu_fkid")
        val linkmenuFkid: Int = 0,

        @field:SerializedName("data_modified")
        val dataModified: Long? = null,

        @field:SerializedName("linkmenu_detail_id")
        val linkmenuDetailId: Int? = null,

        @field:SerializedName("price_add")
        val priceAdd: Int? = null,

        @field:SerializedName("data_created")
        val dataCreated: Long? = null,

        @field:SerializedName("product_detail_fkid")
        val productDetailFkid: Int = 0
)