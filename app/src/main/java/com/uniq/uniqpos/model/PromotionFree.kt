package com.uniq.uniqpos.model

import com.uniq.uniqpos.data.local.entity.PromotionProduct

data class PromotionFree(
    var productName: String = "",
    var price: Int = 0,
    var promoName: String? = null,
    var promoQtyInfo: String? = null,
    var promoInfoDetail: String? = null,
    var productDetailFkid: Int? = null,
    var qtyMax: Int = 0,
    var qtySelected: Int = 0,
    var promotionId: Int = 0,
    var isHeader: Boolean = false,
    var promotionTypeId: Int = 0,
    var minOrder: Int = 0,
    var productBuyIds: ArrayList<Int> = ArrayList(),
    var isUnlimitedItem: Boolean = false,
    var productTerms: ArrayList<PromotionProduct> = ArrayList(),
    var extra: ArrayList<Order> = ArrayList(),
)