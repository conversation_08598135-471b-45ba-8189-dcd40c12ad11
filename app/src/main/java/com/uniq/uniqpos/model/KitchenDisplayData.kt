package com.uniq.uniqpos.model


import com.google.gson.annotations.SerializedName

data class KitchenDisplayData(
    @SerializedName("client_id")
    val clientId: Int,
    @SerializedName("customer_name")
    val customerName: String,
    @SerializedName("data_status")
    val dataStatus: String,
    @SerializedName("date_created")
    val dateCreated: Long,
    @SerializedName("dining_table")
    val diningTable: String,
    @SerializedName("display_nota")
    val displayNota: String,
    @SerializedName("order_details")
    val orderDetails: String,
    @SerializedName("order_id")
    val orderId: String,
    @SerializedName("order_type")
    val orderType: String,
    @SerializedName("type")
    val type: String,
    @SerializedName("is_tmp_sales")
    val isTmpSales: Int,
)