package com.uniq.uniqpos.model

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import com.uniq.uniqpos.util.Constant
import kotlinx.android.parcel.Parcelize

/**
 * Created by annasblackhat on 2019-06-25
 */

@Parcelize
data class PromotionSales (
        @field:SerializedName("promotion_id")
        var promotionId: Int = 0,
        var name: String = "",
        @field:SerializedName("promotion_type_fkid")
        var pomotionTypeFkid: Int = 0,
        var promotionCode: String? = null,
        var discount: Int = 0,
        var parentId: String? = null,
        var minOrder: Int = 0,
        @field:SerializedName("promotion_value")
        var promotionValue: Int = 0,
        var maxQtyPromo: Int? = null
): Parcelable

data class PromotionSalesTypeTranslation(
        @field:SerializedName("promotion_type_id")
        var promotionTypeId: Int = 0,
        var parentName: String = "",
        var name: String = "",
)

//fun PromotionSales.readablePromoType(default: String = ""): String {
//        return ""
//}

//| promotion_type_id | parent_id | name             | active | created             |
//|-------------------|-----------|------------------|--------|---------------------|
//|                 1 |         0 | Special Promo    |      1 | 2017-12-21 04:37:59 |
//|                 2 |         0 | Member Promo     |      1 | 2017-12-21 04:38:04 |
//|                 3 |         0 | Voucher Promo    |      1 | 2017-12-21 04:38:10 |
//|                 4 |         1 | Special Price    |      1 | 2017-12-21 06:03:57 |
//|                 5 |         1 | Discount         |      1 | 2017-12-21 06:04:01 |
//|                 6 |         1 | Free             |      1 | 2017-12-21 06:04:05 |
//|                 7 |         2 | Special Price    |      1 | 2017-12-21 06:04:31 |
//|                 8 |         2 | Discount         |      1 | 2017-12-21 06:04:34 |
//|                 9 |         2 | Free             |      1 | 2017-12-21 06:04:36 |
//|                10 |         2 | Point Redeem     |      1 | 2017-12-21 06:04:42 |
//|                11 |         3 | Special Price    |      1 | 2017-12-21 06:05:08 |
//|                12 |         3 | Discount         |      1 | 2017-12-21 06:05:12 |
//|                13 |         3 | Free             |      1 | 2017-12-21 06:05:14 |
//|                14 |         2 | Point Collection |      1 | 2018-03-28 17:41:55 |
//|                15 |         3 | Deals            |      1 | 2019-01-28 11:43:58 |

fun GetPromoTypeTranslation(id: Int): PromotionSalesTypeTranslation {
        val promoMap = hashMapOf(
                Constant.PROMO_TYPE_SPECIAL_PRICE to PromotionSalesTypeTranslation(id, "Special Promo", "Special Price"),
                Constant.PROMO_TYPE_DISCOUNT to PromotionSalesTypeTranslation(id, "Special Promo", "Discount"),
                Constant.PROMO_TYPE_FREE to PromotionSalesTypeTranslation(id, "Special Promo", "Free"),

                Constant.PROMO_TYPE_SPECIAL_PRICE_MEMBER to PromotionSalesTypeTranslation(id, "Member Promo", "Special Price"),
                Constant.PROMO_TYPE_DISCOUNT_MEMBER to PromotionSalesTypeTranslation(id, "Member Promo", "Discount"),
                Constant.PROMO_TYPE_FREE_MEMBER to PromotionSalesTypeTranslation(id, "Member Promo", "Free"),

                Constant.PROMO_TYPE_SPECIAL_PRICE_VOUCHER to PromotionSalesTypeTranslation(id, "Voucher Promo", "Special Price"),
                Constant.PROMO_TYPE_DISCOUNT_VOUCHER to PromotionSalesTypeTranslation(id, "Voucher Promo", "Discount"),
                Constant.PROMO_TYPE_FREE_VOUCHER to PromotionSalesTypeTranslation(id, "Voucher Promo", "Free"),
                Constant.PROMO_TYPE_DEALS to PromotionSalesTypeTranslation(id, "Voucher Promo", "Deals"),
        )
        return promoMap.getOrElse(id) { PromotionSalesTypeTranslation() }
}