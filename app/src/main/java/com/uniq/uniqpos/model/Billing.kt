package com.uniq.uniqpos.model

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class Billing(
        @SerializedName("account_number")
        val accountNumber: String,
        @SerializedName("bank_code")
        val bankCode: String,
        @SerializedName("currency")
        val currency: String,
        @SerializedName("expected_amount")
        val expectedAmount: Int,
        @SerializedName("expiration_date")
        val expirationDate: String,
        @SerializedName("external_id")
        val externalId: String,
        @SerializedName("id")
        val id: String,
        @SerializedName("is_closed")
        val isClosed: <PERSON><PERSON><PERSON>,
        @SerializedName("is_single_use")
        val isSingleUse: Boolean,
        @SerializedName("merchant_code")
        val merchantCode: String,
        @SerializedName("name")
        val name: String,
        @SerializedName("owner_id")
        val ownerId: String,
        @SerializedName("status")
        val status: String
): Parcelable