package com.uniq.uniqpos.util.lifecycle

import android.app.Activity
import android.content.Context
import android.content.pm.PackageManager
import android.view.View
import androidx.activity.result.ActivityResultLauncher
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.databinding.ViewDataBinding
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.LiveData
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModel
import com.google.android.material.snackbar.Snackbar
import com.uniq.uniqpos.model.SnackbarEvent
import com.uniq.uniqpos.model.ToastMessage
import com.uniq.uniqpos.util.safe
import com.uniq.uniqpos.util.showMessage
import com.uniq.uniqpos.util.toast
import com.uniq.uniqpos.view.global.BaseActivity
import com.uniq.uniqpos.view.global.BaseFragment
import timber.log.Timber

fun View.setupSnackbar(
    lifecycleOwner: LifecycleOwner,
    snackbarEvent: LiveData<Event<SnackbarEvent>>
) {
    snackbarEvent.observe(lifecycleOwner, Observer { event ->
        event.getContentIfNotHandled()?.let { model ->
            val msg = model.msg ?: context.getString(model.msgResource)
            showSnackbar(msg, model.timeLength)
            Timber.i("[[SNACKBAR]] $msg")
        }
    })
}

fun Context?.setupToast(lifecycleOwner: LifecycleOwner, toastEvent: LiveData<Event<String>>) {
    toastEvent.observe(lifecycleOwner) { event ->
        event.getContentIfNotHandled()?.let { this?.toast(it) }
    }
}

fun Context?.setupToastMessage(lifecycleOwner: LifecycleOwner, toastEvent: LiveData<Event<ToastMessage>>){
    toastEvent.observe(lifecycleOwner) { event ->
        event.getContentIfNotHandled()?.let {
            val msg = it.messageResId?.let { id ->this?.resources?.getString(id) } ?: kotlin.run { it.message }
            this?.toast(msg, level = it.type)
        }
    }
}

fun Context?.setupDialogMessage(
    lifecycleOwner: LifecycleOwner,
    dialogEvent: LiveData<Event<String>>
) {
    dialogEvent.observe(lifecycleOwner) { event ->
        event.getContentIfNotHandled()?.let { this?.showMessage(it, "INFORMATION") }
    }
}

fun <VM : ViewModel, DB : ViewDataBinding> Context?.setupLoadingDialog(
    base: BaseActivity<VM, DB>,
    lifecycleOwner: LifecycleOwner,
    loadingEvent: LiveData<Event<Boolean>>
) {
    loadingEvent.observe(lifecycleOwner) { event ->
        event.getContentIfNotHandled()?.let { isShow -> base.showDialog(isShow) }
    }
}

fun <VM : ViewModel, DB : ViewDataBinding> Context?.setupLoadingDialog(
    base: BaseFragment<VM, DB>,
    lifecycleOwner: LifecycleOwner,
    loadingEvent: LiveData<Event<Boolean>>
) {
    loadingEvent.observe(lifecycleOwner) { event ->
        event.getContentIfNotHandled()?.let { isShow -> base.showDialog(isShow) }
    }
}

fun <VM : ViewModel, DB : ViewDataBinding> Context?.setupLoadingDialogMessage(
    base: BaseActivity<VM, DB>,
    lifecycleOwner: LifecycleOwner,
    loadingEvent: LiveData<Event<Pair<String, Boolean>>>
) {
    loadingEvent.observe(lifecycleOwner) { event ->
        event.getContentIfNotHandled()?.let {  base.showDialog(it.second, it.first) }
    }
}

fun Context.setupPermissionRequest(
    lifecycleOwner: LifecycleOwner,
    event: LiveData<Event<String>>,
    permissionLauncher: ActivityResultLauncher<String>? = null,
    activity: Activity? = null,
) {
    event.observe(lifecycleOwner) {
        it.getContentIfNotHandled()?.let { permission ->
            if (ContextCompat.checkSelfPermission(
                    this,
                    permission
                ) != PackageManager.PERMISSION_GRANTED
            ) {
                if (permissionLauncher != null) {
                    permissionLauncher.launch(permission)
                } else if (activity != null) {
                    ActivityCompat.requestPermissions(activity, arrayOf(permission), 0)
                }
            }else if (activity?.shouldShowRequestPermissionRationale(permission).safe()){
                toast("permission is needed")
            }
        }
    }
}

fun View.showSnackbar(snackbarText: String, timeLength: Int = Snackbar.LENGTH_SHORT) {
    Snackbar.make(this, snackbarText, timeLength).run {
        //can add callback here,
        show()
    }
}
