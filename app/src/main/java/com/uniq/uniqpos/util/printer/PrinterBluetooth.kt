package com.uniq.uniqpos.util.printer

import android.Manifest
import android.bluetooth.BluetoothAdapter
import android.bluetooth.BluetoothDevice
import android.bluetooth.BluetoothSocket
import android.content.Context
import android.content.pm.PackageManager
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.os.Build
import android.util.Log
import com.dantsu.escposprinter.EscPosPrinter
import com.dantsu.escposprinter.EscPosPrinterCommands
import com.dantsu.escposprinter.connection.DeviceConnection
import com.dantsu.escposprinter.connection.bluetooth.BluetoothConnection
import com.dantsu.escposprinter.connection.bluetooth.BluetoothConnections
import com.dantsu.escposprinter.connection.bluetooth.BluetoothPrintersConnections
import com.dantsu.escposprinter.textparser.PrinterTextParserImg
import com.google.firebase.ktx.Firebase
import com.google.firebase.remoteconfig.FirebaseRemoteConfig
import com.google.firebase.remoteconfig.ktx.remoteConfig
import com.google.gson.Gson
import com.uniq.uniqpos.BuildConfig
import com.uniq.uniqpos.R
import com.uniq.uniqpos.util.*
import com.uniq.uniqpos.util.printer.PrinterCommand.Companion.ESC
import com.uniq.uniqpos.util.printer.PrinterCommand.Companion.LF
import io.ktor.utils.io.core.toByteArray
import kotlinx.coroutines.*
import timber.log.Timber
import java.io.*
import java.util.*


/**
 * Created by annasblackhat on 19/05/18
 */

class PrinterBluetooth(val app: Context) {

    val uuid = UUID.fromString("00001101-0000-1000-8000-00805F9B34FB")

    var fontNormal = byteArrayOf(0x1B, 0x21, 0x00)  // 0- normal size text
    var fontBold = byteArrayOf(0x1B, 0x21, 0x08)  // 1- only bold text
    var fontBoldMedium = byteArrayOf(0x1B, 0x21, 0x20) // 2- bold with medium text
    var fontBoldLarge = byteArrayOf(0x1B, 0x21, 0x10) // 3- bold with large text

    //\u001b\u0061\u0001
    val ALIGN_CENTER = byteArrayOf(0x1b, 'a'.toByte(), 0x01)
    val PRINT_TEST = byteArrayOf(0x1D, 0x28, 0x41)

    private var mOutputStream: OutputStream? = null
    private var mInputStream: InputStream? = null
    private var bSocket: BluetoothSocket? = null
    private lateinit var device: BluetoothDevice
    private var bAdapter: BluetoothAdapter? = null
    private lateinit var readBuffer: ByteArray
    private var readBufferPosition: Int = 0
    private var stopWorker: Boolean = false
    private var workerThread: Thread? = null
    private var isWorkerRunning = false

    private var escPosPrinter: EscPosPrinter? = null
    private var escDevice: BluetoothConnection? = null
    private var escPosAddress: String? = null

    private fun initEscPrinter(address: String) {
        Timber.d("[printer] connected ${escDevice?.isConnected.safe()}")
        if (address == escPosAddress && escDevice?.isConnected.safe()) {
            Timber.i("escPrinter already initialized")
            return
        }
        val btList = BluetoothConnections().list
//        val btList = BluetoothPrintersConnections().list
        btList?.firstOrNull { it.device.address == address }?.let { device ->
            try {
                this.escPosAddress = address
                this.escDevice = device
                this.escPosPrinter = EscPosPrinter(this.escDevice, 203, 48f, 32)
            } catch (e: Exception) {
                Timber.i("[printer] init escPrinter err: $e")
                throw e
            }
        } ?: run {
            Timber.i("[printer] init escPrinter err: not found '$address' | ${btList?.size} | addrs: ${btList?.map { it.device.address }} ")
            throw Exception("device not found")
        }
    }

    fun openCashDrawer(address: String, callback: (PrintResponse) -> Unit) {
        if (app.deactivatePrinterList().any { it == address }) {
            callback(
                PrintResponse(
                    false,
                    "This printer BT '$address' is deactivated at this device!"
                )
            )
            return
        } else if (!app.role().bukalaciuang) {
            callback(PrintResponse(false, "User has no permission to open cash drawer!"))
            return
        }

        //val useV2 = BuildConfig.DEBUG || app.outlet()?.experimentConfig()?.printer.safe()
        val useV2 = Firebase.remoteConfig.getBoolean("print_bluetooth")
        Timber.i("opening cashdrawer useV2? $useV2")
        if (useV2) {
            openCashDrawerV2(address, callback)
            return
        }

        //if (!BuildConfig.DEBUG) {
        //    callback(PrintResponse(true, "non debug is currently forbid to open cash drawer"))
        //    return
        //}

        if (!initDevice(address)) {
            callback(PrintResponse(false, "device not found"))
            return
        }

        if (mOutputStream == null) {
            connectPrinter()
        }
//      val status = connectPrinter()

        try {
//            mOutputStream?.write(">>OPEN CASH DRAWER<<<".toByteArray())
            mOutputStream?.write(PrinterCommand.openCashDrawer)
            mOutputStream?.write(PrinterCommand.CD_KICK_2)
            mOutputStream?.write(PrinterCommand.CD_KICK_5)
        } catch (e: Exception) {
            Timber.i("opening cash drawer error $e")
        }
        callback(PrintResponse(true))

//        BluetoothConnector(device, false, BluetoothAdapter.getDefaultAdapter(), null)
//                .connect()
//                .outputStream
//                .write(PrinterCommand.openCashDrawer)
//        callback(PrintResponse(true))
    }

    private fun openCashDrawerV2(address: String, callback: (PrintResponse) -> Unit) {
        try {
            initEscPrinter(address)
            escDevice?.let { device ->
                EscPosPrinterCommands(device).openCashBox()
            }
            callback(PrintResponse(escDevice != null))
//            BluetoothPrintersConnections().list?.firstOrNull { it.device.address == address }?.let { device ->
//                val printer = EscPosPrinter(device, 203, 48f, 32)
//                EscPosPrinterCommands(device).openCashBox()
//                printer.disconnectPrinter()
//            } ?: run {
//                callback(PrintResponse(false, "device not found"))
//            }
        } catch (e: Exception) {
            callback(PrintResponse(false, "error: $e"))
        }
    }

    fun printText(
        address: String? = null,
        callback: (PrintResponse) -> Unit,
        bitmap: Bitmap? = null,
        vararg texts: String
    ) {
        if (app.deactivatePrinterList().any { it == address }) {
            Timber.i("(Printer BT) $address is deactivate at this device")
            callback(PrintResponse(true, "(Printer BT) $address is deactivate at this device"))
            return
        }

//        val useV2 = BuildConfig.DEBUG || app.outlet()?.experimentConfig()?.printer.safe()
        val useV2 = Firebase.remoteConfig.getBoolean("print_bluetooth")
        Timber.i("useV2? $useV2")
        if (useV2) {
            if (printTextV2(address, callback, bitmap, *texts)) {
                return
            }
        }

        if (!initDevice(address)) {
            callback(PrintResponse(false, "printer $address belum dipairing"))
            return
        }

        GlobalScope.launch(Dispatchers.IO) {
            val status = withContext(Dispatchers.Default) { connectPrinter() }

            Timber.i(">> Connect Printer BT Status : $status | address : $address")
            if (status) {
                var logoByte: ByteArray? = null
                app.receiptLogoFile()?.let { file ->
                    try {
//                        file.toImageByte()
                        val isFileExist = file.exists()
                        Timber.i("Printer bluetooth set logo... - File logo path : ${file.path}, isFileExist: $isFileExist")
                        if (isFileExist) {
                            logoByte = file.toImageByteResized() //getByteArrayFromFile(file)
                        }
                    } catch (e: Exception) {
                        Timber.i("Create byteArray from bitmap to print via BT error - $e")
                    }
                }

                mOutputStream?.write(EscPosPrinterCommands.RESET_PRINTER)
                mOutputStream?.write(EscPosPrinterCommands.TEXT_ALIGN_CENTER)

                texts.forEach { data ->
                    val textSplit = data.split(Constant.PRINTER_CODE_LOGO)
                    textSplit.map {
                        it.replace(Constant.PRINTER_CODE_CUT, "")
                            .replace(Constant.PRINTER_CODE_LOGO, "")
                    }.forEachIndexed { index, txt ->
                        try {
                            logoByte?.takeIf { index > 0 }?.let {
                                mOutputStream?.write(it)
                                mOutputStream?.write("\n".toByteArray())
                            }
                            mOutputStream?.write(txt.toByteArray())
//                            val bw = OutputStreamWriter(mOutputStream, "8859_6")
//                            val pw = PrintWriter(bw, true)
//                            pw.println("\u001b\u0061\u0001 $txt")
//                            bw.close()
//                            pw.close()
                        } catch (e: Exception) {
                            Timber.i("Failed to write!! $e")
                            bSocket?.close()
                        }
                    }
                }
                callback(PrintResponse(status))
            } else {
                callback(PrintResponse(status, "tidak dapat terhubung ke printer $address"))
            }
            Timber.i("Print BT v1 process finish...")
        }
    }

    private fun printTextV2(
        address: String? = null,
        callback: (PrintResponse) -> Unit,
        bitmap: Bitmap? = null,
        vararg texts: String
    ): Boolean {
        try {
            initEscPrinter(address.safe())
            val textToPrint = StringBuilder()

            Timber.d("printing, has bitmap: ${bitmap != null}")
            bitmap?.let { bmp ->
                Timber.d("printing: adding bitmap")
                textToPrint.append(
                    "[C]<img>${
                        PrinterTextParserImg.bitmapToHexadecimalString(
                            escPosPrinter,
                            bmp
                        )
                    }</img>\n"
                )
                textToPrint.append("[L]\n")
            }

            var logoBmp: Bitmap? = null
            try {
                val withLogo = texts.any { it.contains(Constant.PRINTER_CODE_LOGO) }
                app.takeIf { withLogo }?.receiptLogoFile()?.let { file ->
                    if (file.exists()) {
                        logoBmp = BitmapFactory.decodeFile(file.path)
                        Timber.i(">> Image is exist | Logo : $logoBmp")
                    }
                }
            } catch (e: Exception) {
                Timber.i("Getting image logo to print error : $e")
            }

            texts.forEach { data ->
                val textSplit = data.split(Constant.PRINTER_CODE_LOGO)
//                data.splitWithSplitters(Constant.PRINTER_CODE_LOGO, Constant.PRINTER_CODE_QR)
//                Timber.d("printing ${Gson().toJson(textSplit)}")
                val cutCommand = ".[L]\n[L]\n[L]\n${PrinterCommand.COMMAND_CUT}"

                textSplit.map {
                    it.replace(Constant.PRINTER_CODE_CUT, cutCommand)
                        .replace(Constant.PRINTER_CODE_LOGO, "")
                        .replace(Regex("${Constant.PRINTER_CODE_QR}(.*?)${Constant.PRINTER_CODE_QR}"), "<qrcode size='30'>$1</qrcode>\n")
                }.forEachIndexed { index, txt ->
                    try {
                        Timber.d("printing: $index | $txt")
                        logoBmp?.takeIf { index > 0 }?.let { bmp ->
                            Timber.d("printing: adding logo")
                            textToPrint.append(
                                "[C]<img>${
                                    PrinterTextParserImg.bitmapToHexadecimalString(
                                        escPosPrinter,
                                        bmp
                                    )
                                }</img>\n"
                            )
                            textToPrint.append("[L]\n")
                        }
                        textToPrint.appendLine("[C]$txt")
//                        textToPrint.append(txt.addPrefixOnNewLines("[C]"))
                    } catch (e: Exception) {
                        Timber.i("Failed to write!! $e")
                    }
                }
            }

            Timber.d("printing: result -> $textToPrint")
            escPosPrinter?.printFormattedText(textToPrint.toString())
//                escPrinter.disconnectPrinter()
            callback(PrintResponse(true))
            Timber.i("print bt v2 success...")
            return true
        } catch (e: Exception) {
            callback(PrintResponse(false, "error: $e"))
        }
        return false
    }

    fun printTest(address: String? = null, callback: (PrintResponse) -> Unit) {
        if (!initDevice(address)) {
            callback(
                PrintResponse(
                    false,
                    app.getString(R.string.device_not_paired_info),
                    PRINT_ERR_NOT_PAIRED
                )
            )
            return
        }

        Timber.i(">>> begin to connect....")
        val status = connectPrinter()
        Timber.i(">>> connection finished....")
        if (status) {
            val ESC = 27.toChar().toString()
            val reset = "$ESC@".toByteArray()

            mOutputStream?.write(EscPosPrinterCommands.RESET_PRINTER)
            mOutputStream?.write(EscPosPrinterCommands.TEXT_ALIGN_CENTER)

            var isStoreLogoPrinted = false
            try {
                app.receiptLogoFile()?.let { file ->
//                    getByteArrayFromFile(file)?.let { logoByte ->
//                        mOutputStream?.write(logoByte)
//                        mOutputStream?.flush()
//                        Thread.sleep(50)
////                        mOutputStream?.write(EscPosPrinterCommands.RESET_PRINTER)
////                        mOutputStream?.write(byteArrayOf(EscPosPrinterCommands.LF))
////                        val feed = "${ESC}d" + 4.toChar()
////                        mOutputStream?.write(feed.toByteArray())
////                        mOutputStream?.write(byteArrayOf(ESC.toByte(), "d".toByte(), 4)) //feed x line
////                        mOutputStream?.write(EscPosPrinterCommands.RESET_PRINTER)
//                        isStoreLogoPrinted = true
//                    }
//
                    file.toImageByteResized()?.let { logoByte ->
                        mOutputStream?.write(logoByte)
                        mOutputStream?.flush()
                        Thread.sleep(50)
                        isStoreLogoPrinted = true
                    }

//                    file.toImageByte()?.let { logoByte ->
//                        mOutputStream?.write(logoByte)
//                        mOutputStream?.flush()
//                    }
                }
            } catch (e: Exception) {
                Timber.i("PrintTes logo outlet err: $e")
            }

            //if not printed, print UNIQ logo
            if(!isStoreLogoPrinted){
                try {
//                    val bmp = BitmapFactory.decodeResource(app.resources, R.drawable.logo_black_small_bitmap)
//                    val command = BitmapUtils.decodeBitmap(bmp)
//                mOutputStream?.write(PrinterUtil.printfont((app.getString(R.string.print_test_success) + "\n"), PrinterUtil.FONT_32PX, PrinterUtil.Align_CENTER, 0x1A.toByte(), PrinterUtil.LANGUAGE_ENGLISH))
//                mOutputStream?.write(command)
//                mOutputStream?.write("\n".toByteArray())
                } catch (e: Exception) {
                    Timber.i("PrintTest BT, logo err - $e")
                }
//                mOutputStream?.write("\n\n\n".toByteArray()
            }

            val textToPrint = "\n### UNIQ POS PRINT TEST ###\n" +
                    "App Version ${BuildConfig.VERSION_NAME}\n" +
                    "Time ${System.currentTimeMillis().dateTimeFormat()}\n" +
                    "Address $address\n" +
                    "Printer Type BLUETOOTH\n" +
                    ""
//
            mOutputStream?.write(textToPrint.toByteArray())
//            mOutputStream?.write(EscPosPrinterCommands.RESET_PRINTER)
//            mOutputStream?.write(PrinterCommand.openCashDrawer)
//            mOutputStream?.write(byteArrayOf(ESC.toByte(), "d".toByte(), 4)) //feed x line
//            mOutputStream?.write(byteArrayOf(EscPosPrinterCommands.LF))
            mOutputStream?.flush()

//           val GS = 29.toChar().toString()

//            val COMMAND = "$ESC@" + GS + "V" + 1.toChar()
//            mOutputStream?.write(COMMAND.toByteArray()) //to reset

            Timber.i("PrintTest BT finish...")
        }
        callback(
            PrintResponse(
                status,
                message = app.getString(R.string.can_not_connect_to_printer_info)
            )
        )
    }

    private fun initDevice(address: String?): Boolean {
        val deviceList = getPrintersInRange()
        deviceList.firstOrNull { it.address == address }?.let { bluetoothDevice ->
            device = bluetoothDevice
        } ?: kotlin.run {
            Timber.i(
                "could not find device with address: $address - listDevice: ${
                    Gson().toJson(
                        deviceList
                    )
                }"
            )
            return false
        }
        return true
    }

    private fun getPrintersInRange(): ArrayList<BluetoothDevice> {
        val list = arrayListOf<BluetoothDevice>()

        //checking permission
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            //Manifest.permission.BLUETOOTH_CONNECT
            if (app.checkSelfPermission(Manifest.permission.BLUETOOTH_CONNECT) != PackageManager.PERMISSION_GRANTED) {
                app.toast("bluetooth permission not granted!")
                return list
            }
        }

        bAdapter = BluetoothAdapter.getDefaultAdapter()
        bAdapter?.let {
            if (!it.isEnabled) it.enable()
            val pairedDevice = it.bondedDevices
            pairedDevice?.forEach { p -> list.add(p) }
        }
            ?: kotlin.run { app.toast("No Bluetooth device detected!") }
        return list
    }

    private fun connectPrinter(): Boolean {
        val logMsg = StringBuilder()
        Timber.i("current device: ${device.address} | bsocket addres : ${bSocket?.remoteDevice?.address}")
        if (mOutputStream != null && device.address == bSocket?.remoteDevice?.address) {
            logMsg.append("printer already connected! ")
            try {
                mOutputStream?.write(".".toByteArray())
                logMsg.append("try to print success... ")
                Timber.i(logMsg.toString())
                return true
            } catch (e: Exception) {
                logMsg.appendLine("but,,, trying print error  ")
                bSocket?.close()
            }
        }

        logMsg.append("start connecting...")
        try {

            logMsg.append("close socket... ")
            bSocket?.close()

            logMsg.append("createRfcommSocketToServiceRecord - ")
            bSocket = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                device.javaClass.getMethod("createRfcommSocket", Int::class.java)
                    .invoke(device, 1) as BluetoothSocket
            } else {
                device.createRfcommSocketToServiceRecord(uuid)
            }

            logMsg.append("try to connect - ")
            bSocket?.connect()

//            mOutputStream?.close()
//            mInputStream?.close()

            logMsg.append("init mOutputStream and mInputStream - ")
            mOutputStream = bSocket?.outputStream
            mInputStream = bSocket?.inputStream

            if (!isWorkerRunning) {
                beginListenerForData()
            }
            return true
        } catch (e: Exception) {
            Timber.i("connection to printer bluetooth failed, log : $logMsg | err : $e")
            bSocket?.close()
            return false
        }
    }

    private fun beginListenerForData() {
        val delimiter: Byte = 10

        isWorkerRunning = true
        stopWorker = false
        readBufferPosition = 0
        readBuffer = ByteArray(1024)

        Timber.i("beginListenerForData...")
        workerThread = Thread(Runnable {
            while (!Thread.currentThread().isInterrupted && !stopWorker) {
                try {
                    val bytesAvailable = mInputStream?.available() ?: 0
                    if (bytesAvailable > 0) {
                        val packetBytes = ByteArray(bytesAvailable)
                        mInputStream?.read(packetBytes)

                        for (i in 0 until bytesAvailable - 1) {
                            val b = packetBytes[i]
                            if (b == delimiter) {
                                val encodedBytes = ByteArray(readBufferPosition)
                                System.arraycopy(
                                    readBuffer, 0,
                                    encodedBytes, 0,
                                    encodedBytes.size
                                )
                                //                            val data = String(encodedBytes, Charset("US-ASCII"))
                                readBufferPosition = 0
                                //                            handler.post { pushInfo(data) }
                            } else {
                                readBuffer[readBufferPosition++] = b
                            }
                        }
                    }
                } catch (e: Exception) {
                    Timber.i("Write error $e")
                    stopWorker = true
                    isWorkerRunning = false
                }
            }
        })

        workerThread?.start()
    }

    fun disconnect() {
        stopWorker = true
        try {
            mInputStream?.close()
            mInputStream = null
        } catch (e: Exception) {
            Timber.i(">> Close input stream failed - $e")
        }
        try {
            mOutputStream?.close()
            mOutputStream = null
        } catch (e: Exception) {
            Timber.i(">> Close output stream failed - $e")
        }
        try {
            bSocket?.close()
            bSocket = null
        } catch (e: Exception) {
            Timber.i(">> Close bluetooth socket failed - $e")
        }
        try {
            workerThread?.interrupt()
            workerThread = null
        } catch (e: Exception) {
            Timber.i(">> Interrupt worker thread error  - $e")
        }
        Timber.i("Printer bluetooth disconnected!")
    }

    private fun getByteArrayFromFile(file: File): ByteArray? {
        // try {
        //     val result = file.toImageByte()
        //     Timber.i("getByteArrayFromFile, use extension, isNull? ${result == null}")
        //     if(result != null) return result
        // } catch (e: Exception) {
        //     Timber.i("getByteArrayFromFile using extension err: $e")
        // }

        val processLog = StringBuilder()
        try {
            processLog.append("decode file -> ")
            val logo: Bitmap? = try {
                BitmapFactory.decodeFile(file.path)
            } catch (e: Exception) {
                Timber.i("decode file with first way error : $e")
                BitmapFactory.decodeFile(file.absolutePath)
            }

            //                            Timber.i("Logo File : $logo")
            //                            val stream = ByteArrayOutputStream()
            //                            logo.compress(Bitmap.CompressFormat.PNG, 100, stream)
            //                            logoByte = stream.toByteArray()
            //                            logoByte =  BitmapUtils.decodeBitmap(logo)
            logo?.let {
                processLog.append("get print image -> ")
                val printImg = PrintImage(PrintImage.getResizedBitmap(logo))
                processLog.append("prepare image -> ")
                printImg.prepareImage(PrintImage.dither.floyd_steinberg, 128)
                processLog.append("get image data")
                val result = printImg.printImageData
                Timber.i("get bytes success first way")
                return result
            } ?: run { Timber.i("logo bitmap is null") }
        } catch (e: Exception) {
            Timber.i("[GET BYTE FROM FILE] first way error : $e \nLog : $processLog")
        }

        processLog.clear()
        try {
            val size = file.length()
            val bytes = ByteArray(size.toInt())
            processLog.append("get input stream -> ")
            val buf = BufferedInputStream(FileInputStream(file))
            processLog.append("read bytes -> ")
            buf.read(bytes, 0, bytes.size)
            processLog.append("close -> ")
            buf.close()
            Timber.i("get bytes success second way")
            return bytes
        } catch (e: Exception) {
            Timber.i("[GET BYTE FROM FILE] second way error : $e \nLog : $processLog")
        }

        processLog.clear()
        try {
            val fis = FileInputStream(file)
            val bos = ByteArrayOutputStream()
            val buf = ByteArray(1024)
            var readNum: Int
            while (fis.read(buf).also { readNum = it } != -1) {
                bos.write(buf, 0, readNum) //no doubt here is 0
            }
            return bos.toByteArray()
        } catch (e: Exception) {
            Timber.i("[GET BYTE FROM FILE] third way error : $e \nLog : $processLog")
        }

        return null
    }
}