package com.uniq.uniqpos.util.lifecycle

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.uniq.uniqpos.model.SnackbarEvent
import com.uniq.uniqpos.model.ToastMessage

open class BaseViewModel : ViewModel() {

    protected val _state = MutableLiveData<ViewModelState>()
    val state: LiveData<ViewModelState> = _state

    protected val _snackbarText = MutableLiveData<Event<SnackbarEvent>>()
    val snackbarText: LiveData<Event<SnackbarEvent>> = _snackbarText

    protected val _toastMsg = MutableLiveData<Event<String>>()
    val toastMsg: LiveData<Event<String>> = _toastMsg

    protected val _toastMessage = MutableLiveData<Event<ToastMessage>>()
    val toastMessage: LiveData<Event<ToastMessage>> = _toastMessage

    protected val _dialogMsg = MutableLiveData<Event<String>>()
    val dialogMsg: LiveData<Event<String>> = _dialogMsg

    protected val _loadingDialog = MutableLiveData<Event<Boolean>>()
    val loadingDialog: LiveData<Event<Boolean>> = _loadingDialog

    protected val _loadingDialogMessage = MutableLiveData<Event<Pair<String, Boolean>>>()
    val loadingDialogMessage: LiveData<Event<Pair<String, Boolean>>> = _loadingDialogMessage

    protected val _requestPermission = MutableLiveData<Event<String>>()
    val requestPermission: LiveData<Event<String>> = _requestPermission
}