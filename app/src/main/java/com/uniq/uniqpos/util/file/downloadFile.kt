package com.uniq.uniqpos.util.file

import android.content.Context
import com.bugsnag.android.Bugsnag
import com.uniq.uniqpos.BuildConfig
import com.uniq.uniqpos.data.remote.model.Outlet
import timber.log.Timber
import java.io.BufferedInputStream
import java.io.File
import java.io.FileOutputStream
import java.net.HttpURLConnection
import java.net.URL

fun downloadFile(fileUrl: String, context: Context): String? {
    val folderParent = File(context.filesDir, "app-assets")
    if (!folderParent.exists()) {
        folderParent.mkdirs()
    }
//    folderParent.list()?.forEach { File(folderParent, it).delete() }

    val logoDestinationPath = folderParent.path + "/" + File(fileUrl).name
    if (File(logoDestinationPath).exists()) {
       Timber.i("file path '$logoDestinationPath' already exist... skip download")
        return logoDestinationPath
    }
    var output: FileOutputStream? = null
    var input: BufferedInputStream? = null
    var connection: HttpURLConnection? = null
    try {
        Bugsnag.leaveBreadcrumb("Download logo")
        val url = URL(fileUrl)
        connection = url.openConnection() as HttpURLConnection
        connection.connect()

        val BUFFER_SIZE = 23 * 1024
        input = BufferedInputStream(url.openStream(), BUFFER_SIZE)
        output = FileOutputStream(logoDestinationPath)
        val data = ByteArray(BUFFER_SIZE)
        var count = 0
        while (count != -1) {
            output.write(data, 0, count)
            count = input.read(data, 0, BUFFER_SIZE)
        }

        output.flush()
        Timber.i("Download file $fileUrl success... saved in : $logoDestinationPath")
        return logoDestinationPath
    } catch (e: Exception) {
        Timber.i(">>>> DOWNLOADING FILE $fileUrl ERROR : $e")
    } finally {
        output?.close()
        input?.close()
        connection?.disconnect()
    }
    return null
}