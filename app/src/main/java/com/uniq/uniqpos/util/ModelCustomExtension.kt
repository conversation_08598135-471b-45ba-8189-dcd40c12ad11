package com.uniq.uniqpos.util

import com.uniq.uniqpos.data.local.entity.ProductEntity
import com.uniq.uniqpos.data.local.entity.ProductVariantEntity

fun ProductEntity.getName(variants: List<ProductVariantEntity>): String {
    val variantName = variantFkid?.let { variantId ->
        variants.firstOrNull { it.variantId == variantId }
            ?.let { variant -> " (${variant.variantName})" }
    }.safe()
    return name + variantName
}