package com.uniq.uniqpos.util.view

import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import androidx.databinding.ViewDataBinding
import androidx.recyclerview.widget.ItemTouchHelper
import androidx.recyclerview.widget.RecyclerView
import com.uniq.uniqpos.view.global.GlobalAdapter
import com.uniq.uniqpos.view.global.GlobalViewHolder
import timber.log.Timber
import java.util.Collections

class RecyclerItemMoveCallback<T : ViewDataBinding?>(private val adapter: ItemTouchHelperContract<T>): ItemTouchHelper.Callback() {

    override fun isLongPressDragEnabled() = true
    override fun isItemViewSwipeEnabled() = false

    override fun getMovementFlags(
        recyclerView: RecyclerView,
        viewHolder: RecyclerView.ViewHolder
    ): Int {
        val dragFlags = ItemTouchHelper.UP or ItemTouchHelper.DOWN
        return makeMovementFlags(dragFlags, 0)
    }

    override fun onMove(
        recyclerView: RecyclerView,
        viewHolder: RecyclerView.ViewHolder,
        target: RecyclerView.ViewHolder
    ): Boolean {
        adapter.onRowMoved(viewHolder.adapterPosition, target.adapterPosition)
        return true
    }

    override fun onSelectedChanged(viewHolder: RecyclerView.ViewHolder?, actionState: Int) {
        if(actionState != ItemTouchHelper.ACTION_STATE_IDLE){
            adapter.onRowSelected(viewHolder as GlobalViewHolder<T>)
        }
        super.onSelectedChanged(viewHolder, actionState)
    }

    override fun clearView(recyclerView: RecyclerView, viewHolder: RecyclerView.ViewHolder) {
        super.clearView(recyclerView, viewHolder)
        adapter.onRowClear(viewHolder as GlobalViewHolder<T>)
    }
    override fun onSwiped(viewHolder: RecyclerView.ViewHolder, direction: Int) {}
}

interface ItemTouchHelperContract<T : ViewDataBinding?> {
    fun onRowMoved(fromPosition: Int, toPosition: Int)
    fun onRowSelected(viewHolder: GlobalViewHolder<T>)
    fun onRowClear(viewHolder: GlobalViewHolder<T>)
}

class ItemTouchHelperMove<T : ViewDataBinding?>(val adapter: GlobalAdapter<T>, private val listData: ArrayList<*>? ): ItemTouchHelperContract<T> {

    private var originBackgroundColor: Int = Color.TRANSPARENT
    private var onRowClear: ((Unit) -> Unit)? = null

    private fun getItemTouchHelper(): ItemTouchHelper {
        val moveCallback = RecyclerItemMoveCallback(this)
        val touchHelper = ItemTouchHelper(moveCallback)
        return touchHelper
    }

    fun attachToRecyclerView(recyclerView: RecyclerView){
        getItemTouchHelper().attachToRecyclerView(recyclerView)
    }

    override fun onRowMoved(fromPosition: Int, toPosition: Int) {
        Timber.d("move position from $fromPosition to $toPosition")
        if(listData == null) return
        if(fromPosition < toPosition){
            for(i in fromPosition until toPosition){
                Collections.swap(listData, i, i+1)
            }
        }else{
            for(i in fromPosition downTo toPosition + 1){
                Collections.swap(listData, i, i-1)
            }
        }
        adapter.notifyItemMoved(fromPosition, toPosition)
    }

    override fun onRowClear(viewHolder: GlobalViewHolder<T>) {
        viewHolder.binding?.root?.setBackgroundColor(originBackgroundColor)
        onRowClear?.invoke(Unit)
    }

    override fun onRowSelected(viewHolder: GlobalViewHolder<T>) {
        val background = viewHolder.binding?.root?.background
        // Check if the background is a ColorDrawable
        if (background is ColorDrawable) {
            // If it is, cast it and get the color
            originBackgroundColor = background.color
            Timber.d("BackgroundColor, The view's background color is: $originBackgroundColor")
        } else {
            // The background is not a solid color.
            // It could be a gradient, an image, or null.
            Timber.d("BackgroundColor, The view's background is not a solid color.")
        }
        viewHolder.binding?.root?.setBackgroundColor(Color.WHITE)
    }

    fun setOnRowClear(onRowClear: (Unit) -> Unit){
        this.onRowClear = onRowClear
    }
}