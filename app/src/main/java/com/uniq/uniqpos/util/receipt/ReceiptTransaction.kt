package com.uniq.uniqpos.util.receipt

import com.uniq.uniqpos.data.local.entity.SalesEntity
import com.uniq.uniqpos.data.remote.model.Outlet

data class ReceiptTransactionParam(
    val sales: SalesEntity,
    val outlet: Outlet,
    val employeeName: String,
    val paperSize: Int? = 58,
    val isReprint: Boolean = false,
    val isBill: Boolean = false,
    val language: String = "id"
)

class ReceiptTransaction {

}