package com.uniq.uniqpos.util.intent

import android.Manifest
import android.app.Activity
import android.content.ActivityNotFoundException
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Environment
import android.provider.MediaStore
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.core.content.FileProvider
import com.uniq.uniqpos.R
import com.uniq.uniqpos.util.data.DataHolder
import com.uniq.uniqpos.util.dateFormat
import com.uniq.uniqpos.util.showMessage
import com.uniq.uniqpos.util.toast
import timber.log.Timber
import java.io.File
import java.io.IOException
import java.util.Date


class CameraOrGalleryIntent {
    companion object {
        const val CAMERA = 777
        const val GALLERY = 778

        const val PERMISSION_CAMERA = 1

        var currentPhotoPath: String? = null
    }
}

fun openCamera(activity: Activity, isThumbOnly: Boolean = true, requestCode: Int = CameraOrGalleryIntent.CAMERA){
    //check permissions
    if(ContextCompat.checkSelfPermission(activity, Manifest.permission.CAMERA) != PackageManager.PERMISSION_GRANTED) {
        activity.toast("camera permission not granted")
        ActivityCompat.requestPermissions(activity, arrayOf(Manifest.permission.CAMERA), CameraOrGalleryIntent.PERMISSION_CAMERA)
        return
    }

    if(!isThumbOnly){
        if(ContextCompat.checkSelfPermission(activity, Manifest.permission.WRITE_EXTERNAL_STORAGE) != PackageManager.PERMISSION_GRANTED) {
            activity.toast("write storage permission not granted")
//            ActivityCompat.requestPermissions(activity, arrayOf(Manifest.permission.WRITE_EXTERNAL_STORAGE), CameraOrGalleryIntent.PERMISSION_CAMERA)
//            return
        }
    }

    val takePictureIntent = Intent(MediaStore.ACTION_IMAGE_CAPTURE)
    if(!isThumbOnly){
        val photoURI = getImageFileUri(activity) ?: return
        takePictureIntent.putExtra(MediaStore.EXTRA_OUTPUT, photoURI)
    }

    try {
        activity.startActivityForResult(takePictureIntent, requestCode)
    }catch (e: ActivityNotFoundException) {
        activity.showMessage("Camera not supported", "FAILED")
    } catch (e: Exception) {
        activity.showMessage("can not opening camera: ${e.message}", "FAILED")
    }
}

fun getImageFileUri(activity: Activity): Uri?{
    var photoFile: File? = null
    try {
        photoFile = createImageFile(activity)
    } catch (e: Exception) {
        activity.showMessage("can not create image file: ${e.message}", "FAILED")
        return null
    }
    if (photoFile == null) {
        activity.showMessage("can not create image file", "FAILED")
        return null
    }
    return FileProvider.getUriForFile(activity, activity.getString(R.string.file_authority), photoFile!!)
}

@Throws(IOException::class)
fun createImageFile(activity: Activity): File? {
    try{
        // Create an image file name
        val timeStamp = Date().dateFormat("yyyyMMdd_HHmmss")
        val imageFileName = "JPEG_" + timeStamp + "_"
        val storageDir = activity.getExternalFilesDir(Environment.DIRECTORY_PICTURES)
        Timber.i(">> Storage Dir : $storageDir")

        val image = File.createTempFile(
            imageFileName, /* prefix */
            ".jpg", /* suffix */
            storageDir      /* directory */
        )

        // Save a file: path for use with ACTION_VIEW intents
        CameraOrGalleryIntent.currentPhotoPath = image.absolutePath
        return image
    }catch (e: Exception){
        e.printStackTrace()
        return null
    }
}

fun openGallery( activity: Activity, requestCode: Int = CameraOrGalleryIntent.GALLERY) {
    if (ContextCompat.checkSelfPermission(
            activity,
            Manifest.permission.READ_EXTERNAL_STORAGE
        ) != PackageManager.PERMISSION_GRANTED
    ) {
        activity.toast("read storage permission not granted")
//            ActivityCompat.requestPermissions(
//                this,
//                arrayOf(Manifest.permission.READ_EXTERNAL_STORAGE),
//                PERMISSION_GALERY_REQUEST_CODE
//            )
//            return
    }

    val intent = Intent() //Intent.ACTION_PICK, MediaStore.Images.Media.EXTERNAL_CONTENT_URI
    intent.type = "image/*"
    intent.action = Intent.ACTION_GET_CONTENT//
//        intent.action = Intent.ACTION_OPEN_DOCUMENT
    activity.startActivityForResult(Intent.createChooser(intent, "Select Picture"), requestCode)
}