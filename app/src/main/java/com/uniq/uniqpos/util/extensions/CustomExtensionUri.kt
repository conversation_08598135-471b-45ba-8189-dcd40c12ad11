package com.uniq.uniqpos.util.extensions

import android.content.Context
import android.graphics.Bitmap
import android.net.Uri
import com.bumptech.glide.Glide
import com.uniq.uniqpos.util.BACKGROUND
import timber.log.Timber
import java.io.File
import kotlin.coroutines.resume
import kotlin.coroutines.suspendCoroutine

suspend fun Uri.toBitmap(context: Context, targetFileSize: Double? = null): Bitmap {
    val file = context.defaultTmpImage()
    return suspendCoroutine { continuation ->
        BACKGROUND.submit {
            val res = Glide.with(context).asBitmap().load(this).submit()
            val bitmap = res.get()
            val resizedBitmap =  targetFileSize?.let { size ->
                bitmap.compressFile(size, file)
            } ?: run{ bitmap }
            continuation.resume(resizedBitmap)
        }
    }
}

suspend fun Uri.toFile(context: Context, targetFileSize: Double? = null): File {
    val file = context.defaultTmpImage()
    return suspendCoroutine { continuation ->
        BACKGROUND.submit {
            val res = Glide.with(context).asBitmap().load(this).submit()
            val bitmap = res.get()
            targetFileSize?.let { size ->
                bitmap.compressAsFile(size, file)
            } ?: run{
                bitmap.compress(Bitmap.CompressFormat.JPEG, 100, file.outputStream())
            }
            continuation.resume(file)
        }
    }
}

suspend fun Uri.saveAsBitmap(context: Context, targetFileSize: Double? = null): String {
    val file = context.defaultTmpImage()
    Timber.d("saveAsBitmap file path: ${file.path}")
    return suspendCoroutine { continuation ->
        BACKGROUND.submit {
            val res = Glide.with(context).asBitmap().load(this).submit()
            val bitmap = res.get()
            targetFileSize?.let { size ->
                bitmap.compressFile(size, file)
            } ?: run{
                bitmap.compress(Bitmap.CompressFormat.JPEG, 100, file.outputStream())
            }
            continuation.resume(file.path)
        }
    }
}