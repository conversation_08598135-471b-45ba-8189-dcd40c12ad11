package com.uniq.uniqpos.util

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.net.Uri
import android.os.Environment
import androidx.core.content.FileProvider
import java.io.File
import java.io.FileOutputStream
import java.text.SimpleDateFormat
import java.util.*

object ImageUtils {
    private const val AUTHORITY = "com.uniq.uniqpos.fileprovider"
    private const val IMAGE_QUALITY = 80
    private const val MAX_IMAGE_DIMENSION = 1024

    fun createImageFile(context: Context): File {
        val timeStamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
        val storageDir = context.getExternalFilesDir(Environment.DIRECTORY_PICTURES)
        return File.createTempFile("JPEG_${timeStamp}_", ".jpg", storageDir)
    }

    fun getUriForFile(context: Context, file: File): Uri {
        return FileProvider.getUriForFile(context, AUTHORITY, file)
    }

    fun compressImage(context: Context, uri: Uri): File {
        val inputStream = context.contentResolver.openInputStream(uri)
        val options = BitmapFactory.Options().apply {
            inJustDecodeBounds = true
        }
        BitmapFactory.decodeStream(inputStream, null, options)
        inputStream?.close()

        var scale = 1
        while (options.outWidth / scale / 2 >= MAX_IMAGE_DIMENSION &&
               options.outHeight / scale / 2 >= MAX_IMAGE_DIMENSION) {
            scale *= 2
        }

        val finalOptions = BitmapFactory.Options().apply {
            inSampleSize = scale
        }

        val inputStream2 = context.contentResolver.openInputStream(uri)
        val bitmap = BitmapFactory.decodeStream(inputStream2, null, finalOptions)
        inputStream2?.close()

        val outputFile = createImageFile(context)
        FileOutputStream(outputFile).use { out ->
            bitmap?.compress(Bitmap.CompressFormat.JPEG, IMAGE_QUALITY, out)
        }

        return outputFile
    }
} 