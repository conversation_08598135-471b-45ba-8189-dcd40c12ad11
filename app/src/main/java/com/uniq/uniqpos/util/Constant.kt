package com.uniq.uniqpos.util

import android.os.Build
import com.uniq.uniqpos.BuildConfig


/**
 * Created by ANNASBlackHat on 13/10/2017.
 */

object Constant {
    const val IMAGE_URL = "${BuildConfig.BASE_URL}assets/images/products/"
    const val PRINTER_TYPE_WIFI = "wifi"
    const val PRINTER_TYPE_BT = "bluetooth"
    const val PRINTER_TYPE_USB = "usb"
    const val SALES_STATUS_PAID = "paid"
    const val SALES_STATUS_PENDING = "pending"
    const val SALES_STATUS_TEMPORARY = "temporary"
    const val TAX_CATEGORY_DISC = "discount"
    const val TAX_CATEGORY_VOUCHER = "voucher"
    const val CATEGORY_MAIN = "category"
    const val CATEGORY_EXTRA = "category extra"
    const val TYPE_NOMINAL = "nominal"
    const val TYPE_PERSEN = "percentage"
    const val TABLE_STATUS_USED = "used"
    const val TABLE_STATUS_EMPTY = "empty"

    const val TAX_TYPE_PROMOTION = "promotion"

    const val INTENT_SYNC_REQUEST = "id.uniq.uniqpos.intent.action.sync"

    const val ACTION_FINISH_ALL_ACTIVITIES = "id.uniq.uniqpos.FINISH_ALL_ACTIVITIES_ACTION"
    const val ACTION_DISABLE_AND_CLOSE = "id.uniq.uniqpos.DISABLE_AND_CLOSE"
    const val ACTION_CLOSE_AND_OPEN_SETTING = "id.uniq.uniqpos.DISABLE_AND_CLOSE"
    const val ACTION_SYNC_ORDER_SALES = "id.uniq.uniqpos.SYNC_ORDER_SALES"

    const val STATE_FINISH_SYNCED = "id.uniq.uniqpos.FINISH_SYNC"
    const val STATE_NETWORK_STATUS = "id.uniq.uniqpos.NETWORK_STATE"
    const val STOCK_UNAVAILABLE = "unavailable"
    const val STOCK_AVAILABLE = "available"
    const val PRINTER_CODE_CUT = "✂"
    const val PRINTER_CODE_LOGO = "❀"
    const val PRINTER_CODE_QR = "✦"

    const val TOAST_TYPE_ERROR = "error"
    const val TOAST_TYPE_SUCCESS = "success"
    const val TOAST_TYPE_INFO = "info"

    const val PROMO_SPECIAL_PRICE = "specialpromo_specialprice"
    const val PROMO_DISCOUNT = "specialpromo_discount"
    const val PROMO_DISCOUNT_MEMBER = "memberpromo_discount"
    const val PROMO_FREE = "specialpromo_free"
    const val PROMO_DEALS = "VoucherPromo_Deals"
    const val PROMO_FREE_MEMBER = "memberpromo_free"
    const val PROMO_SPECIAL_PRICE_MEMBER = "memberpromo_specialprice"
    const val PROMO_FREE_VOUCHER = "VoucherPromo_Free"

    const val PROMO_TYPE_SPECIAL_PRICE = 4
    const val PROMO_TYPE_DISCOUNT = 5
    const val PROMO_TYPE_FREE = 6
    const val PROMO_TYPE_SPECIAL_PRICE_MEMBER = 7
    const val PROMO_TYPE_DISCOUNT_MEMBER = 8
    const val PROMO_TYPE_FREE_MEMBER = 9
    const val PROMO_TYPE_SPECIAL_PRICE_VOUCHER = 11
    const val PROMO_TYPE_DISCOUNT_VOUCHER = 12
    const val PROMO_TYPE_FREE_VOUCHER = 13
    const val PROMO_TYPE_DEALS = 15

    const val PROMO_PRODUCT_ORDER = "order_menu"
    const val PROMO_PRODUCT_FREE = "free_menu"

    const val PROMO_DISC_TYPE_ITEM = "items"
    const val PROMO_DISC_TYPE_NOTA = "nota"

    const val PROMO_SOURCE_UNIQ = "uniq"

    const val SPLITTER = "#$#$#"
    val DEVICE_NAME = "${Build.BRAND} ${Build.MODEL}"
    const val ACTION_GET_VALUE_ONLY = "scan_only_for_getting_value"

    const val HELP_TYPE_WEB = "web"
    const val HELP_TYPE_HTML = "html"
    const val HELP_TYPE_TEXT = "text"

    const val MenuCategoryAll: Int = -1
    const val MenuCategoryPromo: Int = -2

    const val KITCHEN_PORT = 9784
    const val PRINTER_PORT = 9100
}
