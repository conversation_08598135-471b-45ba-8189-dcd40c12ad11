package com.uniq.uniqpos.util.printer

import android.app.PendingIntent
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.hardware.usb.UsbConstants
import android.hardware.usb.UsbDevice
import android.hardware.usb.UsbEndpoint
import android.hardware.usb.UsbManager
import android.net.Uri
import android.os.Build
import com.dantsu.escposprinter.EscPosPrinter
import com.dantsu.escposprinter.EscPosPrinterCommands
import com.dantsu.escposprinter.connection.usb.UsbConnection
import com.dantsu.escposprinter.textparser.PrinterTextParserImg
import com.google.firebase.ktx.Firebase
import com.google.firebase.remoteconfig.ktx.remoteConfig
import com.google.gson.Gson
import com.uniq.uniqpos.BuildConfig
import com.uniq.uniqpos.data.local.sharedpref.SharedPref
import com.uniq.uniqpos.data.local.sharedpref.getJson
import com.uniq.uniqpos.data.remote.model.Outlet
import com.uniq.uniqpos.util.*
import timber.log.Timber
import java.io.File
import kotlin.concurrent.thread

/**
 * Created by annasblackhat on 22/12/18
 */
class PrinterUSB(val context: Context) {
    private val TAG = "[PRINT USB]"
    private val ACTION_USB_PERMISSION = "com.uniq.uniqpos.USB_PERMISSION"
    private var usbManager: UsbManager = context.getSystemService(Context.USB_SERVICE) as UsbManager
    private var textToPrint: String = ""
    private var isDeattachAfterPrint = false
    private var isReciverRegistered = false
    private var isOpenCashDrawer = false
    private lateinit var callback: (PrintResponse) -> Unit

    fun openCashDrawer(address: String, callback: (PrintResponse) -> Unit) {
        if (context.deactivatePrinterList().any { it == address }) {
            callback(PrintResponse(false, "Printer USB '$address' is deactivate at this device"))
        } else if (!context.role().bukalaciuang) {
            callback(PrintResponse(false, "User has no access to open cash drawer!"))
        } else {
            print("", address, isOpenCashDrawer = true, callback = callback)
        }
    }

    fun print(text: String, address: String, isDeattachAfterPrint: Boolean = false, isOpenCashDrawer: Boolean = false, callback: (PrintResponse) -> Unit) {
        if (context.deactivatePrinterList().any { it == address }) {
            Timber.i("$address is deactivate at this device")
            callback(PrintResponse(true, "USB Printer With address '$address' is deactivate at this device!"))
        } else {
            this.isDeattachAfterPrint = isDeattachAfterPrint
            this.isOpenCashDrawer = isOpenCashDrawer
            this.callback = callback
            textToPrint = text
            checkPermission(address)
        }
    }

    fun printTest(address: String, isDeattachAfterPrint: Boolean = false, callback: (PrintResponse) -> Unit) {
        this.isDeattachAfterPrint = isDeattachAfterPrint
        this.callback = callback
        textToPrint = "${Constant.PRINTER_CODE_LOGO}" +
                "### UNIQ POS PRINT TEST USB ###\n" +
                "__App Version ${BuildConfig.VERSION_NAME}\n" +
                "__Printer Address $address\n" +
                "__Print Time ${System.currentTimeMillis().dateTimeFormat()}" +
                "_________ www.uniq.id __________\n\n"

        checkPermission(address)
    }

    fun getUsbDevices(): List<UsbDevice> {
        val result = ArrayList<UsbDevice>()
        val deviceList = usbManager.deviceList
        if (deviceList.isNotEmpty()) {
            val deviceIterator = deviceList.values.iterator()
            while (deviceIterator.hasNext()) {
                val usbDevice = deviceIterator.next()
                result.add(usbDevice)
            }
        }
        return result
    }

    private fun checkPermission(address: String) {
        getUsbDevice(address)?.let { usbDevice ->
            if (usbManager.hasPermission(usbDevice)) {
                startPrinting(usbDevice)
            } else {
//                val permissionIntent = PendingIntent.getBroadcast(context, 0, Intent(ACTION_USB_PERMISSION), 0)
                val permissionIntent =  if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) PendingIntent.getBroadcast(context, 0, Intent(ACTION_USB_PERMISSION),
                    PendingIntent.FLAG_IMMUTABLE) else PendingIntent.getBroadcast(context, 0, Intent(ACTION_USB_PERMISSION),
                    PendingIntent.FLAG_ONE_SHOT or PendingIntent.FLAG_IMMUTABLE)
                val intentFilter = IntentFilter(ACTION_USB_PERMISSION)
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                    context.registerReceiver(usbReceiver, intentFilter, Context.RECEIVER_NOT_EXPORTED)
                } else {
                    @Suppress("UnspecifiedRegisterReceiverFlag")
                    context.registerReceiver(usbReceiver, intentFilter)
                }

                isReciverRegistered = true
                usbManager.requestPermission(usbDevice, permissionIntent)
            }
        } ?: kotlin.run {
            Timber.i("$TAG USB Printer With address '$address' Not Found!")
            if (::callback.isInitialized) {
                callback(PrintResponse(false, "USB Printer With address '$address' Not Found!"))
            }
        }
    }

    private fun getUsbDevice(address: String): UsbDevice? {
        getUsbDevices().forEach { usbDevice ->
            if ("${usbDevice.vendorId}:${usbDevice.productId}" == address.substring(0, address.indexAt(":", 1))) {
                return usbDevice
            }
        }
        return null
    }

    private fun startPrinting(device: UsbDevice?) {
        val useV2 = Firebase.remoteConfig.getBoolean("print_usb")
        Timber.i("useV2 usb? $useV2")
        if(useV2){
            startPrintingV2(device)
            return
        }

        device?.takeIf { it.interfaceCount > 0 }?.getInterface(0)?.also { usbInterface ->
            val log = StringBuilder("Endpoint size : ${usbInterface.endpointCount}")
            var usbEndPoint: UsbEndpoint? = null
            for (i in 0 until usbInterface.endpointCount) {
                log.appendLine("$i - type : ${usbInterface.getEndpoint(i).type} | direction : ${usbInterface.getEndpoint(i).direction}")
                if (usbInterface.getEndpoint(i).type == UsbConstants.USB_ENDPOINT_XFER_BULK && usbInterface.getEndpoint(i).direction == UsbConstants.USB_DIR_OUT) {
                    usbEndPoint = usbInterface.getEndpoint(i)
                    break
                }
            }

            usbEndPoint?.let { usbEndpoint ->
                usbManager.openDevice(device)?.apply {
                    //val center = byteArrayOf(0x1b, 0x61, 0x01)
                    val byteToPrint = ArrayList<ByteArray>()

                    byteToPrint.add(EscPosPrinterCommands.RESET_PRINTER)
                    byteToPrint.add(EscPosPrinterCommands.TEXT_ALIGN_CENTER)//set to center

                    if (isOpenCashDrawer && context.role().bukalaciuang) byteToPrint.add(PrinterCommand.openCashDrawer)
                    if (textToPrint.isNotBlank()) {
                        val logo = getLogo()

                        textToPrint.split(Constant.PRINTER_CODE_CUT).forEach { text ->
                            if (text.isNotEmpty()) {
                                text.split(Constant.PRINTER_CODE_LOGO).forEach { print ->
                                    if (print.isEmpty()) {
                                        logo?.let { logo ->
                                            byteToPrint.add(logo)
                                            byteToPrint.add("\n".toByteArray())
                                        }
                                    } else {
                                        byteToPrint.add("$text \n".toByteArray())
                                        byteToPrint.add(PrinterCommand.cutPaper)
                                    }
                                }
                            }
                        }
                    }

                    byteToPrint.add(EscPosPrinterCommands.RESET_PRINTER)
                    val result = byteToPrint.merge()

                    claimInterface(usbInterface, true)
                    thread(true) {
                        bulkTransfer(usbEndpoint, result, result.size, 0)
                    }

                    if (::callback.isInitialized) {
                        callback(PrintResponse(true))
                    }
                    unregisterReceiver()
                }
            } ?: kotlin.run {
                Timber.i("$TAG Device has not enough endpoint || $log")
                unregisterReceiver()
                if (::callback.isInitialized) {
                    callback(PrintResponse(false, "Printer USB not found!"))
                }
            }
        } ?: kotlin.run { Timber.i("$TAG Can not print to USB. Device interface is null. InterfaceCount : ${device?.interfaceCount}") }
    }

    private fun startPrintingV2(device: UsbDevice?){
        val escPrinter = EscPosPrinter(UsbConnection(usbManager, device), 203, 48f, 32)
        val texts = StringBuilder()

        var logoBmp: Bitmap? = null
        try {
            val withLogo = textToPrint.contains(Constant.PRINTER_CODE_LOGO)
            context.takeIf { withLogo && textToPrint.isNotBlank() }?.receiptLogoFile()?.let { file ->
                if (file.exists()) {
                    logoBmp = BitmapFactory.decodeFile(file.path)
                    Timber.i(">> Image is exist | Logo : $logoBmp")
                }
            }
        } catch (e: Exception) {
            Timber.i("Getting image logo to print error : $e")
        }

        val textSplit = textToPrint.split(Constant.PRINTER_CODE_LOGO)
//                data.splitWithSplitters(Constant.PRINTER_CODE_LOGO, Constant.PRINTER_CODE_QR)
        val cutCommand = ".[L]\n[L]\n[L]\n${PrinterCommand.COMMAND_CUT}"

        textSplit.map {
            it.replace(Constant.PRINTER_CODE_CUT, cutCommand)
                .replace(Constant.PRINTER_CODE_LOGO, "")
                .replace(Regex("${Constant.PRINTER_CODE_QR}(.*?)${Constant.PRINTER_CODE_QR}"), "<qrcode size='40'>$1</qrcode>")
        }.forEachIndexed { index, txt ->
            try {
                Timber.d("printing: $index | $txt")
                logoBmp?.takeIf { index > 0 }?.let { bmp ->
                    Timber.d("printing: adding logo")
                    texts.append(
                        "[C]<img>${
                            PrinterTextParserImg.bitmapToHexadecimalString(
                                escPrinter,
                                bmp
                            )
                        }</img>\n"
                    )
                    texts.append("[L]\n")
                }
                Timber.d("printing: adding text")
                texts.appendLine("[C]$txt")
            } catch (e: Exception) {
                Timber.i("Print to USB fail to write: $e")
            }
        }

        escPrinter.printFormattedText(texts.toString())
        escPrinter.disconnectPrinter()
        if (::callback.isInitialized) {
            callback(PrintResponse(true))
        }
    }

    private fun getLogo(): ByteArray? {
        //try to use new custom extension, remove below code (old one) once this stable
         try {
             val result = context.receiptLogoFile()?.toImageByteResized()
             if (result != null){
                 return result
             }
         } catch (e: Exception) {
             Timber.i("get logo with toImageByteResized err: $e")
         }

        try {
            context.receiptLogoFile()?.let { file ->

                if (file.exists()) {
                    Timber.i(">> Image is exist - ${file.absolutePath}")
//                    val logo: Bitmap? = BitmapFactory.decodeFile(file.absolutePath)

                    val logoBitmap = BitmapFactory.decodeFile(file.absolutePath) ?: kotlin.run {
                        val selectedImg = Uri.fromFile(file)
                        val inputStream = context.contentResolver.openInputStream(selectedImg)
                        BitmapFactory.decodeStream(inputStream)
                    }

                    val printImg = PrintImage(PrintImage.getResizedBitmap(logoBitmap))
                    printImg.prepareImage(PrintImage.dither.floyd_steinberg, 128)
                    val byteImg = printImg.printImageData
                    Timber.i(">>> byteIMG : $byteImg")
                    return byteImg
                }
            }
        } catch (e: Exception) {
            Timber.i("$TAG Adding image to print error : $e")
        }
        return null
    }

    private fun unregisterReceiver() {
        if (isDeattachAfterPrint && isReciverRegistered) {
            context.unregisterReceiver(usbReceiver)
            isReciverRegistered = false
        }
    }

    private val usbReceiver = object : BroadcastReceiver() {
        override fun onReceive(p0: Context?, intent: Intent?) {
            val action = intent?.action
            if (ACTION_USB_PERMISSION == action) {
                synchronized(this) {
                    val device = intent.getParcelableExtra<UsbDevice?>(UsbManager.EXTRA_DEVICE)
                    if (intent.getBooleanExtra(UsbManager.EXTRA_PERMISSION_GRANTED, false)) {
                        startPrinting(device)
                    } else {
                        Timber.i("$TAG PERMISSION DENIED FOR THIS DEVICE")
                        unregisterReceiver()
                        if (::callback.isInitialized) {
                            callback(PrintResponse(false, "Permission Denied!"))
                        }
                    }
                }
            }
        }
    }

    companion object {
        fun getAddressFormat(usbDevice: UsbDevice) = "${usbDevice.vendorId}:${usbDevice.productId}:${usbDevice.deviceId}"
        fun getUsbDeviceId(address: String): Pair<Int, Int> {
            val usbInf = address.split(":")
            //first : is vendor ID
            //second : is product Id
            return Pair(usbInf[0].toInt(), usbInf[1].toInt())
        }
    }

}