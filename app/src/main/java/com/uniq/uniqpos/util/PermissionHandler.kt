package com.uniq.uniqpos.util

import android.Manifest
import android.content.Context
import android.content.pm.PackageManager
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import timber.log.Timber

class PermissionHandler(private val fragment: Fragment) {
    private var onCameraGranted: (() -> Unit)? = null
    private var onGalleryGranted: (() -> Unit)? = null

    private val cameraPermissionLauncher = fragment.registerForActivityResult(
        ActivityResultContracts.RequestPermission()
    ) { isGranted ->
        if (isGranted) {
            onCameraGranted?.invoke()
        }
    }

    private val galleryPermissionLauncher = fragment.registerForActivityResult(
        ActivityResultContracts.RequestPermission()
    ) { isGranted ->
        if (isGranted) {
            onGalleryGranted?.invoke()
            Timber.d("Permission gallery granted")
        }else{
            Timber.i("Permission gallery Denied")
        }
    }

    fun requestCameraPermission(onGranted: () -> Unit) {
        onCameraGranted = onGranted
        when {
            hasPermission(Manifest.permission.CAMERA) -> onGranted()
            else -> cameraPermissionLauncher.launch(Manifest.permission.CAMERA)
        }
    }

    fun requestGalleryPermission(onGranted: () -> Unit) {
        onGalleryGranted = onGranted
        when {
            hasPermission(Manifest.permission.READ_EXTERNAL_STORAGE) -> onGranted()
            else -> galleryPermissionLauncher.launch(Manifest.permission.READ_EXTERNAL_STORAGE)
        }
    }

    private fun hasPermission(permission: String): Boolean {
        return ContextCompat.checkSelfPermission(
            fragment.requireContext(),
            permission
        ) == PackageManager.PERMISSION_GRANTED
    }
} 