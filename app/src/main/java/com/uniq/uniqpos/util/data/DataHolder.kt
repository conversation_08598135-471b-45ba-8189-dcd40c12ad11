package com.uniq.uniqpos.util.data

class DataHolder {
    companion object {
        private var data: String? = null

        fun hasData(): Boolean {
            return data != null
        }

        fun  setData(data: String){
            this.data = data
        }

        fun getData(): String? {
            return data
        }

        fun removeData() {
            this.data = null
        }
    }
}