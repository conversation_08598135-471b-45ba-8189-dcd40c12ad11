package com.uniq.uniqpos.util.lifecycle

import android.util.Log
import androidx.annotation.MainThread
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.Observer
import java.util.concurrent.atomic.AtomicBoolean

/**
 * A lifecycle-aware observable that sends only new updates after subscription, used for events like
 * navigation and Snackbar messages.
 *
 *
 * This avoids a common problem with events: on configuration change (like rotation) an update
 * can be emitted if the observer is active. This LiveData only calls the observable if there's an
 * explicit call to setValue() or call().
 *
 *
 * Note that only one observer is going to be notified of changes.
 */
class SingleLiveEvent<T> : MutableLiveData<T>() {
    private val mPending = AtomicBoolean(false)

    @MainThread
    override fun observe(owner: LifecycleOwner, observer: Observer<in T>) {
        if (hasActiveObservers()) {
            Log.w(TAG, "Multiple observers registered but only one will be notified of changes.")
        }

        // Observe the internal MutableLiveData
        super.observe(owner, Observer<T> { t ->
            if (mPending.compareAndSet(true, false)) {
                observer.onChanged(t)
            }
        })

//        // Observe the internal MutableLiveData
//        super.observe(owner, object : Observer<T> {
//            override fun onChanged(t: T) {
//                if (mPending.compareAndSet(true, false)) {
//                    observer.onChanged(t)
//                }
//            }
//        })
    }

    @MainThread
    override fun setValue(t: T) {
        mPending.set(true)
        super.setValue(t)
    }

    // Make call() inline + reified:
    inline fun <reified R> call() where R : Any {
        val default: R = when (R::class) {
            String::class -> "" as R
            Int::class    -> 0  as R
            else          -> throw IllegalArgumentException("No default for type ${R::class}")
        }
        @Suppress("UNCHECKED_CAST")
        setValue(default as T)
    }

    companion object {
        private const val TAG = "SingleLiveEvent"
    }
}