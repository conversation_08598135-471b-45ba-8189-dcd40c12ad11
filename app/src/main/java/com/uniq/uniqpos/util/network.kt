package com.uniq.uniqpos.util

import android.content.Context
import android.net.ConnectivityManager
import android.net.wifi.WifiManager

fun getWifiName(context: Context): String? {
    val connectivityManager =
        context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
    val wifiManager =
        context.applicationContext.getSystemService(Context.WIFI_SERVICE) as WifiManager

    if (connectivityManager.activeNetworkInfo?.type == ConnectivityManager.TYPE_WIFI) {
        val wifiInfo = wifiManager.connectionInfo
        return wifiInfo.ssid.removeSurrounding("\"") // Remove quotes from SSID
    }
    return null
}