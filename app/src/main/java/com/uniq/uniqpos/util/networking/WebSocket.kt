package com.uniq.uniqpos.util.networking

import android.content.res.AssetManager
import io.ktor.client.HttpClient
import io.ktor.client.engine.cio.CIO
import io.ktor.client.features.websocket.DefaultClientWebSocketSession
import io.ktor.client.features.websocket.WebSockets
import io.ktor.client.features.websocket.webSocketRawSession
import io.ktor.client.features.websocket.webSocketSession
import io.ktor.client.features.websocket.ws
import io.ktor.client.features.websocket.wss
import io.ktor.http.HttpMethod
import io.ktor.http.cio.websocket.Frame
import io.ktor.http.cio.websocket.readBytes
import timber.log.Timber
import java.io.InputStream
import java.security.KeyStore
import java.security.cert.CertificateFactory
import javax.net.ssl.TrustManagerFactory
import io.ktor.http.cio.websocket.readText
import kotlinx.coroutines.isActive

//object KtorClient {
//    val client = HttpClient(CIO) {
//        install(WebSockets)
//        engine {
//            https {
//                trustManager = getTrustManager("certificate.pem", assetManager)
//            }
//        }
//    }
//}

//reference : https://medium.com/@suatzengin/android-websocket-with-ktor-server-client-d28b37cd38a4
suspend fun sendWebSocket(data: String, host: String, port: Int, path: String = "/ws", assetManager: AssetManager) {
    val client = HttpClient(CIO) {
        install(WebSockets)
        engine {
            https {
                trustManager = getTrustManager("certificate.pem", assetManager)
            }
        }
    }

    Timber.d("send socke to $host:$port, path $path")
//    val sess = client.webSocketSession()
//    val sc = client.webSocketRawSession(HttpMethod.Get, host = "https://$host", port = port, path = path)
//    val sc = client.webSocketSession(HttpMethod.Get, host = host, port = port, path = path)
//    sc.send(Frame.Text(data))


    var sc: DefaultClientWebSocketSession? = null
    client.wss(HttpMethod.Get, host = host, port = port, path = path){
        sc = this
        Timber.i("socket connected to $host:$port")
        send(Frame.Text(data))
        Timber.i("socket sent message: $data")
        Timber.i("is socket active: ${sc?.isActive} | null? ${sc == null}")
//        for (message in incoming) {
//            Timber.i("get message $message")
//            message as? Frame.Text ?: continue
//            Timber.i("socket Received message: ${message.readText()}")
//        }
    }

    sc = client.webSocketSession(HttpMethod.Get, host = host, port = port, path = path)
    Timber.i("is socket active: ${sc?.isActive} | null? ${sc == null}")

    for (message in sc!!.incoming) {
        Timber.i("get message $message | isFrame: ${message is Frame.Text}")
        message as? Frame.Text ?: continue
        Timber.i("socket Received message: ${message.readText()}")
    }

//    client.webSocket(method = HttpMethod.Get, host = host, port = port, path = path) {
//        send(Frame.Text(data))
//    }

    client.close()
    Timber.i("socket closed")
}

fun getTrustManager(certificatePath: String, assetManager: AssetManager): javax.net.ssl.X509TrustManager {
    Timber.d("certificatePath: $certificatePath")
    val keyStore = KeyStore.getInstance(KeyStore.getDefaultType())
    val certificateInputStream: InputStream = assetManager.open(certificatePath)// this.javaClass.classLoader.getResourceAsStream(certificatePath)
    val certificateFactory = CertificateFactory.getInstance("X.509")
    val certificate = certificateFactory.generateCertificate(certificateInputStream)
    keyStore.load(null)
    keyStore.setCertificateEntry("cert", certificate)
    val trustManagerFactory = TrustManagerFactory.getInstance(TrustManagerFactory.getDefaultAlgorithm())
    trustManagerFactory.init(keyStore)
    Timber.d("trustManagerFactory: ${trustManagerFactory.trustManagers[0]}")
    return trustManagerFactory.trustManagers[0] as javax.net.ssl.X509TrustManager
}