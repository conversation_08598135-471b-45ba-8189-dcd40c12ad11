package com.uniq.uniqpos.util.receipt

import com.google.gson.Gson
import com.uniq.uniqpos.data.local.entity.*
import com.uniq.uniqpos.data.remote.model.Employee
import com.uniq.uniqpos.data.remote.model.Outlet
import com.uniq.uniqpos.util.Constant
import timber.log.Timber
import kotlin.random.Random

/**
 * Created by annasblack<PERSON> on 11/12/18
 */
class NotaManager {
    companion object {
        fun createNota(printers: List<PrinterEntity>, ticketList: List<PrinterTicketEntity>,
                       sales: SalesEntity, outlet: Outlet, employee: Employee,
                       isOnlyPrintKitchen: Boolean = false, isOpenCashDrawer: Boolean = false): List<PendingPrintEntity>{
            Timber.i("printer size : ${printers.size} || printers : ${Gson().toJson(printers)}")

            val printDataList = ArrayList<PendingPrintEntity>()

            //look for captain order printer
            val printerOrder = printers.filter { it.settingPrintorder == "1" }
            printerOrder.sortedBy { it.address }.forEach { p ->
                val tickets = ticketList.filter { it.printerSettingFkid == p.printerSettingId }
                Timber.i("Ticket found : ${tickets.size} for printer id: ${p.printerSettingId}")
                val notaKitchen = StringBuilder()
                tickets.forEach { ticket ->
                    val subcategories = ticket.detail.map { it.productSubcategoryFkid }
                    Timber.i("Ticket : ${ticket.name}\nCategories : $subcategories")
//                    val orderFilter = sales.orderList?.filter { order -> subcategories.contains(order.product.productSubcategoryFkid) }
//                    val salesFilter = sales.copy(orderList = ArrayList(orderFilter))
                    val (kitchen, _) = PrintNotaUtil.getPrintKitchenFormat(sales, ticket, p.settingPrintpapersize)
                    notaKitchen.append(kitchen)
                }
                if (tickets.isEmpty()) {
                    Timber.i("User has not created any ticked for printer ${p.name}. Generate default nota")
                    val format = PrintNotaUtil.getPrintKitchenFormat(sales, paperSize = p.settingPrintpapersize)
                    notaKitchen.append(format.first)
                }
                notaKitchen.append("\n ")
                Timber.i("Generate Print order to : ${p.name} | type : ${p.type} | paper size : ${p.settingPrintpapersize}")
                printDataList.add(PendingPrintEntity(p.address, p.type, notaKitchen.toString(), printerName = p.name))
            }

            Timber.i("Printer receipt size : ${printers.filter { it.settingPrintreceipt == "1" }.size} | isOnlyPrintKitchen : $isOnlyPrintKitchen")
            //look for receipt printer
            //bypass if current state is tmpSales
            if (!isOnlyPrintKitchen)
                printers.filter { it.settingPrintreceipt == "1" }.sortedBy { it.address }.forEach { p ->
                    val (strNota, _) = PrintNotaUtil.getPrintNotaFormat(sales, outlet, employee, paperSize = p.settingPrintpapersize)
                    for (i in 0 until (p.settingPrintreceiptJumlah?.toInt() ?: 1)) {
                        printDataList.add(PendingPrintEntity(p.address, p.type, strNota, p.name, isOpenCashDrawer =  isOpenCashDrawer))
                    }
                }

            //group data print by ip address, so it will sent once
            val printGroup = printDataList.groupBy { it.address }
            printDataList.clear()
            printGroup.forEach {
                var nota = ""
                it.value.forEach { data -> nota += data.dataString + Constant.PRINTER_CODE_CUT } //append cut code
                printDataList.add(PendingPrintEntity(it.key, it.value[0].type, nota, it.value[0].printerName, System.currentTimeMillis() + Random.nextInt(), it.value[0].isOpenCashDrawer))
                Thread.sleep(50) //add sleep so the id will be unique
            }

            val ids = printDataList.map { it.id }.joinToString(",")
            Timber.i("Data to print size : ${printDataList.size} | ids : $ids")
            return printDataList
        }
    }
}