package com.uniq.uniqpos.util.printer

import android.app.Activity
import android.content.Context
import com.google.firebase.analytics.FirebaseAnalytics
import com.google.firebase.analytics.ktx.analytics
import com.google.firebase.ktx.Firebase
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.uniq.uniqpos.R
import com.uniq.uniqpos.data.local.entity.PendingPrintEntity
import com.uniq.uniqpos.data.local.entity.TmpSalesEntity
import com.uniq.uniqpos.data.local.sharedpref.SharedPref
import com.uniq.uniqpos.data.local.sharedpref.getLocalDataString
import com.uniq.uniqpos.data.local.sharedpref.sharedPref
import com.uniq.uniqpos.model.SocketMessage
import com.uniq.uniqpos.util.*
import kotlinx.coroutines.*
import timber.log.Timber
import java.io.DataInputStream
import java.io.DataOutputStream
import java.net.ServerSocket
import java.net.Socket
import kotlin.concurrent.thread

/**
 * Created by annasblack<PERSON> on 13/02/18.
 */
class PrinterSocket(val printerManager: PrinterManager, val context: Context) {

    private val MAX_LOOP_CONNECT = 7
    private var activity: Activity? = null
    private var serverSocket: ServerSocket? = null
    private var socket: Socket? = null
    private var loopConnect = 0
    private var isSendingData = false

    companion object {
        const val socketServerPort = 2893
    }

    fun setActivity(activity: Activity?) {
        this.activity = activity
    }

    fun removeActivityBound() {
        activity = null
    }

    fun start() {
        if (serverSocket?.isClosed != false) {
            SocketServerThread().start()
        }
    }

    fun startKitchenServer() {
        SocketServerThreadKds().start()
    }

    fun sendToServer(data: String, listener: SocketListener? = null) = GlobalScope.launch(Dispatchers.Main) {
        var delay = 0
        while (isSendingData && delay < 10) {
            delay(1000)
            delay++
        }
        isSendingData = true
        val ip = <EMAIL>(SharedPref.IP_SERVER)
        val localIP = GetIpAddress()
        GlobalScope.async {
            try {
                socket = Socket(ip, socketServerPort)
            } catch (e: Exception) {
                Timber.i("connect to server ($ip) error : $e")
            }
        }
        var loop = 0
        do {
            Timber.i("Loop $loop  - reconnecting to server with IP $ip")
            delay(1000)
            loop++
        } while ((!isSocketConnected() || socket?.isClosed == true) && loop < 7)

        if (isSocketConnected() && socket?.isClosed == false) {
            Timber.i("Send this data to server ($ip) => \n$data")
            var step = ""
            try {
                GlobalScope.async {
                    step += "stream-"
                    val dataOut = DataOutputStream(socket?.getOutputStream())
                    step += "write-"
                    dataOut.writeUTF(data)
                    step += "flush-"
                    dataOut.flush()
                    step += "close-"
                    dataOut.close()
                    step += "finish-"
                    listener?.onConnectionFinish(true, null)
                }.await()
                step += "toast"
                showToast("Data sent to server")
            } catch (e: Exception) {
                showToast("Ops Something Error - ${e.message}") //"Communication with server error!"
                Timber.i("- Caused : $e \n IsSocketClosed : ${socket?.isClosed} | Local IP $localIP | Ping Result : ${Utility.ping(ip)}")
                listener?.onConnectionFinish(false, null)
            }
        } else {
            showToast("Can not connect to server! Local IP $localIP")
            Timber.i("isSocketConnected ? ${isSocketConnected()} | Ping result : ${Utility.ping(ip)}")
            listener?.onConnectionFinish(false, null)

            context.outlet()?.let { outlet ->
                var deviceID = context.sharedPref().getString(SharedPref.DEVICE_ID)
                Firebase.analytics
                        .logEvent("printer_server",
                                bundleOf("Outlet" to "${outlet.outletId}:${outlet.name}",
                                        "Identifier" to "${outlet.outletId}:$deviceID:${System.currentTimeMillis().dateTimeFormat("HH:mm dd-MM")}",
                                        "IP (Local:Server)" to "$localIP:$ip"))
            }
        }
        closeSocket()
        isSendingData = false
    }

    private fun closeSocket() {
        socket?.let {
            try {
                it.close()
            } catch (e: Exception) {
                showToast("Closing socket failed. $e")
            }
        }
    }

    /*fun sendToServer(data: String, listener: SocketListener? = null) {
        async(UI) {
            try {
                val dataOut = DataOutputStream(socket?.getOutputStream())
                dataOut.writeUTF("tes send data")
                dataOut.flush()
                Timber.i("Empty data sent...")
            } catch (e: Exception) {
                showToast("Reconnecting...")
                val ip = <EMAIL>(SharedPref.IP_SERVER)

                bg { socket = Socket(ip, socketServerPort) }

                var loop = 0
                do {
                    Timber.i("xxx loop $loop")
                    bg { Thread.sleep(1000) }.await()
                    loop++
                } while (!isSocketConnected() && loop < 5)
            }

            if(isSocketConnected()){
                try {
                    val dataOut = DataOutputStream(socket?.getOutputStream())
                    dataOut.writeUTF(data)
                    dataOut.flush()
                    showToast("Data sent to server")
                    listener?.onConnectionFinish(true)
                } catch (e: Exception) {
                    showToast("Communication with server error!")
                    listener?.onConnectionFinish(false)
                }
            }else{
                showToast("Communication with server error!")
                listener?.onConnectionFinish(false)
            }
        }
    }*/

    fun connectToServer(ipAddress: String, listener: SocketListener? = null) {
        socket?.let {
            try {
                it.close()
            } catch (e: Exception) {
                showToast("Closing socket failed. $e")
            }
        }

        GlobalScope.launch(Dispatchers.Main) {
            Timber.i("Connecting to $ipAddress:$socketServerPort")
            var errorMessage: String? = null
            GlobalScope.launch {
                try {
                    socket = Socket(ipAddress, socketServerPort)
                } catch (e: Exception) {
                    Timber.i("connect to server error : $e")
                    if (e is java.net.NoRouteToHostException) {
                        errorMessage = context.getString(R.string.host_unreachable)
                    }
                }
            }
            loopConnect = 0
            do {
                Timber.i("try connect to server at loop $loopConnect")
                delay(1000)
                loopConnect++
            } while (!isSocketConnected() && loopConnect < 7)

            listener?.onConnectionFinish(isSocketConnected(), errorMessage)
        }

//        GlobalScope.launch { socket = Socket(ipAddress, socketServerPort) }
//        thread(true){ socket = Socket(ipAddress, socketServerPort) }
//        checkConnection(MAX_LOOP_CONNECT, listener)
    }

    private fun checkConnection(loop: Int, listener: SocketListener? = null) {
        Timber.i("try connect to server at loop $loop")
        android.os.Handler().postDelayed({
            if (!isSocketConnected() && loop > 0) {
                checkConnection(loop - 1, listener)
            } else {
                listener?.onConnectionFinish(isSocketConnected(), null)
            }
        }, 1000)
    }

    private fun isSocketConnected(): Boolean {
        return socket?.isConnected ?: false
    }

    fun showToast(msg: String) {
        activity?.runOnUiThread { context.toast(msg) }
        Timber.i(msg)
    }

    inner class SocketServerThreadKds: Thread(){
        override fun run() {
            showToast("kitchen server running...")
            try {
                serverSocket = ServerSocket(Constant.KITCHEN_PORT)
            } catch (e: Exception) {
                showToast("Initialize socket error. ${e.message}")
            }
        }
    }

    inner class SocketServerThread : Thread() {
        override fun run() {
            showToast("Server starting...")
            Timber.i("Local IP ${GetIpAddress()}")
            try {
                serverSocket = ServerSocket(socketServerPort)
            } catch (e: Exception) {
                showToast("Initialize socket error. ${e.message}")
            }
            try {
                while (true) {
                    val socket = serverSocket?.accept()
                    showToast("a Client connected to this device")
                    val input = DataInputStream(socket?.getInputStream())
                    thread(true) {
                        while (true) {
                            val msgClient = try {
                                input.readUTF()
                            } catch (e: Exception) {
                                break
                            }
                            showToast("Receive data from client")
                            Timber.i("From Client : $msgClient")
                            try {
                                msgClient?.let {
                                    val req = Gson().fromJson(it, SocketMessage::class.java)
                                    Timber.i("Data Type : ${req.type}")
                                    when (req.type) {
                                        "print_list" -> {
                                            val type = object : TypeToken<ArrayList<PendingPrintEntity>>() {}.type
                                            val pendingPrint = Gson().fromJson<ArrayList<PendingPrintEntity>>(req.data, type)
                                            printerManager.addAllToQueue(pendingPrint)
                                        }
                                        "print_single" -> {
                                            val pendingPrint = Gson().fromJson(req.data, PendingPrintEntity::class.java)
                                            printerManager.addAllToQueue(arrayListOf(pendingPrint))
                                        }
                                        "sales_cart" -> {
                                            val sales = Gson().fromJson(req.data, TmpSalesEntity::class.java)
                                            printerManager.saveSalesCart(sales)
                                        }
                                    }
                                    input.close()
                                }
                            } catch (e: Exception) {
                                Timber.i("Parse data Failed => $msgClient. \n$e")
                            }
                        }
                    }
                }
            } catch (e: Exception) {
                showToast("Error $e")
            }
        }
    }

    fun onDestroy() {
        serverSocket?.let {
            try {
                it.close()
            } catch (e: Exception) {
                showToast("Close socket error. " + e.message)
            }
        }

        socket?.let {
            try {
                it.close()
            } catch (e: Exception) {
            }
        }
    }

    fun interface SocketListener {
        fun onConnectionFinish(isConnected: Boolean, message: String?)
    }
}