package com.uniq.uniqpos.util

import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.FragmentPagerAdapter
import android.view.ViewGroup

/**
 * Created by annasblack<PERSON> on 19/06/18
 */
class ViewPagerAdapter(fm: FragmentManager): FragmentPagerAdapter(fm){

    private val fragmentList = ArrayList<Fragment>()
    private val titleList = ArrayList<String>()
    private val tagList = ArrayList<String>()

    fun addFragment(fragment: Fragment, title: String = ""){
        fragmentList.add(fragment)
        titleList.add(title)
    }

    fun clearFragments() {
        fragmentList.clear()
        titleList.clear()
    }

//    override fun instantiateItem(container: ViewGroup, position: Int): Any {
//        val createdFragment = super.instantiateItem(container, position) as Fragment
//
//        return super.instantiateItem(container, position)
//    }

    override fun getPageTitle(position: Int) = titleList[position]
    override fun getItem(position: Int) = fragmentList[position]
    override fun getCount() = fragmentList.size
}