package com.uniq.uniqpos.util.security

import javax.crypto.Cipher
import javax.crypto.spec.IvParameterSpec
import javax.crypto.spec.SecretKeySpec
import java.security.MessageDigest
import java.security.SecureRandom
import java.util.Arrays

//import java.nio.charset.StandardCharsets.US_ASCII
//import java.nio.charset.StandardCharsets.UTF_8

class Aes256Legacy {

//    private val SALTED = "Salted__".toByteArray(US_ASCII)
    private val SALTED = "Salted__".toByteArray()

    @Throws(Exception::class)
    fun encrypt(input: String, passphrase: String): String {
//        return Base64.encodeToString(_encrypt(input.toByteArray(UTF_8), passphrase.toByteArray(UTF_8)), Base64.NO_WRAP)
        return Base64.encodeToString(_encrypt(input.toByteArray(), passphrase.toByteArray()), Base64.NO_WRAP)
    }

    @Throws(Exception::class)
    fun encrypt(input: ByteArray, passphrase: ByteArray): ByteArray {
        return Base64.encode(_encrypt(input, passphrase), Base64.NO_WRAP)
    }

    @Throws(Exception::class)
    fun decrypt(crypted: String, passphrase: String): String {
//        return String(_decrypt(Base64.decode(crypted, Base64.NO_WRAP), passphrase.toByteArray(UTF_8)), UTF_8)
        return String(_decrypt(Base64.decode(crypted, Base64.NO_WRAP), passphrase.toByteArray()))
    }

    @Throws(Exception::class)
    fun decrypt(crypted: ByteArray, passphrase: ByteArray): ByteArray {
        return _decrypt(Base64.decode(crypted, Base64.NO_WRAP), passphrase)
    }

    @Throws(Exception::class)
    internal fun _encrypt(input: ByteArray, passphrase: ByteArray): ByteArray {
        val salt = SecureRandom().generateSeed(8)
        val keyIv = deriveKeyAndIv(passphrase, salt)

        val cipher = Cipher.getInstance("AES/CBC/PKCS5Padding")
        cipher.init(Cipher.ENCRYPT_MODE, SecretKeySpec(keyIv[0] as ByteArray, "AES"), IvParameterSpec(keyIv[1] as ByteArray))

        val enc = cipher.doFinal(input)
        return concat(concat(SALTED, salt), enc)
    }

    @Throws(Exception::class)
    internal fun _decrypt(data: ByteArray, passphrase: ByteArray): ByteArray {
        val salt = Arrays.copyOfRange(data, 8, 16)

        if (!Arrays.equals(Arrays.copyOfRange(data, 0, 8), SALTED)) {
            throw IllegalArgumentException("Invalid crypted data")
        }

        val keyIv = deriveKeyAndIv(passphrase, salt)

        val cipher = Cipher.getInstance("AES/CBC/PKCS5Padding")
        cipher.init(Cipher.DECRYPT_MODE, SecretKeySpec(keyIv[0] as ByteArray, "AES"), IvParameterSpec(keyIv[1] as ByteArray))
        return cipher.doFinal(data, 16, data.size - 16)
    }

    @Throws(Exception::class)
    internal fun deriveKeyAndIv(passphrase: ByteArray, salt: ByteArray): Array<Any> {
        val md5 = MessageDigest.getInstance("MD5")
        val passSalt = concat(passphrase, salt)
        var dx = ByteArray(0)
        var di = ByteArray(0)

        for (i in 0..2) {
            di = md5.digest(concat(di, passSalt))
            dx = concat(dx, di)
        }

        return arrayOf(Arrays.copyOfRange(dx, 0, 32), Arrays.copyOfRange(dx, 32, 48))
    }

    private fun concat(a: ByteArray, b: ByteArray): ByteArray {
        val c = ByteArray(a.size + b.size)
        System.arraycopy(a, 0, c, 0, a.size)
        System.arraycopy(b, 0, c, a.size, b.size)
        return c
    }
}