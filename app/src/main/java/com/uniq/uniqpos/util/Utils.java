package com.uniq.uniqpos.util;

import android.Manifest;
import android.app.Activity;
import android.content.ContentUris;
import android.content.Context;
import android.content.pm.PackageManager;
import android.database.Cursor;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.net.Uri;
import android.os.Build;
import android.os.Environment;
import android.provider.DocumentsContract;
import android.provider.MediaStore;
import android.telephony.TelephonyManager;
import android.text.Editable;
import android.text.TextWatcher;
import android.util.Log;
import android.view.View;
import android.view.ViewParent;
import android.view.inputmethod.InputMethodManager;
import android.widget.AutoCompleteTextView;
import android.widget.EditText;

import androidx.core.app.ActivityCompat;

import com.google.android.material.textfield.TextInputEditText;
import com.google.android.material.textfield.TextInputLayout;
import com.uniq.uniqpos.BuildConfig;
import com.weiwangcn.betterspinner.library.material.MaterialBetterSpinner;

import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.net.URISyntaxException;
import java.text.NumberFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Random;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.RequestBody;
import timber.log.Timber;

/**
 * Created by Git Solution on 29/09/2017.
 */

public class Utils {

    public static int getColor(Context context, int id) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            return context.getColor(id);
        } else {
            //noinspection deprecation
            return context.getResources().getColor(id);
        }
    }

    public static boolean isEmailValid(TextInputEditText txtEmail) {
        String EMAIL_PATTERN =
                "^[_A-Za-z0-9-\\+]+(\\.[_A-Za-z0-9-]+)*@"
                        + "[A-Za-z0-9-]+(\\.[A-Za-z0-9]+)*(\\.[A-Za-z]{2,})$";

        Pattern pattern = Pattern.compile(EMAIL_PATTERN);
        Matcher matcher = pattern.matcher(txtEmail.getText().toString());
        boolean isValid = matcher.matches();
        if (!isValid) txtEmail.setError("Email not valid");
        return isValid;
    }

    public static boolean isValidField(View... views) {
        boolean status = true;
        View firstViewErr = null;
        for (View v : views) {
            if (v.getVisibility() == View.VISIBLE) {
                if (v instanceof TextInputEditText) {
                    boolean isParentVisibile = true;
                    ViewParent parent = v.getParent().getParent();
                    if (parent instanceof TextInputLayout) {
                        isParentVisibile = ((TextInputLayout) parent).getVisibility() == View.VISIBLE;
                    }

                    if (isParentVisibile) {
                        if (((TextInputEditText) v).getText().toString().trim().isEmpty()) {
                            ((TextInputEditText) v).setError("Field can't be blank");
                            if (firstViewErr == null) {
                                firstViewErr = v;
                            }
                            status = false;
                        } else {
                            ((TextInputEditText) v).setError(null);
                        }
                    }
                } else if (v instanceof MaterialBetterSpinner) {
                    if (((MaterialBetterSpinner) v).getEditableText().toString().trim().isEmpty()) {
                        ((MaterialBetterSpinner) v).setError("Field can't be blank");
                        if (firstViewErr == null) {
                            firstViewErr = v;
                        }
                        status = false;
                    }
                } else if (v instanceof AutoCompleteTextView) {
                    if (((AutoCompleteTextView) v).getText().toString().trim().isEmpty()) {
                        if (firstViewErr == null) {
                            firstViewErr = v;
                        }
                        status = false;
                        ((AutoCompleteTextView) v).setError("Field can't be blank");
                    } else {
                        ((AutoCompleteTextView) v).setError(null);
                    }
                }
            }
        }
        if (!status && firstViewErr != null) {
            firstViewErr.requestFocus();
        }
        return status;
    }

    public static void hideKeyboard(Activity activity) {
        View view = activity.getCurrentFocus();
        if (view != null) {
            InputMethodManager imm = (InputMethodManager) activity.getSystemService(Context.INPUT_METHOD_SERVICE);
            imm.hideSoftInputFromWindow(view.getWindowToken(), 0);
        }
    }

    public static String generateNoNota() {
        List<String> words = new ArrayList<>(Arrays.asList("M", "N", "K", "O", "H", "S", "N", "I", "A", "L"));
        String[] time = String.valueOf(System.currentTimeMillis()).split("");
        List<Integer> changes = new ArrayList();
        int max = time.length;
        int min = 1;
        for (int i = 0; i < time.length / 2; i++) {
            int index = new Random().nextInt(max - min + 1) + min;
            if (!changes.contains(index)) changes.add(index);
        }

        StringBuilder value = new StringBuilder();
        for (int i = 0; i < time.length; i++) {
            if (!time[i].isEmpty()) {
                if (changes.contains(i)) value.append(words.get(Integer.parseInt(time[i])));
                else value.append(time[i]);
            }
        }
        return value.toString();
    }

    public static void registerToCurrency(TextInputEditText... editTexts) {
        for (final TextInputEditText editText : editTexts) {
            editText.addTextChangedListener(new TextWatcher() {
                @Override
                public void beforeTextChanged(CharSequence s, int i, int i1, int i2) {
                }

                @Override
                public void onTextChanged(CharSequence s, int i, int i1, int i2) {
                    if (editText.getText().toString().trim().length() > 0) {
                        editText.removeTextChangedListener(this);
                        try {
                            String strNumber = s.toString().replace(",", "");
                            strNumber = strNumber.replace(".", "");
                            int number = Integer.parseInt(strNumber);
                            String currency = NumberFormat.getInstance().format(number);
                            editText.setText(currency);
                            editText.setSelection(currency.length());
                        } catch (Exception e) {
                        }
                        editText.addTextChangedListener(this);
                    }
                }

                @Override
                public void afterTextChanged(Editable editable) { }
            });
        }
    }

    public static void getDeviceID(Context context) {
        TelephonyManager telephonyManager = (TelephonyManager) context.getSystemService(Context.TELEPHONY_SERVICE);
        if (ActivityCompat.checkSelfPermission(context, Manifest.permission.READ_PHONE_STATE) != PackageManager.PERMISSION_GRANTED) {
            return;
        }
        telephonyManager.getDeviceId();
    }

    public static Long getMinTimeMillisToday() {
        Calendar cal = Calendar.getInstance();
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);
        return cal.getTimeInMillis();
    }

    public static Long getMaxTimeMillisToday() {
        Calendar cal = Calendar.getInstance();
        cal.set(Calendar.HOUR_OF_DAY, 23);
        cal.set(Calendar.MINUTE, 59);
        cal.set(Calendar.SECOND, 59);
        cal.set(Calendar.MILLISECOND, 999);
        return cal.getTimeInMillis();
    }

    /* Checks if external storage is available for read and write */
    public static boolean isExternalStorageWritable() {
        String state = Environment.getExternalStorageState();
        if (Environment.MEDIA_MOUNTED.equals(state)) {
            return true;
        }
        return false;
    }

    /* Checks if external storage is available to at least read */
    public static boolean isExternalStorageReadable() {
        String state = Environment.getExternalStorageState();
        if (Environment.MEDIA_MOUNTED.equals(state) ||
                Environment.MEDIA_MOUNTED_READ_ONLY.equals(state)) {
            return true;
        }
        return false;
    }

    public static void appendLog(String text, String type) {
        if (BuildConfig.FLAVOR == "production" && !BuildConfig.DEBUG) return;

        if (!Utils.isExternalStorageWritable()) {
            Timber.d("External Storage not writeable");
            return;
        }
        if (!Utils.isExternalStorageReadable()) {
            Timber.d("External Storage not readable");
            return;
        }

        File externalStorageDir = Environment.getExternalStorageDirectory();
        Calendar cal = Calendar.getInstance();
        SimpleDateFormat sdfDay = new SimpleDateFormat("yyyy_MM_dd");
        String parent = externalStorageDir + "/UNIQ/" + sdfDay.format(new Date());
        String date = new SimpleDateFormat("MMdd").format(new Date());
        File logFile = new File(parent, type + "_" + date + (type == "starting" ? "" : "_" + cal.get(Calendar.HOUR_OF_DAY)) + ".txt");
        if (!new File(parent).exists()) {
            new File(parent).mkdirs();
        }
        if (!logFile.exists()) {
            try {
                logFile.createNewFile();
            } catch (IOException e) {
                Timber.d("Create log file error. " + e);
            }
        }

        try {
            SimpleDateFormat sdf = new SimpleDateFormat("dd-MM-yyyy HH:mm:ss");
            //BufferedWriter for performance, true to set append to file flag
            BufferedWriter buf = new BufferedWriter(new FileWriter(logFile, true));
            buf.append("\n" + sdf.format(new Date()) + " : " + text);
            buf.newLine();
            buf.close();
        } catch (IOException e) {
            Timber.d("Write log file error. " + e);
        }
    }

    public static Set<String> getNDK() {
        try {
            Set<String> libs = new HashSet<String>();
            String mapsFile = "/proc/" + android.os.Process.myPid() + "/maps";
            BufferedReader reader = new BufferedReader(new FileReader(mapsFile));
            String line;
            while ((line = reader.readLine()) != null) {
                if (line.endsWith(".so")) {
                    int n = line.lastIndexOf(" ");
                    libs.add(line.substring(n + 1));
                }
            }
            Log.d("Ldd", libs.size() + " libraries:");
            for (String lib : libs) {
                Log.d("Ldd", lib);
            }
            return libs;
        } catch (FileNotFoundException e) {
            // Do some error handling...
        } catch (IOException e) {
            // Do some error handling...
        }
        return null;
    }

    public static void saveBitmapToLocal(String sourceFile, String destinationFile) {
        Bitmap bmp = BitmapFactory.decodeFile(sourceFile);
        FileOutputStream out = null;
        try {
            out = new FileOutputStream(destinationFile);
            bmp.compress(Bitmap.CompressFormat.PNG, 100, out); // bmp is your Bitmap instance
            // PNG is a lossless format, the compression factor (100) is ignored
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                if (out != null) {
                    out.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    public static String getFilePath(Context context, Uri uri) throws URISyntaxException {
        String selection = null;
        String[] selectionArgs = null;
        // Uri is different in versions after KITKAT (Android 4.4), we need to
        if (Build.VERSION.SDK_INT >= 19 && DocumentsContract.isDocumentUri(context.getApplicationContext(), uri)) {
            if (isExternalStorageDocument(uri)) {
                final String docId = DocumentsContract.getDocumentId(uri);
                final String[] split = docId.split(":");
                return Environment.getExternalStorageDirectory() + "/" + split[1];
            } else if (isDownloadsDocument(uri)) {
                final String id = DocumentsContract.getDocumentId(uri);
                uri = ContentUris.withAppendedId(
                        Uri.parse("content://downloads/public_downloads"), Long.valueOf(id));
            } else if (isMediaDocument(uri)) {
                final String docId = DocumentsContract.getDocumentId(uri);
                final String[] split = docId.split(":");
                final String type = split[0];
                if ("image".equals(type)) {
                    uri = MediaStore.Images.Media.EXTERNAL_CONTENT_URI;
                } else if ("video".equals(type)) {
                    uri = MediaStore.Video.Media.EXTERNAL_CONTENT_URI;
                } else if ("audio".equals(type)) {
                    uri = MediaStore.Audio.Media.EXTERNAL_CONTENT_URI;
                }
                selection = "_id=?";
                selectionArgs = new String[]{
                        split[1]
                };
            }
        }
        if ("content".equalsIgnoreCase(uri.getScheme())) {
            String[] projection = {
                    MediaStore.Images.Media.DATA
            };
            Cursor cursor = null;
            try {
                cursor = context.getContentResolver()
                        .query(uri, projection, selection, selectionArgs, null);
                int column_index = cursor.getColumnIndexOrThrow(MediaStore.Images.Media.DATA);
                if (cursor.moveToFirst()) {
                    return cursor.getString(column_index);
                }
            } catch (Exception e) {
                if (cursor != null) {
                    cursor.close();
                }
            }
        } else if ("file".equalsIgnoreCase(uri.getScheme())) {
            return uri.getPath();
        }
        return null;
    }

    public static RequestBody requestBody(String val) {
        if (val == null) val = "";
        return RequestBody.create(MediaType.parse("text/plain"), val);
    }

    public static MultipartBody.Part requestBodyFile(String filePath, MediaTypes mediaTypes, String key) {
        if (filePath == null || filePath.isEmpty()) return null;

        String type = "";
        if (mediaTypes == MediaTypes.IMAGE) type = "image/jpg";
        else if (mediaTypes == MediaTypes.TEXT_FILE) type = "text/html";
        else if (mediaTypes == MediaTypes.JSON) type = "application/json";
        File file = new File(filePath);
        RequestBody reqFile = RequestBody.create(MediaType.parse(type), file);
        MultipartBody.Part body = MultipartBody.Part.createFormData(key, file.getName(), reqFile);
        return body;
    }

    public static enum MediaTypes {
        IMAGE, TEXT_FILE, JSON
    }

    public static boolean isExternalStorageDocument(Uri uri) {
        return "com.android.externalstorage.documents".equals(uri.getAuthority());
    }

    public static boolean isDownloadsDocument(Uri uri) {
        return "com.android.providers.downloads.documents".equals(uri.getAuthority());
    }

    public static boolean isMediaDocument(Uri uri) {
        return "com.android.providers.media.documents".equals(uri.getAuthority());
    }

    public static String escape(String s) {
        StringBuilder builder = new StringBuilder();
        boolean previousWasASpace = false;
        for (char c : s.toCharArray()) {
            if (c == ' ') {
                if (previousWasASpace) {
                    builder.append("&nbsp;");
                    previousWasASpace = false;
                    continue;
                }
                previousWasASpace = true;
            } else {
                previousWasASpace = false;
            }
            switch (c) {
                case '<':
                    builder.append("&lt;");
                    break;
                case '>':
                    builder.append("&gt;");
                    break;
                case '&':
                    builder.append("&amp;");
                    break;
                case '"':
                    builder.append("&quot;");
                    break;
                case '\n':
                    builder.append("<br/>");
                    break;
                case ' ':
                    builder.append("&nbsp;");
                    break;
                // We need Tab support here, because we print StackTraces as HTML
                case '\t':
                    builder.append("&nbsp; &nbsp; &nbsp;");
                    break;
                default:
                    if (c < 128) {
                        builder.append(c);
                    } else {
                        builder.append("&#").append((int) c).append(";");
                    }
            }
        }
        return builder.toString();
    }
}
