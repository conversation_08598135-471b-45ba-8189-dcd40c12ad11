package com.uniq.uniqpos.util.extensions

import android.content.Context
import android.graphics.Bitmap
import android.net.Uri
import android.os.Environment
import android.util.Base64
import com.bumptech.glide.Glide
import com.uniq.uniqpos.util.BACKGROUND
import com.uniq.uniqpos.util.safe
import okhttp3.MediaType
import okhttp3.MultipartBody
import okhttp3.RequestBody
import timber.log.Timber
import java.io.ByteArrayOutputStream
import java.io.File
import kotlin.coroutines.resume
import kotlin.coroutines.suspendCoroutine

fun File.size(): Double{
    return if (!exists()) 0.0 else length().toDouble()
}

fun File.sizeInKb(): Double  = size() / 1024
fun File.sizeInMb(): Double =  sizeInKb() / 1024
fun File.sizeInGb(): Double = sizeInMb() / 1024
fun File.sizeInTb() = sizeInGb() / 1024

fun File.hasExtension(): Boolean {
    val fileName = this.name
    val lastDotIndex = fileName.lastIndexOf('.')
    return lastDotIndex != -1 && lastDotIndex != fileName.length - 1 // Check if dot is not the last character
}

fun File.toBase64(): String {
    if(!exists()){
        Timber.i("can not convert to base64, file not found")
        return ""
    }
    val bytes = this.readBytes()
    return android.util.Base64.encodeToString(bytes, android.util.Base64.DEFAULT)
}

fun String.base64ToFile(baseDir: File): File? {
    return try {
        val decodedBytes = Base64.decode(this, Base64.DEFAULT)
        val file = File(baseDir, "uniq_product_" + System.currentTimeMillis() + ".jpeg")
        file.writeBytes(decodedBytes)
        file // Return the created file
    } catch (e: Exception) {
        Timber.i("failed convert base64 to file: $e")
        null // Return null on error
    }
}

fun Uri.asFile(): File = File(toString())

fun Context.defaultTmpImage(): File {
    val externalFilesDir = getExternalFilesDir(Environment.DIRECTORY_PICTURES)
    return File(externalFilesDir, "uniq_product_" + System.currentTimeMillis() + ".jpeg")
}

suspend fun File.toBitmap(context: Context, targetFileSize: Double? = null): Bitmap {
    return suspendCoroutine { continuation ->
        BACKGROUND.submit {
            val res = Glide.with(context).asBitmap().load(this).submit()
            val bitmap = res.get()
            val resizedBitmap =  targetFileSize?.let { size ->
                bitmap.compressFile(size, this)
            } ?: run{ bitmap }
            continuation.resume(resizedBitmap)
        }
    }
}