package com.uniq.uniqpos.util

import android.content.Context
import android.net.wifi.WifiManager
import android.util.Log
import kotlinx.coroutines.*
import timber.log.Timber
import java.io.BufferedReader
import java.io.FileReader
import java.io.IOException
import java.net.InetAddress
import java.util.regex.Matcher
import java.util.regex.Pattern

/**
 * Created by annasblackhat on 08/01/19
 */
class NetworkScanner {

    private var job = Job()
    private var jobScanner = CoroutineScope(Dispatchers.Main + job)
    private var listener: NetworkScanListener? = null
    private val MAC_RE = "^%s\\s+0x1\\s+0x2\\s+([:0-9a-fA-F]+)\\s+\\*\\s+\\w+$"
    private val BUF = 8 * 1024
    val NOMAC = "00:00:00:00:00:00"
    private val TAG = "HardwareAddress"

    companion object {
        fun isNetworkReachable(host: String, timeout: Int = 100): Boolean {
            val inet = try {
                InetAddress.getByName(host)
            } catch (e: Exception) { null }
            return inet?.isReachable(timeout).safe()
        }
    }

    fun scan(context: Context, listener: NetworkScanListener? = null) {
        this.listener = listener
        val subnet = getSubnet(context)
        val timeout = 100

        jobScanner.launch {
            for (i in 1..254) {
                val host = "$subnet.$i"
                val inet = InetAddress.getByName(host)
                val isReachable = GlobalScope.async { inet.isReachable(timeout) }.await()
                if (isReachable) {
                    listener?.onDeviceDiscovery(ScannerData(host, getHardwareAddress(host)))
                }
                Timber.i("try to reach $host... is reachable ? $isReachable")
                if (i == 254) {
                    listener?.onJobFinish()
                }
            }
        }
    }

    private fun getSubnet(context: Context): String {
        val mWifiManager = context.getSystemService(Context.WIFI_SERVICE) as WifiManager
//        val mWifiInfo = mWifiManager.connectionInfo
        val address = mWifiManager.dhcpInfo.gateway
        return String.format(
                "%d.%d.%d",
                address and 0xff,
                address shr 8 and 0xff,
                address shr 16 and 0xff
        )
    }

    private fun getHardwareAddress(ip: String?): String {
        var hw = NOMAC
        var bufferedReader: BufferedReader? = null
        try {
            if (ip != null) {
                val ptrn = String.format(MAC_RE, ip.replace(".", "\\."))
                val pattern = Pattern.compile(ptrn)
                bufferedReader = BufferedReader(FileReader("/proc/net/arp"), BUF)
                var line = bufferedReader.readLine()
                var matcher: Matcher

                while (line != null) {
                    matcher = pattern.matcher(line)
                    if (matcher.matches()) {
                        hw = matcher.group(1)
                        break
                    }
                    line = bufferedReader.readLine()
                }
            } else {
                Log.e(TAG, "ip is null")
            }
        } catch (e: IOException) {
            Log.e(TAG, "Can't open/read file ARP: " + e.message)
            return hw
        } finally {
            try {
                bufferedReader?.close()
            } catch (e: IOException) {
                Timber.i("err: $e")
            }

        }
        return hw
    }

    fun stop() {
        job.cancel()
        listener = null
    }

    interface NetworkScanListener {
        fun onDeviceDiscovery(data: ScannerData)
        fun onJobFinish()
    }

    data class ScannerData(
            var ipAddress: String,
            var macAddress: String
    )
}