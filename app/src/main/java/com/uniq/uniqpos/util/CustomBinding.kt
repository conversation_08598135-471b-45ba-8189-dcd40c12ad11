package com.uniq.uniqpos.util

import androidx.databinding.BindingAdapter
import com.google.android.material.textfield.TextInputEditText
import android.graphics.drawable.Drawable
import androidx.appcompat.content.res.AppCompatResources
import android.text.Html
import android.view.View
import android.widget.ArrayAdapter
import android.widget.Button
import android.widget.ImageView
import android.widget.TextView
import androidx.appcompat.widget.AppCompatAutoCompleteTextView
import androidx.core.content.res.ResourcesCompat
import com.amulyakhare.textdrawable.TextDrawable
import com.amulyakhare.textdrawable.util.ColorGenerator
import com.bumptech.glide.load.DataSource
import com.bumptech.glide.load.engine.GlideException
import com.bumptech.glide.request.RequestListener
import com.bumptech.glide.request.target.Target
import com.uniq.uniqpos.BuildConfig
import com.uniq.uniqpos.R
import com.uniq.uniqpos.app.GlideApp
import com.uniq.uniqpos.data.local.sharedpref.SharedPref
import com.uniq.uniqpos.data.local.sharedpref.getJson
import com.uniq.uniqpos.model.Admin
import com.weiwangcn.betterspinner.library.material.MaterialBetterSpinner
import de.hdodenhof.circleimageview.CircleImageView
import timber.log.Timber
import java.io.File
import java.net.URL
import java.text.NumberFormat
import java.text.SimpleDateFormat
import java.util.*


/**
 * Created by ANNASBlackHat on 06/10/2017.
 */

@BindingAdapter(value = ["imgUrl", "hideOnError", "placeHolder"], requireAll = false)
fun setImageUrl(img: ImageView, url: String?, hideOnError: Boolean = false, placeHolder: Int = R.drawable.default_image) {
    if (hideOnError && (url == null || url.isBlank())) {
        img.visibility = View.GONE
        return
    }

    var imgUrl = url ?: ""
    if (!imgUrl.startsWith("https://")) {
        val admin = img.context.getJson(SharedPref.ADMIN_DATA, Admin::class.java)
        imgUrl = "${BuildConfig.WEB_URL}assets/images/products/${admin?.adminId}/$url"
    }
    GlideApp.with(img.context)
        .load(imgUrl)
        .placeholder(placeHolder)
        .addListener(object : RequestListener<Drawable> {
            override fun onResourceReady(
                resource: Drawable?,
                model: Any?,
                target: Target<Drawable>?,
                dataSource: DataSource?,
                isFirstResource: Boolean
            ) = false

            override fun onLoadFailed(
                e: GlideException?,
                model: Any?,
                target: Target<Drawable>?,
                isFirstResource: Boolean
            ): Boolean {
                if (hideOnError) {
                    img.visibility = View.GONE
                    img.context.toast("image not found!")
                }
                return true
            }
        })
        .into(img)
}

@BindingAdapter("imgUrlTextRound", "name")
fun setImageUrlTextRound(img: ImageView, url: String?, name: String?) {
    var text = name
    val placeholder = ResourcesCompat.getDrawable(img.resources, R.drawable.default_image, null)
    if (url != null) {
        if (text != null) {
            text = text.trim { it <= ' ' }
            var teks = "A"
            if (text.length > 1) teks = text.substring(0, 2)
            if (text.contains(" "))
                teks = text.substring(0, 1) + text.substring(
                    text.lastIndexOf(" ") + 1,
                    text.lastIndexOf(" ") + 2
                )
//            val generator = ColorGenerator.MATERIAL
        }

    }

    GlideApp.with(img.context)
        .load(Constant.IMAGE_URL + url)
        .placeholder(placeholder)
        .into(img)
}

@BindingAdapter("textRandomBack")
fun setTextRandomBackground(txt: TextView, name: String?) {
    val generator = ColorGenerator.MATERIAL
    txt.setBackgroundColor(generator.getColor(name ?: "a"))
}

@BindingAdapter(value = ["imgUrlText", "name"], requireAll = false)
fun setImageUrlText(img: ImageView, url: String?, name: String?) {
    var name = name
    var placeholder = ResourcesCompat.getDrawable(img.resources, R.drawable.default_image, null)
    if (name != null) {
        name = name.trim { it <= ' ' }
        var teks = "A"
        if (name != null && name.length > 1) teks = name.substring(0, 2)
        if (name != null && name.contains(" ")) {
            var seconds: String
            var loop = 0
            do {
                val index = name.lastIndexOf(" ") + loop
                seconds = if ((index + 2) < name.length) {
                    name.substring(index + 1, index + 2)
                } else {
                    "X"
                }
                loop++
            } while (seconds == "(")
            teks = name.substring(0, 1) + seconds
        }
        val generator = ColorGenerator.MATERIAL
        placeholder = TextDrawable.Builder()
            .setColor(generator.getColor(teks))
            .setShape(TextDrawable.SHAPE_RECT)
            .setFontSize(50)
            .setText(teks)
            .build()
//            .buildRect(teks.uppercase(), generator.getColor(teks))
    }

    var imgUrl = url ?: ""
    if (!imgUrl.startsWith("https://") && imgUrl.isNotBlank()) {
        val admin = img.context.getJson(SharedPref.ADMIN_DATA, Admin::class.java)
        imgUrl = "${BuildConfig.WEB_URL}assets/images/products/${admin?.adminId}/$url"
    }

    GlideApp.with(img.context)
        .load(imgUrl)
        .placeholder(placeholder)
        .into(img)
}

@BindingAdapter("imgDrawable")
fun setImgDrawable(img: ImageView, drawable: Int) {
    GlideApp.with(img.context)
        .load(drawable)
        .placeholder(R.drawable.default_image)
        .into(img)
}

@BindingAdapter("timeFromDate")
fun setTimeFromDate(txt: TextView, date: Date) {
    val sdf = SimpleDateFormat("HH:mm", Locale.getDefault())
    txt.text = sdf.format(date)
}

@BindingAdapter("timeMillisToTime")
fun setTimeFromTimeMillis(txt: TextView, timeMillis: Long?) {
    val sdf = SimpleDateFormat("HH:mm", Locale.getDefault())
    timeMillis?.let { txt.text = sdf.format(Date(timeMillis)) }
}

@BindingAdapter(value = ["timeMillisToDate", "format"], requireAll = false)
fun setDateFromTimeMillis(txt: TextView, timeMillis: Long?, format: String?) {
    if (timeMillis == 0L) {
        return
    }

    val dateFormat = format?.let { format } ?: run { "dd-MM-yyyy HH:mm" }
    val sdf = SimpleDateFormat(dateFormat, Locale.getDefault())
    timeMillis?.let { txt.text = sdf.format(Date(timeMillis)) }
}

@BindingAdapter(value = ["textCurrency", "symbol", "visible"], requireAll = false)
fun setTextCurrency(txt: TextView, value: Int, paramSymbol: String? = null , visible: Boolean? = true) {
    var symbol = paramSymbol
    if (symbol == null) symbol = ""
    txt.text = symbol + NumberFormat.getNumberInstance().format(value.toLong())
}

@BindingAdapter(value = ["strCurrency", "symbol"], requireAll = false)
fun setStringCurrency(txt: TextView, value: String?, symbol: String?) {
    var symbol = symbol
    if (symbol == null) symbol = ""
    var number = 0
    if (value != null) number = Integer.parseInt(value)
    txt.text = symbol + NumberFormat.getNumberInstance().format(number.toLong())
}

@BindingAdapter(value = ["dateFromat", "format"], requireAll = false)
fun setTextDate(txt: TextView, date: Date, format: String) {
    val sdf = SimpleDateFormat(format, Locale.getDefault())
    txt.text = sdf.format(date)
}

@BindingAdapter("spinnerItem")
fun setBetterSpinnerItem(spinner: MaterialBetterSpinner, items: Array<String>) {
    val adapter = ArrayAdapter(spinner.context, android.R.layout.simple_list_item_1, items)
    spinner.setAdapter(adapter)
}

@BindingAdapter(value = ["imgLocalFile", "path"], requireAll = false)
fun setImageCircleFromLocalFile(img: CircleImageView, imgLocalFile: String?, path: String?) {
    val fileName =
        if (imgLocalFile?.startsWith("https://") == true || imgLocalFile?.startsWith("http://") == true) {
            val path = URL(imgLocalFile).path
            path.substring(path.lastIndexOf("/") + 1)
        } else imgLocalFile

    GlideApp.with(img.context)
        .load(File(img.context.filesDir, (path + fileName)))
        .placeholder(R.drawable.icon_shop_outline)
        .into(img)
}

@BindingAdapter(value = ["timeBetween", "timeEnd"], requireAll = true)
fun setTextTimeBetween(txt: TextView, timeStart: Long?, timeEnd: Long?) {
    val timeFormat = SimpleDateFormat("HH:mm", Locale.getDefault())
    txt.text = timeFormat.format(
        Date(
            timeStart
                ?: System.currentTimeMillis()
        )
    ) + " - " + timeFormat.format(
        Date(
            timeEnd
                ?: System.currentTimeMillis()
        )
    )
}

@BindingAdapter("app:imgTop")
fun setDrawableStart(textView: TextView, resourceId: Int) {
    val drawable = AppCompatResources.getDrawable(textView.context, resourceId)
    val drawables = textView.compoundDrawables
    textView.setCompoundDrawablesWithIntrinsicBounds(
        drawable,
        drawables[1], drawables[2], drawables[3]
    )
}

@BindingAdapter(value = ["drawableBound", "position"])
fun setDrawableBound(textView: TextInputEditText, resourceId: Int, position: String) {
    if (resourceId > 0) {
        val drawable = AppCompatResources.getDrawable(textView.context, resourceId)
        val drawables = textView.compoundDrawables
        textView.setCompoundDrawablesWithIntrinsicBounds(
            null,
            null, drawable, null
        )
    }
}

@BindingAdapter(value = ["drawableBoundAutoComp"])
fun setDrawableBound(textView: AppCompatAutoCompleteTextView, resourceId: Int) {
    if (resourceId > 0) {
        val drawable = AppCompatResources.getDrawable(textView.context, resourceId)
//        val drawables = textView.compoundDrawables
        textView.setCompoundDrawablesWithIntrinsicBounds(
            null,
            null, drawable, null
        )
    }
}

@BindingAdapter("textColor")
fun setTextColorMaterial(textView: TextView, identifier: String) {
    textView.setTextColor(ColorGenerator.MATERIAL.getColor(identifier))
}

@BindingAdapter(value = ["splitView", "index"], requireAll = true)
fun splitView(view: Button, splitView: String?, index: Int) {
    Timber.i(">>> btn : $view | item : $splitView | index : $index")
    splitView?.let { item ->
        val items = item.split(Constant.SPLITTER)
        if (index < items.size && items[index].isNotEmpty()) {
            view.text = items[index]
            view.visibility = View.VISIBLE
        } else {
            view.visibility = View.GONE
        }
    }
}

@BindingAdapter("textHtml")
fun setTextFromHtml(textView: TextView, content: String) {
    if (android.os.Build.VERSION.SDK_INT >= 24) {
        textView.text = Html.fromHtml(content, Html.FROM_HTML_MODE_LEGACY)
    } else {
        textView.text = Html.fromHtml(content)
    }
}

@BindingAdapter("textPromotionType")
fun setPromotionName(textView: TextView, typeId: Int) {
    textView.text = typeId.readablePromoType()
}

@BindingAdapter("textError")
fun setTextError(view: TextInputEditText, message: String) {
    if (message.isNotBlank())
        view.error = message
}