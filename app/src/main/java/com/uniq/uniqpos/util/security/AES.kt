package com.uniq.uniqpos.util.security

import com.uniq.uniqpos.BuildConfig
import java.security.NoSuchAlgorithmException

import javax.crypto.Cipher
import javax.crypto.NoSuchPaddingException
import javax.crypto.spec.IvParameterSpec
import javax.crypto.spec.SecretKeySpec

/**
 * Created by annasblack<PERSON> on 15/02/18.
 */

class AES {
    private val ivspec: IvParameterSpec = IvParameterSpec(BuildConfig.IV_SPEC.toByteArray())
    private val keyspec: SecretKeySpec = SecretKeySpec(BuildConfig.KEY_SPEC.toByteArray(), "AES")
    private var cipher: Cipher? = null

    init {
        try {
            cipher = Cipher.getInstance("AES/CBC/NoPadding")//AES/CBC/NoPadding
        } catch (e: NoSuchAlgorithmException) {
            e.printStackTrace()
        } catch (e: NoSuchPaddingException) {
            e.printStackTrace()
        }
    }

    @Throws(Exception::class)
    fun encrypt(text: String?): ByteArray? {
        if (text == null || text.isEmpty())
            throw Exception("Empty string")

        var encrypted: ByteArray?
        try {
            cipher!!.init(Cipher.ENCRYPT_MODE, keyspec, ivspec)
            encrypted = cipher!!.doFinal(padString(text).toByteArray())
        } catch (e: Exception) {
            throw Exception("[encrypt] " + e.message)
        }

        return encrypted
    }

    @Throws(Exception::class)
    fun decrypt(code: String?): ByteArray? {
        if (code == null || code.isEmpty())
            throw Exception("Empty string")
        var decrypted: ByteArray?
        try {
            cipher!!.init(Cipher.DECRYPT_MODE, keyspec, ivspec)
            decrypted = cipher!!.doFinal(hexToBytes(code))
        } catch (e: Exception) {
            throw Exception("[decrypt] " + e.message)
        }

        return decrypted
    }

    fun hexToBytes(str: String?): ByteArray? {
        return when {
            str == null -> null
            str.length < 2 -> null
            else -> {
                val len = str.length / 2
                val buffer = ByteArray(len)
                for (i in 0 until len) {
                    buffer[i] = Integer.parseInt(str.substring(i * 2, i * 2 + 2), 16).toByte()
                }
                buffer
            }
        }
    }

    private fun padString(source: String): String {
        var source = source
        val paddingChar = ' '
        val size = 16
        val x = source.length % size
        val padLength = size - x

        for (i in 0 until padLength) {
            source += paddingChar
        }

        return source
    }

    companion object {
        fun decryptStr(value: String): String {
            return try {
                String(AES().decrypt(value)!!).trim { it <= ' ' }
            } catch (e: Exception) {
                ""
            }
        }
    }
}
