package com.uniq.uniqpos.util.kds

import android.content.res.AssetManager
import com.uniq.uniqpos.util.Constant
import com.uniq.uniqpos.util.networking.getTrustManager
import io.ktor.client.HttpClient
import io.ktor.client.engine.cio.CIO
import io.ktor.client.features.websocket.WebSockets
import io.ktor.client.features.websocket.wss
import io.ktor.http.HttpMethod
import io.ktor.http.cio.websocket.Frame
import io.ktor.http.cio.websocket.readText
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import timber.log.Timber

class KdsConnection(private val assetManager: AssetManager) {
    private val clientList = HashMap<String, HttpClient>()

    private fun getClient(host: String, port: Int): HttpClient {
        if (clientList.containsKey("$host:$port")) {
            Timber.i("use existing client $host:$port")
            return clientList["$host:$port"]!!
        }
        val client = HttpClient(CIO) {
            install(WebSockets)
            engine {
                https {
                    trustManager = getTrustManager("certificate.pem", assetManager)
                }
            }
        }
        clientList["$host:$port"] = client
        return client
    }

    fun closeAllSockets(){
        clientList.forEach {
            it.value.close()
        }
    }

    suspend fun listen(host: String, port: Int= Constant.KITCHEN_PORT, path: String = "/ws") {
        val client = getClient(host, port)
        Timber.i("socket trying to listen to $host:$port")
        client.wss(HttpMethod.Get, host = host, port = port, path = path) {
            for (message in incoming) {
                Timber.i("get message $message")
                message as? Frame.Text ?: continue
                Timber.i("socket Received message: ${message.readText()}")
            }
        }
    }

    fun sendData(data: String,
                       host: String,
                       port: Int = Constant.KITCHEN_PORT,
                       path: String = "/ws",
                       onSent: () -> Unit = {}){
        GlobalScope.launch(Dispatchers.IO) {
            sendDataAndListen(data, host, port, path, onSent)
        }
    }

    suspend fun sendDataAndListen(
        data: String,
        host: String,
        port: Int = Constant.KITCHEN_PORT,
        path: String = "/ws",
        onSent: () -> Unit = {}
    ) {
        val client = getClient(host, port)
        Timber.i("socket trying to connect to $host:$port")
        client.wss(HttpMethod.Get, host = host, port = port, path = path) {
            Timber.i("socket connected to $host:$port")
            send(Frame.Text(data))
            Timber.i("socket sent message: $data")
            onSent.invoke()

            for (message in incoming) {
                Timber.i("get message $message")
                message as? Frame.Text ?: continue
                Timber.i("socket Received message: ${message.readText()}")
            }
        }
//        client.close()
//        Timber.i("socket closed $host:$port")
    }
}