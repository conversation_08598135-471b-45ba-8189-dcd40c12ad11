package com.uniq.uniqpos.util.extensions

import android.graphics.Bitmap
import android.graphics.BitmapFactory
import com.uniq.uniqpos.util.safe
import timber.log.Timber
import java.io.ByteArrayInputStream
import java.io.ByteArrayOutputStream
import java.io.File

fun Bitmap.compressFile(targetSizeKb: Double = 500.0, externalDir: File? = null): Bitmap {
    val file = if (externalDir?.hasExtension().safe()) externalDir!! else  File(externalDir, "uniq_product_" + System.currentTimeMillis() + ".jpeg")
    Timber.i("compressFle file path: ${file.path}")
    val bitmapResult: Bitmap
    var quality = 80
    val byteArray = ByteArrayOutputStream()
    do {
        byteArray.reset()
        this.compress(Bitmap.CompressFormat.JPEG, quality, byteArray)
        file.writeBytes(byteArray.toByteArray())
        Timber.i("compresed file size: ${file.sizeInKb()} kb, target: $targetSizeKb kb (quality: $quality)")

        quality -= 10
    }while(file.sizeInKb() >= targetSizeKb && quality > 30)

    Timber.i("file path: ${file.path}")
    bitmapResult = BitmapFactory.decodeStream(ByteArrayInputStream(byteArray.toByteArray()))
    byteArray.close()

    file.delete()
    bitmapResult.compress(Bitmap.CompressFormat.JPEG, 100, file.outputStream())

    return bitmapResult
}

fun Bitmap.compressAsFile(targetSizeKb: Double = 500.0, externalDir: File? = null): File {
    val file = if (externalDir?.hasExtension().safe()) externalDir!! else  File(externalDir, "uniq_product_" + System.currentTimeMillis() + ".jpeg")
    Timber.i("compressFle file path: ${file.path}")
    var quality = 90
    val byteArray = ByteArrayOutputStream()
    do {
        byteArray.reset()
        this.compress(Bitmap.CompressFormat.JPEG, quality, byteArray)
        file.writeBytes(byteArray.toByteArray())
        Timber.i("compresed file size: ${file.sizeInKb()} kb, target: $targetSizeKb kb (quality: $quality)")

        quality -= 10
    }while(file.sizeInKb() >= targetSizeKb && quality > 30)

    Timber.i("file path: ${file.path}")
    return file
}