package com.uniq.uniqpos.util.receipt;

import android.util.Log;

/**
 * Created by Git Solution on 23/10/2017.
 */

public class PrinterUtil {

    public final static byte LANGUAGE_ENGLISH = 2;
    public final static byte LANGUAGE_CHINESE = 48;

    // add decode and encode
    public final static byte B_SOF = (byte) 0xc0;
    public final static byte B_EOF = (byte) 0xc1;
    public final static byte B_TRANSFER = (byte) 0x7D;
    public final static byte B_XOR = (byte) 0x20;
    public final static byte B_ESC = (byte) 0x1B;

    // font
    public static final byte FONT_24PX = (byte)0x01;
    public static final byte FONT_32PX = (byte)0x00;

    //align
    public static final byte Align_LEFT = (byte)0x30;
    public static final byte Align_CENTER = (byte)0x31;
    public static final byte Align_RIGHT = (byte)0x32;

    // the frame type
    public static final byte FRAME_ACK = 0x06;
    public static final byte FRAME_NACK = 0x15;
    public static final byte FRAME_ENQ = 0x05;
    public static final byte FRAME_EOT = 0x04;
    public static final byte FRAME_ETX = 0x03;
    public static final byte FRAME_PHONE = 0x50;
    public static final byte FRAME_RSP = 0x52;
    public static final byte FRAME_TOF_PRINT = 0x44;
    public static final byte FRAME_TOF_PWD = 0x4D;
    public static final byte FRAME_TOF_PROCESSDATA = 0x50;
    public static final byte FRAME_MSR= 0x48;

    private static int dataID = 0;
    public static final int BYTES_OF_DATA_LENGTH = 4;

    public static byte[] printfont (String content,byte fonttype,byte fontalign,byte linespace,byte language){
        if (content != null && content.length() > 0) {

            byte[] temp = null;
            temp = convertPrintData(content, 0,content.length(), language, fonttype,fontalign,linespace);
            return temp;
        }else{
            return null;
        }
    }

    public static byte[] convertPrintData(String str, int offset, int length, byte languageSet, byte fontSet,byte align,byte linespace) {
        byte[] buffer = null;
        if (languageSet == LANGUAGE_CHINESE)
        {
            buffer = char2ByteUTF8(str, offset, length);
        } else {
            buffer = new byte[length];
            System.arraycopy(str.getBytes(), offset, buffer, 0, length);
        }

        byte[] lang = null;
//    if (languageSet != LANGUAGE_ENGLISH)
//    {
        lang = new byte[]{B_ESC, (byte)0x4B, (byte)0x31,B_ESC, (byte)0x52, languageSet};
//    }
        byte[] font = null;
        byte[] fontalign = null;
        byte[] fontlinespace = null;
        //  if (fontSet != FONT_DEFAULT)
        // {

        font = new byte[]{B_ESC, (byte)0x21, fontSet};
        fontalign = new byte[]{B_ESC,(byte)0x61,align};
        fontlinespace = new byte[]{B_ESC,(byte)0x33,linespace};

        //}
        byte[] formate = concatByteArray(lang, font, fontalign, fontlinespace);
        return concatByteArray(formate, buffer);
    }

    private static byte[] char2ByteUTF8(String input, int offset, int length)
    {
        byte[] output = new byte[length * 3];

        int i = offset;
        int inEnd = offset + length;
        int outLength = 0;

        char inputChar;

        while (i < inEnd) {
            inputChar = input.charAt(i);
            if (inputChar < 0x80) {
                output[outLength++] = (byte) inputChar;
            } else if (inputChar < 0x800) {
                output[outLength++] = (byte) (0xc0 | ((inputChar >> 6) & 0x1f));
                output[outLength++] = (byte) (0x80 | (inputChar & 0x3f));
            } else {
                output[outLength++] = (byte) (0xe0 | ((inputChar >> 12)) & 0x0f);
                output[outLength++] = (byte) (0x80 | ((inputChar >> 6) & 0x3f));
                output[outLength++] = (byte) (0x80 | (inputChar & 0x3f));
            }
            i ++;
        }
        byte[] ret=new byte[outLength];
        System.arraycopy(output, 0, ret, 0, outLength);
        return ret;
    }

    private static byte[] concatByteArray(byte[] a, byte[] b)
    {
        if (a == null && b == null )
            return null;

        int aL = (a == null?0:a.length);
        int bL = (b ==null?0:b.length);

        if (bL == 0)
            return a;
        int len = aL + bL ;
        byte[] result = new byte[len];

        if (a != null)
            System.arraycopy(a, 0, result, 0, aL);
        if (b != null)
            System.arraycopy(b, 0, result, aL, bL);

        return result;
    }

    private static byte[] concatByteArray(byte[] a, byte[] b,byte c[], byte[] d)
    {

        if (a == null && b == null && c == null && d == null)
            return null;

        int aL = (a == null?0:a.length);
        int bL = (b ==null?0:b.length);
        int cL = (c == null?0:c.length);
        int dL = (d == null?0:d.length);

//    if (bL == 0)
//      return a;

        int len = aL + bL + cL + dL;
        byte[] result = new byte[len];

        Log.i("len",len+"");


        if (a != null)
            System.arraycopy(a, 0, result, 0, aL);
        if (b != null)
            System.arraycopy(b, 0, result, aL, bL);
        if(c != null)
            System.arraycopy(c, 0, result, aL+bL, cL);
        if(d != null)
            System.arraycopy(d, 0, result, aL+bL+cL, dL);


        return result;
    }

    public static byte[] FramePack(byte typeFrame, byte[] srcBuff, int srcOffset, int srcLen)
    {
        if (typeFrame == FRAME_NACK || typeFrame == FRAME_ACK
                || typeFrame == FRAME_ENQ || typeFrame == FRAME_EOT) {
            return new byte[]{B_SOF, B_SOF, B_SOF, typeFrame, B_EOF, B_EOF, B_EOF};
        }
        if (typeFrame == FRAME_ETX) {
            return new byte[]{B_SOF, B_SOF, B_SOF, typeFrame, getDataID(), B_EOF, B_EOF, B_EOF};
        }
        if (typeFrame == FRAME_PHONE
                || typeFrame == FRAME_RSP || typeFrame == FRAME_TOF_PRINT || typeFrame == FRAME_TOF_PWD || typeFrame == FRAME_TOF_PROCESSDATA

                ) {
            if(srcOffset < 0 || srcLen < 1 || (srcOffset + srcLen) > srcBuff.length)
                return null;
            byte[] checkSum = new byte[2];
            int countSpecialBytes = calcCheckSumAndSpecialBytes(srcBuff, srcOffset, srcLen, checkSum, 0);
            //byte[] buffer = new byte[(1+1+1) + 1 + 1 + BYTES_OF_DATA_LENGTH + countSpecialBytes  + (1+1+1)];
            byte[] buffer = new byte[(1) + 1 + 1 + BYTES_OF_DATA_LENGTH + countSpecialBytes  + (1)]; //For P25
            // |C0H(SOF) | (TOF) | DATA_ID | DATA Length | DATA | CHECK_SUM |C1H(EOF)|
            int offset = 0;
            buffer[offset] = B_SOF;
            offset++;
//      buffer[offset] = B_SOF;
//      offset++;
//      buffer[offset] = B_SOF;
//      offset++;
            buffer[offset] = typeFrame;
            offset++;
            buffer[offset] = getDataID();
            offset++;
            intToAscii(srcLen, buffer, offset, BYTES_OF_DATA_LENGTH);
            offset += BYTES_OF_DATA_LENGTH;

            offset = convertSpecialBytes(srcBuff, srcOffset, srcLen, buffer, offset);

            offset = convertSpecialBytes(checkSum, 0, checkSum.length, buffer, offset);

//      buffer[offset] = B_EOF;
//      offset++;
//      buffer[offset] = B_EOF;
//      offset++;
            buffer[offset] = B_EOF;
            offset++;
            return buffer;
        }
        return null;
    }

    private static byte getDataID()
    {
        dataID++;
        if (dataID >= 10)
            dataID = 0;
        return (byte)('0' + dataID);
    }

    private static int calcCheckSumAndSpecialBytes(byte[] buf, int offset, int len, byte[] checkSum, int checkSumOffset)
    {
        byte even = 0x00;
        byte odd = 0x00;
        boolean isEven = true;
        int cnt = 0;
        int length = len + offset;
        for (int i = offset; i < length; i++, cnt++)
        {
            if (isEven) {
                even = (byte) (even ^ buf[offset + i]);
            } else {
                odd = (byte) (odd ^ buf[offset + i]);
            }
            isEven = !isEven;
            if (buf[i] == B_SOF || buf[i] == B_EOF || buf[i] == B_TRANSFER)
            {
                cnt++;
            }
        }
        if (even == B_SOF || even == B_EOF || even == B_TRANSFER)
            cnt++;
        if (odd == B_SOF || odd == B_EOF || odd == B_TRANSFER)
            cnt++;
        cnt += 2;
        checkSum[checkSumOffset] = even;
        checkSumOffset++;
        checkSum[checkSumOffset] = odd;
        return cnt;
    }

    private static int convertSpecialBytes(byte[] srcBuff, int srcOffset, int srcLen, byte[] destBuff, int destOffset)
    {
        for (int j = srcOffset; j < srcLen; j++, destOffset++)
        {
            if (srcBuff[j] == B_SOF || srcBuff[j] == B_EOF || srcBuff[j] == B_TRANSFER)
            {
                destBuff[destOffset] = B_TRANSFER;
                destOffset++;
                destBuff[destOffset] = (byte) ((srcBuff[j] ^ B_XOR) & 0xff);
            } else {
                destBuff[destOffset] = srcBuff[j];
            }
        }
        return destOffset;
    }

    private static int intToAscii(long number, byte[] buf, int offset, int length)
    {
        if (buf == null || offset < 0 || length < 1 || buf.length < (offset + length))
            return -1;
        length = offset + length;
        int charPos = length -1;
        int index = 0;

        if (number > 0) {
            number = -number;
        }
        for(; charPos >= offset; charPos--){
            if (0 != number){
                index = (int)(-(number % 10));
                number = number / 10;
            } else {
                index = 0;
            }
            buf[charPos] = (byte)('0' + index);
        }

        return charPos;
    }
}
