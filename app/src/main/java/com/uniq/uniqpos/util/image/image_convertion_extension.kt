package com.uniq.uniqpos.util.image

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.net.Uri
import android.os.Environment
import android.util.Base64
import com.bumptech.glide.Glide
import com.uniq.uniqpos.util.BACKGROUND
import java.io.ByteArrayOutputStream
import java.io.File
import kotlin.coroutines.resume
import kotlin.coroutines.suspendCoroutine

fun Bitmap.toBytes(): ByteArray {
    val stream = ByteArrayOutputStream()
    this.compress(Bitmap.CompressFormat.PNG, 100, stream)
    return stream.toByteArray()
}

fun ByteArray.toBitmap(): Bitmap {
    return BitmapFactory.decodeByteArray(this, 0, this.size)
}

//bitmap to base64
fun Bitmap.toBase64(): String {
    return Base64.encodeToString(this.toBytes(), Base64.DEFAULT)
}

