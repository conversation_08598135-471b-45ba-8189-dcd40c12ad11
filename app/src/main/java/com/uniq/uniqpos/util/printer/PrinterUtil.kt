package com.uniq.uniqpos.util.printer

import android.graphics.Bitmap
import android.graphics.BitmapFactory
import com.uniq.uniqpos.util.PrintImage
import timber.log.Timber
import java.io.File

val imageByteCache = HashMap<String, ByteArray>()
val imageByteResizedCache = HashMap<String, ByteArray>()

fun File.toImageByte(): ByteArray? {
    // Check if file exists, return null if it doesn't
    if (!exists()) {
        Timber.i("logo file not exists, path: $absolutePath")
        return null
    }

    val cache = imageByteCache[this.absolutePath]
    if(cache != null){
        Timber.i("logo use byte from cache, path: $absolutePath")
        return cache
    }

    // 1. Convert file to Bitmap
    val logoBitmap = BitmapFactory.decodeFile(this.absolutePath)
    
    // 2. Resize the bitmap and create PrintImage instance
    val printImg = PrintImage(PrintImage.getResizedBitmap(logoBitmap))
    
    // 3. Convert to black & white using <PERSON><PERSON><PERSON><PERSON> dithering
    printImg.prepareImage(PrintImage.dither.floyd_steinberg, 128)
    
    // 4. Get the final byte array ready for printer
    val byteImg = printImg.printImageData
    
    Timber.i(">>> byteIMG '${absolutePath}' : $byteImg")
    imageByteCache[this.absolutePath] = byteImg
    return byteImg
}

fun File.toImageByteResized(): ByteArray? {
    val cache = imageByteResizedCache[this.absolutePath]
    if(cache != null){
        Timber.i("toImageByteResized, logo use byte from cache, path: $absolutePath")
        return cache
    }
    val processLog = StringBuilder()
    try {
        processLog.append("decode file -> ")
        val logo: Bitmap? = try {
            BitmapFactory.decodeFile(this.path)
        } catch (e: Exception) {
            Timber.i("decode file with first way error : $e")
            BitmapFactory.decodeFile(this.absolutePath)
        }

        logo?.let {
            processLog.append("get print image -> ")
            val printImg = PrintImage(PrintImage.getResizedBitmap(logo))
            processLog.append("prepare image -> ")
            printImg.prepareImage(PrintImage.dither.floyd_steinberg, 128)
            processLog.append("get image data")
            val result = printImg.printImageData
            Timber.i("get bytes success first way")
            imageByteResizedCache[this.absolutePath] = result
            return result
        } ?: run { Timber.i("logo bitmap is null") }
    } catch (e: Exception) {
        Timber.i("[GET BYTE FROM FILE] first way error : $e \nLog : $processLog")
    }
    return null;
}