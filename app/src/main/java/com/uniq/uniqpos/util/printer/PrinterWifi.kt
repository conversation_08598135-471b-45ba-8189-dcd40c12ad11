package com.uniq.uniqpos.util.printer

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import com.dantsu.escposprinter.EscPosPrinter
import com.dantsu.escposprinter.EscPosPrinterCommands
import com.dantsu.escposprinter.connection.tcp.TcpConnection
import com.dantsu.escposprinter.textparser.PrinterTextParserImg
import com.google.firebase.ktx.Firebase
import com.google.firebase.remoteconfig.ktx.remoteConfig
import com.uniq.uniqpos.BuildConfig
import com.uniq.uniqpos.R
import com.uniq.uniqpos.data.local.entity.PendingPrintEntity
import com.uniq.uniqpos.data.local.sharedpref.SharedPref
import com.uniq.uniqpos.data.local.sharedpref.getLocalDataBoolean
import com.uniq.uniqpos.data.local.sharedpref.sharedPref
import com.uniq.uniqpos.util.*
import com.uniq.uniqpos.util.receipt.PrintNotaUtil
import timber.log.Timber

/**
 * Created by annasblack<PERSON> on 02/02/18.
 */

class PrinterWifi(val context: Context)  {

    private var mContext: Context? = null
    var pendingPrint = ArrayList<PendingPrintEntity>()
    private var ipPrinter: String = ""
    private var printerName: String? = ""
    private var isDataSent = false
    private val printerGlobalUtil = PrintWifiCore()
    private lateinit var callback: (PrintResponse) -> Unit

    private val printerPort: Int get() = context.sharedPref().getString(SharedPref.PRINTER_PORT, Constant.PRINTER_PORT.toString()).safeToInt(Constant.PRINTER_PORT)
//    private val printerPort by lazy { context.sharedPref().getString(SharedPref.PRINTER_PORT, Constant.PRINTER_PORT.toString())?.toInt() ?: Constant.PRINTER_PORT }

    fun openCashDrawer(ipAddress: String, callback: (PrintResponse) -> Unit) {
        if (context.deactivatePrinterList().any { it == ipAddress }) {
            callback(
                PrintResponse(
                    false,
                    "Printer WiFi ($ipAddress) is deactivate at this device!"
                )
            )
        } else if (!context.role().bukalaciuang) {
            callback(PrintResponse(false, "User has not permission to open cash drawer"))
        } else {
            Timber.i("Opening cashdrawer to $ipAddress")
            printWifi(ipAddress, "", isOpenCashDrawer = true, callback = callback)
        }
    }

    fun printWifi(
        ipAddres: String,
        data: String,
        id: Long = 0,
        printerName: String? = "",
        isOpenCashDrawer: Boolean = false,
        callback: (PrintResponse) -> Unit
    ): Boolean {
        mContext = context
        ipPrinter = ipAddres
        this.callback = callback
        this.printerName = printerName
        var isEpsonPrinter = false

        Timber.i(">>> Printing to $ipAddres")
        if (ipAddres.startsWith("TCP")) {
            ipPrinter = ipAddres.replace("TCP:", "")
        }

//        val useV2 = BuildConfig.DEBUG || mContext?.outlet()?.experimentConfig()?.printer.safe()
//        val useV2 = Firebase.remoteConfig.getBoolean("print_wifi")
        if (useV2()){
            if(printV2(ipPrinter, data, isOpenCashDrawer, id)){
                return true
            }
            Timber.i("print wifi use V2 failed, try to use old way...")
        }
        printUsingGenericWay(ipPrinter, data, isOpenCashDrawer, id)
        return true
    }

    private fun printV2(
        ipPrinter: String,
        data: String,
        isOpenCashDrawer: Boolean,
        id: Long
    ): Boolean {
        val logs = ArrayList<String>()
        logs.add("ip: $ipPrinter")

        val paperSize = PrintNotaUtil.determinePaperSize(data)
        Timber.i("print using v2.... $paperSize")

        try {
            logs.add("opening port $printerPort")

            //ports: 9100, 9300
            val escPrinter = EscPosPrinter(TcpConnection(ipPrinter, printerPort    , 35), 203, paperSize.toFloat(), 32)

            if (isOpenCashDrawer && context.role().bukalaciuang) {
                try {
                    logs.add("opening cash drawer")
//                    printerGlobalUtil.openport(ipPrinter, 9100)
//                    printerGlobalUtil.sendCommand(PrinterCommand.openCashDrawer)
//                    printerGlobalUtil.closeport()
                    escPrinter.printFormattedText("${PrinterCommand.openCashDrawer}")
                } catch (e: Exception) {
                    Timber.i("Open cash drawer from printer $ipPrinter error - $e")
                }
            }

            var logoBmp: Bitmap? = null
            try {
                mContext?.takeIf { data.isNotBlank() }?.receiptLogoFile()?.let { file ->
                    if (file.exists()) {
                        logoBmp = BitmapFactory.decodeFile(file.path)
                        Timber.i(">> Image is exist | Logo : $logoBmp")
                    }
                }
            } catch (e: Exception) {
                Timber.i("Getting image logo to print error : $e")
            }

            val textSplit = data.split(Constant.PRINTER_CODE_LOGO)
            val textToPrint = StringBuilder()
            textSplit.map {
                it.replace(Constant.PRINTER_CODE_CUT, ".[L]\n[L]\n[L]\n${PrinterCommand.COMMAND_CUT}")
                .replace(Regex("${Constant.PRINTER_CODE_QR}(.*?)${Constant.PRINTER_CODE_QR}"), "<qrcode size='30'>$1</qrcode>\n")
            }.forEachIndexed { index, text ->
                if (index > 0) {
                    logoBmp?.let { bmp ->
                        textToPrint.append("[C]<img>${PrinterTextParserImg.bitmapToHexadecimalString(escPrinter, bmp)}</img>\n")
                        textToPrint.append("[L]\n")
                    }
                }
                logs.add("printing")
                textToPrint.append(text)
//                text.split("\n").forEach { Timber.i("-> $it") }
                textToPrint.append("[L]\n")
            }
            escPrinter.printFormattedText(textToPrint.toString())
            escPrinter.disconnectPrinter()
            removePendingPrint(id)
            callback(PrintResponse(true))
            return true
        } catch (e: Exception) {
            Timber.i("Connecting to printer $ipPrinter:$printerPort failed. $e | logs: ${logs.joinToString()}")
            callback(PrintResponse(false, e.message ?: ""))
        }
        return false
    }

    private fun printUsingGenericWay(
        ipPrinter: String,
        data: String,
        isOpenCashDrawer: Boolean,
        id: Long
    ) {
//        val GS = Character.toString(29.toChar())
//        val ESC = Character.toString(27.toChar())
//        val COMMAND_CUT = "$ESC@" + GS + "V" + 1.toChar()
        val logs = ArrayList<String>()
        logs.add("ip: $ipPrinter")

        try {
            logs.add("opening port: $printerPort")
            printerGlobalUtil.openport(ipPrinter, printerPort)
//                printerGlobalUtil.setup(80,50, 4, 4, 0, 0, 0)
//                printerGlobalUtil.clearbuffer()

            if (isOpenCashDrawer && context.role().bukalaciuang) {
                try {
                    logs.add("opening cash drawer")
                    printerGlobalUtil.sendCommand(PrinterCommand.openCashDrawer)
                } catch (e: Exception) {
                    Timber.i("Open cash drawer from printer $ipPrinter error - $e")
                }
            }

            //in case no data to print, example for opening cash drawer ony
            if(data.isBlank()){
                printerGlobalUtil.closeport()
                callback(PrintResponse(true))
                return
            }

//           val byteHeader = PrinterUtil.printfont("",  PrinterUtil.FONT_24PX, PrinterUtil.Align_CENTER, 0x1A.toByte(), PrinterUtil.LANGUAGE_ENGLISH)

            var outletLogo: ByteArray? = null
            try {
                mContext?.takeIf { data.isNotBlank() && data.contains(Constant.PRINTER_CODE_LOGO) }?.receiptLogoFile()?.let { file ->
                    outletLogo = file.toImageByteResized()
                }
            } catch (e: Exception) {
                Timber.i("Getting image logo to print error : $e")
            }

            logs.add("set align center")

            printerGlobalUtil.sendCommand(EscPosPrinterCommands.RESET_PRINTER)
            printerGlobalUtil.sendCommand(EscPosPrinterCommands.TEXT_ALIGN_CENTER)

            val textSplit = data.split(Constant.PRINTER_CODE_LOGO)
            textSplit.takeIf { data.isNotBlank() }?.forEachIndexed { index, text ->
                if (index > 0) {
                    outletLogo?.let {
                        logs.add("print logo")
                        printerGlobalUtil.sendCommand(EscPosPrinterCommands.TEXT_ALIGN_CENTER)
                        printerGlobalUtil.sendCommand(outletLogo)
                    }
                }
                logs.add("printing")
                printerGlobalUtil.sendCommand(text.replace(Constant.PRINTER_CODE_CUT, "\n\n${PrinterCommand.COMMAND_CUT}"))
            }

            if (data.isNotEmpty() && !data.trim().endsWith(Constant.PRINTER_CODE_CUT)) {
                Timber.i("print data not ended with cut command, add cut..")
//                printerGlobalUtil.sendCommand(COMMAND_CUT)
            }

            logs.add("close port")
            printerGlobalUtil.closeport()
            removePendingPrint(id)
            callback(PrintResponse(true))
        } catch (e: Exception) {
            Timber.i("Connecting to printer $ipPrinter failed. $e | logs: ${logs.joinToString()}")
            callback(PrintResponse(false, e.message ?: ""))
        }
    }

    fun removePendingPrint(id: Long) {
        Timber.i("Will remove ID : $id")
        pendingPrint.filter { it.id == id }
            .forEach { pendingPrint.remove(it) }
    }

    fun printTestWifi(
        context: Context,
        ipAddress: String,
        callback: (PrintResponse) -> Unit
    ): Boolean {
        mContext = context
        ipPrinter = ipAddress
        this.callback = callback

        if (ipAddress.startsWith("TCP")) {
            ipPrinter = ipAddress.replace("TCP:", "")
        }

        val textToPrint = "### UNIQ P.O.S PRINT TEST ###\n" +
                "APP VERSION     : ${BuildConfig.VERSION_NAME}\n" +
                "PRINTER ADDRESS : $ipAddress\n" +
                "_________ www.uniq.id __________\n\n"

//        val useV2 = Firebase.remoteConfig.getBoolean("print_wifi")
        if (useV2()) {
            Timber.i("printed test using v2")
            return printV2(ipPrinter, textToPrint, false, 0)
        }

        Timber.i("IP Printer : $ipPrinter")
        try {
            printerGlobalUtil.openport(ipPrinter, printerPort)
//            if (!printerGlobalUtil.isSocketInitialized) {
                //socket is failed to connect with given ip and port
//            }
            Timber.i("printerGlobalUtil.isSocketInitialized : ${printerGlobalUtil.isSocketInitialized}")
            //                printerGlobalUtil.setup(80,50, 4, 4, 0, 0, 0)
            //                printerGlobalUtil.clearbuffer()
            //                data.forEach {
            //                    printerGlobalUtil.sendcommand(it.replace(Constant.PRINTER_CODE_CUT, COMMAND))
            //                    if(!it.endsWith(Constant.PRINTER_CODE_CUT)) printerGlobalUtil.sendcommand(COMMAND)
            //
            //                }

            val byteLogo = context.receiptLogoFile()?.toImageByteResized() ?: BitmapUtils.decodeBitmap( BitmapFactory.decodeResource(context.resources, R.drawable.logo_black_small))

            printerGlobalUtil.sendCommand(EscPosPrinterCommands.RESET_PRINTER)
            printerGlobalUtil.sendCommand(EscPosPrinterCommands.TEXT_ALIGN_CENTER)
            printerGlobalUtil.sendCommand(byteLogo)
            printerGlobalUtil.sendCommand(textToPrint)
            printerGlobalUtil.closeport()
        } catch (e: Exception) {
            Timber.i("Print Error - $e")
            if (e is java.lang.NullPointerException) {
                callback(PrintResponse(false, "device is not printable"))
            } else if(e is android.os.NetworkOnMainThreadException){
                callback(PrintResponse(false, "can not print on main thread"))
            }  else {
                callback(PrintResponse(false, e.message ?: "Error unknown"))
            }
            return false
        }
        callback(PrintResponse(true, "success"))
        return true
    }

    interface PrinterWifiListener {
        fun onFinish(status: Boolean)
    }

    private fun useV2(): Boolean{
        val useV2Config =  context.getLocalDataBoolean(SharedPref.PRINT_WIFI_V2)
        val useV2Firebase =  Firebase.remoteConfig.getBoolean("print_wifi")
        Timber.i("useV2Config: $useV2Config | useV2Firebase: $useV2Firebase")
        return useV2Config || useV2Firebase
//        return false
    }

}
