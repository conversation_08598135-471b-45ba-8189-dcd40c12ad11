package com.uniq.uniqpos.util.networking

import android.content.res.AssetManager
import com.uniq.uniqpos.util.Constant
import io.ktor.client.HttpClient
import io.ktor.client.engine.cio.CIO
import io.ktor.client.features.websocket.DefaultClientWebSocketSession
import io.ktor.client.features.websocket.WebSockets
import io.ktor.client.features.websocket.webSocketSession
import io.ktor.http.cio.websocket.Frame
import io.ktor.http.cio.websocket.readText
import timber.log.Timber

class WebSocketServer {
    private var socket: DefaultClientWebSocketSession? = null
    suspend fun run(assetManager: AssetManager){
        val client = HttpClient(CIO) {
            install(WebSockets)
            engine {
                https {
                    trustManager = getTrustManager("certificate.pem", assetManager)
                }
            }
        }

        runCatching {
            socket = client.webSocketSession(port = Constant.KITCHEN_PORT)
            while (true) {
                val frame = socket?.incoming?.receive() as? Frame.Text
                val text = frame?.readText() ?: "No message"
                Timber.i("Received message: $text")
            }
        }.onFailure { error ->
           Timber.i("Error websocket server: $error")
        }
    }

    suspend fun close() {
//        socket?.close(CloseReason(CloseReason.Codes.NORMAL, "Client shutting down."))
//        socket = null
    }
}