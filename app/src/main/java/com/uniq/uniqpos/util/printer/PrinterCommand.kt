package com.uniq.uniqpos.util.printer

/**
 * Created by an<PERSON><PERSON><PERSON><PERSON> on 23/12/18
 */
class PrinterCommand {
    //ref : http://www.beaglehardware.com/howtoprogramcashdrawer.html
    //ref: https://github.com/stefanosbou/esc-pos-java/blob/master/src/main/java/io/github/escposjava/print/Commands.java
    companion object {
        val cutPaper = byteArrayOf(0x1D, 0x56, 0x41, 0x10)
        val openCashDrawer =  (27.toChar().toString() + 112.toChar() + 0.toChar() + 25.toChar() + 250.toChar()).toByteArray()
        val ALIGN_CENTER =  byteArrayOf(0x1b, 0x61, 0x01)

        val GS = Character.toString(29.toChar())
        val ESC = Character.toString(27.toChar())
        val COMMAND_CUT = "$ESC@" + GS + "V" + 1.toChar()

        // Cash Drawer
        val CD_KICK_2 = byteArrayOf(0x1b, 0x70, 0x00) // Sends a pulse to pin 2 []
        val CD_KICK_5 = byteArrayOf(0x1b, 0x70, 0x01) // Sends a pulse to pin 5 []

        val OPEN_DRAWER_1 = "$ESC@${GS}p030"
        val OPEN_DRAWER_2 = "$ESC@${GS}p130"

        const val LF = 0x0A
    }
}