package com.uniq.uniqpos.util

import android.content.Context
import android.content.ContextWrapper
import android.graphics.Color
import android.os.Build
import android.os.Bundle
import android.os.Environment
import android.text.Editable
import android.text.TextWatcher
import android.view.Gravity
import android.widget.Button
import androidx.core.content.ContextCompat
import com.bugsnag.android.Bugsnag
import com.google.android.material.textfield.TextInputEditText
import com.uniq.uniqpos.BuildConfig
import com.uniq.uniqpos.R
import com.uniq.uniqpos.app.UniqApplication
import com.uniq.uniqpos.data.local.sharedpref.*
import com.uniq.uniqpos.util.security.Aes256Legacy
import timber.log.Timber
import java.io.*
import java.net.NetworkInterface
import java.text.NumberFormat
import java.text.SimpleDateFormat
import java.util.*
import kotlin.concurrent.thread
import kotlin.random.Random

/**
 * Created by annas<PERSON><PERSON>hat on 13/02/18.
 */

fun GetIpAddress(): String {
    var ip = "not found"
    try {
        val enumNetworkInterface = NetworkInterface.getNetworkInterfaces()
        while (enumNetworkInterface.hasMoreElements()) {
            val networkInterface = enumNetworkInterface.nextElement()
            val enumInetAddress = networkInterface.inetAddresses
            while (enumInetAddress.hasMoreElements()) {
                val inetAddress = enumInetAddress.nextElement()

                if (inetAddress.isSiteLocalAddress) {
                    ip = inetAddress.hostAddress
                }
            }
        }
    } catch (e: Exception) {
        Timber.i("Get IP Address Failed. $e")
    }
    return ip
}

fun generateDisplayNota(adminId: Int?, outletId: Int?, salesCount: Int): String {
    val nota = StringBuilder()
    try {
        val id = (outletId ?: 0) + (adminId ?: 1)
        val words = listOf("M", "N", "K", "O", "H", "S", "N", "I", "A", "L")
        id.toString().split("").forEach { if (it.isNumeric()) nota.append(words[it.toInt()]) }

        val cal = Calendar.getInstance()
        nota.append(cal.get(Calendar.YEAR))
        nota.append(String.format("%02d", cal.get(Calendar.MONTH) + 1))
        nota.append(String.format("%02d", cal.get(Calendar.DAY_OF_MONTH)))
        nota.append(salesCount + 1)
    } catch (e: Exception) {
        Timber.i("Generate Display Nota error. $e")
    }

    return nota.toString()
}

fun appendLog(text: String, type: String, context: Context?) {
//    if (BuildConfig.FLAVOR === "production" && !BuildConfig.DEBUG) return

//    if (BuildConfig.DEBUG) {
//        var prefix = ""
//        val key = Date().dateFormat("ddMMyyyyHH")
//        val log = context?.getLocalDataString(key, default = null)
//        if (log == null) {
//            val keys = context?.getLocalDataString(SharedPref.LOG_KEYS, "").safe()
//            context?.putData(SharedPref.LOG_KEYS, "${keys}$key,")
//            prefix = "version: 1\n"
//        }
//        context?.putData(key, "$prefix${log}\n${text}")
//        return
//    }

    if (!Utils.isExternalStorageWritable()) {
        warnAboutLog("external storage not writable", context)
        return
    }
    if (!Utils.isExternalStorageReadable()) {
        warnAboutLog("external storage not readable", context)
        return
    }

    getLogPath(type, context)?.let { logFile ->
        var prefix = ""
        if (!logFile.exists()) {
            prefix = "version: 1"
            try {
                Bugsnag.leaveBreadcrumb("create new log file")
                logFile.createNewFile()
            } catch (e: IOException) {
                Timber.d("Create log file '${logFile.path}', error. $e")
//                Bugsnag.notify(e)
            }
        }
        try {
//            Bugsnag.leaveBreadcrumb("append log file")
            val log = Aes256Legacy().encrypt("${Date().dateTimeFormat("dd-MM-yyyy HH:mm:ss")} $text", "sERycGn9k3bRSmHnq7bgJwJ59KVpcL6G")

//            val buf = BufferedWriter(FileWriter(logFile, true))
//            buf.append("$prefix\n" + log)
//            buf.newLine()
//            buf.close()

            val fw = FileWriter(logFile, true)
            fw.write("$prefix\n" + log)
            fw.close()
        } catch (e: Exception) {
            Timber.d("Write log file error. $e")
//            Bugsnag.notify(e)
        }
    }
}

private fun warnAboutLog(warning: String, context: Context?) {
    context?.apply {
        if (!getLocalDataBoolean("warn_log")) {
//            Bugsnag.notify(Exception(warning))
        }
    }
    Timber.i(warning)
}

private fun getLogPath(type: String, context: Context?): File? {
    return if (type == "logcat" && BuildConfig.FLAVOR != "development") {
        context?.let {
            val folderParent = File(context.filesDir, "logcat")
            if (!folderParent.exists()) {
                folderParent.mkdirs()
            }
            File(folderParent, Date().dateFormat("yyyy_MM_dd HH") + ".txt")
        }
    } else if (BuildConfig.FLAVOR !== "production" || BuildConfig.DEBUG) {
//        val externalStorageDir = Environment.getExternalStorageDirectory()
        val externalStorageDir = context?.getExternalFilesDir(null)
        val parent = externalStorageDir.toString() + "/UNIQ-LOG/" + Date().dateFormat("yyyy_MM_dd")
        val date = Date().dateFormat("MMdd")
        val cal = Calendar.getInstance()
        if (!File(parent).exists()) {
            File(parent).mkdirs()
        }
        File(parent, type + "_" + date + (if (type === "starting") "" else "_" + cal.get(Calendar.HOUR_OF_DAY)) + ".txt")
    } else null
}

class Utility {
    companion object {
        fun ping(host: String): String {
            try {
                val process = executeCmd("ping -c 1 -w 5 $host", false)
                val stdInput = BufferedReader(InputStreamReader(process?.inputStream))
                val stdError = BufferedReader(InputStreamReader(process?.errorStream))

                val mExitValue = process?.waitFor()
                var output = ""
                var s: String? = ""

                if (mExitValue != 0) {
                    if (mExitValue != 1) {
                        while (true) {
                            s = stdError.readLine()
                            if (s == null) {
                                break
                            }
                            output += s
                        }
                    } else {
                        output = "Request Time Out!"
                    }
                } else {
                    var i = 0
                    while (true) {
                        s = stdInput.readLine()
                        if (s == null) {
                            break
                        } else if (i == 1) {
                            break
                        } else {
                            i++
                        }
                    }
                    output += s
                }
                process?.destroy()
                return output
            } catch (e: Exception) {
                println(">>> ERROR - $e")
                return ">>> ERROR - $e"
            }
        }

        fun executeCmd(cmd: String, sudo: Boolean): Process? {
            if (sudo) {
                return Runtime.getRuntime().exec(arrayOf("su", "-c", cmd))
            }
            return try {
                Runtime.getRuntime().exec(cmd)
            } catch (e: Exception) {
                e.printStackTrace()
                null
            }

        }
    }
}

fun getDeviceName(): String {
    if (Build.MANUFACTURER.equals("To Be Filled By O.E.M.", ignoreCase = true)) {
        return Build.BRAND
    }

    if (Build.MANUFACTURER.equals(Build.MODEL, ignoreCase = true)) {
        return Build.MANUFACTURER.upperCaseWord()
    }

    return "${Build.MANUFACTURER.upperCaseWord()} ${Build.MODEL}"
}

fun changeSwitchUI(btnLeft: Button, btnRight: Button, enable: Int, context: Context) {
    if (enable == Gravity.RIGHT) {
        btnRight.background = ContextCompat.getDrawable(context, R.drawable.backg_round_right)
        btnLeft.background = ContextCompat.getDrawable(context, R.drawable.backg_round_left_diselect)
        btnRight.setTextColor(Color.WHITE)
        btnLeft.setTextColor(Color.parseColor("#96999F"))
    } else {
        btnLeft.background = ContextCompat.getDrawable(context, R.drawable.backg_round_left)
        btnRight.background = ContextCompat.getDrawable(context, R.drawable.backg_round_right_diselect)
        btnLeft.setTextColor(Color.WHITE)
        btnRight.setTextColor(Color.parseColor("#96999F"))
    }
}

fun bundleOf(vararg pairs: Pair<String, String?>): Bundle {
    val bundle = Bundle()
    for ((k, v) in pairs) {
        bundle.putString(k, v)
    }
    return bundle
}

fun randomLong(): Long {
    return System.currentTimeMillis() + Random.nextInt(0, 1000)
}

fun registerToCurrency(vararg editTexts: TextInputEditText) {
    for (editText in editTexts) {
        editText.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence, i: Int, i1: Int, i2: Int) {}
            override fun onTextChanged(s: CharSequence, i: Int, i1: Int, i2: Int) {
                if (editText.text.toString().trim { it <= ' ' }.isNotEmpty()) {
                    editText.removeTextChangedListener(this)
                    try {
                        var strNumber = s.toString().replace(",", "")
                        strNumber = strNumber.replace(".", "")
                        val number = strNumber.toInt()
                        val currency = NumberFormat.getInstance().format(number.toLong())
                        editText.setText(currency)
                        editText.setSelection(currency.length)
                    } catch (e: java.lang.Exception) {
                    }
                    editText.addTextChangedListener(this)
                }
            }
            override fun afterTextChanged(editable: Editable) {}
        })
    }
}