package com.uniq.uniqpos.util.view

import androidx.recyclerview.widget.ItemTouchHelper
import androidx.recyclerview.widget.RecyclerView

class SwipeController(private val swipeListener: SwipeListener) : ItemTouchHelper.Callback() {
    override fun getMovementFlags(recyclerView: RecyclerView, viewHolder: RecyclerView.ViewHolder): Int {
        return if (swipeListener.enableSwipe(viewHolder.adapterPosition)) {
            makeMovementFlags(0, ItemTouchHelper.RIGHT)
        } else {
            makeMovementFlags(0, 0)
        }
    }

    override fun onMove(recyclerView: RecyclerView, viewHolder: RecyclerView.ViewHolder, target: RecyclerView.ViewHolder): Boolean {
        return false
    }

    override fun onSwiped(viewHolder: RecyclerView.ViewHolder, direction: Int) {
        swipeListener.onSwiped(viewHolder.adapterPosition)
    }

    interface SwipeListener {
        fun enableSwipe(position: Int): Boolean
        fun onSwiped(position: Int)
    }
}