package com.uniq.uniqpos.util.printer


import android.content.Context
import com.google.firebase.analytics.ktx.analytics
import com.google.firebase.ktx.Firebase
import com.google.gson.Gson
import com.uniq.uniqpos.data.local.dao.SalesDao
import com.uniq.uniqpos.data.local.entity.PendingPrintEntity
import com.uniq.uniqpos.data.local.entity.PrinterEntity
import com.uniq.uniqpos.data.local.entity.TmpSalesEntity
import com.uniq.uniqpos.data.local.sharedpref.SharedPref
import com.uniq.uniqpos.data.local.sharedpref.getLocalDataBoolean
import com.uniq.uniqpos.data.local.sharedpref.getLocalDataString
import com.uniq.uniqpos.model.SocketMessage
import com.uniq.uniqpos.util.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import timber.log.Timber

/**
 * Created by ANNASBlackHat on 19/10/2017.
 */

class PrinterManager(private val app: Context, val salesDao: SalesDao) {

    var isPrintingWifi = false
        private set

    var isPrintingBT = false
        private set

    var isPrintingUSB = false
        private set

    private val printerWifi = PrinterWifi(app)
    private val printerBluetooth = PrinterBluetooth(app)
    private val printerUsb = PrinterUSB(app)
    private val pendingPrintBT = ArrayList<PendingPrintEntity>()
    private val pendingPrintUSB = ArrayList<PendingPrintEntity>()

    fun interface PrintListener {
        fun onPrintFinish()
    }

    fun isDeviceAsClient(): Boolean {
        val ipServer = app.getLocalDataString(SharedPref.IP_SERVER, "")
        val isServer = app.getLocalDataBoolean(SharedPref.PRINTER_SERVER_STATUS)
        return ipServer.isNotBlank() && !isServer
    }

    fun managePrint(
        list: List<PendingPrintEntity>,
        printerSocket: PrinterSocket,
        listener: PrintListener? = null
    ) {
        if (list.isEmpty()) {
            listener?.onPrintFinish()
            return
        }

        //if current device is a client, then send to device server
//        val ipServer = app.getLocalDataString(SharedPref.IP_SERVER, "")
//        if (ipServer.isNotBlank()) {
//            val isServer = app.getLocalDataBoolean(SharedPref.PRINTER_SERVER_STATUS)
//            if (!isServer) {
//                val req = Gson().toJson(SocketMessage("print_list", Gson().toJson(list.filter { it.dataString?.length.safe() > 10 }), getDeviceName()))
//                printerSocket.sendToServer(req) { isConnected, _ ->
//                    if (isConnected) removeFromPendingPrint(list)
//                    else saveToPendingPrint(list)
//                    listener?.onPrintFinish()
//                }
//                return
//            }
//        }

        if (isDeviceAsClient()) {
            val req = Gson().toJson(
                SocketMessage(
                    "print_list",
                    Gson().toJson(list.filter { it.dataString?.length.safe() > 10 }),
                    getDeviceName()
                )
            )
            printerSocket.sendToServer(req) { isConnected, _ ->
                if (isConnected) removeFromPendingPrint(list)
                else {
                    app.toast("failed to connect to server...", level = Level.ERROR)
                    saveToPendingPrint(list)
                }
                listener?.onPrintFinish()
            }
            return
        }

        addAllToQueue(list)

        //wait until finish if listener provided
        listener?.apply {
            GlobalScope.launch {
                do {
                    delay(500)
                } while (!isPrinted(list.map { it.id }))
                onPrintFinish()
            }
        }
    }

    fun openCashDrawer(printers: List<PrinterEntity>, callback: (PrintResponse) -> Unit) {
        Timber.i("Open cash drawer... printer size ${printers.size}")
        printers.forEach { printer ->
            Timber.i("open cash drawer to ${printer.address} (${printer.name})")
            if (printer.type == Constant.PRINTER_TYPE_WIFI) {
                if (!isPrintingWifi){
                    printerWifi.openCashDrawer(printer.address, callback)
                }
                else callback(
                    PrintResponse(
                        false,
                        "Printer WiFi '${printer.address}' is on printing..."
                    )
                )
            } else if (printer.type == Constant.PRINTER_TYPE_USB) {
                if (!isPrintingUSB) printerUsb.openCashDrawer(printer.address, callback)
                else callback(
                    PrintResponse(
                        false,
                        "Printer USB '${printer.address}' is on printing..."
                    )
                )
            } else if (printer.type == Constant.PRINTER_TYPE_BT) {
                //for temporary: opening cash-drawer for BT is disable, still caused bugs
                if (!isPrintingBT) {
                    printerBluetooth.openCashDrawer(printer.address) { response ->
//                            printerBt.disconnect()
                        callback(response)
                    }
                } else callback(
                    PrintResponse(
                        false,
                        "Printer BT '${printer.address}' is on printing..."
                    )
                )
            }
        }
            ?: kotlin.run {
                callback(
                    PrintResponse(
                        false,
                        "No Printer Available. Printer size ${printers.size}"
                    )
                )
            }
    }

    fun saveSalesCart(sales: TmpSalesEntity) {
        app.outlet()?.outletId?.let { outletId ->
            if (outletId == sales.outletId) {
                val salesCart = salesDao.getTmpSalesById(sales.noNota)
                if(salesCart?.status == "paid"){
                    Timber.i("it's already paid, skip...")
                    return@let
                }

                //first, check if sales already paid
                val salesSuccess = salesDao.getSalesById(sales.noNota)
                if (salesSuccess == null) {
                    salesDao.saveTmpSales(sales)
                } else {
                    Timber.i("<WARNING> ${sales.noNota} will not save to this db. This already paid")
                }
            } else {
                Timber.i("<WARNING> receive sales cart through socket in different outletId")
            }
        }
    }

    fun addAllToQueue(listData: List<PendingPrintEntity>, saveToPending: Boolean = true) {
        val inactivePrinters = app.deactivatePrinterList()
        val list = listData.filter { p -> !inactivePrinters.any { it == p.address } }

        list.filter {
            (it.dataString?.trim()?.length ?: 0) > 10 && it.type == Constant.PRINTER_TYPE_WIFI
        }.let { printerWifi.pendingPrint.addAll(it) }
        list.filter { it.type == Constant.PRINTER_TYPE_BT }.let { pendingPrintBT.addAll(it) }
        list.filter { it.type == Constant.PRINTER_TYPE_USB }.let { pendingPrintUSB.addAll(it) }

        val log = StringBuilder()
        log.appendLine("Add data print to queue. Print Saved Now => Wifi : ${printerWifi.pendingPrint.size} | BT : ${pendingPrintBT.size} | USB : ${pendingPrintUSB.size}\n")
        log.appendLine("Printing status => WiFi : $isPrintingWifi  | BT : $isPrintingBT  |  USB : $isPrintingUSB")
        Timber.i(log.toString())

        GlobalScope.launch(Dispatchers.IO) {
            //withContext(Dispatchers.IO){ salesDao.savePendingPrint(list) }
            Timber.d("added to list pending: ${Gson().toJson(list)}")
            salesDao.savePendingPrint(list)
            if (!isPrintingBT && pendingPrintBT.isNotEmpty()) {
                printBluetooth()
            }
            if (!isPrintingWifi && printerWifi.pendingPrint.isNotEmpty()) {
                printWifi()
            }
        }
        if (!isPrintingUSB && pendingPrintUSB.isNotEmpty()) {
            printUSB()
        }
    }

    private fun saveToPendingPrint(list: List<PendingPrintEntity>) {
        GlobalScope.launch(Dispatchers.IO) {
            salesDao.savePendingPrint(list)
        }
    }

    private fun removeFromPendingPrint(data: List<PendingPrintEntity>) {
        GlobalScope.launch(Dispatchers.IO) {
            salesDao.deletePendingPrint(data)
        }
    }

    private fun printBluetooth(index: Int = 0) {
        if (index < pendingPrintBT.size) {
            isPrintingBT = true
            val data = pendingPrintBT[index].copy()
            data.bitmap = pendingPrintBT[index].bitmap
            Timber.i(">> Print BLUETOOTH, ID : ${data.id} \n>> To : ${data.printerName} (${data.address}) \n>> bitmap?: ${data.bitmap != null} \n${data.dataString}")
            val printerBT = printerBluetooth //PrinterBluetooth(app)
            printerBT.printText(data.address, { response ->
                if (response.status) {
                    GlobalScope.launch { salesDao.deletePendingPrint(data) }
                    Timber.i("Print BT Success. ID : ${data.id}")
                } else {
                    Timber.i("Print BT Failed. ID : ${data.id}")
//                    app.toast("tidak dapat terhubung ke printer ${data.printerName?.toUpperCase()} (${data.address})", level = Level.ERROR)
                    app.toast(
                        response.message ?: "gagal print ke ${data.printerName?.uppercase()}",
                        level = Level.ERROR
                    )

                    app.outlet()?.let { outlet ->
                        Firebase.analytics
                            .logEvent(
                                "pending_print",
                                bundleOf(
                                    "Printer" to "${outlet.outletId}:${outlet.name}:BT",
                                    "Type" to "Bluetooth",
                                    "MAC Address" to data.address
                                )
                            )
                    }
                }
//                printerBT.disconnect()

                removePendingPrintBTFromQueue(data.id)
//                GlobalScope.launch {
//                    delay(5000)
//                    printerBT.disconnect()
                printBluetooth(index)
//                }
            }, data.bitmap, data.dataString ?: "")
        } else {
            isPrintingBT = false
        }
    }

    private fun printWifi(index: Int = 0) {
        if (index < printerWifi.pendingPrint.size) {
            isPrintingWifi = true
            val data = printerWifi.pendingPrint[index]
            Timber.i(">> Print WIFI, ID : ${data.id} | >> To : ${data.printerName}(${data.address}) \n[START]${data.dataString}[END]")
            printerWifi.printWifi(
                data.address,
                data.dataString
                    ?: "",
                printerName = data.printerName,
                id = data.id,
                isOpenCashDrawer = data.isOpenCashDrawer
            ) { response ->
                if (response.status) {
                    Timber.i("Print Wifi success. Delete print data with id : ${data.id}")
                    printWifi(index)
                    GlobalScope.launch { salesDao.deletePendingPrint(data) }
                } else {
                    Timber.i("Print WIfi failed. ID : ${data.id}")
                    app.toast(response.message)
                    app.outlet()?.let { outlet ->
                        Firebase.analytics
                            .logEvent(
                                "pending_print",
                                bundleOf(
                                    "Printer" to "${outlet.outletId}:${outlet.name}:WiFi",
                                    "Type" to "WiFi",
                                    "MAC Address" to data.address
                                )
                            )
                    }
                    printerWifi.removePendingPrint(data.id)
                    printWifi(index)
                }
            }
        } else {
            isPrintingWifi = false
        }
    }

    private fun printUSB(index: Int = 0) {
        if (index < pendingPrintUSB.size) {
            isPrintingUSB = true
            val data = pendingPrintUSB[index]
            Timber.i(">> Print USB, ID : ${data.id} To : ${data.address} \n${data.dataString}")
            printerUsb.print(
                data.dataString
                    ?: "", data.address, isOpenCashDrawer = data.isOpenCashDrawer
            ) { response ->
                if (response.status) {
                    GlobalScope.launch { salesDao.deletePendingPrint(data) }
                } else {
                    app.toast(response.message ?: "print gagal!")
                }
                Timber.i(">> STATUS << Print USB to ${data.address} status : ${response.status}")
                pendingPrintUSB.firstOrNull { it.id == data.id }?.let { pendingPrintUSB.remove(it) }
                printUSB(index)
            }
        } else {
            isPrintingUSB = false
        }
    }

    /**
     * Removes a pending Bluetooth print job from the queue based on its ID.
     *
     * This function searches the `pendingPrintBT` list for an item with a matching ID and removes it.
     * It also logs the removal action for debugging purposes.
     *
     * @param id The unique identifier of the pending print job to remove.
     *
     * @throws NoSuchElementException if the list 'pendingPrintBT' does not contain any elements with the specified ID. This is handled internally by the `filter` function, so no explicit try-catch is necessary.
     * @see pendingPrintBT
     * @see Timber
     */
    private fun removePendingPrintBTFromQueue(id: Long) {
        Timber.i("Remove Pending print BT, ID : $id")
        pendingPrintBT.filter { it.id == id }
            .forEach { pendingPrintBT.remove(it) }
    }

    fun isPrintingInProcess(): Boolean {
        return pendingPrintUSB.isNotEmpty() || pendingPrintBT.isNotEmpty() || printerWifi.pendingPrint.isNotEmpty()
    }

    fun isPrinted(id: Long): Boolean {
        return !(pendingPrintBT.any { it.id == id } || printerWifi.pendingPrint.any { it.id == id } || pendingPrintUSB.any { it.id == id })
    }

    fun isPrinted(ids: List<Long>): Boolean {
        val pendingIds =
            pendingPrintBT.map { it.id } + pendingPrintUSB.map { it.id } + printerWifi.pendingPrint.map { it.id }
        return !ids.any { it in pendingIds }
    }

    fun getPrinterWiFi(): PrinterWifi {
        return printerWifi
    }
}