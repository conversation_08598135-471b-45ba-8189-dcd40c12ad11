package com.uniq.uniqpos.util.receipt

import com.bugsnag.android.Bugsnag
import com.google.gson.Gson
import com.uniq.uniqpos.data.local.converter.SalesConverter
import com.uniq.uniqpos.data.local.entity.*
import com.uniq.uniqpos.data.remote.model.Employee
import com.uniq.uniqpos.data.remote.model.OperationalCost
import com.uniq.uniqpos.data.remote.model.Outlet
import com.uniq.uniqpos.data.remote.model.Promotion
import com.uniq.uniqpos.model.*
import com.uniq.uniqpos.util.Constant
import com.uniq.uniqpos.util.appendnl
import com.uniq.uniqpos.util.center
import com.uniq.uniqpos.util.dateTimeFormat
import com.uniq.uniqpos.util.loop
import com.uniq.uniqpos.util.readablePromoType
import com.uniq.uniqpos.util.removeHtmlTag
import com.uniq.uniqpos.util.safe
import com.uniq.uniqpos.util.safeRepeat
import com.uniq.uniqpos.util.safeToInt
import com.uniq.uniqpos.util.toCurrency
import com.uniq.uniqpos.util.upperCaseWord
import com.uniq.uniqpos.util.width
import com.uniq.uniqpos.util.widthRight
import timber.log.Timber
import java.util.*
import kotlin.collections.ArrayList
import kotlin.math.abs

/**
 * Created by ANNASBlackHat on 13/11/17.
 */
class PrintNotaUtil {
    companion object {
        fun getPrintNotaFormat(
            sales: SalesEntity, outlet: Outlet, employee: Employee, isBill: Boolean = false,
            paperSize: Int? = 58, isReprint: Boolean = false, language: String = "id"
        ): Pair<String, ByteArray> {
            val teks = StringBuilder()
            val header = StringBuilder(Constant.PRINTER_CODE_LOGO)
            val MAX = getMaxCharacter(paperSize)
            var totalSaving = 0

            try {
                header.appendnl(outlet.name?.trim()?.center(MAX))
                header.appendnl(outlet.receiptAddress?.trim()?.center(MAX))
//            header.appendnl("${outlet.city}, ${outlet.country}".center(MAX))
                outlet.receiptPhone?.let { header.appendnl(it.trim().center(MAX)) }
                header.appendnl("=".loop(MAX))

                val paymentDetail = sales.payments.firstOrNull()?.bank?.name?.let { "($it)" }
                    ?: kotlin.run { "" }

                sales.salesTag?.let { tag ->
                    teks.appendLine("#${tag.name.uppercase()}")
                }

                val lng = receiptLanguage(language)
                teks.appendnl("${lng.receiptNumber}   : ${sales.displayNota}".width(MAX))
                teks.appendnl("${lng.customer}  : ${sales.customer}".width(MAX))
                teks.appendnl("${lng.date}    : ${sales.timeCreated.dateTimeFormat()}".width(MAX))
                teks.appendnl("${lng.cashier}      : ${employee.name}".width(MAX))
                teks.appendnl("${lng.payment} : ${sales.payment} $paymentDetail".width(MAX))
                if (sales.table.isNotEmpty())
                    teks.appendnl("${lng.table}       : ${sales.table}".width(MAX))

                sales.promotions?.firstOrNull()?.let { promo ->
                    promo.displayVoucher?.let { voucherCode ->
                        teks.appendnl(
                            "Voucher    : ${
                                voucherCode.width(
                                    MAX
                                )
                            }"
                        )
                    }
//                    promo.name?.takeIf { promo.typeId == Constant.PROMO_TYPE_DEALS }?.let { name -> teks.appendnl("Deals      : ${name.width(MAX)}") }
                }

                teks.append("\n\n")

                var disc = 0
                sales.orderList?.sortBy { it.product?.productId }
                sales.orderList?.forEach { orders ->
                    var total = 0

                    var promoItemValue = orders.promotion?.promotionValue.safe()
                    if (sales.status == "Refund") {
                        orders.qty *= -1
                        orders.subTotal *= -1
                    }else{
                        totalSaving += promoItemValue
                    }

                    total += orders.subTotal
                    var name =
                        if (orders.isItemVoid) "VOID: ${orders.product?.name}" else orders.product?.name
                    teks.append(name?.width(MAX - 4 - 8))
                    teks.append(orders.qty.toString().center(4))
                    teks.appendnl((orders.subTotal + promoItemValue).toCurrency().widthRight(8))
                    orders.extra.forEach { extra ->
                        name =
                            "-" + if (orders.isItemVoid) "VOID: ${extra.product?.name}" else extra.product?.name
                        teks.append(
                            name?.width(
                                MAX - 4 - 8,
                                MAX
                            )
                        ) // 3 untuk spasi antara nama menu dan qty
                        teks.append(extra.qty.toString().width(4))
                        teks.appendnl((extra.subTotal).toCurrency().widthRight(8))
                        total += extra.subTotal
                    }

                    orders.promotion?.let { promo ->
                        Timber.i("promo : ${promo.name} (${promo.promotionId}) --> ${promo.promotionValue}")
                        val promoName =
                            if (promo.name.isNotBlank()) promo.name else promo.pomotionTypeFkid.readablePromoType(
                                default = ""
                            )

                        if (promoName.isNotBlank()) {
                            teks.append("#$promoName".width(MAX - 9))
                        }
                        if (promoItemValue != 0) {
                            promoItemValue =
                                if (orders.isItemVoid) abs(promoItemValue) else abs(promoItemValue)*-1
                            teks.append(
                                "(${promoItemValue.toCurrency()})".widthRight(9)
                            )
                        }
                        teks.appendLine()
//                        promo.pomotionTypeFkid.readablePromoType(default = "")
//                            .takeIf { it.isNotEmpty() }?.let { promoName ->
//                                teks.appendLine("#$promoName".width(MAX))
//                        }
                    }
                    if (orders.discount.discountNominal != 0) {
                        disc += orders.discount.discountNominal
                        totalSaving += disc
                        val txtDisc =
                            if (orders.discount.discountType == Constant.TYPE_PERSEN) "-Discount (${orders.discount.discount}%)" else "-Discount"
                        teks.append(txtDisc.width(MAX - 8))
                        teks.appendnl(
                            ("-" + orders.discount.discountNominal.toCurrency()).widthRight(
                                8
                            )
                        )
                        teks.appendnl(" (" + orders.discount.discountInfo + ")".width(MAX))
                    }
                    orders.note?.let { if (!isBill) teks.appendnl(orders.note?.width(MAX)) }
                    //if(orders.isItemVoid)teks.appendnl("VOID".width(32))
                }

                //#Refund #Refund #Refund #Refund
                val extras = sales.orderList?.flatMap { it.extra as Iterable<Order> }
                val subtotal = ((sales.orderList?.sumOf { it.subTotal })
                    ?: 0) + (extras?.sumOf { it.subTotal } ?: 0) - disc
                //----------------------------------------
                teks.appendnl("-".loop(MAX))
                teks.append("Total".widthRight(MAX - 13))
                teks.appendnl(subtotal.toCurrency().widthRight(13))

                //#Discount
                sales.discount?.let {
                    if (it.discount > 0) {
                        teks.append("Discount".widthRight(MAX - 13))
                        teks.appendnl(it.discountNominal.toCurrency().widthRight(13))
                    }
                    if (it.voucher > 0) {
                        teks.append("Voucher".widthRight(MAX - 13))
                        val voucher = if (it.voucherNominal > 0) it.voucherNominal else it.voucher
                        teks.appendnl(voucher.toCurrency().widthRight(13))
                    }
                    totalSaving += it.discountNominal + it.voucherNominal
                }

                //#Promotion
                sales.promotions?.forEach { promo ->
                    teks.append(promo.name?.widthRight(MAX - 13))
                    teks.appendnl(promo.promotionValue.toCurrency().widthRight(13))
                    totalSaving += promo.promotionValue
                }

                //#Tax
                sales.taxes?.filter { it.total > 0 }?.forEach {
                    teks.append(it.name?.widthRight(MAX - 13))
                    teks.appendnl(it.total.toCurrency().widthRight(13))
                    if(listOf("discount", "voucher").any {p -> p == it.category.safe().lowercase() }){
                        totalSaving += it.total
                    }
                }

                teks.append("\n")
                teks.appendnl(
                    "Grand Total : ".widthRight(MAX - 13) + sales.grandTotal.toCurrency()
                        .widthRight(13)
                )

                var totalPay = 0
                sales.payments.forEach {
                    totalPay += if (it.pay < 0 && it.method.lowercase() == "piutang") it.pay * -1 else it.pay
                    teks.appendnl(
                        "${it.method} : ".widthRight(MAX - 13) + it.pay.toCurrency().widthRight(13)
                    )
                }
                if (!isBill) {
                    teks.appendnl(
                        "${lng.change} : ".widthRight(MAX - 13) + (totalPay - sales.grandTotal).toCurrency()
                            .widthRight(13), 2
                    )
                }
                if (totalSaving > 0) {
                    teks.appendnl(
                        "Anda Hemat : ".widthRight(MAX - 13) + totalSaving.toCurrency()
                            .widthRight(13), 2
                    )
                }

                sales.discount?.let {
                    if (it.discount > 0) teks.appendnl("Ket. Disc : ${it.discountInfo?.trim()}")
                    if (it.voucher > 0) teks.appendnl("Ket. Voucher: ${it.voucherInfo?.trim()}")
                }

                sales.note?.takeIf { it.isNotEmpty() }?.let { note ->
                    teks.appendnl("#Note: $note")
                }

                if (isBill) {
                    teks.append("\n")
                    teks.appendnl(" NOTA TAGIHAN ".center(MAX, "="))
                    teks.appendnl(" NOTA TAGIHAN ".center(MAX, "="))
                }

                if (isReprint) {
                    teks.append("\n")
                    teks.appendnl(" REPRINT ".center(MAX, "="))
                    teks.appendnl(" REPRINT ".center(MAX, "="))
                    teks.append("\n")
                }

                if (sales.status == "Refund")
                    teks.appendnl(" #REFUND #REFUND #REFUND #REFUND".center(MAX), 2)

                outlet.receiptNote?.let { note ->
                    var receiptNoteFormatted = note.trim()
                    if(note.contains("[QR]")){
                        receiptNoteFormatted = receiptNoteFormatted.replace("[QR]",
                            Constant.PRINTER_CODE_QR
                        )
                    }else{
                        receiptNoteFormatted = receiptNoteFormatted.center(MAX)
                    }
                    teks.appendnl(receiptNoteFormatted)
                    Timber.d("#Note: '$receiptNoteFormatted'")
                }
                outlet.receiptSocialmedia?.let { teks.appendnl(it.trim().center(MAX)) }

                val footer = StringBuilder()
                footer.appendnl("powered by www.uniq.id".center(MAX))
                footer.append("\n")
//                footer.append(Constant.PRINTER_CODE_CUT)

                val all = header.toString() + "\n" + teks.toString() + "\n" + footer.toString()
                val resultByte = byteArrayOf() //<- currently return empty, not used //convertToByte(header.toString(), teks.toString(), footer.toString())
                return Pair(all, resultByte)
            } catch (e: Exception) {
                Timber.i("Create receipt format error. $e")
                Bugsnag.notify(e)
                return Pair("", "".toByteArray())
            }
        }

        private fun convertToByte(header: String, content: String, footer: String): ByteArray {
            val byteHeaderOri = header.toByteArray()
            val byteContent = content.toByteArray()
            //        val byteFooter = footer.toString().toByteArray()
            //        val byteHeader = PrinterUtil.printfont(header.toString(), PrinterUtil.FONT_32PX, PrinterUtil.Align_CENTER, 0x1A.toByte(), PrinterUtil.LANGUAGE_ENGLISH)
            //        val byteContent = PrinterUtil.printfont(teks.toString(), PrinterUtil.FONT_32PX, PrinterUtil.Align_LEFT, 0x1A.toByte(), PrinterUtil.LANGUAGE_ENGLISH)
            val byteFooter = PrinterUtil.printfont(
                footer.toString(),
                PrinterUtil.FONT_24PX,
                PrinterUtil.Align_CENTER,
                0x1A.toByte(),
                PrinterUtil.LANGUAGE_ENGLISH
            )
            val byteReset = PrinterUtil.printfont(
                " ",
                PrinterUtil.FONT_32PX,
                PrinterUtil.Align_CENTER,
                0x1A.toByte(),
                PrinterUtil.LANGUAGE_ENGLISH
            )

            val totalByte =
                ByteArray(byteHeaderOri.size + byteContent.size + byteFooter.size + byteReset.size)
            var offset = 0
            System.arraycopy(byteHeaderOri, 0, totalByte, offset, byteHeaderOri.size)
            offset += byteHeaderOri.size
            System.arraycopy(byteContent, 0, totalByte, offset, byteContent.size)
            offset += byteContent.size
            System.arraycopy(byteFooter, 0, totalByte, offset, byteFooter.size)
            offset += byteFooter.size
            System.arraycopy(byteReset, 0, totalByte, offset, byteReset.size)

//              return PrinterUtil.FramePack(PrinterUtil.FRAME_TOF_PRINT, totalByte, 0, totalByte.size)
            return totalByte
        }

        private fun receiptLanguage(language: String): ReceiptText {
            var receiptText = ReceiptText()
            if (language == "en") {
                receiptText.receiptNumber = "Receipt Number"
                receiptText.customer = "Customer"
                receiptText.date = "Date"
                receiptText.cashier = "Cashier"
                receiptText.payment = "Payment"
                receiptText.table = "Table"
                receiptText.change = "Change"
            }
            return receiptText
        }

        fun getPrintKitchenFormat(
            sales: SalesEntity,
            tickets: PrinterTicketEntity? = null,
            paperSize: Int? = 58,
            isReprint: Boolean = false
        ): Pair<String, ByteArray> {
            val header = StringBuilder()
            val teks = StringBuilder()
//            val footer = StringBuilder()
            var printed = 0
            val MAX = getMaxCharacter(paperSize)

            try {
                sales.orderList?.sortBy { it.tmpId }
                val ordersToPrint = sales.orderList?.filter { it.qty > it.printed }

                header.appendnl("CAPTAIN ORDER".center(MAX))
                header.appendnl(" BILL INFORMATION ".center(MAX, "="))

                tickets?.let { teks.appendLine("#${it.name}") } //ticket name
                sales.displayNota.takeIf { it.isNotBlank() }
                    ?.let { //display nota only available after sales saved (reprint from history)
                        teks.appendnl("No Nota       : ${sales.displayNota}".width(MAX))
                    }
                teks.appendnl("Pelanggan     : ${sales.customer}".width(MAX))
                if (sales.table.isNotEmpty())
                    teks.appendnl("Meja          : ${sales.table}".width(MAX))
                ordersToPrint?.firstOrNull()?.employeeName?.let { operator ->
                    teks.appendnl("Nama Operator : $operator".width(MAX))
                }

                teks.appendnl("Nama Login    : ${sales.employeeName}".width(MAX))
                teks.appendnl(
                    "Tanggal       : ${sales.timeModified.dateTimeFormat("HH:mm dd/MM/yyyy")}".width(
                        MAX
                    ), 2
                )

                val ticketSubCategories = tickets?.detail?.map { it.productSubcategoryFkid }
                    ?: emptyList()
                ordersToPrint?.forEach { orders ->
//                    if (tickets != null && tickets.detail.isNotEmpty()) {
//                        val subcategories = tickets.detail.map { it.productSubcategoryFkid }
//                        subcategories.firstOrNull { it == orders.product?.productSubcategoryFkid }
//                                ?: return@forEach
//                    }

                    var isParentPrinted = false
                    if (tickets == null || (ticketSubCategories.isNotEmpty() && ticketSubCategories.any { it == orders.product?.productSubcategoryFkid })) {
                        var qty = orders.qty - orders.printed
                        if (sales.status == "Refund") {
                            orders.qty = orders.qty * -1
                            orders.subTotal = orders.subTotal * -1
                        }
                        var name =
                            if (orders.isItemVoid) "VOID: ${orders.product?.name}" else orders.product?.name
                        val table = if (sales.table.isNotEmpty()) "/${sales.table}" else ""
                        val customer =
                            if ((sales.customer?.length ?: 0) <= 6) sales.customer else sales.customer!!.substring(
                                0,
                                6
                            )
                        teks.append((qty.toCurrency() + "x").width(5))
                        teks.append(name?.width(MAX - 5 - 10, MAX, 5))
                        teks.appendnl(" #$customer$table".widthRight(10))

                        printed++
                        isParentPrinted = true
                    }

                    orders.extra.forEach { extra ->
                        if (tickets == null || (ticketSubCategories.isNotEmpty() && ticketSubCategories.any { it == extra.product?.productSubcategoryFkid })) {
                            teks.append("\n")
                            val prefix = if (!isParentPrinted) {
                                "[EXTRA] ${orders.qty - orders.printed}x "
                            } else "- "
                            val name =
                                "$prefix" + if (orders.isItemVoid) "VOID: ${extra.product?.name}" else extra.product?.name
                            teks.appendnl(name.width(24))
                            printed++
                        }
                    }
                    orders.note?.takeIf { isParentPrinted }
                        ?.let { teks.appendnl("($it)".width(32)) }
                    teks.append("\n")
                }
                if (sales.status == "Refund")
                    teks.appendnl(" #Refund #Refund #Refund #Refund".center(MAX), 2)
                if (isReprint) {
                    teks.appendnl(" REPRINT ".center(MAX, "="))
                    teks.appendnl(" REPRINT ".center(MAX, "="))
                }
                teks.append("\n")
                Timber.i("Create Nota Order Format Success...")
            } catch (e: Exception) {
                Timber.e("Create nota order error. $e")
                Bugsnag.notify(e)
            }

            val all =
                if (printed > 0) header.toString() + "\n" + teks.toString() else ""

            try {
                val byteHeader = header.toString().toByteArray()
//        val byteHeader = PrinterUtil.printfont(header.toj         String(), PrinterUtil.FONT_32PX, PrinterUtil.Align_LEFT, 0x1A.toByte(), PrinterUtil.LANGUAGE_ENGLISH)
//        val byteContent = PrinterUtil.printfont(teks.toString(), PrinterUtil.FONT_32PX, PrinterUtil.Align_LEFT, 0x1A.toByte(), PrinterUtil.LANGUAGE_ENGLISH)
                val byteContent = teks.toString().toByteArray()
//                val byteFooter = PrinterUtil.printfont(footer.toString(), PrinterUtil.FONT_24PX, PrinterUtil.Align_CENTER, 0x1A.toByte(), PrinterUtil.LANGUAGE_ENGLISH)
                val byteFooter = " ".toByteArray()
                val byteReset = PrinterUtil.printfont(
                    " ",
                    PrinterUtil.FONT_32PX,
                    PrinterUtil.Align_CENTER,
                    0x1A.toByte(),
                    PrinterUtil.LANGUAGE_ENGLISH
                )

                val totalByte =
                    ByteArray(byteHeader.size + byteContent.size + byteFooter.size + byteReset.size)
                var offset = 0
                System.arraycopy(byteHeader, 0, totalByte, offset, byteHeader.size)
                offset += byteHeader.size
                System.arraycopy(byteContent, 0, totalByte, offset, byteContent.size)
                offset += byteContent.size
                System.arraycopy(byteFooter, 0, totalByte, offset, byteFooter.size)
                offset += byteFooter.size
                System.arraycopy(byteReset, 0, totalByte, offset, byteReset.size)

//        return PrinterUtil.FramePack(PrinterUtil.FRAME_TOF_PRINT, totalByte, 0, totalByte.size)
                if (printed == 0) {
                    Timber.i("Nothing to print. Return empty")
                    return Pair("", "".toByteArray())
                }
                return Pair(all, totalByte)
            } catch (e: Exception) {
                Timber.i("Generating byte array Order format error. $e")
                Timber.i(">>> GENERATED : $teks")
                return Pair(all, "".toByteArray())
            }
        }

        fun getOperationalCostFormat(
            operationalCost: OperationalCostEntity,
            outlet: Outlet,
            employee: Employee,
            paperSize: Int? = 58
        ): String {
            val teks = StringBuilder()
            val header = StringBuilder(Constant.PRINTER_CODE_LOGO)
            val MAX = getMaxCharacter(paperSize)

            header.appendnl(outlet.name?.center(MAX))
            header.appendnl(outlet.receiptAddress?.center(MAX))
            outlet.receiptPhone?.let { header.appendnl(it.center(MAX)) }
            header.appendnl("=".loop(MAX))

            val bankName = operationalCost.bankName ?: ""

            teks.appendnl("Supplier   : ${operationalCost.supplierName}".width(MAX))
            teks.appendnl("Tanggal    : ${operationalCost.timeCreated?.dateTimeFormat()}".width(MAX))
            teks.appendnl("Kasir      : ${employee.name}".width(MAX))
            teks.appendnl("Pembayaran : ${operationalCost.payment} $bankName".width(MAX))

            teks.appendnl()
            teks.append(operationalCost.opcostName?.width(MAX - 4 - 8))
            teks.append(operationalCost.qty.toCurrency().width(4))
            teks.appendnl(operationalCost.total.toCurrency().width(8))

            teks.append("\npowered by www.uniq.id\n\n\n")

            return header.toString() + teks.toString()
        }

        //Generate for print cash recap
        fun getCashRecapFormat(
            cashRecaps: List<CashRecapEntity>,
            outlet: Outlet,
            employee: Employee,
            sales: List<SalesEntity>,
            pending: List<TmpSalesEntity>,
            op: ArrayList<OperationalCost>,
            setting: PrinterClosingShiftEntity?,
            categories: List<SubCategoryEntity>,
            func: (String) -> Unit,
            paperSize: Int? = 58,
            watermark: String? = null,
            vararg openShifts: OpenShiftEntity,
            isRecap: Boolean = false,
            isReprint: Boolean = true,
            debtPaymentList: List<PiutangHistoryEntity> = emptyList()
        ): String {

            val nota = StringBuilder(Constant.PRINTER_CODE_LOGO)
            val MAX = getMaxCharacter(paperSize)
            nota.appendnl()

            Timber.d("rule: ${setting?.rules}")
            Timber.d("cash-recap: ${Gson().toJson(cashRecaps)}")
            Timber.d("sales: ${Gson().toJson(sales)}")
            Timber.d("categories: ${Gson().toJson(categories)}")
            Timber.d("openShifts: ${Gson().toJson(openShifts)}")

            val rule = if (setting != null) try {
                Gson().fromJson(setting.rules, RolePrinterClosing::class.java)
            } catch (e: Exception) {
                Timber.i("[[ERROR]] getting role error. '$setting' --> $e")
                getDefaultPrintCloseShiftSetting()
            } else getDefaultPrintCloseShiftSetting()

            //val sortedOpenShift = openShifts.sortedWith(compareBy { it.openShiftId })
            val cashRecapSorted = cashRecaps.sortedWith(compareBy { it.timeCreated })
            var dateFormat = "dd/MM/yyyy"
            if (rule.time) {
                dateFormat += " HH:mm"
            }

            val date = if (cashRecapSorted.isNotEmpty()) {
                cashRecapSorted.first().timeCreated.dateTimeFormat(dateFormat)
            } else Date().dateTimeFormat(dateFormat)

            try {
                Timber.d("printerRule: ${Gson().toJson(rule)}")
                val cashier = ArrayList<String>()
                cashRecaps.forEach {
                    if (!cashier.contains(it.employeeName.safe().upperCaseWord())) {
                        cashier.add(it.employeeName.safe().upperCaseWord())
                    }
                }

                //short watermark
                val sw = "#".safeRepeat(watermark?.split("\n")?.size)
                val title = if (isRecap) "LAPORAN REKAP TUTUP KASIR" else "LAPORAN TUTUP KASIR"

                nota.appendnl(outlet.name?.trim()?.center(MAX))
                nota.appendnl(outlet.address?.center(MAX))
                nota.appendnl("=".loop(MAX))
                nota.appendnl("${sw}${title}${sw}".center(MAX))
                nota.appendnl("Kasir   : ${cashier.joinToString()}".width(MAX))
                nota.appendnl("Tanggal : $date".width(MAX), 2)
//                nota.appendnl("Shift     : ${shiftName.width(32)}")

                val salesSuccess = sales.filter { p -> p.status == "Success" }
                val netSales = salesSuccess.filter { p ->
                    p.payment.contains("CASH") || p.payment.contains("CARD") || p.payment.contains("PIUTANG")
                }
                val payments = salesSuccess.flatMap { it.payments }

                val totalNetSales =
                    payments.filter { it.method.lowercase() == "cash" || it.method.lowercase() == "card" || it.method.lowercase() == "piutang" }
                        .sumOf { it.total }
                val netSalesTotal = netSales.sumOf { it.grandTotal }

                val orders = salesSuccess.flatMap { it.orderList as Iterable<Order> }
                    .filter { !it.isItemVoid }
                val ordersVoid = salesSuccess.flatMap { it.orderList as Iterable<Order> }
                    .filter { it.isItemVoid }

                val ordersCombined: ArrayList<Order> =
                    if (rule.itemsales || rule.groupsales) orders.flatMap { it.extra as Iterable<Order> } as ArrayList else ArrayList()
                ordersCombined.addAll(orders)

                val ordersVoidCombined: ArrayList<Order> =
                    if (rule.itemsales || rule.groupsales) ordersVoid.flatMap { it.extra as Iterable<Order> } as ArrayList else ArrayList()
                ordersVoidCombined.addAll(ordersVoid)

                val promotions =
                    orders.filter { it.promotion != null }.map { it.promotion!! } as ArrayList
                promotions.addAll(ordersVoid.filter { it.promotion != null }.map { it.promotion!! })
                salesSuccess.filter { it.promotions != null }
                    .flatMap { it.promotions as Iterable<Promotion> }.forEach { promo ->
                        promotions.add(
                            PromotionSales(
                                name = promo.name.safe(),
                                promotionValue = promo.promotionValue
                            )
                        )
                    }

                val totalPromotion = promotions.sumOf { it.promotionValue }
                var discTotal = totalPromotion
                var allQty = 0

                netSales.forEach { sales ->
                    discTotal += sales.discount?.discountNominal ?: 0
                    sales.orderList?.filter { !it.isItemVoid && it.discount.discountNominal > 0 }
                        ?.forEach { o ->
                            discTotal += o.discount.discountNominal
                        }

                    sales.taxes?.forEach { t ->
                        if (t.category == Constant.TAX_CATEGORY_DISC) {
                            discTotal += t.total
                        }
                    }

                    sales.orderList?.forEach { order ->
                        val helper = if (order.isItemVoid) -1 else 1
                        allQty += (abs(order.qty) * helper)
                        order.extra.forEach {
                            allQty += (abs(it.qty) * helper)
                        }
                    }
                }

                nota.appendnl(" SALES ".center(MAX, "="))
                nota.append("Item Sales".width(MAX - 4 - 15))
                nota.append(allQty.toCurrency().width(5))
                nota.appendnl((totalNetSales + discTotal).toCurrency().widthRight(15))

                nota.append("Discount Sales".width(MAX - 15))
                nota.appendnl(discTotal.toCurrency().widthRight(15))

                nota.appendnl("-".loop(MAX))
                nota.append("Net Sales".width(MAX - 15))
                nota.appendnl(totalNetSales.toCurrency().widthRight(15), 2)

                var totalMedia = 0
                var totalCard = 0
                var totalCash = 0

                if (rule.paymentmedia) nota.appendnl(" MEDIA ".center(MAX, "="))
                val medias = listOf("Cash", "Card", "Piutang")
                medias.forEach { media ->
                    val pay = payments.filter { it.method.lowercase() == media.lowercase() }
                    val total = pay.sumOf { it.total }

                    if (media.lowercase() == "card") {
                        val bankList = pay.map { it.bank }.groupBy { it?.bankId }
                        for ((key, value) in bankList) {
                            val totalBank = pay.filter { it.bank?.bankId == key }.sumOf { it.total }
                            totalCard += totalBank

                            if (rule.paymentmedia) {
                                nota.append(value[0]?.name.safe().width(MAX - 15))
                                nota.appendnl(totalBank.toCurrency().width(15))
                            }
                        }
                    }

                    if (media.lowercase() == "cash") totalCash += total
                    totalMedia += total

                    if (rule.paymentmedia) {
                        nota.append(media.width(MAX - 13))
                        nota.appendnl(total.toCurrency().widthRight(13))
                    }
                }

                nota.appendnl("-".loop(MAX))
                nota.appendnl(
                    "Grand Total : ".widthRight(MAX - 13) + totalMedia.toCurrency().widthRight(13),
                    2
                )
                Timber.i("Total Media $totalMedia")

                var allTax = 0
                if (rule.tax) {
                    nota.appendnl(" TAX ".center(MAX, "="))
//                    val taxes = salesSuccess.map { it.taxes }
//                    val allTaxes = ArrayList<TaxSales>()
//                    netSales.forEach { it.taxes?.let { p -> allTaxes.addAll(p) } }

                    val allTaxes = netSales.flatMap { it.taxes as Iterable<TaxSales> }
                    allTaxes.filter { it.total > 0 && (it.category == "service" || it.category == "tax") }
                        .groupBy { it.name }.forEach {
                            var total = 0
                            it.value.forEach { tax ->
                                total += tax.total
                            }
                            allTax += total
                            nota.append(it.key?.width(MAX - 11))
                            nota.appendnl(total.toCurrency().widthRight(11))
                        }
                    nota.appendnl("-".loop(MAX))
                    nota.appendnl(
                        "Grand Total : ".widthRight(MAX - 13) + allTax.toCurrency().widthRight(13),
                        2
                    )
                    Timber.i("All Tax : $allTax")
                }

                if (rule.itemsales) {
                    nota.appendnl(" ITEM SALES ".center(MAX, "="))
                    var allTotal = 0
                    var allQtyItemSales = 0

                    ordersVoidCombined.forEach {
                        it.extraType = if (it.extraType == "link") "link" else ""
                    }

                    categories.sortedBy { it.name }.forEach { category ->
                        ordersCombined.filter { it.product?.productSubcategoryFkid == category.productCategoryId }
                            .takeIf { it.isNotEmpty() }?.let { orderFilter ->
                                var items = StringBuilder()
                                orderFilter.forEach {
                                    it.extraType = if (it.extraType == "link") "link" else ""
                                }
                                val salesGroup = orderFilter.sortedBy { it.extraType }.groupBy {
                                    "${it.product?.productDetailId}#${it.extraType}"
                                }

                                var isFirstExtra = true
                                for ((prodId, orderList) in salesGroup) {
                                    var qty = orderList.sumOf { it.qty }
                                    var subTotal = orderList.sumOf { it.subTotal }

                                    val productId = prodId.substring(0, prodId.indexOf("#")).toInt()
                                    val extraType = prodId.substring(prodId.indexOf("#") + 1)
                                    ordersVoidCombined.filter { it.product?.productDetailId == productId && it.extraType == extraType }
                                        .forEach { void ->
                                            Timber.i("found void for item : ${void.product?.name} (${void.extraType}). Qty Before : $qty")
                                            qty -= abs(void.qty)
                                            subTotal -= abs(void.subTotal)
                                            Timber.i("Qty After : $qty")
                                        }

                                    if (qty > 0) {
                                        Timber.d("[REPORT] ${orderList.first().product?.name} : ${orderList.first().extraType}")
                                        if (extraType.isNotEmpty() && isFirstExtra) {
                                            isFirstExtra = false
                                            items.appendnl(
                                                "[${(if (extraType == "link") "LINK MENU" else extraType).uppercase()}]".width(
                                                    MAX
                                                )
                                            )
                                        }
                                        items.append(orderList.first().product?.name?.width(MAX - 5 - 11))
                                        items.append(qty.toString().center(5))
                                        items.appendnl(subTotal.toCurrency().widthRight(11))

                                        allTotal += subTotal
                                        allQtyItemSales += qty
                                    }
                                }
                                if (items.isNotBlank()) {
                                    nota.appendnl("#${category.name}".width(MAX))
                                    nota.append(items)
                                }
                            }
                    }

                    nota.appendnl("-".loop(MAX))
                    nota.appendnl(
                        "Grand Total : ".widthRight(MAX - 5 - 11) + allQtyItemSales.toString()
                            .center(5) + allTotal.toCurrency().widthRight(11), 2
                    )
                }

                if (rule.groupsales) {
                    var itemSalesTotal = 0
                    var itemSalesQty = 0
                    nota.appendnl(" GROUP SALES ".center(MAX, "="))
                    categories.sortedBy { it.name }.forEach { category ->
                        val orderFilter =
                            ordersCombined.filter { it.product?.productSubcategoryFkid == category.productCategoryId && !it.isItemVoid }
                        var total = orderFilter.sumOf { it.subTotal }
                        var qty = orderFilter.sumOf { it.qty }

                        if (ordersVoidCombined.isNotEmpty()) {
                            val salesGroup = orderFilter.groupBy {
                                it.product?.productDetailId ?: 0
                            }
                            for ((prodId, _) in salesGroup) {
                                ordersVoidCombined.filter { it.product?.productDetailId == prodId }
                                    .forEach { orderVoid ->
                                        qty -= abs(orderVoid.qty)
                                        total -= abs(orderVoid.subTotal)
                                    }
                            }
                        }

                        if (total > 0 || qty > 0) {
                            itemSalesQty += qty
                            itemSalesTotal += total
                            nota.append(category.name?.width(MAX - 5 - 11))
                            nota.append(qty.toString().center(5))
                            nota.appendnl(total.toCurrency().widthRight(11))
                        }
                    }
                    nota.appendnl("-".loop(MAX))
                    //teks.appendnl("Grand Total : ".widthRight(MAX - 13) + sales.grandTotal.toCurrency().widthRight(13))
                    nota.appendnl(
                        "Grand Total : ".widthRight(MAX - 5 - 11) + itemSalesQty.toCurrency()
                            .center(5) + itemSalesTotal.toCurrency().widthRight(11), 2
                    )
                }

                if (promotions.isNotEmpty()) {
                    nota.appendnl(" PROMOTIONS ".center(MAX, "="))
                    Timber.d("promotions -> ${Gson().toJson(promotions)}")

                    // First group by promotion type, then by name within each type
                    val promotionTypeGroups = promotions.groupBy { it.pomotionTypeFkid }

                    var grandTotal = 0
                    for ((typeId, typeGroup) in promotionTypeGroups) {
                        val promoTranslation = GetPromoTypeTranslation(typeId)
                        // Add header for promotion type
                        if(promoTranslation.parentName.isNotBlank()){
                            nota.appendnl("#${promoTranslation.parentName.replace("\\s".toRegex(), "")} (${promoTranslation.name})")
                        }

                        // Group promotions of this type by name
                        val nameGroups = typeGroup.groupBy { it.name }

                        for ((name, promoList) in nameGroups) {
                            val sumValue = promoList.sumOf { it.promotionValue }
                            grandTotal += sumValue

                            Timber.i("$name : $sumValue -> ${Gson().toJson(promoList)}")
                            if(sumValue == 0) continue
                            nota.append(name.width(MAX - 11))
                            nota.appendnl(sumValue.toCurrency().widthRight(11))
                        }

                        // Add empty line between different types if needed
                        nota.appendnl()
                    }

                    nota.appendnl("-".loop(MAX))
                    nota.appendnl(
                        "Grand Total".widthRight(MAX - 16) + grandTotal.toCurrency()
                            .widthRight(16), lineAfter = 2
                    )
                }

                val allTaxes = netSales.flatMap { it.taxes as Iterable<TaxSales> }
                val totalTax =
                    allTaxes.filter { it.total > 0 && (it.category == "service" || it.category == "tax") }
                        .sumOf { it.total }
                val totalOdb = netSalesTotal - totalTax + discTotal
                val textDiscSuffix = "+Promo".takeIf { totalPromotion > 0 }.safe()
                nota.appendnl(" SALES RECAP ".center(MAX, "="))
                nota.append("Total ODB".width(MAX - 13))
                nota.appendnl(totalOdb.toCurrency().widthRight(13))
                nota.append("Discount$textDiscSuffix".width(MAX - 13))
                nota.appendnl(discTotal.toCurrency().widthRight(13))
                nota.append("After disc.".width(MAX - 13))
                nota.appendnl((totalOdb - discTotal).toCurrency().widthRight(13))
                allTaxes.filter { it.total > 0 && (it.category == "service" || it.category == "tax") }
                    .groupBy { it.name }.forEach {
                        var total = 0
                        it.value.forEach { tax ->
                            total += tax.total
                        }
                        nota.append(it.key?.width(MAX - 13))
                        nota.appendnl(total.toCurrency().widthRight(13))
                    }
                nota.append("\n\n")

                Timber.i("Pending size : ${pending.size}")
                var pendingBillTotal = cashRecaps.sumOf { it.pendingBillTotal }
                if (pendingBillTotal > 0) {
                    nota.appendnl(" PENDING BILL ".center(32, "="))
                    nota.appendnl(
                        "Grand Total : ".widthRight(MAX - 16) + pendingBillTotal.toCurrency()
                            .widthRight(11), 2
                    )
                } else if (pending.isNotEmpty() && date == Date().dateTimeFormat(dateFormat)) {
                    nota.appendnl(" PENDING BILL ".center(32, "="))
                    pendingBillTotal = 0
                    var pendingBillQty = 0
                    val salesPending = pending.map { SalesConverter().fromStringToObject(it.sales) }
                    val orderPending =
                        salesPending.flatMap { it.orderList as Iterable<Order> } as ArrayList
                    orderPending.addAll(orderPending.flatMap { it.extra })
                    val orderGroup = orderPending.groupBy { it.product?.productDetailId }
                    for ((_, orderList) in orderGroup) {
                        val totalQty =
                            orderList.sumOf { if (it.isItemVoid) abs(it.qty) * -1 else it.qty }
                        val totalSubtotal =
                            orderList.sumOf { if (it.isItemVoid) abs(it.subTotal) * -1 else it.subTotal }
                        nota.append(orderList.first().product?.name.safe().width(MAX - 5 - 8))
                        nota.append(totalQty.toCurrency().width(5))
                        nota.appendnl(totalSubtotal.toCurrency().widthRight(8))
                        pendingBillTotal += totalSubtotal
                        pendingBillQty += totalQty
                    }
                    nota.appendnl("-".loop(MAX))
                    nota.appendnl(
                        "Grand Total".widthRight(MAX - 5 - 11) + pendingBillQty.toCurrency()
                            .center(5) + pendingBillTotal.toCurrency().widthRight(11), 2
                    )
                    Timber.i("Pending bill $pendingBillTotal")
                }

                val salesVoid = salesSuccess.flatMap { it.orderList as Iterable<Order> }
                    .filter { it.isItemVoid }
                if (salesVoid.isNotEmpty()) {
                    nota.appendnl(" VOID ".center(MAX, "="))
                    var subtotalExtra = 0
                    salesVoid.forEach { v ->
                        nota.append(v.product?.name?.width(MAX - 5 - 11))// 3 spasi antara menu dan qty
                        nota.append(v.qty.toCurrency().center(5))
                        nota.appendnl((abs(v.subTotal)).toCurrency().widthRight(11))

                        v.extra.forEach { e ->
                            nota.append("- ${e.product?.name}".width(MAX - 5 - 11))// 3 spasi antara menu dan qty
                            nota.append(e.qty.toCurrency().center(5))
                            nota.appendnl((abs(e.subTotal)).toCurrency().widthRight(11))
                            subtotalExtra += abs(e.subTotal)
                        }
                    }
                    var totalVoid = salesVoid.sumOf { abs(it.subTotal) } + subtotalExtra
                    nota.appendnl("-".loop(MAX))
                    nota.appendnl(
                        "Grand Total : ".widthRight(MAX - 13) + totalVoid.toCurrency()
                            .widthRight(13)
                    )
                    nota.appendnl()
                }

                val refund = sales.filter { it.status?.lowercase() == "refund" }
                if (refund.isNotEmpty()) {
                    nota.appendnl(" REFUND ".center(MAX, "="))
                    refund.forEach { sale ->
                        var totalTax = sale.taxes?.sumOf { it.total } ?: 0
                        var avgTax = totalTax / (sale.orderList?.size ?: 1)
                        sale.orderList?.forEach { order ->
                            nota.append(order.product?.name?.width(MAX - 5 - 10))
                            nota.append(order.qty.toString().center(5))
                            nota.appendnl((order.subTotal + avgTax).toCurrency().widthRight(10))
                            order.extra.forEach { extra ->
                                nota.append(("- " + extra.product?.name).width(MAX - 5 - 10))
                                nota.append(extra.qty.toString().center(5))
                                nota.appendnl((extra.subTotal).toCurrency().widthRight(10))
                            }
                        }
                    }

                    val allRefund = refund.sumOf { it.grandTotal }
                    nota.appendnl("-".loop(MAX))

                    val taxesRefund = refund.flatMap { it.taxes as Iterable<TaxSales> }
                    taxesRefund.groupBy { it.name }.forEach { tax ->
                        var total = 0
                        tax.value.forEach { total += it.total }
                        nota.append(tax.key?.width(MAX - 10))
                        nota.appendnl(total.toCurrency().widthRight(10))
                    }

                    nota.appendnl(
                        "Grand Total : ".widthRight(MAX - 13) + allRefund.toCurrency()
                            .widthRight(13)
                    )
                    nota.appendnl()
                }

                if (rule.avgpaxbill) {
                    val allPax = salesSuccess.sumOf { it.customersQty }
                    val avgPax = if (allPax > 0) netSalesTotal / allPax else 0
                    nota.append("Total Pax".width(MAX - 8))
                    nota.appendnl(allPax.toCurrency().widthRight(8))
                    nota.append("Avg Pax".width(MAX - 8))
                    nota.appendnl(avgPax.toCurrency().widthRight(8))
                    nota.append("Total Bill".width(MAX - 8))
                    nota.appendnl(salesSuccess.size.toCurrency().widthRight(8))
                    nota.append("Avg Bill".width(MAX - 10))
                    nota.appendnl(
                        (netSalesTotal / (if (sales.isEmpty()) 1 else sales.size)).toCurrency()
                            .widthRight(10), 2
                    )
                }

                if (rule.fifo) {
                    val earlyCash = openShifts.sumOf { it.earlyCash.safe() }
                    nota.appendnl(" FIFO ".center(MAX, "="))
                    nota.append("Kas Awal".width(MAX - 10))
                    nota.appendnl(earlyCash.toCurrency().widthRight(10))

                    val cashIn = 0
                    nota.append("Kas Masuk".width(MAX - 11))
                    nota.appendnl(cashIn.toCurrency().widthRight(11))

                    nota.append("Pendapatan Tunai".width(MAX - 13))
                    nota.appendnl(totalCash.toCurrency().width(13))

                    nota.append("Pendapatan Kartu".width(MAX - 13))
                    nota.appendnl(totalCard.toCurrency().width(13))

                    nota.append("Total Pendapatan".width(MAX - 10))
                    nota.appendnl((totalCash + totalCard).toCurrency().widthRight(10))
                    nota.append("Kas Keluar".width(MAX - 9))
                    nota.appendnl((0).toCurrency().widthRight(9))
                    nota.appendnl("-".loop(MAX))

                    val sisa = totalCash + totalCard + earlyCash
                    nota.appendnl(
                        "Sisa : ".widthRight(MAX - 13) + sisa.toCurrency().widthRight(13),
                        2
                    )
                }

                if (rule.actual) {
                    val totalActualCash = cashRecaps.sumOf { it.cash }
                    val totalActualCard = cashRecaps.sumOf { it.card }
                    nota.appendnl(" AKTUAL ".center(MAX, "="))
                    nota.append("Cash".width(MAX - 13))
                    nota.appendnl(totalActualCash.toCurrency().widthRight(13))
                    nota.append("Card".width(MAX - 13))
                    nota.appendnl(totalActualCard.toCurrency().widthRight(13))
                    nota.appendnl("-".loop(MAX))
                    nota.appendnl(
                        "Total : ".widthRight(MAX - 13) + (totalActualCash + totalActualCard).toCurrency()
                            .widthRight(13), 2
                    )
                    nota.appendnl(
                        "SELISIH : ".widthRight(MAX - 13) + ((totalActualCash + totalActualCard) - (totalCash + totalCard)).toCurrency()
                            .widthRight(13)
                    )
                    nota.append("\n\n")
                }

                val totalOP = op.sumOf { it.total.toInt() }

                if (rule.opcostdetail && op.isNotEmpty()) {
                    nota.appendnl(" OPERATIONAL COST ".center(MAX, "="))
                    op.forEach { operationalCost ->
                        nota.append(operationalCost.opcostName?.width(MAX - 13))
                        nota.appendnl(operationalCost.total.safeToInt().toCurrency().widthRight(13))
                    }
                    nota.append("\n\n")
                }

                nota.appendnl(" CASHFLOW ".center(MAX, "="))
                nota.append("Net Sales".width(MAX - 15))
                nota.appendnl((totalNetSales + discTotal).toCurrency().widthRight(15))
                nota.append("Operational Cost".width(MAX - 15))
                nota.appendnl((totalOP).toCurrency().widthRight(15))
                nota.appendnl("-".loop(MAX))
                nota.appendnl(
                    "Total Cashflow : ".widthRight(MAX - 13) + ((totalNetSales + discTotal) - totalOP).toCurrency()
                        .widthRight(13), 2
                )

                nota.appendnl(" PURCHASING ".center(MAX, "="))
                nota.append("Operational Cost".width(MAX - 10))
                nota.appendnl((totalOP).toCurrency().widthRight(10))
                nota.append("Purchase Cash".width(MAX - 10))
                nota.appendnl((0).toCurrency().widthRight(10))
                nota.append("Purchase Hutang".width(MAX - 10))
                nota.appendnl((0).toCurrency().widthRight(10), 2)

                val totalDutyMeals =
                    salesSuccess.filter { it.payment.lowercase().contains("duty meals") }
                        .sumOf { it.grandTotal }
                val totalCompliment =
                    salesSuccess.filter { it.payment.lowercase().contains("compliment") }
                        .sumOf { it.grandTotal }

                var allDiscTotal = 0//totalPromotion
                var allVoucherTotal = 0
                salesSuccess.forEach { s ->
                    allDiscTotal += s.discount?.discountNominal ?: 0
                    allVoucherTotal += s.discount?.voucherNominal ?: 0

                    s.orderList?.forEach { o ->
                        allDiscTotal += o.discount.discountNominal
                    }

                    s.taxes?.forEach { t ->
                        if (t.category == Constant.TAX_CATEGORY_DISC) {
                            allDiscTotal += t.total
                        } else if (t.category == Constant.TAX_CATEGORY_VOUCHER) {
                            allVoucherTotal += t.total
                        }
                    }
                }

                watermark?.split("\n")?.forEach { nota.appendnl(it.width(MAX)) }
                nota.appendnl(lineAfter = 1)

                if (rule.entertainincome) {
                    val typesFree =
                        listOf(Constant.PROMO_TYPE_FREE_MEMBER, Constant.PROMO_TYPE_FREE)
                    val typeVoucher = listOf(
                        Constant.PROMO_TYPE_DISCOUNT_VOUCHER,
                        Constant.PROMO_TYPE_FREE_VOUCHER,
                        Constant.PROMO_TYPE_SPECIAL_PRICE_VOUCHER
                    )

//                  var allFree = salesSuccess.filter { it.discount?.discountNominal.safe() >= it.grandTotal }.sumOf { it.grandTotal }
                    var allFree = 0
                    var promoVoucher = 0

                    (orders + ordersVoid).forEach { order ->
                        val helper = if (order.isItemVoid) -1 else 1
                        if (typesFree.any { it == order.promotion?.pomotionTypeFkid.safe() }) {
                            allFree += abs(order.promotion?.promotionValue.safe()) * helper
                        } else if (typeVoucher.any { it == order.promotion?.pomotionTypeFkid.safe() }) {
                            promoVoucher += abs(order.promotion?.promotionValue.safe()) * helper
                        }

                        if (abs(order.discount.discountNominal) >= abs(order.subTotal)) {
                            allFree += abs(order.subTotal) * helper
                        }
                    }

                    val discAll = allDiscTotal //allDiscTotal - allFree - promoVoucher //allDiscTotal is containing free and promo voucher, so we have to subtract it
                    val voucher = allVoucherTotal //+ promoVoucher
                    val entertainTotal = totalDutyMeals + totalCompliment + discAll + voucher + totalPromotion

                    nota.appendnl(" ENTERTAIN INCOME ".center(MAX, "="))
                    nota.append("Discount".width(MAX - 9))
                    nota.appendnl(discAll.toCurrency().widthRight(9))
                    nota.append("Voucher".width(MAX - 9))
                    nota.appendnl(voucher.toCurrency().widthRight(9))
                    nota.append("Free".width(MAX - 9))
                    nota.appendnl((allFree).toCurrency().widthRight(9))
                    nota.append("Promotion".width(MAX - 9))
                    nota.appendnl((totalPromotion).toCurrency().widthRight(9))
                    nota.append("Duty Meals".width(MAX - 9))
                    nota.appendnl(totalDutyMeals.toCurrency().widthRight(9))
                    nota.append("Compliment".width(MAX - 9))
                    nota.appendnl(totalCompliment.toCurrency().widthRight(9))
                    nota.append("\n")

                    nota.append("Entertain Income +".width(MAX - 11))
                    nota.appendnl((entertainTotal + netSalesTotal).toCurrency().widthRight(11))
                    nota.appendnl("Net Sales".width(MAX), lineAfter = 5)
                }

                if(rule.outletCommissionSharing){
                    nota.appendnl(" SHARING COMMISSION ".center(MAX, "="))
                    var commission = outlet.commissionSharing.safe()
                    var totalCommission =  (totalNetSales * (commission/100)).toInt()
                    if(isReprint){
                        totalCommission = cashRecaps.sumOf { it.outletCommission.safe() }
                        commission = (totalCommission/totalNetSales*100).toDouble()
                    }
                    nota.append("(${commission}%)".width(MAX - 9))
                    nota.appendnl(totalCommission.toCurrency().widthRight(9))
                }

                if(debtPaymentList.isNotEmpty()){
                    nota.appendnl(" DEBT PAYMENT ".center(MAX, "="))
                    debtPaymentList.groupBy { it.method.uppercase() }.forEach { (method, debtList) ->
                        nota.append(method.width(MAX - 10))
                        nota.appendnl(debtList.sumOf { it.total }.toCurrency().widthRight(10))
                    }
                    nota.appendnl("-".loop(MAX))
                    nota.appendnl(debtPaymentList.sumOf { it.total }.toCurrency().widthRight(MAX))
                }
            } catch (e: Exception) {
                Timber.i("Generate Nota tutup kasir error $e")
                Bugsnag.notify(e)
            }

            Timber.i("NOTA: \n $nota")

//            val byteNota = PrinterUtil.printfont(nota.toString(), PrinterUtil.FONT_32PX, PrinterUtil.Align_LEFT, 0x1A.toByte(), PrinterUtil.LANGUAGE_ENGLISH)
            func(nota.toString())
            return nota.toString()
        }

        private fun getMaxCharacter(paperSize: Int?) = when (paperSize) {
            75 -> 40
            80 -> 48
            else -> 32
        }

        fun determinePaperSize(textToPrint: String): Int {
            val paperList = mapOf<Int, Int>(40 to 75, 48 to 80)
            var result = 70
            val texts = textToPrint.split("\n")
            for (txt in texts){
                if(txt == "="){
                    val lengthTxt = txt.split("").size - 2
                    result = paperList[lengthTxt].safe(result)
                    break
                }
            }
            return result
        }

        fun getDefaultPrintCloseShiftSetting() =
            RolePrinterClosing(sales = true, paymentmedia = true, groupsales = true, refund = true)
    }
}
