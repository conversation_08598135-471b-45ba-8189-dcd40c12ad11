package com.uniq.uniqpos.util.printer;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.Socket;

import timber.log.Timber;

/**
 * Created by annasblackhat on 21/09/18
 */
public class PrintWifiCore {

    private Socket socket = null;
    private InputStream inStream = null;
    private OutputStream outStream = null;

    public void openport(String ipaddress, int portnumber) {

//        StrictMode.setThreadPolicy(
//                new StrictMode.ThreadPolicy.Builder()
//                        .detectDiskReads()
//                        .detectDiskWrites()
//                        .detectNetwork()
//                        .penaltyLog()
//                        .build());
//
//        StrictMode.setVmPolicy(
//                new StrictMode.VmPolicy.Builder()
//                        .detectLeakedSqlLiteObjects()
//                        .detectLeakedClosableObjects()
//                        .penaltyLog()
//                        .penaltyDeath()
//                        .build());

        String log = String.format("opening port %s:%d", ipaddress, portnumber);
        try {
            this.socket = new Socket(ipaddress, portnumber);
            log += " success open socket,";
            this.inStream = this.socket.getInputStream();
            log += " success get input stream,";
            this.outStream = this.socket.getOutputStream();
            log += " success get output stream";
        } catch (Exception err) {
            Timber.i(log);
            Timber.i("opening port error: %s", err.getMessage());
            Timber.e(err);
        }
    }

    public boolean isSocketInitialized() {
        return this.socket != null;
    }

    public void sendCommand(String message) {
        byte[] msgBuffer = message.getBytes();

        try {
            this.outStream.write(msgBuffer);
        } catch (IOException var4) {
            Timber.i("failed sendCommand string, IOException: ");
            Timber.i(var4);
            Timber.i("text to print: %s", message);
        }catch (Exception e){
            Timber.i("failed sendCommand string, Exception: ");
            Timber.i(e);
            Timber.i("text to print: %s", message);
        }
    }

    public void sendCommand(byte[] message) {
        try {
            this.outStream.write(message);
        } catch (IOException var3) {
            Timber.i("failed sendCommand byte, IOException: ");
            Timber.e(var3);
        }catch (Exception e){
            Timber.i("failed sendCommand byte, Exception: ");
            Timber.i(e);
        }
    }

    public void clearbuffer() {
        String message = "CLS\n";
        byte[] msgBuffer = message.getBytes();

        try {
            this.outStream.write(msgBuffer);
        } catch (IOException var4) {
            Timber.i("failed clearbuffer, IOException:");
            Timber.i(var4);
            var4.printStackTrace();
        }

    }

    public void closeport() {
        try {
            outStream.flush();
            outStream.close();
            inStream.close();

            socket.close();
//            socket = null;
        } catch (IOException var2) {
            Timber.i("cosing socket error:");
            Timber.i(var2);
        }

    }
}
