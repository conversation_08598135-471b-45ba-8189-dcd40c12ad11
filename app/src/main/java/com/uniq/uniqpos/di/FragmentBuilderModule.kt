package com.uniq.uniqpos.di

import com.uniq.uniqpos.view.cashdrawer.InputCashDrawerFragment
import com.uniq.uniqpos.view.closeshift.CloseShiftFragment
import com.uniq.uniqpos.view.help.HelpFragment
import com.uniq.uniqpos.view.purchase.OperationalCostFragment
import com.uniq.uniqpos.view.setting.kitchendisplay.KitchenDisplaySettingFragment
import com.uniq.uniqpos.view.setting.printerserver.PrinterServerFragment
import com.uniq.uniqpos.view.setting.printer.SettingPrinterFragment
import com.uniq.uniqpos.view.transaction.TransactionFragment
import com.uniq.uniqpos.view.transaction.TransactionMainFragment
import com.uniq.uniqpos.view.transactionhistory.TransactionHistoryFragment
import dagger.Module
import dagger.android.ContributesAndroidInjector

/**
 * Created by ANNASBlackHat on 12/10/2017.
 */

@Module
abstract class FragmentBuilderModule {

    @ContributesAndroidInjector
    abstract fun transactionFragment(): TransactionFragment

    @ContributesAndroidInjector
    abstract fun transactionMain(): TransactionMainFragment

    @ContributesAndroidInjector
    abstract fun printerSettingFragment(): SettingPrinterFragment

    @ContributesAndroidInjector
    abstract fun transactionHistoryFragment(): TransactionHistoryFragment

    @ContributesAndroidInjector
    abstract fun cashRecapFragment(): CloseShiftFragment

    @ContributesAndroidInjector
    abstract fun inputCashDrawerFragment(): InputCashDrawerFragment

    @ContributesAndroidInjector
    abstract fun printerServerFragment(): PrinterServerFragment

    @ContributesAndroidInjector
    abstract fun operationalCostFragment(): OperationalCostFragment

    @ContributesAndroidInjector
    abstract fun helpFragment(): HelpFragment

    @ContributesAndroidInjector
    abstract fun kitchenDisplayFragment(): KitchenDisplaySettingFragment
}