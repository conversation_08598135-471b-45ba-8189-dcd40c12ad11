package com.uniq.uniqpos.di

import com.uniq.uniqpos.data.remote.model.SelfOrder
import com.uniq.uniqpos.data.remote.service.*
import dagger.Module
import dagger.Provides
import retrofit2.Retrofit
import javax.inject.Singleton

/**
 * Created by ANNASBlackHat on 11/10/2017.
 */

@Module
class ServiceModule {

    @Singleton
    @Provides
    fun provideProductService(retrofit: Retrofit) = retrofit.create(ProductService::class.java)

    @Singleton
    @Provides
    fun provideAuthService(retrofit: Retrofit) = retrofit.create(AuthService::class.java)

    @Singleton
    @Provides
    fun provideSalesService(retrofit: Retrofit) = retrofit.create(SalesService::class.java)

    @Singleton
    @Provides
    fun provideReportService(retrofit: Retrofit) = retrofit.create(ReportService::class.java)

    @Singleton
    @Provides
    fun provideSettingService(retrofit: Retrofit) = retrofit.create(SettingService::class.java)

    @Singleton
    @Provides
    fun provideSystemService(retrofit: Retrofit) = retrofit.create(SystemService::class.java)

    @Singleton
    @Provides
    fun provideOutletService(retrofit: Retrofit) = retrofit.create(OutletService::class.java)

    @Singleton
    @Provides
    fun provideCustomerService(retrofit: Retrofit) = retrofit.create(CustomerService::class.java)

    @Singleton
    @Provides
    fun providePurchaseService(retrofit: Retrofit) = retrofit.create(PurchaseService::class.java)

    @Singleton
    @Provides
    fun providePromotionService(retrofit: Retrofit) = retrofit.create(PromotionService::class.java)

    @Singleton
    @Provides
    fun provideBillingService(retrofit: Retrofit) = retrofit.create(BillingService::class.java)

    @Singleton
    @Provides
    fun provideHelpService(retrofit: Retrofit) = retrofit.create(HelpService::class.java)

    @Singleton
    @Provides
    fun provideSelfOrderService(retrofit: Retrofit) = retrofit.create(SelfOrderService::class.java)
}