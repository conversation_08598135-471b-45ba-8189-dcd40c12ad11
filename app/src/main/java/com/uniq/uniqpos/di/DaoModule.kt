package com.uniq.uniqpos.di

import com.uniq.uniqpos.data.local.UniqDatabase
import dagger.Module
import dagger.Provides
import javax.inject.Singleton

/**
 * Created by ANNASBlackHat on 13/10/2017.
 */

@Module
class DaoModule {

    @Singleton
    @Provides
    fun provideProductDao(db: UniqDatabase) =  db.productDao()

    @Singleton
    @Provides
    fun provideSettingDao(db: UniqDatabase) = db.settingDao()

    @Singleton
    @Provides
    fun provideSalesDao(db: UniqDatabase) = db.salesDao()

    @Singleton
    @Provides
    fun provideLastSyncDao(db: UniqDatabase) = db.lastSyncDao()

    @Singleton
    @Provides
    fun provideDeleteRecordDao(db: UniqDatabase) = db.deleteRecordDao()

    @Singleton
    @Provides
    fun proviceOutletDao(db: UniqDatabase) = db.outletDao()

    @Singleton
    @Provides
    fun provideCustomerDao(db: UniqDatabase) = db.customerDao()

    @Singleton
    @Provides
    fun purchaseDao(db: UniqDatabase) = db.purchaseDao()

    @Singleton
    @Provides
    fun promotionDao(db: UniqDatabase) = db.promotionDao()
}