package com.uniq.uniqpos.di

import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import com.uniq.uniqpos.view.billing.BillingViewModel
import com.uniq.uniqpos.view.cart.TransactionCartViewModel
import com.uniq.uniqpos.view.cashdrawer.InputCashDrawerViewModel
import com.uniq.uniqpos.view.closeshift.CloseShiftViewModel
import com.uniq.uniqpos.view.help.HelpViewModel
import com.uniq.uniqpos.view.login.LoginViewModel
import com.uniq.uniqpos.view.main.MainViewModel
import com.uniq.uniqpos.view.member.MemberViewModel
import com.uniq.uniqpos.view.ordersales.OrderSalesViewModel
import com.uniq.uniqpos.view.payment.PaymentViewModel
import com.uniq.uniqpos.view.pendingprint.PendingPrintViewModel
import com.uniq.uniqpos.view.piutang.PiutangViewModel
import com.uniq.uniqpos.view.productcatalogue.ProductCatalogViewModel
import com.uniq.uniqpos.view.purchase.OperationalCostViewModel
import com.uniq.uniqpos.view.runoutstock.RunOutOfStockViewModel
import com.uniq.uniqpos.view.selforder.SelfOrderViewModel
import com.uniq.uniqpos.view.setting.PrinterSettingViewModel
import com.uniq.uniqpos.view.setting.kitchendisplay.KitchenDisplayViewModel
import com.uniq.uniqpos.view.table.TableViewModel
import com.uniq.uniqpos.view.transaction.TransactionViewModel
import com.uniq.uniqpos.view.transactionhistory.TransactionHistoryViewModel
import com.uniq.uniqpos.viewmodel.*
import dagger.Binds
import dagger.Module
import dagger.multibindings.IntoMap

/**
 * Created by ANNASBlackHat on 11/10/2017.
 */
@Module
abstract class ViewModelModule {

    @Binds
    @IntoMap
    @ViewModelKey(TransactionViewModel::class)
    abstract fun bindsTransactionViewModel(transactionViewModel: TransactionViewModel): ViewModel

    @Binds
    @IntoMap
    @ViewModelKey(PrinterSettingViewModel::class)
    abstract fun bindsPrinterSettingViewModel(printerSettingViewModel: PrinterSettingViewModel): ViewModel

    @Binds
    @IntoMap
    @ViewModelKey(AuthViewModel::class)
    abstract fun bindsAuthViewModel(authViewModel: AuthViewModel): ViewModel

    @Binds
    @IntoMap
    @ViewModelKey(CloseShiftViewModel::class)
    abstract fun bindsSalesViewModel(salesViewModel: CloseShiftViewModel): ViewModel

    @Binds
    @IntoMap
    @ViewModelKey(TransactionCartViewModel::class)
    abstract fun bindsTransactionCartViewModel(transactionCartViewModel: TransactionCartViewModel): ViewModel

    @Binds
    @IntoMap
    @ViewModelKey(TableViewModel::class)
    abstract fun bindsDiningTableViewModel(diningTableViewModel: TableViewModel): ViewModel

    @Binds
    @IntoMap
    @ViewModelKey(PendingPrintViewModel::class)
    abstract fun bindsPendingPrintTableViewModel(pendingPrintViewModel: PendingPrintViewModel): ViewModel

    @Binds
    @IntoMap
    @ViewModelKey(RunOutOfStockViewModel::class)
    abstract fun bindRunOutOfStockViewModel(runOutOfStockViewModel: RunOutOfStockViewModel): ViewModel

    @Binds
    @IntoMap
    @ViewModelKey(MemberViewModel::class)
    abstract fun bindMemberViewModel(memberViewModel: MemberViewModel): ViewModel

    @Binds
    @IntoMap
    @ViewModelKey(TransactionHistoryViewModel::class)
    abstract fun bindTransactionHistoryViewModel(transactionHistoryViewModel: TransactionHistoryViewModel): ViewModel

    @Binds
    @IntoMap
    @ViewModelKey(ReservationViewModel::class)
    abstract fun bindReservationViewModel(reservationViewModel: ReservationViewModel): ViewModel

    @Binds
    @IntoMap
    @ViewModelKey(ProductCatalogViewModel::class)
    abstract fun bindProductCatalogViewModel(productCatalogViewModel: ProductCatalogViewModel): ViewModel

    @Binds
    @IntoMap
    @ViewModelKey(PiutangViewModel::class)
    abstract fun bindPiutangViewModel(piutangViewModel: PiutangViewModel): ViewModel

    @Binds
    @IntoMap
    @ViewModelKey(PaymentViewModel::class)
    abstract fun bindPaymentViewModel(piutangViewModel: PaymentViewModel): ViewModel

    @Binds
    @IntoMap
    @ViewModelKey(OperationalCostViewModel::class)
    abstract fun bindOperationalCostViewModel(operationalCost: OperationalCostViewModel): ViewModel

    @Binds
    @IntoMap
    @ViewModelKey(OrderSalesViewModel::class)
    abstract fun bindOrderSalesViewModel(orderSalesViewModel: OrderSalesViewModel): ViewModel

    @Binds
    @IntoMap
    @ViewModelKey(LoginViewModel::class)
    abstract fun bindsLoginViewModel(loginViewModel: LoginViewModel): ViewModel

    @Binds
    @IntoMap
    @ViewModelKey(InputCashDrawerViewModel::class)
    abstract fun bindInputCashDrawerViewModel(inputCashDrawerViewModel: InputCashDrawerViewModel): ViewModel

    @Binds
    @IntoMap
    @ViewModelKey(HelpViewModel::class)
    abstract fun bindHelpViewModel(helpViewModel: HelpViewModel): ViewModel

    @Binds
    @IntoMap
    @ViewModelKey(BillingViewModel::class)
    abstract fun bindBillingViewModel(helpViewModel: BillingViewModel): ViewModel

    @Binds
    @IntoMap
    @ViewModelKey(SelfOrderViewModel::class)
    abstract fun bindSelfOrderViewModel(selfOrderViewModel: SelfOrderViewModel): ViewModel

    @Binds
    @IntoMap
    @ViewModelKey(MainViewModel::class)
    abstract fun bindMainViewModel(mainViewModel: MainViewModel): ViewModel

    @Binds
    @IntoMap
    @ViewModelKey(KitchenDisplayViewModel::class)
    abstract fun bindKitchenDisplayViewModel(mainViewModel: KitchenDisplayViewModel): ViewModel

    @Binds
    abstract fun bindsViewModelFactory(viewModelFactory: ViewModelFactory): ViewModelProvider.Factory
}