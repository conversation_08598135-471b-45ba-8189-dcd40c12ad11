package com.uniq.uniqpos.di

import android.content.Context
import com.uniq.uniqpos.app.UniqApplication
import dagger.BindsInstance
import dagger.Component
import dagger.android.AndroidInjectionModule
import com.uniq.uniqpos.di.AppModule
import javax.inject.Singleton

/**
 * Created by AnnasBlackHat on 10/10/2017.
 */

@Singleton
@Component(modules = arrayOf(AppModule::class, AndroidInjectionModule::class,
        ActivityBuilderModule::class))
interface AppComponent {

    @Component.Builder
    interface Builder{
        @BindsInstance fun application(app: Context): Builder
        fun build(): AppComponent
    }

    fun inject(app: UniqApplication)
}