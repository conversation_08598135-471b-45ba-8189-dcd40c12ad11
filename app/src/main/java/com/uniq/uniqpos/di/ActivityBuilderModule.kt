package com.uniq.uniqpos.di


import com.uniq.uniqpos.di.modules.FragmentBuilderSelfOrderModule
import com.uniq.uniqpos.sync.UniqSyncAdapterService
import com.uniq.uniqpos.view.billing.BillingActivity
import com.uniq.uniqpos.view.chooseoperator.ChooseOperatorActivity
import com.uniq.uniqpos.view.chooseoutlet.ChooseOutletActivity
import com.uniq.uniqpos.view.login.LoginAdminActivity
import com.uniq.uniqpos.view.main.MainActivity
import com.uniq.uniqpos.view.member.MemberActivity
import com.uniq.uniqpos.view.ordersales.OrderSalesActivity
import com.uniq.uniqpos.view.payment.PaymentActivity
import com.uniq.uniqpos.view.payment.SplitBillActivity
import com.uniq.uniqpos.view.piutang.PiutangActivity
import com.uniq.uniqpos.view.piutang.PiutangDetailActivity
import com.uniq.uniqpos.view.productcatalogue.AddProductActivity
import com.uniq.uniqpos.view.purchase.AddOperationalCostActivity
import com.uniq.uniqpos.view.purchase.AddSupplierActivity
import com.uniq.uniqpos.view.register.RegisterActivity
import com.uniq.uniqpos.view.reservation.AddReservationActivity
import com.uniq.uniqpos.view.reservation.ReservationActivity
import com.uniq.uniqpos.view.runoutstock.RunOutOfStockActivity
import com.uniq.uniqpos.view.setting.printer.AddPrinterActivity
import com.uniq.uniqpos.view.setting.printer.PrinterDetailActivity
import com.uniq.uniqpos.view.setting.SettingDetailActivity
import com.uniq.uniqpos.view.ordersummary.OrderSummaryActivity
import com.uniq.uniqpos.view.pendingprint.PendingPrintActivity
import com.uniq.uniqpos.view.table.TableListActivity
import com.uniq.uniqpos.view.cart.TransactionCartActivity
import com.uniq.uniqpos.view.closeshift.CloseShiftDetailActivity
import com.uniq.uniqpos.view.login.code.LoginWithCodeActivity
import com.uniq.uniqpos.view.payment.PaymentQrActivity
import com.uniq.uniqpos.view.payment.PaymentV2Activity
import com.uniq.uniqpos.view.productcatalogue.AddProductMainActivity
import com.uniq.uniqpos.view.productcatalogue.ocr.ProductEditList
import com.uniq.uniqpos.view.selforder.SelfOrderActivity
import com.uniq.uniqpos.view.transactionhistory.HistoryDetailActivity
import com.uniq.uniqpos.view.verifypin.VerifyPINActivity
import dagger.Module
import dagger.android.ContributesAndroidInjector


/**
 * Created by AnnasBlackHat on 11/10/2017.
 */

@Module
abstract class ActivityBuilderModule {

    @ContributesAndroidInjector(modules = [(FragmentBuilderModule::class)])
    abstract fun mainActivity(): MainActivity

    @ContributesAndroidInjector
    abstract fun addBluetoothActivity(): AddPrinterActivity

    @ContributesAndroidInjector
    abstract fun loginActivity(): LoginAdminActivity

    @ContributesAndroidInjector
    abstract fun chooseOutletActivity(): ChooseOutletActivity

    @ContributesAndroidInjector
    abstract fun chooseOperatorActivity(): ChooseOperatorActivity

    @ContributesAndroidInjector
    abstract fun verifyPinActivity(): VerifyPINActivity

    @ContributesAndroidInjector
    abstract fun paymentActivity(): PaymentActivity

    @ContributesAndroidInjector
    abstract fun transactionCartActivity(): TransactionCartActivity

    @ContributesAndroidInjector
    abstract fun syncAdapterService(): UniqSyncAdapterService

    @ContributesAndroidInjector
    abstract fun diningTableActivity(): TableListActivity

    @ContributesAndroidInjector
    abstract fun pendingPrintActivity(): PendingPrintActivity

    @ContributesAndroidInjector
    abstract fun runOutOfStockActivity(): RunOutOfStockActivity

    @ContributesAndroidInjector
    abstract fun memberActivity(): MemberActivity

    @ContributesAndroidInjector
    abstract fun transactionHistoryDetail(): HistoryDetailActivity

    @ContributesAndroidInjector
    abstract fun reservation(): ReservationActivity

    @ContributesAndroidInjector(modules = [(FragmentBuilderModule::class)])
    abstract fun settingDetailActivity(): SettingDetailActivity

    @ContributesAndroidInjector
    abstract fun addReservationActivity(): AddReservationActivity

    @ContributesAndroidInjector
    abstract fun orderSummaryActivity(): OrderSummaryActivity

    @ContributesAndroidInjector
    abstract fun splitBillActivity(): SplitBillActivity

    @ContributesAndroidInjector
    abstract fun registerActivity(): RegisterActivity

    @ContributesAndroidInjector
    abstract fun addProductCatalogActivity(): AddProductActivity

    @ContributesAndroidInjector
    abstract fun piutangActivity(): PiutangActivity

    @ContributesAndroidInjector
    abstract fun piutangDetailActivity(): PiutangDetailActivity

    @ContributesAndroidInjector
    abstract fun addOperationalCostActivity(): AddOperationalCostActivity

    @ContributesAndroidInjector
    abstract fun addSupplierActivity(): AddSupplierActivity

    @ContributesAndroidInjector
    abstract fun printerDetailActivity(): PrinterDetailActivity

    @ContributesAndroidInjector
    abstract fun orderSalesActivity(): OrderSalesActivity

    @ContributesAndroidInjector
    abstract fun closeShiftDetailActivity(): CloseShiftDetailActivity

    @ContributesAndroidInjector
    abstract fun billingActivity(): BillingActivity

    @ContributesAndroidInjector(modules = [(FragmentBuilderSelfOrderModule::class)])
    abstract fun selfOrderActivity(): SelfOrderActivity

    @ContributesAndroidInjector
    abstract fun loginWithCodeActivity(): LoginWithCodeActivity

    @ContributesAndroidInjector
    abstract fun paymentV2Activity(): PaymentV2Activity

    @ContributesAndroidInjector
    abstract fun paymentQrActivity(): PaymentQrActivity

    @ContributesAndroidInjector
    abstract fun productEditListActivity(): ProductEditList

    @ContributesAndroidInjector
    abstract fun addProductMainActivity(): AddProductMainActivity
}