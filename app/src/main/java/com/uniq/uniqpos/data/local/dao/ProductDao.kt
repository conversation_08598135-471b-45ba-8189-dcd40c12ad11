package com.uniq.uniqpos.data.local.dao

import androidx.lifecycle.LiveData
import androidx.room.*
import com.google.android.gms.vision.L
import com.uniq.uniqpos.data.local.entity.*
import retrofit2.http.POST

/**
 * Created by ANNASBlackHat on 13/10/2017.
 */

@Dao
interface ProductDao {

    @Query("SELECT * FROM product WHERE dataStatus = 'on' AND (active='on_all' OR active='on_sales') ORDER BY name ")
    fun loadProducts(): LiveData<List<ProductEntity>>

    @Query("SELECT * FROM product WHERE dataStatus = 'on' AND (active='on_all' OR active='on_sales') ORDER BY name ")
    fun getProducts(): List<ProductEntity>

    @Query("SELECT * FROM product WHERE sync = 0")
    fun getUnsyncProduct(): List<ProductEntity>

    @Query("SELECT * FROM product WHERE productDetailId IN (:ids) order by name")
    fun getProductsByIds(ids: List<Int>): List<ProductEntity>

    @Query("SELECT * FROM product WHERE productDetailId = :id")
    fun getProductById(id: Int): ProductEntity?

    @Query("select * from product where productId = :id and  dataStatus = 'on' ")
    fun getProductListById(id: Long): List<ProductEntity>

    @Query("SELECT * FROM product WHERE variantFkid = :id LIMIT 1")
    fun getProductByVariantId(id: Int): ProductEntity

    @Query("SELECT * FROM product WHERE variantFkid in (:ids)")
    fun getProductsByVariantIds(ids: List<Int>): List<ProductEntity>

    @Query("SELECT * FROM product WHERE variantFkid = :id LIMIT 1")
    fun getProductVariant(id: Int): ProductEntity?

    @Query("SELECT * FROM product_variant")
    fun getAllProductVariantLive(): LiveData<List<ProductVariantEntity>>

    @Query("SELECT * FROM product WHERE catalogueType = :catalogue")
    fun getProductByCatalogueType(catalogue: String): LiveData<List<ProductEntity>>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun saveProducts(product: List<ProductEntity>)

    @Update
    fun updateProduct(productEntity: ProductEntity)

    @Query("DELETE FROM product WHERE productDetailId = :id")
    fun deleteProductByDetailId(id: Int)

    @Query("UPDATE product SET stockQty = :qty WHERE productDetailId = :id")
    fun updateQtyStockProduct(qty: Int, id: Int)

    @Query("UPDATE product SET stockQty = stockQty - :qty WHERE productDetailId = :id")
    fun minusQtyStockProduct(qty: Int, id: Int)

    @Query("SELECT * FROM gratuity WHERE dataStatus = 'on'")
    fun getGratuity(): LiveData<List<GratuityEntity>>

    @Query("SELECT * FROM gratuity WHERE dataStatus = 'on'")
    fun getGratuityList(): List<GratuityEntity>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun saveGratuity(gratuity: List<GratuityEntity>)

    @Query("SELECT * FROM tax")
    fun getTax(): LiveData<List<TaxEntity>>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun saveTax(taxEntity: List<TaxEntity>)

    @Query("DELETE FROM tax WHERE taxdetailId = :id")
    fun deleteTaxById(id: Int)

    @Query("SELECT * From subcategory ORDER BY name")
    fun loadCategories(): LiveData<List<SubCategoryEntity>>

    @Query("SELECT * From subcategory ORDER BY name")
    fun getSubCategories(): List<SubCategoryEntity>

    @Query("SELECT * From subcategory ORDER BY name")
    fun getSubCategoriesLive(): LiveData<List<SubCategoryEntity>>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun saveSubCategory(subCategories: List<SubCategoryEntity>)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun addSubCategory(subCategory: SubCategoryEntity)

    @Delete
    fun deleteSubCategory(subCategoryEntity: SubCategoryEntity)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun saveProductDescriptions(products: List<ProductDescEntity>)

    @Query("SELECT * FROM product_desc WHERE dataStatus = 'on'")
    fun getProductDescription(): LiveData<List<ProductDescEntity>>

    @Query("SELECT * FROM multiple_price WHERE dataStatus = 'on' ")
    fun getMultiplePriceLive(): LiveData<List<MultiplePriceEntity>>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun saveMultiplePrice(multiplePrice: List<MultiplePriceEntity>)

    @Query("SELECT * FROM link_menu  WHERE dataStatus = 'on' ORDER BY orderNo")
    fun getLinkMenuLive(): LiveData<List<LinkMenuEntity>>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun saveLinkMenus(list: List<LinkMenuEntity>)

    @Query("DELETE FROM link_menu WHERE linkmenuId = :id")
    fun deleteLinkMenuById(id: Int)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun saveProductVariants(list: List<ProductVariantEntity>)

    @Query("SELECT * FROM product_variant WHERE dataStatus = '1'")
    fun getAllVariant(): List<ProductVariantEntity>

    @Query("SELECT * FROM product_variant WHERE dataStatus = '1' AND productFkid = :id ")
    fun getProductVariantListById(id: Int): List<ProductVariantEntity>

    @Query("SELECT * FROM product_variant WHERE variantId in (select variantFkid from product where dataStatus = 'on' and  productFkid = :id) ")
    fun getVariantListById(id: Int): List<ProductVariantEntity>

    @Query("DELETE FROM product_variant WHERE variantId = :id")
    fun deleteVarianById(id: Int)

    @Query("SELECT * FROM product_type WHERE dataStatus='on' ORDER bY name")
    fun getProductTypeLive(): LiveData<List<ProductTypeEntity>>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun saveProductTypes(list: List<ProductTypeEntity>)

    @Query("SELECT * FROM category WHERE dataStatus='on' ORDER by name")
    fun getCategoriesLive(): LiveData<List<CategoryEntity>>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun saveCategories(list: List<CategoryEntity>)

    @Query("SELECT * FROM purchase_report_category WHERE dataStatus = 'on' ORDER BY name")
    fun getPurchaseReportCategoriesLive(): LiveData<List<PurchaseReportCategoryEntity>>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun savePurchaseReportCategories(list: List<PurchaseReportCategoryEntity>)

    @Query("SELECT * FROM unit WHERE dataStatus='on'")
    fun getUnitLive(): LiveData<List<UnitEntity>>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun saveUnits(list: List<UnitEntity>)
}