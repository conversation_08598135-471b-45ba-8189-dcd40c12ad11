package com.uniq.uniqpos.data.local.converter

import androidx.room.TypeConverter
import com.google.gson.Gson
import com.uniq.uniqpos.data.remote.model.SalesRefund
import com.uniq.uniqpos.util.safe

class SalesRefundConverter {
    @TypeConverter
    fun fromObjectToJson(data: SalesRefund?): String {
        return data?.let { Gson().toJson(data) } ?: ""
    }

    @TypeConverter
    fun fromJsonToObject(json: String?): SalesRefund? {
        return if(json.safe().isNotBlank()) Gson().fromJson(json, SalesRefund::class.java) else null
    }
}