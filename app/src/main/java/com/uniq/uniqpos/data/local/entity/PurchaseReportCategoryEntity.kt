package com.uniq.uniqpos.data.local.entity

import androidx.room.Entity
import androidx.room.PrimaryKey
import com.google.gson.annotations.SerializedName

@Entity(tableName = "purchase_report_category")
data class PurchaseReportCategoryEntity(

        @PrimaryKey
        @field:SerializedName("purchase_report_category_id")
        var purchaseReportCategoryId: String = "",

        @field:SerializedName("name")
        var name: String = "",

        @field:SerializedName("admin_fkid")
        var adminFkid: Int = 0,

        @field:SerializedName("data_modified")
        var dataModified: String? = null,

        @field:SerializedName("data_created")
        var dataCreated: String? = null,

        @field:SerializedName("data_status")
        var dataStatus: String? = null
)