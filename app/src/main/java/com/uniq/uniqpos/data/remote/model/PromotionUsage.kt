package com.uniq.uniqpos.data.remote.model

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class PromotionUsage(
    @field:SerializedName("outlet_name")
    val outletName: String,

    @field:SerializedName("time_created")
    val timeCreated: Long,

    @field:SerializedName("display_nota")
    val displayNota: String
) : Parcelable
