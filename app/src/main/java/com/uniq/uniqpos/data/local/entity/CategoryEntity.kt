package com.uniq.uniqpos.data.local.entity

import androidx.room.Entity
import androidx.room.PrimaryKey
import com.google.gson.annotations.SerializedName

@Entity(tableName = "category")
data class CategoryEntity(

        @PrimaryKey
        @field:SerializedName("product_category_id")
        val productCategoryId: Int,

        @field:SerializedName("admin_fkid")
        val adminFkid: Int,

        @field:SerializedName("name")
        val name: String,

        @field:SerializedName("code")
        val code: String? = null,

        @field:SerializedName("data_modified")
        val dataModified: Long? = null,

        @field:SerializedName("data_type")
        val dataType: String? = null,


        @field:SerializedName("data_created")
        val dataCreated: Long? = null,

        @field:SerializedName("data_status")
        val dataStatus: String? = null
)