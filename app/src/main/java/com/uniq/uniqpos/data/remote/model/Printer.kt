package com.uniq.uniqpos.data.remote.model


import com.google.gson.annotations.SerializedName

data class Printer(

	@field:SerializedName("address")
	val address: String? = null,

	@field:SerializedName("date_created")
	val dateCreated: String? = null,

	@field:SerializedName("printer_name")
	val printerName: String? = null,

	@field:SerializedName("id_printer_setting")
	val idPrinterSetting: String? = null,

	@field:SerializedName("outlet_fkid")
	val outletFkid: String? = null
)