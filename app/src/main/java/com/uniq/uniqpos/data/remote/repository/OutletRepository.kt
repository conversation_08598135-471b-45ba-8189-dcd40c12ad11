package com.uniq.uniqpos.data.remote.repository

import androidx.lifecycle.LiveData
import android.content.Context
import com.bugsnag.android.Bugsnag
import com.uniq.uniqpos.BuildConfig
import com.uniq.uniqpos.data.local.dao.DeleteRecordDao
import com.uniq.uniqpos.data.local.dao.LastSyncDao
import com.uniq.uniqpos.data.local.dao.OutletDao
import com.uniq.uniqpos.data.local.entity.*
import com.uniq.uniqpos.data.local.sharedpref.SharedPref
import com.uniq.uniqpos.data.local.sharedpref.getJson
import com.uniq.uniqpos.data.local.sharedpref.putJson
import com.uniq.uniqpos.data.remote.NetworkBoundResource
import com.uniq.uniqpos.data.remote.Resource
import com.uniq.uniqpos.data.remote.model.Outlet
import com.uniq.uniqpos.data.remote.model.ServerResponse
import com.uniq.uniqpos.data.remote.model.ServerResponseList
import com.uniq.uniqpos.data.remote.model.ShiftOpen
import com.uniq.uniqpos.data.remote.service.OutletService
import com.uniq.uniqpos.model.RoleMobile
import com.uniq.uniqpos.util.await
import com.uniq.uniqpos.util.awaitBase
import com.uniq.uniqpos.util.awaitListBase
import com.uniq.uniqpos.util.safe
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import retrofit2.Call
import timber.log.Timber
import java.io.BufferedInputStream
import java.io.File
import java.io.FileOutputStream
import java.net.HttpURLConnection
import java.net.URL
import javax.inject.Inject

/**
 * Created by ANNASBlackHat on 08/01/18.
 */
class OutletRepository @Inject
constructor(private val outletDao: OutletDao, private val outletService: OutletService,
            private val lastSyncDao: LastSyncDao,
            private val deleteRecordDao: DeleteRecordDao) {

    fun getShiftSync(): LiveData<Resource<List<ShiftEntity>>> {
        return object : NetworkBoundResource<List<ShiftEntity>, ServerResponseList<ShiftEntity>>() {
            override fun saveCallResult(item: ServerResponseList<ShiftEntity>?) {
                item?.data?.let { outletDao.addShifts(it) }
            }

            override fun loadFromDb() = outletDao.getShiftsLive()
            override fun createCall(lastSync: Long) = outletService.getShift()
            override fun getLastSync() = 0L
        }.asLiveData
    }

    suspend fun getAllShift() = outletDao.getShifts()

    fun getOpenShiftSync(outletId: Int): LiveData<Resource<List<OpenShiftEntity>>> {
        return object : NetworkBoundResource<List<OpenShiftEntity>, ServerResponseList<OpenShiftEntity>>() {
            override fun saveCallResult(item: ServerResponseList<OpenShiftEntity>?) {
                item?.let {
                    it.data?.let { data ->
                        data.forEach { it.synced = true }
                        outletDao.addOpenShifts(data)
                    }
                    lastSyncDao.saveLastSync(LastSyncEntity(OpenShiftEntity::class.java.simpleName, it.millis))
                }
            }

            override fun loadFromDb() = outletDao.getOpenShiftsLive()
            override fun createCall(lastSync: Long) = outletService.getOpenShift(outletId, lastSync)
            override fun getLastSync() = lastSyncDao.getLastSync(OpenShiftEntity::class.java.simpleName)?.lastSync
        }.asLiveData
    }

    fun getDiningTableSync(outletId: Int): LiveData<Resource<List<DiningTableEntity>>> {
        return object : NetworkBoundResource<List<DiningTableEntity>, ServerResponseList<DiningTableEntity>>() {
            override fun saveCallResult(item: ServerResponseList<DiningTableEntity>?) {
                item?.let {
                    it.data?.let { data ->
                        data.forEachIndexed { index, _ -> data[index].synced = true }
                        outletDao.saveTables(data)
                    }
                    lastSyncDao.saveLastSync(LastSyncEntity(DiningTableEntity::class.java.simpleName, it.millis))
                }
            }

            override fun loadFromDb() = outletDao.getTablesLive()
            override fun createCall(lastSync: Long) = outletService.getDiningTable(outletId, lastSync)
            override fun getLastSync() = lastSyncDao.getLastSync(DiningTableEntity::class.java.simpleName)?.lastSync
        }.asLiveData
    }

    suspend fun updateTableStatus(diningTableEntity: DiningTableEntity) {
        withContext(Dispatchers.IO) {
            outletDao.updateTableStatusByName(diningTableEntity.status, diningTableEntity.tableName, System.currentTimeMillis())
            outletDao.getTableByName(diningTableEntity.tableName)?.let { table ->
                Timber.d("update table: $table")
//                outletService.updateTable(table).awaitBase {
//                    outletDao.updateTable(table.copy(synced = true))
//                }
            } ?: kotlin.run {
                Timber.i("(update table status to server aborted) Can not find table with name : ${diningTableEntity.tableName}")
            }
        }
    }

    fun syncOutlet(outletId: Int, adminId: Int?, context: Context) {
        //update last sync device
        //outletService.updateLastSyncDevice().await {  } last sync will update when user request to check active device

        val tables = outletDao.getUnsyncedTable()
        tables.forEach { dt ->
            outletService.postTable(dt).awaitBase {
                outletDao.updateTable(dt.copy(synced = true))
            }
        }

        outletService.getOutletDetail(outletId)
                .awaitBase {
                    it?.data?.let { outlet ->
                        if (outlet.outletId == outletId) {
                            context.putJson(SharedPref.OUTLET_DATA, outlet)
                        } else {
//                            Bugsnag.notify(Exception("Sync outlet Error - origin : '$outletId', new : '${outlet.outletId}'"))
                            Timber.i("Sync outlet Error - origin : '$outletId', new : '${outlet.outletId}'")
                        }
                        if (isNeedReloadLogo(outlet, context)) {
                            downloadOutletLogo(outlet, adminId, context)
                        }
                    }
                }

        var sync = lastSyncDao.getLastSync(OpenShiftEntity::class.java.simpleName)
        outletService.getOpenShift(outletId, sync?.lastSync ?: 0)
                .awaitListBase {
                    it.data?.takeIf { it.isNotEmpty() }?.let { data ->
                        data.forEach { it.synced = true }
                        data.sortedBy { it.openShiftId }
                        outletDao.addOpenShifts(data)
                        context.getJson(SharedPref.SHIFT_DATA, ShiftOpen::class.java)?.takeIf { it.openShiftId != 0L }?.let {
                            context.putJson(SharedPref.SHIFT_DATA, data.last())
                        }
                        lastSyncDao.saveLastSync(LastSyncEntity(OpenShiftEntity::class.java.simpleName, it.millis))
                    }
                }

        sync = lastSyncDao.getLastSync(ShiftEntity::class.java.simpleName)
        outletService.getShift(sync?.lastSync ?: 0)
                .awaitListBase {
                    it.data?.let { outletDao.addShifts(it) }
                    lastSyncDao.saveLastSync(LastSyncEntity(ShiftEntity::class.java.simpleName, it.millis))
                }

        deleteRecordDao.getDeleteRecord(DiningTableEntity::class.java.simpleName)
                .forEach { item ->
                    outletService.deleteTable(item.data).await {
                        deleteRecordDao.delete(item)
                    }
                }

        sync = lastSyncDao.getLastSync(EmployeeEntity::class.java.simpleName)
        outletService.getAllEmployee(outletId, sync?.lastSync.safe())
                .awaitListBase { resp ->
                    resp.data?.takeIf { it.isNotEmpty() }?.let { item ->
                        outletDao.addEmployee(item)
                        context.putJson(SharedPref.EMPLOYEE_LIST, outletDao.getEmployees())
                    }
                    lastSyncDao.saveLastSync(LastSyncEntity(EmployeeEntity::class.java.simpleName, resp.millis))
                }
    }

    private fun isNeedReloadLogo(outlet: Outlet, context: Context): Boolean {
        return outlet.receiptLogo?.let {
            val logoFile = File(context.filesDir, "logo/" + File(outlet.receiptLogo).name)
            !logoFile.exists()
        } ?: false
    }

    private fun downloadOutletLogo(outlet: Outlet, adminId: Int?, context: Context) {
        val folderParent = File(context.filesDir, "logo")
        if (!folderParent.exists()) {
            folderParent.mkdirs()
        }
        folderParent.list()?.forEach { File(folderParent, it).delete() }

        val logoUrl = if (outlet.receiptLogo?.startsWith("https://") == true) outlet.receiptLogo else BuildConfig.WEB_URL + "assets/images/outlets/$adminId/${outlet.receiptLogo}"
        Timber.i("Download Logo From : $logoUrl")

        val logoDestinationPath = folderParent.path + "/" + File(outlet.receiptLogo).name
        var output: FileOutputStream? = null
        var input: BufferedInputStream? = null
        var connection: HttpURLConnection? = null
        try {
            Bugsnag.leaveBreadcrumb("Download logo")
            val url = URL(logoUrl)
            connection = url.openConnection() as HttpURLConnection
            connection.connect()

            val BUFFER_SIZE = 23 * 1024
            input = BufferedInputStream(url.openStream(), BUFFER_SIZE)
            output = FileOutputStream(logoDestinationPath)
            val data = ByteArray(BUFFER_SIZE)
            var count = 0
//            do {
//                count = input.read(data, 0, BUFFER_SIZE)
//                if(count != -1)output.write(data, 0, count)
//            }while (count != -1)

            while (count != -1) {
                output.write(data, 0, count)
                count = input.read(data, 0, BUFFER_SIZE)
            }

            output.flush()
            Timber.i("Download new logo success... saved in : $logoDestinationPath")
        } catch (e: Exception) {
            Timber.i(">>>> DOWNLOADING FILE ERROR : $e")
        } finally {
            output?.close()
            input?.close()
            connection?.disconnect()
        }

//        val externalStorageDir = Environment.getExternalStorageDirectory()
//        val newPath = "$externalStorageDir/UNIQ/logo.bmp"
//        Timber.i("Save to storage as bmp : $newPath")
//        Utils.saveBitmapToLocal(logoDestinationPath, newPath)
    }

    suspend fun addOrUpdateTable(diningTableEntity: DiningTableEntity) {
        withContext(Dispatchers.IO) {
            outletDao.saveTable(diningTableEntity)
        }
    }

    suspend fun removeTable(diningTable: DiningTableEntity) {
        withContext(Dispatchers.IO) {
            outletDao.deleteTable(diningTable)
            val status = outletService.deleteTable(diningTable.diningTableId.toString()).await { }
            if (!status) {
                Timber.i("Delete table : ${diningTable.diningTableId} Failed")
                deleteRecordDao.saveDeleteRecord(DeleteRecordEntity(tableName = DiningTableEntity::class.java.simpleName, data = diningTable.diningTableId.toString()))
            }
        }
    }

    fun getEmployeeRole(employeeId: Int) : Call<ServerResponse<String>> {
        var sync = lastSyncDao.getLastSync(RoleMobile::class.java.simpleName)
        return outletService.getRoleMobile(employeeId, sync?.lastSync.safe())
    }

    fun getMediaBank() = outletDao.getBanksMedia()
    fun getEmployees() = outletDao.getEmployees()

}
