package com.uniq.uniqpos.data.local.converter

import androidx.room.TypeConverter
import com.google.gson.Gson
import com.uniq.uniqpos.data.local.entity.SalesTagEntity
import com.uniq.uniqpos.data.remote.model.Member
import com.uniq.uniqpos.util.safe

class SalesTagConverter {
    @TypeConverter
    fun fromObjectToJson(data: SalesTagEntity?): String {
        return data?.let { Gson().toJson(data) } ?: ""
    }

    @TypeConverter
    fun fromJsonToObject(json: String?): SalesTagEntity? {
        return if(json.safe().isNotBlank()) Gson().fromJson(json, SalesTagEntity::class.java) else null
    }
}