package com.uniq.uniqpos.data.remote.auth

import android.content.Context
import com.bugsnag.android.Bugsnag
import com.uniq.uniqpos.data.local.sharedpref.SharedPref
import com.uniq.uniqpos.data.local.sharedpref.getLocalDataString
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withTimeoutOrNull
import okhttp3.Interceptor
import okhttp3.Request
import okhttp3.Response
import timber.log.Timber

/**
 * Created by annasblackhat on 15/02/18.
 */
class AuthenticationInterceptor(val context: Context) : Interceptor {

    private val tokenManager = TokenManager(context)

    override fun intercept(chain: Interceptor.Chain): Response {
        val request = chain.request()
        val response = chain.proceed(buildAuthHeader(request))
        var waitCount = 0

        if (response.code() == 401) {
            if (tokenManager.isReauthorizeOnProcess()) {
                Timber.i(">> Reauthorize is on process, we wait for it... <<")
                while (tokenManager.isReauthorizeOnProcess()) {
                    Thread.sleep(1000)
                    waitCount++
                    Timber.i(">> still reauthorizing [$waitCount]... <<")
                    if (waitCount > 60) {
                        Timber.i("reauthorize wait stopped, because of time limit")
                        break
                    }
                }
                Timber.i(">> Reauthorize process finish <<")
            } else {
                tokenManager.refreshToken()
            }
            response.close()
            return chain.proceed(buildAuthHeader(request))
        }
        return response
    }

    private fun buildAuthHeader(request: Request): Request {
        val token = tokenManager.getToken()
        val modifiedRequest = request.newBuilder()
                .addHeader("Authorization", "${token.type} ${token.token}")
                .addHeader("Device", context.getLocalDataString(SharedPref.DEVICE_ID, ""))
        return modifiedRequest.build()
    }
}