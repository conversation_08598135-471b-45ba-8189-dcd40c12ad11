package com.uniq.uniqpos.data.remote.model

import com.google.gson.annotations.SerializedName

data class MacVendor(

	@field:SerializedName("country")
	val country: String? = null,

	@field:SerializedName("address")
	val address: String? = null,

	@field:SerializedName("start_hex")
	val startHex: String? = null,

	@field:SerializedName("company")
	val company: String? = null,

	@field:SerializedName("type")
	val type: String? = null,

	@field:SerializedName("mac_prefix")
	val macPrefix: String? = null,

	@field:SerializedName("end_hex")
	val endHex: String? = null
)