package com.uniq.uniqpos.data.remote.model


import com.google.gson.annotations.SerializedName

data class AppRelease(
    @SerializedName("release_note")
    val releaseNote: String,
    @SerializedName("time_created")
    val timeCreated: Long,
    @SerializedName("track")
    val track: String,
    @SerializedName("version_code")
    val versionCode: Int,
    @SerializedName("version_name")
    val versionName: String,
    @SerializedName("file")
    val fileUrl: String? = null
)