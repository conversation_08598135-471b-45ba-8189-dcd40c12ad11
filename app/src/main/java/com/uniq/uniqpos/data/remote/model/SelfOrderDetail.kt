package com.uniq.uniqpos.data.remote.model


import com.google.gson.Gson
import com.google.gson.annotations.SerializedName
import com.uniq.uniqpos.data.local.entity.LinkMenuEntity
import com.uniq.uniqpos.data.local.entity.ProductEntity
import com.uniq.uniqpos.data.local.entity.ProductVariantEntity
import com.uniq.uniqpos.model.LinkMenuDetail
import com.uniq.uniqpos.model.LinkMenuProduct
import com.uniq.uniqpos.model.Order
import com.uniq.uniqpos.util.safe
import timber.log.Timber

data class SelfOrderDetail(
    @SerializedName("note")
    val note: String?,
    @SerializedName("price")
    val price: Int?,
    @SerializedName("product_detail_id")
    val productDetailId: Int?,
    @SerializedName("qty")
    val qty: Int?,
    @SerializedName("sub_total")
    val subTotal: Int?,
    @SerializedName("link_menu")
    val linkMenu: ArrayList<LinkMenuSelfOrder>? = null
)

data class LinkMenuSelfOrder(
    @SerializedName("link_menu_detail")
    val linkMenuDetail: ArrayList<LinkMenuDetail>
)

fun parseToOrderList(orders: List<SelfOrderDetail?>?, products: List<ProductEntity>, variants: List<ProductVariantEntity>): ArrayList<Order> {
    val result = ArrayList<Order>()

    orders?.forEachIndexed { index, order ->
        products.firstOrNull { p -> p.productDetailId == order?.productDetailId }
            ?.let { product ->
                var variantName = ""
                variants.firstOrNull { it.variantId == product.variantFkid }
                    ?.let { variant ->
                        variantName = " (${variant.variantName})"
                    }
                val extraList = ArrayList<Order>()
                Timber.i("size of linkMenu: ${order?.linkMenu?.size}")
                order?.linkMenu?.forEach { extra ->
                    Timber.i("size of linkMenuDetail: ${extra.linkMenuDetail.size}")
                    extra.linkMenuDetail.forEach { extraDetail ->
                        products.firstOrNull { p -> p.productDetailId == extraDetail.productDetailFkid }?.let { product ->
                            extraList.add(Order(
                                product = product,
                                qty = order.qty.safe(1),
                            ))
                        } ?: Timber.e("product not found, id: ${extraDetail.productDetailFkid}")
                    }
                }
                result.add(
                    Order(
                        product = product.copy(name = "${product.name}$variantName"),
                        qty = order?.qty.safe(1),
                        note = order?.note?.takeIf { it.isNotEmpty() },
                        tmpId = System.currentTimeMillis() + index,
                        extra = extraList,
                    )
                )
            } ?: Timber.e("product not found, id: ${order?.productDetailId}")
    }
    Timber.i("result size: ${result.size}, data: ${Gson().toJson(result)}")
    return result
}