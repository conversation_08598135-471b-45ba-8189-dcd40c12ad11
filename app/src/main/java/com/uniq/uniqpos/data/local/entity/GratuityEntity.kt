package com.uniq.uniqpos.data.local.entity


import android.annotation.SuppressLint
import androidx.room.Entity
import androidx.room.PrimaryKey
import android.os.Parcelable
import androidx.room.Ignore
import com.google.gson.annotations.SerializedName
import com.uniq.uniqpos.util.Constant
import com.uniq.uniqpos.util.toCurrency
import kotlinx.android.parcel.IgnoredOnParcel
import kotlinx.android.parcel.Parcelize
import java.io.Serializable

@Parcelize
@SuppressLint("ParcelCreator")
@Entity(tableName = "gratuity")
data class GratuityEntity(

        @PrimaryKey
        @field:SerializedName("gratuity_id")
        val gratuityId: Int,

        @field:SerializedName("jumlah")
        val jumlah: Int = 0,

        @field:SerializedName("tax_type")
        val taxType: String? = null,

        @field:SerializedName("name")
        val name: String? = null,

        @field:SerializedName("data_created")
        val dataCreated: String? = null,

        @field:SerializedName("data_status")
        val dataStatus: String? = null,

        @field:SerializedName("admin_fkid")
        val adminFkid: Int? = null,

        @field:SerializedName("tax_category")
        val taxCategory: String? = null,

        @field:SerializedName("tax_status")
        val taxStatus: String? = null,

        @field:SerializedName("data_modified")
        val dataModified: String? = null
) : Parcelable {
    @Ignore
    @IgnoredOnParcel
    var readableTotal: String = ""
        get() = if (taxType == Constant.TYPE_PERSEN) "${jumlah}%" else "Rp${jumlah.toCurrency()}"
}