package com.uniq.uniqpos.data.remote.service

import com.uniq.uniqpos.data.local.entity.MemberEntity
import com.uniq.uniqpos.data.local.entity.MemberTypeEntity
import com.uniq.uniqpos.data.remote.model.Member
import com.uniq.uniqpos.data.remote.model.ServerResponse
import com.uniq.uniqpos.data.remote.model.ServerResponseList
import retrofit2.Call
import retrofit2.http.*

interface CustomerService {

    @GET("v1/member/type")
    fun getMemberType(): Call<ServerResponseList<MemberTypeEntity>>

    @GET("v1/member/admin")
    fun getMember(): Call<ServerResponseList<MemberEntity>>


    @POST("v1/member")
    fun registerMember(@Body member: MemberEntity): Call<ServerResponse<MemberEntity>>

}