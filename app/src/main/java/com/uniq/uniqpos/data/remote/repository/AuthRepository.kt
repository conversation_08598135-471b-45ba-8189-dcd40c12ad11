package com.uniq.uniqpos.data.remote.repository

import androidx.lifecycle.LiveData
import com.uniq.uniqpos.data.local.dao.OutletDao
import com.uniq.uniqpos.data.local.entity.EmployeeEntity
import com.uniq.uniqpos.data.remote.NetworkBoundResource
import com.uniq.uniqpos.data.remote.Resource
import com.uniq.uniqpos.data.remote.model.ServerResponseList
import com.uniq.uniqpos.data.remote.service.AuthService
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import javax.inject.Inject

/**
 * Created by ANNASBlackHat on 19/10/2017.
 */
class AuthRepository @Inject constructor(private val authService: AuthService,
                                         private val outletDao: OutletDao) {

    fun loginAdminCall(email: String, password: String, firebaseToken: String? = "") = authService.loginAdminCall(email, password, firebaseToken)

    fun getOutlet() = authService.outlet()

    fun getEmployee(outletId: Int) = authService.getEmployee(outletId)

    fun loginEmployee(data: Map<String, String?>) = authService.loginEmployee(data)

    suspend fun saveEmployee(employees: List<EmployeeEntity>) {
        withContext(Dispatchers.IO) {
            outletDao.addEmployee(employees)
        }
    }

    fun requestQuotation(name: String, email: String, phone: String, bussiness: String) = authService.joinUniq(name, phone, email, bussiness)

    fun loadEmployee(outletId: Int): LiveData<Resource<List<EmployeeEntity>>> {
        return object : NetworkBoundResource<List<EmployeeEntity>, ServerResponseList<EmployeeEntity>>(){
            override fun saveCallResult(item: ServerResponseList<EmployeeEntity>?) {
                item?.data?.let { items ->
                    outletDao.deleteAllEmployee()
                    outletDao.addEmployee(items)
                }
            }
            override fun loadFromDb() = outletDao.getEmployeesLive()
            override fun createCall(lastSync: Long) = authService.getEmployee(outletId)
            override fun getLastSync() = 0L
        }.asLiveData
    }

    fun fetchEmployeePosition() = authService.fetchEmployeePosition()
    fun chooseMultiAccount(adminId: Int, token: String)  = authService.chooseMultiAccount(adminId, token)
    fun sendAuthCode(email: String, deviceInfo: String = "") = authService.sendAuthCode(email, deviceInfo)
    fun loginWithCode(token: String, code: String) = authService.loginWithCode(token, code)
}