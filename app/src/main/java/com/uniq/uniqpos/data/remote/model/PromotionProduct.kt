package com.uniq.uniqpos.data.remote.model

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class PromotionProduct(

        @field:SerializedName("qty")
        val qty: Int = 0,

        @field:SerializedName("product_detail_fkid")
        val productDetailFkid: Int = 0,

        val price: Int = 0,

        val amount: Int = 0,

        @SerializedName("is_percent")
        val isPercent: Int = 0,

        @field:SerializedName("type")
        val type: String? = null,
) : Parcelable