package com.uniq.uniqpos.data.local.entity

import androidx.room.Entity
import androidx.room.PrimaryKey
import com.google.gson.annotations.SerializedName

@Entity(tableName = "product_variant")
data class ProductVariantEntity(

        @PrimaryKey
        @field:SerializedName("variant_id")
        val variantId: Int,

        @field:SerializedName("product_fkid")
        val productFkid: Int,

        @field:SerializedName("data_modified")
        val dataModified: Long,

        @field:SerializedName("variant_sku")
        val variantSku: String? = null,

        @field:SerializedName("variant_name")
        val variantName: String,

        @field:SerializedName("data_created")
        val dataCreated: Long,

        @field:SerializedName("data_status")
        val dataStatus: Int,

        @field:SerializedName("admin_fkid")
        val adminFkid: Int,

        @field:SerializedName("variant_barcode")
        val variantBarcode: String? = null
)