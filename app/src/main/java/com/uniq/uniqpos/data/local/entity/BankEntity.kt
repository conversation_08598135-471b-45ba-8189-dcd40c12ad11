package com.uniq.uniqpos.data.local.entity


import androidx.room.Entity
import androidx.room.PrimaryKey
import com.google.gson.annotations.SerializedName
import java.io.Serializable

@Entity(tableName = "bank")
data class BankEntity(

        @PrimaryKey
        @field:SerializedName("bank_id")
        val bankId: Int,

        @field:SerializedName("data_modified")
        val dataModified: String? = null,

        @field:SerializedName("name")
        val name: String? = null,

        @field:SerializedName("no_rekening")
        val noRekening: String? = null,

        @field:SerializedName("data_created")
        val dataCreated: String? = null,

        @field:SerializedName("data_status")
        val dataStatus: String? = null,

        @field:SerializedName("admin_fkid")
        val adminFkid: Int? = null,

        @field:SerializedName("account_number")
        var accountNumber: String? = null
): Serializable