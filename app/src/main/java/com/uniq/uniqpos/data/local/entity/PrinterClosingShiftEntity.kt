package com.uniq.uniqpos.data.local.entity

import androidx.room.Entity
import androidx.room.PrimaryKey
import com.google.gson.annotations.SerializedName

/**
 * Created by annasblackhat on 01/02/18.
 */

@Entity(tableName = "printer_closingshift")
data class PrinterClosingShiftEntity(
        @PrimaryKey
        @SerializedName("closingshift_id")
        var closingshiftId: String,
        @SerializedName("printer_setting_fkid")
        var printerSettingFkid: Int = 0,
        @SerializedName("data_modified")
        var dataModified: String = "",
        var name: String = "",
        var rules: String = "",
        @SerializedName("admin_fkid")
        var adminFkid: String = ""
)