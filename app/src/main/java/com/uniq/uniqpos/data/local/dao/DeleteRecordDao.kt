package com.uniq.uniqpos.data.local.dao

import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.Query
import com.uniq.uniqpos.data.local.entity.DeleteRecordEntity

/**
 * Created by ANNASBlackHat on 30/11/17.
 */

@Dao
interface DeleteRecordDao {

    @Query("SELECT * FROM delete_record WHERE tableName = :tableName")
    fun getDeleteRecord(tableName: String): List<DeleteRecordEntity>

    @Insert
    fun saveDeleteRecord(deleteRecordEntity: DeleteRecordEntity)

    @Delete
    fun delete(deleteRecordEntity: DeleteRecordEntity)
}