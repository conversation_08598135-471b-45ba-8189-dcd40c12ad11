package com.uniq.uniqpos.data.local.entity


import androidx.room.Entity
import androidx.room.PrimaryKey
import com.google.gson.annotations.SerializedName

@Entity(tableName = "shift")
data class ShiftEntity(

        @PrimaryKey
        @field:SerializedName("shift_id")
        var shiftId: Int,

        @field:SerializedName("data_modified")
        var dataModified: String,

        @field:SerializedName("name")
        var name: String,

        @field:SerializedName("data_created")
        var dataCreated: String,

        @field:SerializedName("data_status")
        var dataStatus: String,

        @field:SerializedName("admin_fkid")
        var adminFkid: Int
)