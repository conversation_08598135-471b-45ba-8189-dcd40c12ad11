package com.uniq.uniqpos.data.remote.model

import com.google.gson.annotations.SerializedName
import com.uniq.uniqpos.model.PromotionSales


data class DetailItem(

	@field:SerializedName("product_fkid")
	val productFkid: Long,

	@field:SerializedName("servis")
	val servis: String? = null,

	@field:SerializedName("product_type_fkid")
	val productTypeFkid: Int? = 0,

	@field:SerializedName("unit_fkid")
	val unitFkid: String? = null,

	@field:SerializedName("photo")
	val photo: String? = null,

	@field:SerializedName("tax")
	val tax: String? = null,

	@field:SerializedName("data_created")
	val dataCreated: Long? = 0,

	@field:SerializedName("admin_fkid")
	val adminFkid: Int? = 0,

	@field:SerializedName("sales_detail_id")
	val salesDetailId: String? = null, //could be null if item is void, only has sales_void_id

	@field:SerializedName("sales_void_id")
	val salesVoidId: String? = null, //could be null if iteme is not void

	@field:SerializedName("product_subcategory_fkid")
	val productSubcategoryFkid: Int = 0,

	@field:SerializedName("purchase_report_category_fkid")
	val purchaseReportCategoryFkid: String? = null,

	@field:SerializedName("price")
	val price: String? = null,

	@field:SerializedName("data_modified")
	val dataModified: String? = null,

	@field:SerializedName("qty")
	val qty: String? = null,

	@field:SerializedName("product_id")
	val productId: String? = null,

	@field:SerializedName("sub_total")
	val subTotal: String? = null,

	val discount: String?= null,

	@field:SerializedName("discount_info")
	val discountInfo: String?= null,

	@field:SerializedName("name")
	val name: String? = null,

	@field:SerializedName("product_category_fkid")
	val productCategoryFkid: Int? = 0,

	@field:SerializedName("sales_fkid")
	val salesFkid: String? = null,

	@field:SerializedName("sku")
	val sku: String? = null,

	@field:SerializedName("data_status")
	val dataStatus: String? = null,

	@field:SerializedName("barcode")
	val barcode: String? = null,

	val parent: String? = null,

	@field:SerializedName("is_item_void")
	var isItemVoid: Boolean = false,

	@field:SerializedName("product_detail_fkid")
	var productDetailFkId: Int,

	var note: String = "",

	@field:SerializedName("child_type")
	var childType: String? = null,

	@field:SerializedName("promotions")
	var promotions: ArrayList<PromotionSales>? = null
)