package com.uniq.uniqpos.data.remote.service

import com.uniq.uniqpos.data.local.entity.*
import com.uniq.uniqpos.data.remote.model.GenerateImageRequest
import com.uniq.uniqpos.data.remote.model.GenerateImageResponse
import com.uniq.uniqpos.data.remote.model.ProductExtractionResponse
import com.uniq.uniqpos.data.remote.model.ServerResponse
import com.uniq.uniqpos.data.remote.model.ServerResponseList
import com.uniq.uniqpos.data.remote.model.UpdateProductResponse
import okhttp3.MultipartBody
import okhttp3.RequestBody
import retrofit2.Call
import retrofit2.http.*
import java.util.ArrayList

/**
 * Created by ANNASBlackHat on 13/10/2017.
 */
interface ProductService {

    @GET("v1/product/{outlet_id}/{lastSync}")
    fun getProducts(@Path("outlet_id")outletId: Int,
                    @Path("lastSync")lastSync: Long? = 0): Call<ServerResponseList<ProductEntity>>

    @POST("v1/product")
    fun addProduct(@Body product: ProductEntity): Call<ServerResponse<ProductEntity>>

    @Multipart
    @POST("v1/product")
    fun addProductMultipart(@PartMap map: Map<String, @JvmSuppressWildcards RequestBody>,
                            @Part image: MultipartBody.Part?): Call<ServerResponse<ProductEntity>>

    @GET("v1/gratuity/{lastSync}")
    fun getGratuity(@Path("lastSync")lastSync: Long = 0): Call<ServerResponseList<GratuityEntity>>

    @GET("v1/tax/{outletId}/{lastSync}")
    fun getTax(@Path("outletId")outletId: Int,
               @Path("lastSync")lastSync: Long = 0): Call<ServerResponseList<TaxEntity>>

    @GET("v1/subcategory/{lastSync}")
    fun getSubCategory(@Path("lastSync")lastSync: Long? = 0): Call<ServerResponseList<SubCategoryEntity>>

    @GET("v1/product_description/{outletId}/{lastSync}")
    fun getProductDescription(@Path("outletId")outletId: Int,
                              @Path("lastSync")lastSync: Long = 0): Call<ServerResponseList<ProductDescEntity>>

    @GET("v1/product_type/{lastSync}")
    fun getProductType(@Path("lastSync")lastSync: Long = 0): Call<ServerResponseList<ProductTypeEntity>>

    @GET("v1/multiple_price/{outletId}/{lastSync}")
    fun getMultiplePriceProduct(@Path("outletId")outletId: Int?,
                                @Path("lastSync")lastSync: Long = 0): Call<ServerResponseList<MultiplePriceEntity>>

    @PUT("v1/product")
    fun updateProduct(@Body product: ProductEntity): Call<ServerResponse<Any>>

    @Multipart
    @PUT("v1/product/{productId}/photo")
    fun updateProductPhoto(
        @Path("productId") productId: Int,
        @Part photo: MultipartBody.Part
    ): Call<ServerResponse<UpdateProductResponse>>

    @GET("v1/link_menu/{outletId}/{lastSync}")
    fun linkMenu(@Path("outletId")outletId: Int,
                 @Path("lastSync")lastSync: Long = 0) : Call<ServerResponseList<LinkMenuEntity>>

    @GET("v1/product_variant/{lastSync}")
    fun getProductVariant(@Path("lastSync")lastSync: Long = 0): Call<ServerResponseList<ProductVariantEntity>>

    @GET("v1/category/{lastSync}")
    fun getCategory(@Path("lastSync")lastSync: Long = 0) : Call<ServerResponseList<CategoryEntity>>

    @GET("v1/purchase/purchase_report_category/{lastSync}")
    fun getPurchaseReportCategory(@Path("lastSync")lastSync: Long = 0) : Call<ServerResponseList<PurchaseReportCategoryEntity>>

    @GET("v1/unit")
    fun getUnit() : Call<ServerResponseList<UnitEntity>>

    @FormUrlEncoded
    @POST("v1/extract-menu")
    fun extractMenu(@Field("file") fileBase64: String): Call<ServerResponseList<ProductExtractionResponse>>

    @Multipart
    @POST("v1/extract-menu")
    fun extractMenuFile(@Part image: MultipartBody.Part): Call<ServerResponseList<ProductExtractionResponse>>

    @POST("v2/product")
    fun saveProductExtraction(@Body data: HashMap<String, Any>): Call<ServerResponseList<ProductEntity>>

    @POST("v1/product/generate-image")
    fun generateImage(@Body request: GenerateImageRequest): Call<ServerResponse<GenerateImageResponse>>
}