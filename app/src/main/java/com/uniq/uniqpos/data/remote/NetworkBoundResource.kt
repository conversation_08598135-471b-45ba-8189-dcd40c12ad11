/*
 * Copyright (C) 2017 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.uniq.uniqpos.data.remote

import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.annotation.MainThread
import androidx.annotation.WorkerThread
import com.uniq.uniqpos.util.await
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import retrofit2.Call
import timber.log.Timber
import java.util.concurrent.Executors
import kotlin.coroutines.resume
import kotlin.coroutines.suspendCoroutine

/**
 * A generic class that implements the Repository pattern for handling data from both
 * local database and remote network sources. This class follows the single source of truth
 * principle where the database serves as the authoritative data source.
 *
 * The class provides a unified interface for data access that:
 * 1. First loads data from the local database
 * 2. Optionally fetches fresh data from the network
 * 3. Saves network data to the database
 * 4. Returns updated data from the database
 *
 * This pattern ensures that the UI always receives data from the database, providing
 * offline capability and consistent data state management.
 *
 * @param ResultType The type of data that will be returned to the UI (usually an entity or list of entities)
 * @param RequestType The type of data received from the network API (usually a server response wrapper)
 * @param shouldFetch Whether to fetch data from network. If false, only returns cached data from database
 *
 * ## Usage Example:
 * ```kotlin
 * fun getProducts(): LiveData<Resource<List<ProductEntity>>> {
 *     return object : NetworkBoundResource<List<ProductEntity>, ServerResponseList<ProductEntity>>() {
 *         override fun saveCallResult(item: ServerResponseList<ProductEntity>?) {
 *             item?.data?.let { productDao.saveProducts(it) }
 *         }
 *         override fun loadFromDb() = productDao.getProductsLive()
 *         override fun createCall(lastSync: Long) = productService.getProducts(lastSync)
 *         override fun getLastSync() = lastSyncDao.getLastSync("ProductEntity")?.lastSync
 *     }.asLiveData
 * }
 * ```
 *
 * ## Data Flow:
 * 1. **Initial Load**: Returns loading state with cached data (if any)
 * 2. **Database Load**: Loads data from local database
 * 3. **Network Fetch** (if shouldFetch=true): Fetches fresh data from API
 * 4. **Save & Reload**: Saves network data to database and reloads from database
 * 5. **Final State**: Returns success state with updated data
 *
 * ## Error Handling:
 * - Network errors return the cached data with error status
 * - Database operations are performed on IO dispatcher
 * - Failed network calls trigger onFetchFailed() callback
 *
 * @see Resource for the wrapper class that provides loading/success/error states
 * @see Status for the different states (LOADING, SUCCESS, ERROR)
 */
abstract class NetworkBoundResource<ResultType, RequestType> @MainThread
internal constructor(private val shouldFetch: Boolean = true) {

    /**
     * MediatorLiveData that manages the final result combining database and network sources.
     * This serves as the single source of truth for the UI.
     */
    private val result = MediatorLiveData<Resource<ResultType>>()

    /**
     * Cached thread pool executor for background operations like fetching last sync timestamps.
     */
    private val executor = Executors.newCachedThreadPool()

    /**
     * Public interface to access the LiveData result.
     * This is what repositories return to ViewModels/UI components.
     *
     * @return LiveData wrapped in Resource providing loading/success/error states with data
     */
    val asLiveData: LiveData<Resource<ResultType>>
        get() = result

    /**
     * Initialization block that sets up the data flow:
     * 1. Sets initial loading state
     * 2. Loads data from database
     * 3. Decides whether to fetch from network or use cached data
     */
    init {
        // Start with loading state and no data
        result.value = Resource.loading(null)

        // Load initial data from database
        val dbSource = loadFromDb()
        result.addSource(dbSource) {
            result.removeSource(dbSource)

            if (shouldFetch) {
                // Fetch fresh data from network and update database
                fetchFromNetwork(dbSource)
            } else {
                // Use only cached data from database
                result.addSource(dbSource) { newData -> result.value = Resource.success(newData) }
            }
        }
    }

    /**
     * Handles the network fetch operation with proper error handling and state management.
     *
     * This method:
     * 1. Shows loading state with current cached data
     * 2. Fetches the last sync timestamp
     * 3. Makes the network call with incremental sync support
     * 4. Saves the result and reloads from database
     * 5. Handles errors by showing cached data with error state
     *
     * @param dbSource The LiveData source from the database to show during loading and errors
     */
    private fun fetchFromNetwork(dbSource: LiveData<ResultType>) {
        // Show loading state with current cached data
        result.addSource(dbSource) { newData -> result.value = Resource.loading(newData) }

        GlobalScope.launch(Dispatchers.Main) {
            val lastSync = fetchLastSync()
            try {
                // Make network call with last sync timestamp for incremental updates
                createCall(lastSync)?.let { call ->
                    val data = call.await()
                    result.removeSource(dbSource)
                    saveResultAndReInit(data)
                }
            } catch (e: Exception) {
                Timber.i("Fetch from network error... $e")
                onFetchFailed()
                result.removeSource(dbSource)
                // Show error state with cached data still available
                result.addSource(dbSource) { newData -> result.postValue(Resource.error(e.message, newData)) }
            }
        }
    }

    /**
     * Saves the network response to the database and reinitializes the data source.
     *
     * This method:
     * 1. Switches to IO dispatcher for database operations
     * 2. Saves the network response using the abstract saveCallResult method
     * 3. Reloads data from database to get the updated state
     * 4. Emits success state with the fresh data
     *
     * @param response The network response data to be saved to the database
     */
    @MainThread
    private suspend fun saveResultAndReInit(response: RequestType?) {
        withContext(Dispatchers.IO){
            saveCallResult(response)
        }
        result.addSource(loadFromDb()) { newData ->
            result.value = Resource.success(newData)
        }
    }

    /**
     * Fetches the last synchronization timestamp in a background thread.
     *
     * This is used for incremental sync to only fetch data that has changed
     * since the last successful synchronization. The timestamp is typically
     * stored in a LastSyncEntity table in the database.
     *
     * @return The last sync timestamp in milliseconds, or 0 if no previous sync exists
     */
    private suspend fun fetchLastSync(): Long {
        return suspendCoroutine { continuation ->
            executor.submit {
                val last = getLastSync()
                continuation.resume(last ?: 0)
            }
        }
    }

    /**
     * Saves the network API response to the local database.
     *
     * This method is called on a background thread (IO dispatcher) and should handle:
     * - Parsing the network response
     * - Transforming data if needed (e.g., marking items as synced)
     * - Saving to appropriate DAO methods
     * - Updating last sync timestamps
     *
     * Example implementation:
     * ```kotlin
     * override fun saveCallResult(item: ServerResponseList<ProductEntity>?) {
     *     item?.data?.let { products ->
     *         products.forEach { it.synced = true }
     *         productDao.saveProducts(products)
     *         lastSyncDao.saveLastSync(LastSyncEntity("ProductEntity", item.millis))
     *     }
     * }
     * ```
     *
     * @param item The network response data to save, can be null if the call failed
     */
    @WorkerThread
    protected abstract fun saveCallResult(item: RequestType?)

    /**
     * Loads data from the local database.
     *
     * This method should return a LiveData that emits the current state of data
     * from the local database. The LiveData will be observed and any changes
     * will automatically update the UI.
     *
     * Example implementation:
     * ```kotlin
     * override fun loadFromDb() = productDao.getProductsLive()
     * ```
     *
     * @return LiveData containing the data from the local database
     */
    @MainThread
    protected abstract fun loadFromDb(): LiveData<ResultType>

    /**
     * Creates the network API call to fetch fresh data.
     *
     * This method should create a Retrofit Call object for fetching data from
     * the remote server. The lastSync parameter enables incremental synchronization
     * by only fetching data that has changed since the last successful sync.
     *
     * Example implementation:
     * ```kotlin
     * override fun createCall(lastSync: Long) = productService.getProducts(outletId, lastSync)
     * ```
     *
     * @param lastSync Timestamp of the last successful synchronization in milliseconds
     * @return Retrofit Call object for the network request, or null to skip network fetch
     */
    @MainThread
    protected abstract fun createCall(lastSync: Long): Call<RequestType>?

    /**
     * Retrieves the timestamp of the last successful synchronization.
     *
     * This method should query the local database to get the last sync timestamp
     * for the specific data type. This enables incremental sync by only fetching
     * data that has changed since the last sync.
     *
     * Example implementation:
     * ```kotlin
     * override fun getLastSync() = lastSyncDao.getLastSync("ProductEntity")?.lastSync
     * ```
     *
     * @return Timestamp of last sync in milliseconds, or null if no previous sync exists
     */
    @MainThread
    protected abstract fun getLastSync(): Long?

    /**
     * Called when the network fetch operation fails.
     *
     * This is a hook for subclasses to perform any cleanup or additional
     * error handling when network operations fail. The default implementation
     * does nothing.
     *
     * Example usage:
     * ```kotlin
     * override fun onFetchFailed() {
     *     // Log analytics event, clear cache, etc.
     *     analyticsService.logSyncFailure("ProductSync")
     * }
     * ```
     */
    @MainThread
    protected fun onFetchFailed() {}

}
