package com.uniq.uniqpos.data.local.converter

import androidx.room.TypeConverter
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.uniq.uniqpos.model.PrinterTicketDetail

/**
 * Created by annasblackhat on 19/02/18.
 */
class PrinterTicketDetailConverter {
    @TypeConverter
    fun fromObjectToArray(obj: ArrayList<PrinterTicketDetail>): String {
        return Gson().toJson(obj)
    }

    @TypeConverter
    fun fromStringToObject(strValue: String): ArrayList<PrinterTicketDetail> {
        val type = object : TypeToken<ArrayList<PrinterTicketDetail>>() {}.type
        return Gson().fromJson<ArrayList<PrinterTicketDetail>>(strValue, type)
    }
}