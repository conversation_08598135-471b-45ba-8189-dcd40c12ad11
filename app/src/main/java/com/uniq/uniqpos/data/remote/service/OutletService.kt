package com.uniq.uniqpos.data.remote.service

import com.uniq.uniqpos.data.local.entity.*
import com.uniq.uniqpos.data.remote.model.Outlet
import com.uniq.uniqpos.data.remote.model.ServerResponse
import com.uniq.uniqpos.data.remote.model.ServerResponseList
import retrofit2.Call
import retrofit2.http.*

/**
 * Created by ANNASBlackHat on 08/01/18.
 */
interface OutletService {

    @GET("v1/shift/{lastSync}")
    fun getShift(@Path("lastSync") lastSync: Long = 0): Call<ServerResponseList<ShiftEntity>>

    @GET("v1/open_shift/{outletId}/{lastSync}")
    fun getOpenShift(@Path("outletId") outletId: Int,
                     @Path("lastSync") lastSync: Long = 0): Call<ServerResponseList<OpenShiftEntity>>

    @GET("v1/table/{outletId}/{lastSync}")
    fun getDiningTable(@Path("outletId") outletId: Int,
                       @Path("lastSync") lastSync: Long = 0): Call<ServerResponseList<DiningTableEntity>>

    @PUT("v1/table")
    fun updateTable(@Body diningTableEntity: DiningTableEntity): Call<ServerResponse<Any>>

    @POST("v1/table")
    fun postTable(@Body diningTableEntity: DiningTableEntity): Call<ServerResponse<Any>>

    @FormUrlEncoded
    @HTTP(method = "DELETE", path = "v1/table", hasBody = true)
    fun deleteTable(@Field("dining_table_id") tableId: String): Call<ServerResponse<Any>>

    @GET("v1/role_mobile/{id}/{lastSync}/1")
    fun getRoleMobile(@Path("id") employeeId: Int,
                      @Path("lastSync") lastSync: Long): Call<ServerResponse<String>>

    @GET("v1/outlet/detail/{outletId}")
    fun getOutletDetail(@Path("outletId") outletId: Int): Call<ServerResponse<Outlet>>

    @POST("v1/outlet/device/lastSync")
    fun updateLastSyncDevice(): Call<ServerResponse<Any>>

    @GET("v1/bank/{outlet_id}")
    fun getBank(@Path("outlet_id") outletId: Int?): Call<ServerResponseList<BankEntity>>

    @GET("v1/employee/{outletId}/{lastSync}/1")
    fun getAllEmployee(@Path("outletId") outletId: Int,
                       @Path("lastSync") lastSync: Long): Call<ServerResponseList<EmployeeEntity>>
}