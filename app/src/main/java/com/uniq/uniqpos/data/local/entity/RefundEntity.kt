package com.uniq.uniqpos.data.local.entity

import android.os.Parcelable
import androidx.room.Entity
import androidx.room.PrimaryKey
import androidx.room.TypeConverters
import com.google.gson.annotations.SerializedName
import com.uniq.uniqpos.data.local.converter.DateConverter
import kotlinx.parcelize.Parcelize

/**
 * Created by ANNASBlackHat on 29/10/2017.
 */

@Entity(tableName = "refund")
@TypeConverters(DateConverter::class)
@Parcelize
data class RefundEntity(
        @PrimaryKey(autoGenerate = true)
        var id: Int? = null,
        var salesId: String? = null,
        @SerializedName("employee_fkid")
        var employeeId: Int,
        var grandTotal: Int,
        var synced: Boolean = false,
        var timeMillis: Long = System.currentTimeMillis(),
        var reason: String = "",
        @SerializedName("time_created")
        val timeCreated: Long = 0
) : Parcelable