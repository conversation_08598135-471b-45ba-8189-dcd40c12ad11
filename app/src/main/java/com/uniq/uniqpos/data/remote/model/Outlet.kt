package com.uniq.uniqpos.data.remote.model


import com.google.gson.annotations.SerializedName

data class Outlet(

        @field:SerializedName("country")
        val country: String? = null,

        @field:SerializedName("receipt_address")
        val receiptAddress: String? = null,

        @field:SerializedName("address")
        val address: String? = null,

        @field:SerializedName("receipt_logo")
        val receiptLogo: String? = null,

        @field:SerializedName("city")
        val city: String? = null,

        @field:SerializedName("payment_compliment")
        val paymentCompliment: Int? = null,

        @field:SerializedName("payment_piutang")
        val paymentPiutang: Int? = null,

        @field:SerializedName("receipt_note")
        var receiptNote: String? = null,

        @field:SerializedName("receipt_socialmedia")
        val receiptSocialmedia: String? = null,

        @field:SerializedName("data_created")
        val dataCreated: String? = null,

        @field:SerializedName("admin_fkid")
        val adminFkid: Int? = null,

        @field:SerializedName("expired_date")
        val expiredDate: String? = null,

        @field:SerializedName("payment_card")
        val paymentCard: Int? = null,

        @field:SerializedName("outlet_id")
        val outletId: Int? = null,

        @field:SerializedName("province")
        val province: String? = null,

        @field:SerializedName("payment_cash")
        val paymentCash: Int? = null,

        @field:SerializedName("payment_duty")
        val paymentDuty: Int? = null,

        @field:SerializedName("payment_instant")
        val paymentInstant: Int? = null,

        @field:SerializedName("phone")
        val phone: String? = null,

        @field:SerializedName("data_modified")
        val dataModified: String? = null,

        @field:SerializedName("name")
        val name: String? = null,

        @field:SerializedName("data_status")
        val dataStatus: String? = null,

        @field:SerializedName("postal_code")
        val postalCode: String? = null,

        @field:SerializedName("receipt_phone")
        val receiptPhone: String? = null,

        val feature: String? = null,

        @field:SerializedName("experiment_config")
        val experimentConfig: String? = null, //json format, experiment config will be set here (from server)

        @field:SerializedName("commission_sharing")
        val commissionSharing: Double? = null
)