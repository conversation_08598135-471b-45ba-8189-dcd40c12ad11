package com.uniq.uniqpos.data.remote.model

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class Promotion(

    @field:SerializedName("code")
    val code: String? = null,

    @field:SerializedName("type")
    val type: String? = null,

    @field:SerializedName("type_id")
    val typeId: Int = 0,

    @field:SerializedName("value")
    val value: String? = null,

    var name: String? = null,
    val source: String? = null,
    var displayVoucher: String? = null,

    @field:SerializedName("promo")
    var promotionDetail: PromotionDetail? = null,

    @field:SerializedName("min_order")
    val minOrder: Int = 0,

    @field:SerializedName("promo_discount_type")
    val promoDiscountType: String? = null,

    @field:SerializedName("promo_discount_maximum")
    val promoDiscountMaximum: Int = 0,

    @field:SerializedName("promo_nominal")
    var promoNominal: Int = 0,

    @field:SerializedName("promotion_value")
    var promotionValue: Int = 0,

    @field:SerializedName("promotion_id")
    var promotionId: Int = 0,

    @field:SerializedName("discount_type")
    var discountType: String? = null,

    var amount: Int = 0,

    @field:SerializedName("member_detail")
    var memberDetail: Member? = null,

    @field:SerializedName("max_qty_promo")
    var maxQtyPromo: Int? = null,

    var appliedByManual: Boolean = true, //if appliedByManual mean the promo scanned by user not automatically by system
    var term: String? = null,
    var usage: ArrayList<PromotionUsage>? = null,

    @field:SerializedName("terms_multiples_apply")
    var termsMultipleApply: Int = 0
) : Parcelable

fun ArrayList<Promotion>.simplify(productDetailIds: List<Int> = listOf()): ArrayList<Promotion> {
    val result = ArrayList<Promotion>()
    this.forEach { promo ->
        val detail = promo.promotionDetail?.simplify(promo, productDetailIds)
        result.add(promo.copy(promotionDetail = detail, usage = null))
    }
    return result
}

fun ArrayList<Promotion>.copy(): ArrayList<Promotion> {
    val result = ArrayList<Promotion>()
    this.forEach { promo ->
        result.add(promo.copy())
    }
    return result
}