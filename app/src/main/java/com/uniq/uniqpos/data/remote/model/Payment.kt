package com.uniq.uniqpos.data.remote.model

import com.google.gson.annotations.SerializedName
import com.uniq.uniqpos.data.local.entity.BankEntity
import java.io.Serializable

/**
 * Created by ANNASBlackHat on 11/01/18.
 */
data class Payment(
        var method: String,
        var total: Int,
        var bank: BankEntity? = null,
        var info: String? = null,
        var pay: Int = total,
        @SerializedName("due_date")
        var dueDate: Long = 0) : Serializable