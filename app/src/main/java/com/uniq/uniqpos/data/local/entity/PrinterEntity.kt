package com.uniq.uniqpos.data.local.entity

import androidx.room.Entity
import androidx.room.Ignore
import androidx.room.PrimaryKey
import android.bluetooth.BluetoothDevice
import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

/**
 * Created by ANNASBlackHat on 18/10/2017.
 */

@Parcelize
@Entity(tableName = "printer")
data class PrinterEntity(
        @PrimaryKey
        @field:SerializedName("mac_address")
        var address: String = "",
        @field:SerializedName("printer_name")
        var name: String? = null,
        var type: String? = null, //bluetooth, usb, wifi

        @field:SerializedName("admin_fkid")
        var adminFkid: Int? = 0,

        @field:SerializedName("outlet_fkid")
        var outletFkid: Int? = 0,

        @field:SerializedName("setting_printpapersize")
        var settingPrintpapersize: Int? = null,

        @field:SerializedName("setting_printreceipt_jumlah")
        var settingPrintreceiptJumlah: String? = "0",

        @field:SerializedName("setting_printorder")
        var settingPrintorder: String? = "0",

        @field:SerializedName("setting_printreceipt")
        var settingPrintreceipt: String? = "0",

        @field:SerializedName("setting_printlabel")
        var settingPrintlabel: String? = "0",

        @field:SerializedName("setting_closingshift")
        var settingClosingshift: String? = "0",

        @field:SerializedName("setting_printreceiptorderid")
        var settingPrintreceiptorderid: String? = "0",

        @Ignore
        var device: BluetoothDevice? = null,
        @field:SerializedName("time_created")
        var timeCreated: Long = System.currentTimeMillis(),
        @field:SerializedName("time_modified")
        var timeModified: Long = System.currentTimeMillis(),
        @field:SerializedName("printer_setting_id")
        var printerSettingId: Int? = 0,
        var synced: Boolean? = false
) : Parcelable