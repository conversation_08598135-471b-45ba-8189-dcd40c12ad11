package com.uniq.uniqpos.data.local.dao

import androidx.room.*
import com.uniq.uniqpos.data.local.entity.LastSyncEntity

/**
 * Created by ANNASBlackHat on 10/11/17.
 */
@Dao
interface LastSyncDao {

    @Query("SELECT * FROM last_sync WHERE tableName = :name LIMIT 1")
    fun getLastSync(name: String): LastSyncEntity?

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun saveLastSync(lastSyncEntity: LastSyncEntity)

    @Update
    fun updateLastSync(lastSyncEntity: LastSyncEntity)

}