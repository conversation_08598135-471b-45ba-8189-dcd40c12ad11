package com.uniq.uniqpos.data.local.converter

import androidx.room.TypeConverter
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.uniq.uniqpos.model.Order
import java.util.*

/**
 * Created by AnnasBlackHat on 22/10/2017.
 */
class OrdersConverter {

    @TypeConverter
    fun fromArrayToString(productDetails: ArrayList<Order?>?): String {
        return Gson().toJson(productDetails)
    }

    @TypeConverter
    fun fromStringToArray(productDetails: String?): ArrayList<Order> {
        val type = object : TypeToken<ArrayList<Order?>?>() {}.type
        return Gson().fromJson(productDetails, type)
    }
}