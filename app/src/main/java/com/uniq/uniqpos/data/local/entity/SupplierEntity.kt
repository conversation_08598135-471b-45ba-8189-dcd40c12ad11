package com.uniq.uniqpos.data.local.entity

import androidx.room.Entity
import androidx.room.PrimaryKey
import com.google.gson.annotations.SerializedName


@Entity(tableName = "supplier")
data class SupplierEntity(

        @PrimaryKey
        @field:SerializedName("supplier_id")
        var supplierId: String = "",

        @field:SerializedName("keterangan")
        var keterangan: String? = null,

        @field:SerializedName("address")
        var address: String? = null,

        @field:SerializedName("city")
        var city: String? = null,

        @field:SerializedName("npwp")
        var npwp: String? = null,

        @field:SerializedName("tempo")
        var tempo: String? = null,

        @field:SerializedName("data_created")
        var dataCreated: String? = null,

        @field:SerializedName("type")
        var type: String? = null,

        @field:SerializedName("admin_fkid")
        var adminFkid: Int? = null,

        @field:SerializedName("phone")
        var phone: String? = null,

        @field:SerializedName("name")
        var name: String? = null,

        @field:SerializedName("data_status")
        var dataStatus: String? = null,

        @field:SerializedName("fax")
        var fax: String? = null,

        @field:SerializedName("email")
        var email: String? = null,

        @field:SerializedName("time_created")
        var timeCreated: Long = System.currentTimeMillis(),

        @field:SerializedName("time_modified")
        var timeModified: Long = 0,

        var synced: Boolean = false
)