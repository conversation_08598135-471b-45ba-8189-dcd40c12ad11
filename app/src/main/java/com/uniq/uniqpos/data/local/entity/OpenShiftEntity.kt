package com.uniq.uniqpos.data.local.entity


import android.os.Parcelable
import androidx.room.Entity
import androidx.room.Ignore
import androidx.room.PrimaryKey
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.IgnoredOnParcel
import kotlinx.android.parcel.Parcelize
import java.io.Serializable

@Entity(tableName = "open_shift")
@Parcelize
data class OpenShiftEntity(

        @PrimaryKey
        @field:SerializedName("open_shift_id")
        var openShiftId: Long,

        @field:SerializedName("time_open")
        var timeOpen: Long,

        @field:SerializedName("employee_fkid")
        var employeeFkid: Int,

        @field:SerializedName("shift_fkid")
        var shiftFkid: Int,

        @field:SerializedName("time_close")
        var timeClose: Long? = null,

        @field:SerializedName("cash_drawer")
        var cashDrawer: Int? = null,

        @field:SerializedName("early_cash")
        var earlyCash: Int? = null,

        @field:SerializedName("outlet_fkid")
        var outletFkid: Int? = null,

        var synced: Boolean = false

) : Parcelable {
        @IgnoredOnParcel
        @Ignore
        var timeOpenFormat: String = ""
}