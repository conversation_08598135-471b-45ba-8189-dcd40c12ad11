package com.uniq.uniqpos.data.remote.model

import com.google.gson.annotations.SerializedName

data class Shift(

	@field:SerializedName("data_modified")
	val dataModified: String? = null,

	@field:SerializedName("shift_id")
	val shiftId: Int? = 0,

	@field:SerializedName("name")
	val name: String? = null,

	@field:SerializedName("data_created")
	val dataCreated: String? = null,

	@field:SerializedName("data_status")
	val dataStatus: String? = null,

	@field:SerializedName("admin_fkid")
	val adminFkid: String? = null
)