package com.uniq.uniqpos.data.remote.model

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize


@Parcelize
data class Member(

        @field:SerializedName("member_id")
        val memberId: String? = null,

        @field:SerializedName("phone")
        val phone: String? = null,

        @field:SerializedName("customer_level")
        val customerLevel: String? = null,

        @field:SerializedName("name")
        val name: String? = null,

        @field:SerializedName("saldo")
        val saldo: String? = null,

        @field:SerializedName("source")
        val source: String? = null,

        @field:SerializedName("promo_id")
        val promoId: String? = null,

        @field:SerializedName("type_fkid")
        val typeId: Int = 0,

        @field:SerializedName("type_name")
        val typeName: String = ""
) : Parcelable