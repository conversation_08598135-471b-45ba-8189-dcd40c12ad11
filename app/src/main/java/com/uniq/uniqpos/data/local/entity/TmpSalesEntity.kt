package com.uniq.uniqpos.data.local.entity

import android.os.Parcelable
import androidx.room.Entity
import androidx.room.PrimaryKey
import com.google.gson.annotations.SerializedName
import com.uniq.uniqpos.util.Constant
import kotlinx.android.parcel.Parcelize

/**
 * Created by ANNASBlackHat on 11/11/17.
 */

@Entity(tableName = "tmp_sales")
@Parcelize
data class TmpSalesEntity(
        @field:SerializedName("no_nota")
        @PrimaryKey
        var noNota: String,
        var sales: String = "",
        @field:SerializedName("outlet_fkid")
        var outletId: Int = 0,
        var status: String = Constant.SALES_STATUS_PENDING,
        var synced: Boolean = false,
        @field:SerializedName("time_modified")
        var timeModified: Long = System.currentTimeMillis(),
        @field:SerializedName("time_created")
        var timeCreated: Long = System.currentTimeMillis()) : Parcelable