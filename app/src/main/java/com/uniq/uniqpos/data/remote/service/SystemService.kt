package com.uniq.uniqpos.data.remote.service

import com.uniq.uniqpos.data.remote.model.*
import com.uniq.uniqpos.model.Device
import com.uniq.uniqpos.model.SubscriptionStatus
import okhttp3.MultipartBody
import okhttp3.RequestBody
import retrofit2.Call
import retrofit2.http.*

/**
 * Created by ANNASBlackHat on 07/12/17.
 */
interface SystemService {

    @GET("v1/history_delete/{lastSync}")
    fun getDeleteHistory(
        @Path("lastSync") lastSync: Long,
        @Query("outlet_id") outletId: Int?
    ): Call<ServerResponseList<DeleteHistory>>

    @GET("v1/device/status/{deviceId}")
    fun getDeviceStatus(@Path("deviceId") deviceId: String): Call<ServerResponse<Device>>

    @Multipart
    @POST("v1/system/log")
    fun uploadLog(
        @PartMap map: Map<String, @JvmSuppressWildcards RequestBody>,
        @Part image: MultipartBody.Part?
    ): Call<ServerResponse<Any>>

    @GET("v1/system/mac_vendor/{macAddress}")
    fun getVendor(@Path("macAddress") macAddress: String): Call<ServerResponse<MacVendor>>

    @GET("v1/billing/subscription/status")
    fun getSubscriptionStatus(@Query("device_id") deviceId: String): Call<ServerResponse<SubscriptionStatus>>

    @GET("v1/system/app_version")
    fun getLatestRelease(): Call<ServerResponse<AppRelease>>
}