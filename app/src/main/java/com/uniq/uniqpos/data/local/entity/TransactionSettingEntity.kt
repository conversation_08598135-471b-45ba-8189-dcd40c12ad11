package com.uniq.uniqpos.data.local.entity

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import com.google.gson.annotations.SerializedName

/**
 * Entity for transaction settings
 */
@Entity(tableName = "transaction_setting")
data class TransactionSettingEntity(
    @PrimaryKey
    @SerializedName("id")
    val id: Int,

    @ColumnInfo(name = "outlet_id")
    @SerializedName("outlet_id")
    val outletId: Int,

    @SerializedName("label")
    val label: String,

    @SerializedName("type")
    val type: String,

    @ColumnInfo(name = "option_values")
    @SerializedName("option_values")
    val optionValues: String?,

    @ColumnInfo(name = "is_required")
    @SerializedName("is_required")
    val isRequired: Boolean,

    @ColumnInfo(name = "display_order")
    @SerializedName("display_order")
    val displayOrder: Int,

    @ColumnInfo(name = "created_at")
    @SerializedName("created_at")
    val createdAt: Long,

    @ColumnInfo(name = "updated_at")
    @SerializedName("updated_at")
    val updatedAt: Long,

    var synced: Boolean = true
)
