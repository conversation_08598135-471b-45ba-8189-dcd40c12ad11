package com.uniq.uniqpos.data.remote.model

import com.google.gson.annotations.SerializedName

data class Employee(

	@field:SerializedName("address")
	val address: String? = null,

	@field:SerializedName("role")
	var role: String? = null,

	@field:SerializedName("role_mobile")
	var roleMobile: String? = null,

	@field:SerializedName("join_date")
	val joinDate: String? = null,

	@field:SerializedName("access_status_mobile")
	val accessStatusMobile: String? = null,

	@field:SerializedName("data_created")
	val dataCreated: String? = null,

	@field:SerializedName("access_mode")
	val accessMode: String? = null,

	@field:SerializedName("jabatan_fkid")
	val jabatanFkid: Int? = null,

	@field:SerializedName("password")
	val password: String? = null,

	@field:SerializedName("user_activation_expired")
	val userActivationExpired: String? = null,

	@field:SerializedName("pin")
	val pin: String? = null,

	@field:SerializedName("phone")
	val phone: String? = null,

	@field:SerializedName("employee_id")
	val employeeId: Int = 0,

	@field:SerializedName("access_status_web")
	val accessStatusWeb: String? = null,

	@field:SerializedName("name")
	val name: String? = null,

	@field:SerializedName("data_status")
	val dataStatus: String? = null,

	@field:SerializedName("email")
	val email: String? = null,

	val level: Int = 0,

	@field:SerializedName("last_login")
	val lastLogin: Long? = null,

	val photo: String? = null,


	@field:SerializedName("admin_fkid")
	val adminFkid: Int? = null,

	@field:SerializedName("date_join")
	val dateJoin: Long? = null
)