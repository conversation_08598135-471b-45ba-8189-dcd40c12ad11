package com.uniq.uniqpos.data.local.entity

import androidx.room.Entity
import androidx.room.PrimaryKey
import android.os.Parcelable
import androidx.room.Ignore
import com.google.gson.annotations.SerializedName
import com.uniq.uniqpos.data.remote.model.Sales
import kotlinx.android.parcel.IgnoredOnParcel
import kotlinx.android.parcel.Parcelize

/**
 * Created by annasblackhat on 08/08/18
 */
@Entity(tableName = "piutang")
@Parcelize
data class PiutangEntity (

        @PrimaryKey(autoGenerate = true)
        @field:SerializedName("piutang_id")
        var piutangId: Long = 0,

        @field:SerializedName("total")
        val total: Int,

        @field:SerializedName("due_date")
        val dueDate: Long,

        @field:SerializedName("sales_fkid")
        val salesFkid: String,

        @field:SerializedName("unpaid")
        val unpaid: Int,

        @field:SerializedName("info")
        val info: String,

        @field:SerializedName("data_modified")
        val dataModified: Long = System.currentTimeMillis(),

        var synced: Boolean = false
) : Parcelable {
        @Ignore
        @IgnoredOnParcel
        var sales: SalesEntity? = null
}