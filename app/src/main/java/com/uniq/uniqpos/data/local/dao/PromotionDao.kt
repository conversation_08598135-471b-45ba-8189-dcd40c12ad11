package com.uniq.uniqpos.data.local.dao

import androidx.lifecycle.LiveData
import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import com.uniq.uniqpos.data.local.entity.PromotionEntity
import com.uniq.uniqpos.util.Constant

/**
 * Created by annasblackhat on 06/05/19
 */
@Dao
interface PromotionDao {
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun savePromotions(promotions: List<PromotionEntity>)

    @Query("SELECT * FROM promotion")
    fun getPromotionLive(): LiveData<List<PromotionEntity>>

    //@Query("SELECT * FROM promotion WHERE active = 1 AND status = 1 AND strftime('%Y-%m-%d', startPromotionDate / 1000, 'unixepoch') <= '2020-03-24' AND strftime('%Y-%m-%d', endPromotionDate / 1000, 'unixepoch')  >= '2020-03-24' AND (sunday = :sun OR monday = :mon OR tuesday = :tue OR wednesday = :wed OR thursday = :thu OR friday = :fri OR saturday = :sat)")
    @Query("SELECT * FROM promotion WHERE active = 1 AND status = 1 AND promotionType != :excludeType AND (strftime('%Y-%m-%d', startPromotionDate / 1000, 'unixepoch') <= :date AND strftime('%Y-%m-%d', endPromotionDate / 1000, 'unixepoch') >= :date) AND (sunday = :sun OR monday = :mon OR tuesday = :tue OR wednesday = :wed OR thursday = :thu OR friday = :fri OR saturday = :sat)")
    fun getActivePromotionLive(date: String, sun: Int, mon: Int, tue: Int, wed: Int, thu: Int, fri: Int, sat: Int, excludeType: String = Constant.PROMO_DEALS): LiveData<List<PromotionEntity>>

    @Query("select * from promotion WHERE active = 1 AND status = 1")
    fun getAllPromotionLive():  LiveData<List<PromotionEntity>>

    @Query("DELETE FROM promotion WHERE promotionId = :id")
    fun deletePromotoionById(id: Int)
}