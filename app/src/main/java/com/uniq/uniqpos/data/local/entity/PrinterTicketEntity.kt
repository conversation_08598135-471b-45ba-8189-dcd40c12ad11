package com.uniq.uniqpos.data.local.entity


import androidx.room.Entity
import androidx.room.PrimaryKey
import androidx.room.TypeConverters
import com.google.gson.annotations.SerializedName
import com.uniq.uniqpos.data.local.converter.PrinterTicketDetailConverter
import com.uniq.uniqpos.model.PrinterTicketDetail

@Entity(tableName = "printer_ticket")
@TypeConverters(PrinterTicketDetailConverter::class)
data class PrinterTicketEntity(

        @PrimaryKey
        @field:SerializedName("printersetting_ticket_id")
        var printersettingTicketId: Int,

        @field:SerializedName("printer_setting_fkid")
        var printerSettingFkid: Int? = null,

        @field:SerializedName("data_modified")
        var dataModified: Long? = null,

        @field:SerializedName("setting_type")
        var settingType: String? = null,

        @field:SerializedName("name")
        var name: String? = null,

        @field:SerializedName("detail")
        var detail: ArrayList<PrinterTicketDetail> = ArrayList(),

        @field:SerializedName("data_created")
        var dataCreated: Long? = null
)