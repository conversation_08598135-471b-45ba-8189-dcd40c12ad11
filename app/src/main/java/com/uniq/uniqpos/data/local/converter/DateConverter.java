package com.uniq.uniqpos.data.local.converter;

import androidx.room.TypeConverter;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * Created by AnnasBlackhat on 22/10/2017.
 */

public class DateConverter {

    public static SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @TypeConverter
    public static String fromDateToString(Date date){
        return sdf.format(date);
    }

    @TypeConverter
    public static Date fromStringToDate(String date){
        try {
            return sdf.parse(date);
        } catch (ParseException e) {
            return new Date();
        }
    }
}