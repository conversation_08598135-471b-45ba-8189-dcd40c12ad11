package com.uniq.uniqpos.data.local.entity


import com.google.gson.annotations.SerializedName
import java.io.Serializable

data class ProductDetail(

	@field:SerializedName("price_sell")
	var priceSell: String? = null,

	@field:SerializedName("product_fkid")
	var productFkid: String? = null,

	@field:SerializedName("price_update")
	var priceUpdate: String? = null,

	@field:SerializedName("price")
	var price: String? = null,

	@field:SerializedName("voucher")
	var voucher: String? = null,

	@field:SerializedName("data_modified")
	var dataModified: String? = null,

	@field:SerializedName("price_start")
	var priceStart: String? = null,

	@field:SerializedName("discount")
	var discount: String? = null,

	@field:SerializedName("active")
	var active: String? = null,

	@field:SerializedName("data_status")
	var dataStatusDetail: String? = null,

	@field:SerializedName("product_detail_id")
	var productDetailId: String? = null,

	@field:SerializedName("outlet_fkid")
	var outletFkid: String? = null
): Serializable