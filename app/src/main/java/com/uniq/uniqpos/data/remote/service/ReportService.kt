package com.uniq.uniqpos.data.remote.service

import com.uniq.uniqpos.data.local.entity.CashRecapEntity
import com.uniq.uniqpos.data.remote.model.OperationalCost
import com.uniq.uniqpos.data.remote.model.ServerResponse
import com.uniq.uniqpos.data.remote.model.ServerResponseList
import retrofit2.Call
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.Path

/**
 * Created by ANNASBlackHat on 28/10/2017.
 */
interface ReportService {

    @POST("v1/cash_recap")
    fun postCashRecap(@Body cashRecapEntity: CashRecapEntity): Call<ServerResponse<Any>>

    @GET("v1/cash_recap/{outletId}/{lastSync}")
    fun getCashRecap(@Path("outletId")outletId: Int, @Path("lastSync")lastSync: Long = 0): Call<ServerResponseList<CashRecapEntity>>

    @GET("v1/operational_cost/{openShiftId}")
    fun getOperationalCost(@Path("openShiftId")openShiftId: Long): Call<ServerResponseList<OperationalCost>>
}