package com.uniq.uniqpos.data.local.converter

import androidx.room.TypeConverter
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.uniq.uniqpos.data.local.entity.PromotionProduct

/**
 * Created by annasblackhat on 03/05/19
 */
class PromotionProductConverter {
    @TypeConverter
    fun fromArrayToJson(array: ArrayList<PromotionProduct>): String {
        return Gson().toJson(array)
    }

    @TypeConverter
    fun fromJsonToArray(json: String?): ArrayList<PromotionProduct> {
        val type = object : TypeToken<ArrayList<PromotionProduct>>() {}.type
        return json?.takeIf { it.isNotEmpty() }?.let {
            Gson().fromJson<ArrayList<PromotionProduct>>(it, type)
        } ?: ArrayList()
    }
}