package com.uniq.uniqpos.data.local.entity

import androidx.room.Entity
import androidx.room.PrimaryKey
import com.google.gson.annotations.SerializedName

@Entity(tableName = "unit")
data class UnitEntity(

        @PrimaryKey
        @field:SerializedName("unit_id")
        val unitId: Int,

        @field:SerializedName("name")
        val name: String,

        @field:SerializedName("admin_fkid")
        val adminFkid: Int,

        @field:SerializedName("data_modified")
        val dataModified: String? = null,

        @field:SerializedName("description")
        val description: String? = null,

        @field:SerializedName("data_created")
        val dataCreated: String? = null,

        @field:SerializedName("data_status")
        val dataStatus: String? = null

)