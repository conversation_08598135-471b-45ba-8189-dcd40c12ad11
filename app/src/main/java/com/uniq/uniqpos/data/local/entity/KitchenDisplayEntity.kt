package com.uniq.uniqpos.data.local.entity


import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import androidx.room.TypeConverters
import com.google.gson.annotations.SerializedName
import com.uniq.uniqpos.data.local.converter.KitchenDisplayConverter

@Entity(tableName = "kitchen_display")
@TypeConverters(KitchenDisplayConverter::class)
data class KitchenDisplayEntity(
    @PrimaryKey
    @ColumnInfo(name = "setting_kitchen_display_id")
    @SerializedName("setting_kitchen_display_id")
    val settingKitchenDisplayId: Int,

    @SerializedName("name")
    val name: String,
    @SerializedName("address")
    val address: String,
    @SerializedName("categories")
    val categories: ArrayList<KitchenDisplayCategory>,
    @ColumnInfo(name = "data_created")
    @SerializedName("data_created")
    val dataCreated: Long = System.currentTimeMillis(),

    @ColumnInfo(name = "data_modified")
    @SerializedName("data_modified")
    val dataModified: Long = System.currentTimeMillis(),

    @ColumnInfo(name = "outlet_fkid")
    @SerializedName("outlet_fkid")
    val outletFkid: Int,
    var synced: Boolean? = false
)