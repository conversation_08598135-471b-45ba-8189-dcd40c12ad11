package com.uniq.uniqpos.data.remote.service

import com.uniq.uniqpos.data.local.entity.OperationalCostEntity
import com.uniq.uniqpos.data.local.entity.SupplierEntity
import com.uniq.uniqpos.data.remote.model.ServerResponse
import com.uniq.uniqpos.data.remote.model.ServerResponseList
import retrofit2.Call
import retrofit2.http.*

/**
 * Created by annas<PERSON>ck<PERSON> on 14/02/19
 */
interface PurchaseService {

    @GET("v1/purchase/supplier/{last_sync}")
    fun getSupplier(@Path("last_sync")lastSync: Long = 0): Call<ServerResponseList<SupplierEntity>>

    @POST("v1/purchase/supplier")
    fun addSupplier(@Body supplierEntity: SupplierEntity): Call<ServerResponse<String>>

    @POST("v1/purchase/operational_cost")
    fun saveOperationalCost(@Body operationalCostEntity: OperationalCostEntity): Call<ServerResponse<OperationalCostEntity>>

    @GET("v1/purchase/operational_cost/{outletId}/{last_sync}")
    fun getOperationalCost(@Path("outletId")outletId: Int?,
                           @Path("last_sync")lastSync: Long = 0): Call<ServerResponseList<OperationalCostEntity>>

    @FormUrlEncoded
    @POST("v1/purchase/purchase_report_category")
    fun addPurchaseReportCategory(@Field("category_name")categoryName: String): Call<ServerResponse<String>>
}