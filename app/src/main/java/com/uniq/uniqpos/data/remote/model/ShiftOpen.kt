package com.uniq.uniqpos.data.remote.model


import com.google.gson.annotations.SerializedName

data class ShiftOpen(

	@field:SerializedName("time_open")
	val timeOpen: Long = System.currentTimeMillis(),

	@field:SerializedName("employee_fkid")
	val employeeFkid: Int = 0,

	@field:SerializedName("shift_fkid")
	val shiftFkid: Int = 0,

	@field:SerializedName("time_close")
	val timeClose: Long? = System.currentTimeMillis(),

	@field:SerializedName("open_shift_id")
	val openShiftId: Long = 0,

	@field:SerializedName("early_cash")
	val earlyCash: Int? = 0,

	@field:SerializedName("outlet_fkid")
	val outletFkid: Int? = 0
)