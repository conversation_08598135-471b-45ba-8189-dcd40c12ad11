package com.uniq.uniqpos.data.local.dao

import androidx.lifecycle.LiveData
import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import com.uniq.uniqpos.data.local.entity.MemberEntity
import com.uniq.uniqpos.data.local.entity.MemberTypeEntity

@Dao
interface CustomerDao {
    @Query("SELECT * FROM member_type mt ORDER BY mt.name")
    fun getMemberType(): List<MemberTypeEntity>

    @Query("SELECT * FROM member_type mt ORDER BY mt.name")
    fun getMemberTypeLive(): LiveData<List<MemberTypeEntity>>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun saveMemberTypes(memberTypeEntity: List<MemberTypeEntity>)

    @Query("SELECT * FROM member m ORDER BY m.name")
    fun getMemberLive(): LiveData<List<MemberEntity>>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun saveMember(memberEntity: List<MemberEntity>)

//    @Insert(onConflict = OnConflictStrategy.REPLACE)
//    fun saveMember(memberEntity: MemberEntity)

//    @Query("SELECT m.*, mt.* FROM member m INNER JOIN member_type mt ON m.typeFkid=mt.typeId")
//    fun getMemberAndType(): List<MemberWithType>
}