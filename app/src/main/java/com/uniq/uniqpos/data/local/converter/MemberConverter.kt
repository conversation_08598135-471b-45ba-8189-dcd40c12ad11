package com.uniq.uniqpos.data.local.converter

import androidx.room.TypeConverter
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.uniq.uniqpos.data.local.entity.MemberEntity

/**
 * Created by annasblackhat on 16/04/18
 */
class MemberConverter {
    @TypeConverter
    fun fromObjectToArray(dataList: ArrayList<MemberEntity>): String {
        return Gson().toJson(dataList)
    }

    @TypeConverter
    fun fromStringToObject(json: String): ArrayList<MemberEntity> {
        val type = object : TypeToken<ArrayList<MemberEntity>>() {}.type
        return Gson().fromJson<ArrayList<MemberEntity>>(json, type)
    }
}