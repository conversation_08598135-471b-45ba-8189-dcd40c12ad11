package com.uniq.uniqpos.data.local.converter

import androidx.room.TypeConverter
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.uniq.uniqpos.data.local.entity.KitchenDisplayCategory

class KitchenDisplayConverter {
    @TypeConverter
    fun fromObjectToArray(dataList: ArrayList<KitchenDisplayCategory>): String {
        return Gson().toJson(dataList)
    }

    @TypeConverter
    fun fromStringToObject(json: String): ArrayList<KitchenDisplayCategory> {
        val type = object : TypeToken<ArrayList<KitchenDisplayCategory>>() {}.type
        return Gson().fromJson(json, type)
    }
}