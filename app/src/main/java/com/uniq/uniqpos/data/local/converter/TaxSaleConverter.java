package com.uniq.uniqpos.data.local.converter;

import androidx.room.TypeConverter;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.uniq.uniqpos.model.TaxSales;
import java.lang.reflect.Type;
import java.util.ArrayList;


/**
 * Created by ANNASBlackHat on 21/10/2017.
 */

public class TaxSaleConverter {
    @TypeConverter
    public static String fromArrayToString(ArrayList<TaxSales> taxSales){
        return new Gson().toJson(taxSales);
    }

    @TypeConverter
    public static ArrayList<TaxSales> fromStringToArray(String taxSales){
        Type type = new TypeToken<ArrayList<TaxSales>>(){}.getType();
        return new Gson().fromJson(taxSales, type);
    }
}
