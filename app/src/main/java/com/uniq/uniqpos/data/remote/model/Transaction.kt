package com.uniq.uniqpos.data.remote.model


import com.google.gson.annotations.SerializedName

data class Transaction(
    @SerializedName("payment_timeout")
    val paymentTimeout: Int,
    @SerializedName("refund_policy")
    val refundPolicy: String,
    @SerializedName("refund_policy_detail")
    val refundPolicyDetail: RefundPolicyDetail,
    @SerializedName("stock_confirmation")
    val stockConfirmation: Boolean
)