package com.uniq.uniqpos.data.local.entity


import android.os.Parcelable
import androidx.room.Entity
import androidx.room.Ignore
import androidx.room.PrimaryKey
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.IgnoredOnParcel
import kotlinx.android.parcel.Parcelize

/**
 * Created by ANNASBlackHat on 26/10/2017.
 */

@Entity(tableName = "cash_recap")
@Parcelize
data class CashRecapEntity(

        @PrimaryKey
        @field:SerializedName("cash_recap_id")
        var id: Long,

        @field:SerializedName("cash")
        var cash: Int,

        @field:SerializedName("card")
        var card: Int,

        @field:SerializedName("sales_total")
        var salesTotal: Int = 0,

        @field:SerializedName("pending_bill_total")
        val pendingBillTotal: Int = 0,

        @field:SerializedName("outlet_fkid")
        var outletId: Int? = 0,

        @field:SerializedName("employee_fkid")
        var employeeId: Int? = 0,

        @field:SerializedName("name")
        var employeeName: String? = null,

        @field:SerializedName("previous_recap")
        var previousRecap: Long,

        @field:SerializedName("open_shift_fkid")
        var openShiftId: Long = 0,

        @field:SerializedName("time_created")
        var timeCreated: Long = System.currentTimeMillis(),

        @field:SerializedName("time_modified")
        var timeModified: Long = System.currentTimeMillis(),

        var synced: Boolean = true,

        @field:SerializedName("outlet_commission")
        var outletCommission: Int? = null,
) : Parcelable {
    @IgnoredOnParcel
    @Ignore
    var shiftName: String = ""
}