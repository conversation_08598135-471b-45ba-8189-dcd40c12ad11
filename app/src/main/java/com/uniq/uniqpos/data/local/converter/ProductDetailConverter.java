package com.uniq.uniqpos.data.local.converter;

import androidx.room.TypeConverter;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.uniq.uniqpos.data.local.entity.ProductDetail;

import java.lang.reflect.Type;

/**
 * Created by ANNASBlackHat on 21/10/2017.
 */

public class ProductDetailConverter {
    @TypeConverter
    public static String fromArrayToString(ProductDetail productDetails){
        return new Gson().toJson(productDetails);
    }

    @TypeConverter
    public static ProductDetail fromStringToArray(String productDetails){
        Type type = new TypeToken<ProductDetail>(){}.getType();
        return new Gson().fromJson(productDetails, type);
    }
}
