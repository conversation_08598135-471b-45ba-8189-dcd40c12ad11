package com.uniq.uniqpos.data.local.entity

import androidx.room.Entity
import androidx.room.Ignore
import androidx.room.PrimaryKey
import android.os.Parcelable
import com.google.gson.Gson
import com.google.gson.annotations.SerializedName
import com.google.gson.reflect.TypeToken
import kotlinx.android.parcel.IgnoredOnParcel
import kotlinx.android.parcel.Parcelize
import java.io.Serializable

@Entity(tableName = "product")
@Parcelize
//@TypeConverters(ProductDetailConverter::class)
data class ProductEntity(

        @field:SerializedName("product_id")
        var productId: Long,

        @field:SerializedName("price_buy")
        var priceBuy: Int? = null,

        @field:SerializedName("price_sell")
        var priceSell: Int? = null,

        @field:SerializedName("product_fkid")
        val productFkid: Int? = null,

        @field:SerializedName("product_type_fkid")
        val productTypeFkid: Int? = null,

        @field:SerializedName("catalogue_type")
        val catalogueType: String? = null,

        @field:SerializedName("voucher")
        val voucher: String? = null,

        @field:SerializedName("discount")
        var discount: String? = null,

        @field:SerializedName("data_created")
        val dataCreated: Long? = null,

        @field:SerializedName("stock_management")
        var stockManagement: Int? = null,

        @field:SerializedName("data_modified")
        val dataModified: Long? = null,

        @field:SerializedName("data_status")
        val dataStatus: String? = null,

        @field:SerializedName("sku")
        var sku: String? = null,

        @field:SerializedName("barcode")
        var barcode: String? = null,

        @PrimaryKey
        @field:SerializedName("product_detail_id")
        val productDetailId: Int,

        @field:SerializedName("unit_fkid")
        val unitFkid: Int? = null,

        @field:SerializedName("active")
        var active: String? = null,

        @field:SerializedName("photo")
        var photo: String? = null,

        @field:SerializedName("admin_fkid")
        val adminFkid: Int? = null,

        @field:SerializedName("price_buy_start")
        val priceBuyStart: Int? = null,

        @field:SerializedName("product_subcategory_fkid")
        val productSubcategoryFkid: Int = 0,

        @field:SerializedName("purchase_report_category_fkid")
        val purchaseReportCategoryFkid: Int? = null,

        @field:SerializedName("name")
        var name: String? = null,

        @field:SerializedName("product_category_fkid")
        val productCategoryFkid: Int? = null,

        @field:SerializedName("outlet_fkid")
        var outletFkid: Int? = null,

        @field:SerializedName("variant_fkid")
        var variantFkid: Int? = null,

        @field:SerializedName("product_type_name")
        var productTypeName: String? = null,

        @field:SerializedName("product_category_name")
        var productcategoryName: String? = null,

        @field:SerializedName("product_subcategory_name")
        var productSubCategoryName: String? = null,

        @field:SerializedName("purchase_report_category_name")
        var purchaseReportCategoryName: String? = null,

        @field:SerializedName("unit_name")
        var unitName: String? = null,

        @field:SerializedName("stock_qty")
        var stockQty: Int = 0,

        var stock: String = "available",
        var sync: Boolean = false

) : Parcelable {
        @IgnoredOnParcel
        @Ignore
        var isShowStock: Boolean = false
        get() {
                return (stockManagement == 1 && stockQty > 0)
        }
}

fun ProductEntity.ToHashMap(): HashMap<String, Any?> {
        return Gson().toJson(this).let {
                try {
                        Gson().fromJson(it, HashMap::class.java) as HashMap<String, Any?>
                } catch (e: Exception) {
                        println("Error converting ProductEntity to HashMap: ${e.message}")
                        HashMap()
                }
        }
}

fun ProductEntity.ToHashMapString(): HashMap<String, String?> {
        return Gson().toJson(this).let {
                try {
                        Gson().fromJson(it, object : TypeToken<HashMap<String, String?>>() {}.type) as HashMap<String, String?>
                } catch (e: Exception) {
                        println("Error converting ProductEntity to HashMap<String, String?>: ${e.message}")
                        HashMap()
                }
        }
}