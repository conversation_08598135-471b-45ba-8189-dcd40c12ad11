package com.uniq.uniqpos.data.local.entity

import androidx.room.Entity
import androidx.room.Ignore
import androidx.room.PrimaryKey
import androidx.room.TypeConverters
import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import com.uniq.uniqpos.data.local.converter.PromotionMemberTypeConverter
import com.uniq.uniqpos.data.local.converter.PromotionProductConverter
import com.uniq.uniqpos.util.dateFormat
import com.uniq.uniqpos.util.toTimeMillis
import kotlinx.android.parcel.IgnoredOnParcel
import kotlinx.android.parcel.Parcelize


@Parcelize
@Entity(tableName = "promotion")
@TypeConverters(PromotionProductConverter::class, PromotionMemberTypeConverter::class)
data class PromotionEntity(

        @PrimaryKey
        @field:SerializedName("promotion_id")
        var promotionId: Int = 0,

        @field:SerializedName("promotion_product")
        var promotionProduct: ArrayList<PromotionProduct> = ArrayList(),

        @field:SerializedName("prove")
        var prove: String? = null,

        @field:SerializedName("is_percent")
        var usePercent: Int? = null,

        @field:SerializedName("input_deals")
        var inputDeals: String? = null,

        @field:SerializedName("voucher_code")
        var voucherCode: String? = null,

        @field:SerializedName("deals_value")
        var dealsValue: Int? = null,

        @field:SerializedName("promo_nominal")
        var promoNominal: Int? = null,

        @field:SerializedName("point")
        var point: Int? = null,

        @field:SerializedName("promotion_type_id")
        var promotionTypeId: Int? = null,

        @field:SerializedName("start_promotion_date")
        var startPromotionDate: Long = 0,

        @field:SerializedName("end_promotion_date")
        var endPromotionDate: Long = 0,

        @field:SerializedName("start_promotion_time")
        var startPromotionTime: String = "",

        @field:SerializedName("end_promotion_time")
        var endPromotionTime: String = "",

        @field:SerializedName("maximum_redeem")
        var maximumRedeem: Int? = null,

        @field:SerializedName("maximum_redeem_period")
        var maximumRedeemPeriod: Int? = null,

        @field:SerializedName("voucher_price_type")
        var voucherPriceType: String? = null,

        @field:SerializedName("term")
        var term: String? = null,

        @field:SerializedName("ammount")
        var ammount: String? = null,

        @field:SerializedName("promotion_type")
        var promotionType: String? = null,

        @field:SerializedName("modified")
        var modified: Long = 0,

        @field:SerializedName("created")
        var created: Long = 0,

        @field:SerializedName("active")
        var active: Int = 0,

        @field:SerializedName("admin_fkid")
        var adminFkid: Int = 0,

        @field:SerializedName("voucher_price_value")
        var voucherPriceValue: Int? = null,

        @field:SerializedName("sunday")
        var sunday: Int = 0,

        @field:SerializedName("monday")
        var monday: Int = 0,

        @field:SerializedName("tuesday")
        var tuesday: Int = 0,

        @field:SerializedName("wednesday")
        var wednesday: Int = 0,

        @field:SerializedName("thursday")
        var thursday: Int = 0,

        @field:SerializedName("friday")
        var friday: Int = 0,

        @field:SerializedName("saturday")
        var saturday: Int = 0,

        @field:SerializedName("promo_type")
        var promoType: String? = null,

        @field:SerializedName("name")
        var name: String? = null,

        @field:SerializedName("min_order")
        var minOrder: Int = 0,

        @field:SerializedName("option")
        var option: String? = null,

        @field:SerializedName("member_type")
        var memberType: ArrayList<MemberTypeEntity>? = null,

        var status: Int = 1,

        @field:SerializedName("discount_type")
        var discountType: String? = null,

        @field:SerializedName("maximum_discount_nominal")
        var maximumDiscountNominal: Int = 0,

        @field:SerializedName("max_qty_promo")
        var maxQtyPromo: Int? = null,

        @field:SerializedName("terms_multiples_apply")
        var termsMultipleApply: Int? = null
) : Parcelable {

    @Ignore
    @IgnoredOnParcel
    var isInRange: Boolean = false
        get() {
            val dateFormat = "dd-MM-yyyy"
            val timeFormat = "HH:mm:ss"
            val dateTimeFormat = "$dateFormat $timeFormat"
//            val dateNow = System.currentTimeMillis().dateFormat(dateFormat)
            val now = System.currentTimeMillis()
//            return "${startPromotionDate.dateFormat(dateFormat)} $startPromotionTime".toTimeMillis(dateTimeFormat) <= now && "${endPromotionDate.dateFormat(dateFormat)} $endPromotionTime".toTimeMillis(dateTimeFormat) > now
//            return now in startPromotionDate until endPromotionDate && "${now.dateFormat(dateFormat)} $startPromotionTime".toTimeMillis(dateTimeFormat) <= now && "${now.dateFormat(dateFormat)} $endPromotionTime".toTimeMillis(dateTimeFormat) > now
            return (now in "${startPromotionDate.dateFormat(dateFormat)} 00:01:00".toTimeMillis(dateTimeFormat) until "${endPromotionDate.dateFormat(dateFormat)} 23:59:00".toTimeMillis(dateTimeFormat)) && (now in "${now.dateFormat(dateFormat)} $startPromotionTime".toTimeMillis(dateTimeFormat) until "${now.dateFormat(dateFormat)} $endPromotionTime".toTimeMillis(dateTimeFormat))
//                return true
        }
}