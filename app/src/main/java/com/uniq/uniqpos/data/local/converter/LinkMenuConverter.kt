package com.uniq.uniqpos.data.local.converter

import androidx.room.TypeConverter
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.uniq.uniqpos.model.LinkMenuDetail

/**
 * Created by annasblackhat on 22/03/18.
 */
class LinkMenuConverter {
    @TypeConverter
    fun fromObjectToArray(dataList: ArrayList<LinkMenuDetail>): String {
        return Gson().toJson(dataList)
    }

    @TypeConverter
    fun fromStringToObject(json: String): ArrayList<LinkMenuDetail> {
        val type = object : TypeToken<ArrayList<LinkMenuDetail>>() {}.type
        return Gson().fromJson<ArrayList<LinkMenuDetail>>(json, type)
    }
}