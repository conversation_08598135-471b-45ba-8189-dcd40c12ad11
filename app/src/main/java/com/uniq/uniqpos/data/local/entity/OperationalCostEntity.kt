package com.uniq.uniqpos.data.local.entity

import androidx.room.Entity
import androidx.room.PrimaryKey
import com.google.gson.annotations.SerializedName

/**
 * Created by annasblackhat on 15/02/19
 */
@Entity(tableName = "operational_cost")
data class OperationalCostEntity (

        @PrimaryKey
        @field:SerializedName("operationalcost_id")
        var operationalcostId: String = "",

        @field:SerializedName("keterangan")
        var keterangan: String? = null,

        @field:SerializedName("user_fkid")
        var userFkid: Int? = null,

        @field:SerializedName("shift_fkid")
        var shiftFkid: String? = null,

        @field:SerializedName("employe_fkid")
        var employeFkid: Int? = null,

        @field:SerializedName("data_created")
        var dataCreated: String? = null,

        @field:SerializedName("opcost_name")
        var opcostName: String? = null,

        @field:SerializedName("supplier_fkid")
        var supplierFkid: String = "",

        @field:SerializedName("time_updated")
        var timeUpdated: Long = 0,

        @field:SerializedName("total")
        var total: Int = 0,

        @field:SerializedName("harga")
        var harga: Int = 0,

        @field:SerializedName("qty")
        var qty: Int = 0,

        @field:SerializedName("prc_category_fkid")
        var prcCategoryFkid: String? = null,

        @field:SerializedName("time_created")
        var timeCreated: Long? = null,

        @field:SerializedName("payment")
        var payment: String? = null,

        @field:SerializedName("purchase_category_name")
        var purchaseCategoryName: String? = null,

        @field:SerializedName("data_status")
        var dataStatus: String? = null,

        @field:SerializedName("payment_bank_fkid")
        var paymentBankFkid: String? = null,

        @field:SerializedName("bank_name")
        var bankName: String? = null,

        @field:SerializedName("supplier_name")
        var supplierName: String? = null,

        @field:SerializedName("outlet_fkid")
        var outletFkid: Int = 0,

        var synced: Boolean = true
)