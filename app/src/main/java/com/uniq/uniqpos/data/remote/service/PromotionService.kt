package com.uniq.uniqpos.data.remote.service

import com.uniq.uniqpos.data.local.entity.PromotionEntity
import com.uniq.uniqpos.data.remote.model.PromotionUsageResponse
import com.uniq.uniqpos.data.remote.model.ServerResponseList
import retrofit2.Call
import retrofit2.http.GET
import retrofit2.http.Path
import retrofit2.http.Query

/**
 * Created by annasblackhat on 06/05/19
 */
interface PromotionService {
    @GET("v1/promotion/{outletId}/{lastSync}")
    fun getPromotion(@Path("outletId")outletId: Int,
                     @Path("lastSync")lastSync: Long = 0): Call<ServerResponseList<PromotionEntity>>

    @GET("v1/promotion-usage")
    fun getPromotionUsage(@Query("member_id")memberId: Int,
    @Query("promo_parent_type")promoParentType: String): Call<ServerResponseList<PromotionUsageResponse>>
}