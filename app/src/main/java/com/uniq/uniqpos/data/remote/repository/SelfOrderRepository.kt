package com.uniq.uniqpos.data.remote.repository

import com.uniq.uniqpos.data.local.dao.ProductDao
import com.uniq.uniqpos.data.remote.service.SelfOrderService
import javax.inject.Inject

class SelfOrderRepository @Inject constructor( val service: SelfOrderService, val productDao: ProductDao) {

    fun getProducts() = productDao.getProducts()

    fun getSelfOrder(outletId: Int) = service.getSelfOrder(outletId)

}