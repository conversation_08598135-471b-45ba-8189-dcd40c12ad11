package com.uniq.uniqpos.data.remote.service

import com.uniq.uniqpos.data.remote.model.ServerResponse
import com.uniq.uniqpos.data.remote.model.ServerResponseList
import com.uniq.uniqpos.model.Billing
import com.uniq.uniqpos.model.BillingService
import retrofit2.Call
import retrofit2.http.Field
import retrofit2.http.FormUrlEncoded
import retrofit2.http.GET
import retrofit2.http.POST

interface BillingService {

    @GET("/v1/billing/service")
    fun getBillingService(): Call<ServerResponseList<BillingService>>

    @FormUrlEncoded
    @POST("/v1/billing/subscription")
    fun createBilling(@Field("service_id") serviceId: Int,
                      @Field("period") period: Int,
                      @Field("payment_method") paymentMethod: String,
                      @Field("slot") slot: Int): Call<ServerResponse<Billing>>
}