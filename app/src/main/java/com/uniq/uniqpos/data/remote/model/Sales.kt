package com.uniq.uniqpos.data.remote.model

import com.google.gson.annotations.SerializedName
import com.uniq.uniqpos.data.local.entity.RefundEntity
import com.uniq.uniqpos.data.local.entity.SalesTagEntity


data class Sales(

		@field:SerializedName("employee_fkid")
		val employeeFkid: Int = 0,

		@field:SerializedName("shift_fkid")
		val shiftFkid: Int? = null,

		@field:SerializedName("date_created")
		val dateCreated: String? = null,

		@field:SerializedName("discount")
		val discount: Int? = null,

		@field:SerializedName("tax")
		val tax: List<TaxItem>? = null,

		@field:SerializedName("qty_customers")
		val qtyCustomers: Int? = null,

		@field:SerializedName("date_modified")
		val dateModified: String? = null,

		@field:SerializedName("time_prediction")
		val timePrediction: Int? = null,

		@field:SerializedName("payment")
		val payment: String? = null,

		@field:SerializedName("grand_total")
		val grandTotal: Int? = null,

		@field:SerializedName("customer_name")
		val customerName: String? = null,

		@field:SerializedName("detail")
		val detail: List<DetailItem>? = null,

		@field:SerializedName("data_status")
		val dataStatus: String? = null,

		@field:SerializedName("sales_id")
		val salesId: String,

		@field:SerializedName("outlet_fkid")
		val outletFkid: Int = 0,

		@field:SerializedName("status")
		val status: String? = null,

		@field:SerializedName("outlet_name")
		val outletName: String? = null,

		@field:SerializedName("time_created")
		val timeCreated: Long? = null,

		@field:SerializedName("time_modified")
		val timeModified: Long? = null,

		@field:SerializedName("open_shift_fkid")
		val openShiftFkid: Long = 0,

		val payments: ArrayList<Payment>? = null,

		@field:SerializedName("discount_info")
		val discountInfo: String? = null,

		@field:SerializedName("voucher")
		val voucher: Int? = null,

		@field:SerializedName("voucher_info")
		val voucherInfo: String? = null,

		@field:SerializedName("display_nota")
		val displayNota: String? = null,

		val promotion: ArrayList<Promotion>? = null,

		val reason: String? = null,

		@field:SerializedName("receipt_receiver")
		val receiptReceiver: String? = null,

		@field:SerializedName("dining_table")
		val diningTable: String? = null,

		@field:SerializedName("sales_tag")
		val salesTag: SalesTagEntity? = null,

		@field:SerializedName("sales_refund")
		val salesRefund: RefundEntity? = null
)