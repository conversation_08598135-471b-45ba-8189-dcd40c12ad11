package com.uniq.uniqpos.data.local.entity

import android.os.Parcelable
import androidx.room.Entity
import androidx.room.PrimaryKey
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

@Parcelize
@Entity(tableName = "piutang_history")
data class PiutangHistoryEntity(

        @PrimaryKey(autoGenerate = true)
        @field:SerializedName("piutang_history_id")
        var piutangHistoryId: Long = 0,

        @field:SerializedName("sales_payment_fkid")
        var salesPaymentFkid: Int = 0,

        @field:SerializedName("total")
        var total: Int,

        @field:SerializedName("employee_fkid")
        var employeeFkid: Int,

        @field:SerializedName("method")
        var method: String,

        @field:SerializedName("data_modified")
        var dataModified: Long = System.currentTimeMillis(),

        @field:SerializedName("payment_id")
        var paymentId: Int = 0,

        @field:SerializedName("piutang_fkid")
        var piutangFkid: Long,

        @field:SerializedName("pay")
        var pay: Int,

        @field:SerializedName("sales_fkid")
        var salesFkid: String = "",

        @field:SerializedName("data_created")
        var dataCreated: Long = System.currentTimeMillis(),

        @field:SerializedName("info")
        var info: String = "",

        var synced: Boolean = false
) : Parcelable