package com.uniq.uniqpos.data.local.entity


import android.os.Parcelable
import androidx.room.Entity
import androidx.room.PrimaryKey
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize


@Entity(tableName = "sales_tag")
@Parcelize
data class SalesTagEntity(
    @PrimaryKey
    @SerializedName("sales_tag_id")
    val salesTagId: Int,
    @SerializedName("admin_fkid")
    val adminFkid: Int,
    @SerializedName("data_status")
    val dataStatus: String,
    @SerializedName("date_created")
    val dateCreated: Long,
    @SerializedName("date_modified")
    val dateModified: Long,
    @SerializedName("name")
    val name: String,
): Parcelable