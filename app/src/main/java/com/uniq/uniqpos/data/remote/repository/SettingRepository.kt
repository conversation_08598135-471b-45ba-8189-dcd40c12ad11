package com.uniq.uniqpos.data.remote.repository

import android.content.Context
import androidx.lifecycle.LiveData
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.uniq.uniqpos.data.local.dao.DeleteRecordDao
import com.uniq.uniqpos.data.local.dao.LastSyncDao
import com.uniq.uniqpos.data.local.dao.SettingDao
import com.uniq.uniqpos.data.local.entity.*
import com.uniq.uniqpos.data.local.sharedpref.SharedPref
import com.uniq.uniqpos.data.local.sharedpref.sharedPref
import com.uniq.uniqpos.data.remote.NetworkBoundResource
import com.uniq.uniqpos.data.remote.Resource
import com.uniq.uniqpos.data.remote.model.ServerResponseList
import com.uniq.uniqpos.data.remote.service.SettingService
import com.uniq.uniqpos.model.SubscriptionStatus
import com.uniq.uniqpos.util.BACKGROUND
import com.uniq.uniqpos.util.await
import com.uniq.uniqpos.util.awaitBase
import com.uniq.uniqpos.util.awaitListBase
import com.uniq.uniqpos.util.file.downloadFile
import com.uniq.uniqpos.util.safe
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import timber.log.Timber
import javax.inject.Inject
import kotlin.coroutines.resume
import kotlin.coroutines.suspendCoroutine

/**
 * Created by ANNASBlackHat on 18/10/2017.
 */

class SettingRepository @Inject constructor(private val settingDao: SettingDao,
                                            private val settingService: SettingService,
                                            private val deleteRecordDao: DeleteRecordDao,
                                            private val lastSyncDao: LastSyncDao) {

    val printers: LiveData<List<PrinterEntity>> by lazy { settingDao.getPrintersLive() }

    fun getPrinters() = settingDao.getPrinters()
    fun getPrintersClosing() = settingDao.getPrinterClosingShift()

    suspend fun getPrinterCloseShiftByPrinterId(id: Int): List<PrinterClosingShiftEntity> {
        return suspendCoroutine { continuation ->
            BACKGROUND.submit {
                continuation.resume(settingDao.getPrinterCloseShiftByPrinterId(id))
            }
        }
    }

    suspend fun getPrinterTicketOrders(): List<PrinterTicketEntity> {
        return suspendCoroutine { continuation ->
            BACKGROUND.submit {
                continuation.resume(settingDao.getPrinterTicketOrders())
            }
        }
    }

    suspend fun getPrintTicketOrderByPrinterId(id: Int): List<PrinterTicketEntity> {
        return suspendCoroutine { continuation ->
            BACKGROUND.submit {
                continuation.resume(settingDao.getPrinterTicketOrderById(id))
            }
        }
    }

    suspend fun getPrinterList(): List<PrinterEntity> {
        return suspendCoroutine { continuation ->
            BACKGROUND.submit {
                continuation.resume(settingDao.getPrinters())
            }
        }
    }

    suspend fun getSubscriptionStatus(deviceId: String): SubscriptionStatus {
        return suspendCoroutine { continuation ->
            Timber.i("[subscription] geting subs....")
            BACKGROUND.submit {
                settingService.getSubscriptionStatus(deviceId).await { data ->
                    Timber.i("[subscription] data : $data")
                    data?.let { continuation.resume(it) }
                }
            }
        }
    }

    suspend fun addPrinter(printerEntity: PrinterEntity) {
        withContext(Dispatchers.IO) {
            settingDao.addPrinter(printerEntity)
            settingService.addPrinterSetting(printerEntity.copy(synced = null)).await {
                settingDao.updatePrinter(printerEntity.copy(synced = true))
            }
        }
    }

    suspend fun updatePrinterByAddress(update: PrinterEntity) {
        withContext(Dispatchers.IO) {
            val printer = settingDao.getPrinterByAddress(update.address)
            settingDao.updatePrinter(printer.copy(
                    synced = false,
                    settingPrintpapersize = update.settingPrintpapersize, settingPrintreceipt = update.settingPrintreceipt,
                    settingPrintorder = update.settingPrintorder, settingClosingshift = update.settingClosingshift,
                    settingPrintreceiptJumlah = update.settingPrintreceiptJumlah,
                    name = update.name))
        }
    }

    suspend fun deletePrinter(printerEntity: PrinterEntity, outletId: Int?) {
        withContext(Dispatchers.IO) {
            settingDao.deletePrinter(printerEntity)
            val status = settingService.deletePrinterSetting(printerEntity).await { }
            if (!status) {
                val data = Gson().toJson(hashMapOf("outletid" to outletId, "data" to Gson().toJson(printerEntity)))
                deleteRecordDao.saveDeleteRecord(DeleteRecordEntity(tableName = PrinterEntity::class.java.simpleName, data = data))
            }
        }
    }

    //run in background
    fun syncSetting(outletId: Int?, context: Context? = null) {
        deleteRecordDao.getDeleteRecord(PrinterEntity::class.java.simpleName)
                .forEach { item ->
                    val type = object : TypeToken<Map<String, String>>() {}.type
                    val map = Gson().fromJson<Map<String, String>>(item.data, type)
                    val printer = Gson().fromJson(map["data"], PrinterEntity::class.java)
                    val savedPrinter = settingDao.getPrinters()
                    savedPrinter.firstOrNull { it.address == printer.address }?.let {
                        deleteRecordDao.delete(item)
                    } ?: kotlin.run {
                        settingService.deletePrinterSetting(printer).await {
                            deleteRecordDao.delete(item)
                        }
                    }
                }

        outletId?.let { id ->
            val unSync = settingDao.getUnSyncedPrinter()
            unSync.forEach { printer ->
                settingService.addPrinterSetting(printer).awaitBase {
                    settingDao.updatePrinter(printer.copy(printerSettingId = it?.data, synced = true))
                }
            }

            var sync = lastSyncDao.getLastSync(PrinterEntity::class.java.simpleName)
            settingService.getPrinterSetting(id, sync?.lastSync ?: 0).awaitListBase {
                it.data?.let { data ->
                    data.forEachIndexed { index, _ -> data[index].synced = true }
                    settingDao.addPrinters(data)
                }
                lastSyncDao.saveLastSync(LastSyncEntity(PrinterEntity::class.java.simpleName, it.millis))
            }

            sync = lastSyncDao.getLastSync(PrinterClosingShiftEntity::class.java.simpleName)
            settingService.getPrinterClosingShift(id, sync?.lastSync ?: 0).awaitListBase {
                it.data?.let { data -> settingDao.addPrintersClosingShift(data) }
                lastSyncDao.saveLastSync(LastSyncEntity(PrinterClosingShiftEntity::class.java.simpleName, it.millis))
            }

            sync = lastSyncDao.getLastSync(PrinterTicketEntity::class.java.simpleName)
            settingService.getPrinterTicket(id, sync?.lastSync ?: 0).awaitListBase {
                it.data?.let { data -> settingDao.addPrinterTickets(data) }
                lastSyncDao.saveLastSync(LastSyncEntity(PrinterTicketEntity::class.java.simpleName, it.millis))
            }

            sync = lastSyncDao.getLastSync(KitchenDisplayEntity::class.java.simpleName)
            settingService.getKitchenDisplay(id, sync?.lastSync ?: 0).awaitListBase {
                it.data?.let { data -> settingDao.addKitchenDisplays(data) }
                lastSyncDao.saveLastSync(LastSyncEntity(KitchenDisplayEntity::class.java.simpleName, it.millis))
            }

            // Sync transaction settings
            syncTransactionSettings(id)
        }

        settingService.getTransactionConfig().awaitBase { result ->
            Timber.i("transaction config: ${Gson().toJson(result)}")
            result?.takeIf { it.status }?.data?.let { config ->
                context?.sharedPref()?.saveJson(SharedPref.TRANSACTION_CONFIG, config)
                context?.sharedPref()?.putData(SharedPref.NOTIFICATION_PATH, "") //reset to empty
                config.notificationSound?.takeIf { context != null }?.let { soundUrl ->
                   val filePath = downloadFile(soundUrl, context!!)
                    context.sharedPref().putData(SharedPref.NOTIFICATION_PATH, filePath)
                }
            }
        }
    }

    suspend fun addKitchenDisplay(kitchenDisplayEntity: KitchenDisplayEntity) {
        withContext(Dispatchers.IO){
            settingDao.addKitchenDisplay(kitchenDisplayEntity)
            settingService.addKitchenDisplay(kitchenDisplayEntity).await {
                settingDao.updateKitchenDisplay(kitchenDisplayEntity.copy(synced = true))
            }
        }
    }

    fun loadKitchenDisplay(outletId: Int): LiveData<Resource<List<KitchenDisplayEntity>>> {
        return object : NetworkBoundResource<List<KitchenDisplayEntity>, ServerResponseList<KitchenDisplayEntity>>(){
            override fun saveCallResult(item: ServerResponseList<KitchenDisplayEntity>?) {
                item?.data?.let { data ->
                    settingDao.addKitchenDisplays(data)
                    lastSyncDao.saveLastSync(LastSyncEntity(KitchenDisplayEntity::class.java.simpleName, item.millis))

                    //find duplicate data
                    val savedData = settingDao.getKitchenDisplays().value
                    savedData?.groupBy { it.address }?.let { dataGrouped ->
                        if(dataGrouped.values.size > 1){
                            //remove the older (based on dataModified)
                            Timber.i("duplicate data found... ${dataGrouped.keys} | size: ${dataGrouped.values.size}")
                            val dataList = dataGrouped.values.first().sortedByDescending{it.dataModified}
                            dataList.takeLast(dataList.size - 1).forEach {
                                settingDao.removeKitchenDisplay(it.settingKitchenDisplayId)
                            }
//                            dataGrouped.values.first().sortedByDescending{it.dataModified}.forEachIndexed { index, kitchenDisplayEntity ->
//                                if(index > 0)
//                                settingDao.removeKitchenDisplay(kitchenDisplayEntity.settingKitchenDisplayId)
//                            }
                        }
                    }
                }
            }
            override fun loadFromDb() = settingDao.getKitchenDisplays()
            override fun createCall(lastSync: Long) = settingService.getKitchenDisplay(outletId, lastSync)
            override fun getLastSync() = lastSyncDao.getLastSync(KitchenDisplayEntity::class.java.simpleName)?.lastSync

        }.asLiveData
    }

    suspend fun removeKitchenDisplay(id: Int, outletId: Int?) {
        withContext(Dispatchers.IO){
            settingService.removeKitchenDisplay(id, outletId).awaitBase {
                settingDao.removeKitchenDisplay(id)
            }
        }
    }

    fun getKitchenDisplay() = settingDao.getKitchenDisplays()
    suspend fun updateKitchenDisplay(kitchenDisplayEntity: KitchenDisplayEntity) {
        withContext(Dispatchers.IO){
            settingDao.updateKitchenDisplay(kitchenDisplayEntity)
        }
    }

    private fun syncTransactionSettings(outletId: Int) {
        val sync = lastSyncDao.getLastSync(TransactionSettingEntity::class.java.simpleName)
        settingService.getTransactionSettings(outletId, sync?.lastSync ?: 0).awaitListBase {
            it.data?.let { data ->
                data.forEach { setting -> setting.synced = true }
                settingDao.addTransactionSettings(data)
            }
            lastSyncDao.saveLastSync(LastSyncEntity(TransactionSettingEntity::class.java.simpleName, it.millis))
        }
    }

    fun getTransactionSettings() = settingDao.getTransactionSettings()


}