package com.uniq.uniqpos.data.local.entity

import android.os.Parcelable
import androidx.room.*
import com.google.gson.Gson
import com.google.gson.annotations.SerializedName
import com.uniq.uniqpos.data.local.converter.MemberDetailConverter
import com.uniq.uniqpos.data.remote.model.Member
import com.uniq.uniqpos.util.safe
import kotlinx.android.parcel.IgnoredOnParcel
import kotlinx.android.parcel.Parcelize

@Parcelize
@TypeConverters(MemberDetailConverter::class)
@Entity(tableName = "order_sales")
data class OrderSalesEntity(
        @PrimaryKey
        @field:SerializedName("order_sales_id")
        var orderSalesId: String = "",

        @field:SerializedName("time_order")
        var timeOrder: Long = 0,

        @field:SerializedName("reject_reason")
        var rejectReason: String? = null,

        @field:SerializedName("time_taken")
        var timeTaken: Long? = null,

        @field:SerializedName("time_modified")
        var timeModified: Long = 0,

        @field:SerializedName("time_ready")
        var timeReady: Long? = null,

        @field:SerializedName("time_accept_reject")
        var timeAcceptReject: Long? = null,

        @field:SerializedName("pickup_time")
        var pickupTime: String? = null,

        @field:SerializedName("sales_fkid")
        var salesFkid: String? = null,

        @field:SerializedName("items")
        var items: String = "",

        @field:SerializedName("order_type")
        var orderType: String = "",

        @field:SerializedName("outlet_fkid")
        var outletFkid: Int = 0,

        @field:SerializedName("status")
        var status: String = "",

        @field:SerializedName("member")
        var member: Member? = null,

        var synced: Boolean = false
) : Parcelable {
    @IgnoredOnParcel
    @Ignore
    var customer: String = ""

    @IgnoredOnParcel
    @Ignore
    var grandTotal: Int = 0

    @IgnoredOnParcel
    @Ignore
    var itemNames: String = ""
}

fun OrderSalesEntity.adjustData(): OrderSalesEntity {
    val sales = Gson().fromJson(this.items, SalesEntity::class.java)
    this.customer = this.member?.name.safe(sales.customer ?: "-") + this.member?.phone?.let { phone -> " ($phone)" }.safe()
    this.grandTotal = sales.grandTotal
    this.itemNames = sales.items ?: "[no items]"
    return this
}