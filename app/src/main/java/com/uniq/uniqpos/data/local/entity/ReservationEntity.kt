package com.uniq.uniqpos.data.local.entity

import android.annotation.SuppressLint
import androidx.room.Entity
import androidx.room.PrimaryKey
import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize
import java.io.Serializable

@Parcelize
@SuppressLint("ParcelCreator")
@Entity(tableName = "reservation")
data class ReservationEntity(

        @PrimaryKey
        @field:SerializedName("reservation_id")
        val reservationId: Long,

        @field:SerializedName("time_modified")
        val timeModified: Long? = null,

        @field:SerializedName("phone")
        val phone: String? = null,

        @field:SerializedName("time_start")
        val timeStart: Long? = null,

        @field:SerializedName("name")
        val name: String? = null,

        @field:SerializedName("time_created")
        val timeCreated: Long? = null,

        @field:SerializedName("time_end")
        val timeEnd: Long? = null,

        @field:SerializedName("outlet_fkid")
        val outletFkid: Int? = null,

        @field:SerializedName("table_name")
        val tableName: String? = null,

        var synced: <PERSON>olean = false
) : <PERSON>rcelable