package com.uniq.uniqpos.data.local.dao

import androidx.lifecycle.LiveData
import androidx.room.*
import com.uniq.uniqpos.data.local.entity.OperationalCostEntity
import com.uniq.uniqpos.data.local.entity.SupplierEntity
import com.uniq.uniqpos.util.Utils

/**
 * Created by an<PERSON><PERSON>ck<PERSON> on 14/02/19
 */
@Dao
interface PurchaseDao {

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun saveSuppliers(list: List<SupplierEntity>)

    @Query("SELECT * FROM supplier ORDER BY name")
    fun getSuppliersLive(): LiveData<List<SupplierEntity>>

    //======= OPERATIONAL COST ============
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun saveOperationalCost(operationalCost: OperationalCostEntity)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun saveOperationalCosts(operationalCostList: List<OperationalCostEntity>)

    @Query("SELECT * FROM operational_cost ORDER BY timeCreated DESC")
    fun getOperationalCostsLive(): LiveData<List<OperationalCostEntity>>

    @Query("SELECT * FROM operational_cost WHERE timeCreated >= :timeStart ORDER BY timeCreated DESC")
    fun     getOperationalCostStartAtLive(timeStart: Long = Utils.getMinTimeMillisToday()): LiveData<List<OperationalCostEntity>>

    @Query("SELECT DISTINCT(opcostName) FROM operational_cost")
    fun getUniqueOpCostName(): LiveData<List<String>>

    @Query("SELECT DISTINCT(opcostName) FROM operational_cost")
    suspend fun getUniqueOpCostNameOnce(): List<String>

    @Query("SELECT * FROM operational_cost LIMIT 50")
    suspend fun getHistoryOperationalCost(): List<OperationalCostEntity>

    @Query("SELECT * FROM operational_cost WHERE synced = 0")
    fun getUnSyncOperationalCost(): List<OperationalCostEntity>

    @Delete
    fun deleteOperationalCost(operationalCostEntity: OperationalCostEntity)
}