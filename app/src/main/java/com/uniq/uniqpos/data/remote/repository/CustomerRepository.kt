package com.uniq.uniqpos.data.remote.repository

import androidx.lifecycle.LiveData
import com.uniq.uniqpos.data.local.dao.CustomerDao
import com.uniq.uniqpos.data.local.dao.LastSyncDao
import com.uniq.uniqpos.data.local.entity.LastSyncEntity
import com.uniq.uniqpos.data.local.entity.MemberEntity
import com.uniq.uniqpos.data.local.entity.MemberTypeEntity
import com.uniq.uniqpos.data.remote.NetworkBoundResource
import com.uniq.uniqpos.data.remote.Resource
import com.uniq.uniqpos.data.remote.model.ServerResponseList
import com.uniq.uniqpos.data.remote.service.CustomerService
import timber.log.Timber
import javax.inject.Inject

class CustomerRepository @Inject constructor(val customerDao: CustomerDao,
                                             val customerService: CustomerService,
                                             val lastSyncDao: LastSyncDao) {

    fun getMemberTypeSync(): LiveData<Resource<List<MemberTypeEntity>>> {
        return object : NetworkBoundResource<List<MemberTypeEntity>, ServerResponseList<MemberTypeEntity>>(){
            override fun saveCallResult(item: ServerResponseList<MemberTypeEntity>?) {
                item?.let {
                    it.data?.let { list -> customerDao.saveMemberTypes(list) }
                    lastSyncDao.saveLastSync(LastSyncEntity(MemberTypeEntity::class.java.simpleName, item.millis))
                }
            }
            override fun loadFromDb() = customerDao.getMemberTypeLive()
            override fun createCall(lastSync: Long) = customerService.getMemberType()
            override fun getLastSync() = lastSyncDao.getLastSync(MemberTypeEntity::class.java.simpleName)?.lastSync
        }.asLiveData
    }

    fun getMemberSync(): LiveData<Resource<List<MemberEntity>>> {
        return object : NetworkBoundResource<List<MemberEntity>, ServerResponseList<MemberEntity>>(){
            override fun saveCallResult(item: ServerResponseList<MemberEntity>?) {
                item?.let {
                    try {
                        it.data?.let { list -> customerDao.saveMember(list) }
                        lastSyncDao.saveLastSync(LastSyncEntity(MemberEntity::class.java.simpleName, item.millis))
                    } catch (e: Exception) {
                        Timber.i("Something error on saving member... : $e")
                    }
                }
            }
            override fun loadFromDb() = customerDao.getMemberLive()
            override fun createCall(lastSync: Long) = customerService.getMember()
            override fun getLastSync() = lastSyncDao.getLastSync(MemberEntity::class.java.simpleName)?.lastSync
        }.asLiveData
    }

    fun registerMember(member: MemberEntity) = customerService.registerMember(member)

//    fun getMemberWithType() = customerDao.getMemberAndType()
}