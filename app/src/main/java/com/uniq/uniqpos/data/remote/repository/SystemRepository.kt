package com.uniq.uniqpos.data.remote.repository

import android.content.Context
import com.bugsnag.android.Bugsnag
import com.uniq.uniqpos.data.local.dao.*
import com.uniq.uniqpos.data.local.entity.*
import com.uniq.uniqpos.data.local.sharedpref.SharedPref
import com.uniq.uniqpos.data.local.sharedpref.getLocalDataString
import com.uniq.uniqpos.data.remote.model.DeleteHistory
import com.uniq.uniqpos.data.remote.model.ServerResponse
import com.uniq.uniqpos.data.remote.service.SystemService
import com.uniq.uniqpos.model.Device
import com.uniq.uniqpos.model.SubscriptionStatus
import com.uniq.uniqpos.util.*
import okhttp3.RequestBody
import timber.log.Timber
import java.io.File
import java.util.*
import javax.inject.Inject
import kotlin.coroutines.resume
import kotlin.coroutines.resumeWithException
import kotlin.coroutines.suspendCoroutine

/**
 * Created by ANNASBlackHat on 07/12/17.
 */
class SystemRepository @Inject constructor(
    private val settingDao: SettingDao,
    private val systemService: SystemService,
    private val productDao: ProductDao,
    private val salesDao: SalesDao,
    private val lastSyncDao: LastSyncDao,
    private val outletDao: OutletDao,
    private val promotionDao: PromotionDao
) {

    fun getLatestVersion() = systemService.getLatestRelease()

    fun syncSetting(outletId: Int?) {
        val sync = lastSyncDao.getLastSync(DeleteHistory::class.java.simpleName)
        systemService.getDeleteHistory(sync?.lastSync ?: 0, outletId).awaitListBase { items ->
            items.data?.forEach {
                Timber.i("[DELETE] ${it.tableName}: ${it.tableId}")
                when (it.tableName) {
                    "setting_printer" -> settingDao.deletePrinterById(it.tableId.toInt())
                    "product_category" -> productDao.deleteSubCategory(SubCategoryEntity(it.tableId.toInt()))
                    "product_detail" -> productDao.deleteProductByDetailId(it.tableId.safeToInt())
                    "printer_ticket" -> settingDao.deletePrinterTicket(PrinterTicketEntity(it.tableId.toInt()))
                    "dining_table" -> outletDao.deleteTable(DiningTableEntity(it.tableId.toLong()))
                    "setting_printer_closingshift" -> settingDao.deletePrinterClosingShiftById(it.tableId)
                    "promotions" -> promotionDao.deletePromotoionById(it.tableId.safeToInt())
                    "products_linkmenu" -> productDao.deleteLinkMenuById(it.tableId.safeToInt())
                    "products_detail_variant" -> productDao.deleteVarianById(it.tableId.safeToInt())
                    "products_detail_taxdetail" -> productDao.deleteTaxById(it.tableId.safeToInt())
                    "payment_media_bank" -> outletDao.deleteBankById(it.tableId.safeToInt())
                    "cash_recap" -> salesDao.deleteCashRecapById(it.tableId.safeToLong())
                }
                lastSyncDao.saveLastSync(
                    LastSyncEntity(
                        DeleteHistory::class.java.simpleName,
                        items.millis
                    )
                )
            }
        }
    }

    fun getGetDeviceStatus(devideId: String): ServerResponse<Device>? {
        var resp: ServerResponse<Device>? = null
        systemService.getDeviceStatus(devideId).awaitBase {
            resp = it
        }
        return resp
    }

    fun getSubscriptionStatus(deviceId: String): SubscriptionStatus? {
        var result: SubscriptionStatus? = null
        systemService.getSubscriptionStatus(deviceId).await {
            result = it
        }
        return result
    }

    fun getSubscription(deviceId: String) = systemService.getSubscriptionStatus(deviceId)

    fun uploadLogFile(logFile: File, outletId: Int, context: Context) {
        val data = HashMap<String, RequestBody>()
        data["device"] = Constant.DEVICE_NAME.toRequestBody()
        data["imei"] = context.getLocalDataString(SharedPref.DEVICE_ID, "").toRequestBody()
        data["outlet_id"] = outletId.toString().toRequestBody()
        val filePart = Utils.requestBodyFile(logFile.path, Utils.MediaTypes.TEXT_FILE, "file")
        Bugsnag.leaveBreadcrumb("upload log")
        systemService.uploadLog(data, filePart)
            .awaitBase {
                if (it?.status == true) {
                    try {
                        Bugsnag.leaveBreadcrumb("delete log")
                        logFile.delete()
                    } catch (e: Exception) {
                        Timber.i("deleting log file error: $e")
                    }
                }
            }
    }

    fun countUnsynced(): Pair<Int, Long> {
        var total = 0
        var log = StringBuilder()
        val salesCount = salesDao.countUnsyncedSales()
        total += salesCount

        val tmpSalesCount = salesDao.countUnsyncedTmpSales()
        log.appendLine("Unsynced tmp sales : $tmpSalesCount")
        total += tmpSalesCount

        var lastSync = System.currentTimeMillis()
        var tmpLastSync =
            lastSyncDao.getLastSync(SalesEntity::class.java.simpleName)?.lastSync.safe()
        log.appendLine("Last Sync Sales : $tmpLastSync")
        if (tmpLastSync < lastSync) lastSync = tmpLastSync

        tmpLastSync =
            lastSyncDao.getLastSync(TmpSalesEntity::class.java.simpleName)?.lastSync.safe()
        log.appendLine("Last Sync TmpSales : $tmpLastSync")
        if (tmpLastSync < lastSync) lastSync = tmpLastSync

        tmpLastSync =
            lastSyncDao.getLastSync(CashRecapEntity::class.java.simpleName)?.lastSync.safe()

        log.appendLine("Last Sync Cash Recap : $tmpLastSync")
        if (tmpLastSync < lastSync) lastSync = tmpLastSync

        Timber.i("Unsynced : $log")
        return Pair(total, lastSync)
    }

    suspend fun lookupVendor(macAddress: String): String {
        return suspendCoroutine { continuation ->
            systemService.getVendor(macAddress)
                .awaitAsync({ result ->
                    result.body()?.data?.let { data ->
                        continuation.resume(data.company ?: "Unknown Vendor")
                    }
                        ?: kotlin.run { continuation.resumeWithException(Exception("Server Response Error")) }
                }, { err ->
                    continuation.resumeWithException(Exception("Server Response Error, cause : $err"))
                })
        }
    }
}