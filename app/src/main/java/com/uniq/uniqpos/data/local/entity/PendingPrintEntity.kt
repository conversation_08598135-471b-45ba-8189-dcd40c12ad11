package com.uniq.uniqpos.data.local.entity

import android.graphics.Bitmap
import androidx.room.Entity
import androidx.room.Ignore
import androidx.room.PrimaryKey
import com.uniq.uniqpos.util.displayNotaFormat
import kotlin.random.Random

/**
 * Created by annasblack<PERSON> on 04/02/18.
 */

@Entity(tableName = "pending_print")
data class PendingPrintEntity(
        var address: String,
        var type: String? = "",
        var dataString: String? = null,
        var printerName: String? = "",
        @PrimaryKey(autoGenerate = true)
        var id: Long = System.currentTimeMillis() + Random.nextInt(),
        var isOpenCashDrawer: Boolean = false,
) {
    @Ignore
    var dataPrintDisplay: String? = null
        get() {
            return dataString.displayNotaFormat().trim()
        }

    @Ignore
    var beautifyDisplay: String = ""

    @Ignore
    var bitmap: Bitmap? = null
}