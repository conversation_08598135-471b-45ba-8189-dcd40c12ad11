package com.uniq.uniqpos.data.local.converter

import androidx.room.TypeConverter
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.uniq.uniqpos.data.remote.model.Promotion

/**
 * Created by annasblackhat on 28/02/19
 */
class PromotionConverter {
    @TypeConverter
    fun fromArrayToJson(array: ArrayList<Promotion>?): String {
        return Gson().toJson(array)
    }

    @TypeConverter
    fun fromJsonToArray(json: String?): ArrayList<Promotion> {
        val type = object : TypeToken<ArrayList<Promotion>>() {}.type
        return json?.takeIf { it.isNotEmpty() }?.let {
            Gson().fromJson<ArrayList<Promotion>>(it, type)
        } ?: ArrayList()
    }
}