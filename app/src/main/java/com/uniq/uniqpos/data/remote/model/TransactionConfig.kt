package com.uniq.uniqpos.data.remote.model


import com.google.gson.annotations.SerializedName

data class TransactionConfig(
    @SerializedName("internal_delivery")
    val internalDelivery: InternalDelivery?,
    @SerializedName("payment_gateway")
    val paymentGateway: String?,
    @SerializedName("payment_method")
    val paymentMethod: List<PaymentMethod>?,
    @SerializedName("transaction")
    val transaction: Transaction?,
    @SerializedName("notification_sound")
    val notificationSound: String?
)