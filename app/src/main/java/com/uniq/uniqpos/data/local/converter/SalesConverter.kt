package com.uniq.uniqpos.data.local.converter

import androidx.room.TypeConverter
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.uniq.uniqpos.data.local.entity.SalesEntity

/**
 * Created by ANNASBlackHat on 11/11/17.
 */

class SalesConverter {
    @TypeConverter
    fun fromObjectToArray(salesEntity: SalesEntity): String {
        return Gson().toJson(salesEntity)
    }

    @TypeConverter
    fun fromStringToObject(salesEntity: String): SalesEntity {
        val type = object : TypeToken<SalesEntity>() {}.type
        return Gson().fromJson<SalesEntity>(salesEntity, type)
    }
}
