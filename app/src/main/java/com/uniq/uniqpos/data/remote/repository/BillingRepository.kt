package com.uniq.uniqpos.data.remote.repository

import com.uniq.uniqpos.data.remote.service.BillingService
import javax.inject.Inject

class BillingRepository @Inject constructor(private val service: BillingService) {
    fun getBillingService() = service.getBillingService()
    fun createBilling(serviceId: Int, period: Int, bank: String, slot: Int) = service.createBilling(serviceId, period, bank, slot)
}