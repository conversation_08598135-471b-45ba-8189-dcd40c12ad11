package com.uniq.uniqpos.data.local.entity

import androidx.room.Entity
import androidx.room.Ignore
import androidx.room.PrimaryKey
import com.google.gson.annotations.SerializedName

/**
 * Created by ANNASBlackHat on 15/01/18.
 */

@Entity(tableName = "dining_table")
data class DiningTableEntity(

        @PrimaryKey
        @field:SerializedName("dining_table_id")
        var diningTableId: Long = 0,

        @field:SerializedName("time_modified")
        var timeModified: Long = System.currentTimeMillis(),

        @field:SerializedName("time_created")
        var timeCreated: Long = System.currentTimeMillis(),

        @field:SerializedName("table_name")
        var tableName: String = "",

        @field:SerializedName("outlet_fkid")
        var outletFkid: Int = 0,

        @field:SerializedName("status")
        var status: String = "empty",

        var synced: Boolean? = false
){
        @Ignore constructor(): this(0, 0, 0, "",0,"", false)
}