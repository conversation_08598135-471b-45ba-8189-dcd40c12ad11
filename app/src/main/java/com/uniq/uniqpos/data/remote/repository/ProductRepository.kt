package com.uniq.uniqpos.data.remote.repository

import androidx.lifecycle.LiveData
import com.uniq.uniqpos.data.local.dao.LastSyncDao
import com.uniq.uniqpos.data.local.dao.OutletDao
import com.uniq.uniqpos.data.local.dao.ProductDao
import com.uniq.uniqpos.data.local.entity.*
import com.uniq.uniqpos.data.remote.NetworkBoundResource
import com.uniq.uniqpos.data.remote.Resource
import com.uniq.uniqpos.data.remote.model.GenerateImageRequest
import com.uniq.uniqpos.data.remote.model.ProductExtractionResponse
import com.uniq.uniqpos.data.remote.model.ServerResponseList
import com.uniq.uniqpos.data.remote.service.OutletService
import com.uniq.uniqpos.data.remote.service.ProductService
import com.uniq.uniqpos.util.BACKGROUND
import com.uniq.uniqpos.util.await
import com.uniq.uniqpos.util.awaitAsync
import com.uniq.uniqpos.util.awaitBase
import com.uniq.uniqpos.util.awaitListBase
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import okhttp3.MediaType
import okhttp3.MultipartBody
import okhttp3.RequestBody
import timber.log.Timber
import java.io.File
import java.util.*
import javax.inject.Inject
import kotlin.coroutines.resume
import kotlin.coroutines.resumeWithException
import kotlin.coroutines.suspendCoroutine

/**
 * Created by ANNASBlackHat on 13/10/2017.
 */

class ProductRepository @Inject
constructor(private val productDao: ProductDao,
            private val productService: ProductService,
            private val outletService: OutletService,
            private val outletDao: OutletDao,
            private val lastSyncDao: LastSyncDao) {

    val subCategories: LiveData<List<SubCategoryEntity>> by lazy { productDao.getSubCategoriesLive() }

    fun loadProducts(outletId: Int): LiveData<Resource<List<ProductEntity>>> {
        Timber.i(">>PRODUCT loadProduct...")
        return object : NetworkBoundResource<List<ProductEntity>, ServerResponseList<ProductEntity>>() {
            override fun saveCallResult(item: ServerResponseList<ProductEntity>?) {
                Timber.i(">>PRODUCT got product: ${item?.data?.size} | millis:  ${item?.millis}")
                item?.data?.let {
                    it.forEach { it.sync = true }
                    productDao.saveProducts(it)
                }
                lastSyncDao.saveLastSync(LastSyncEntity(ProductEntity::class.java.simpleName, item?.millis ?: System.currentTimeMillis()))
            }

            override fun loadFromDb() = productDao.loadProducts()
            override fun createCall(lastSync: Long) = productService.getProducts(outletId, lastSync)
            override fun getLastSync() = lastSyncDao.getLastSync(ProductEntity::class.java.simpleName)?.lastSync
        }.asLiveData
    }

    //run in background
    fun syncProduct(outletId: Int) {
        val products = productDao.getUnsyncProduct()
        products.forEach {product ->
            productService.updateProduct(product).awaitBase {
                productDao.updateProduct(product.copy(sync=true))
            }
        }

        var sync = lastSyncDao.getLastSync(ProductEntity::class.java.simpleName)
        productService.getProducts(outletId, sync?.lastSync ?: 0).awaitListBase {
            it.data?.let { products ->
                products.forEach { product -> product.sync = true }
                productDao.saveProducts(products)
            }
            lastSyncDao.saveLastSync(LastSyncEntity(ProductEntity::class.java.simpleName, it.millis))
        }

        sync = lastSyncDao.getLastSync(SubCategoryEntity::class.java.simpleName)
        productService.getSubCategory(sync?.lastSync ?: 0).awaitListBase { c ->
            c.data?.let { productDao.saveSubCategory(it) }
            lastSyncDao.saveLastSync(LastSyncEntity(SubCategoryEntity::class.java.simpleName, c.millis))
        }

        sync = lastSyncDao.getLastSync(TaxEntity::class.java.simpleName)
        productService.getTax(outletId, sync?.lastSync ?: 0).awaitListBase {
            it.data?.let { productDao.saveTax(it) }
            lastSyncDao.saveLastSync(LastSyncEntity(TaxEntity::class.java.simpleName, it.millis))
        }

        sync = lastSyncDao.getLastSync(GratuityEntity::class.java.simpleName)
        productService.getGratuity(sync?.lastSync ?: 0).awaitListBase {
            it.data?.let { productDao.saveGratuity(it) }
            lastSyncDao.saveLastSync(LastSyncEntity(GratuityEntity::class.java.simpleName, it.millis))
        }

        sync = lastSyncDao.getLastSync(ProductDescEntity::class.java.simpleName)
        productService.getProductDescription(outletId, sync?.lastSync ?: 0).awaitListBase {
            it.data?.let { productDao.saveProductDescriptions(it) }
            lastSyncDao.saveLastSync(LastSyncEntity(ProductDescEntity::class.java.simpleName, it.millis))
        }

        sync = lastSyncDao.getLastSync(MultiplePriceEntity::class.java.simpleName)
        productService.getMultiplePriceProduct(outletId, sync?.lastSync ?: 0).awaitListBase {
            it.data?.let { productDao.saveMultiplePrice(it) }
            lastSyncDao.saveLastSync(LastSyncEntity(MultiplePriceEntity::class.java.simpleName, it.millis))
        }

        sync = lastSyncDao.getLastSync(LinkMenuEntity::class.java.simpleName)
        productService.linkMenu(outletId, sync?.lastSync ?: 0).awaitListBase {
            it.data?.let { productDao.saveLinkMenus(it) }
            lastSyncDao.saveLastSync(LastSyncEntity(LinkMenuEntity::class.java.simpleName, it.millis))
        }

        sync = lastSyncDao.getLastSync(ProductVariantEntity::class.java.simpleName)
        productService.getProductVariant(sync?.lastSync ?: 0).awaitListBase {
            it.data?.let { productDao.saveProductVariants(it) }
            lastSyncDao.saveLastSync(LastSyncEntity(ProductVariantEntity::class.java.simpleName, it.millis))
        }

    }

    fun getGratuity(): LiveData<Resource<List<GratuityEntity>>> {
        return object : NetworkBoundResource<List<GratuityEntity>, ServerResponseList<GratuityEntity>>() {
            override fun saveCallResult(item: ServerResponseList<GratuityEntity>?) {
                item?.let {
                    it.data?.let { data -> productDao.saveGratuity(data) }
                    lastSyncDao.saveLastSync(LastSyncEntity(GratuityEntity::class.java.simpleName, it.millis))
                }
            }

            override fun loadFromDb() = productDao.getGratuity()
            override fun createCall(lastSync: Long) = productService.getGratuity(lastSync)
            override fun getLastSync() = lastSyncDao.getLastSync(GratuityEntity::class.java.simpleName)?.lastSync
        }.asLiveData
    }

    fun getTax(outletId: Int): LiveData<Resource<List<TaxEntity>>> {
        return object : NetworkBoundResource<List<TaxEntity>, ServerResponseList<TaxEntity>>() {
            override fun saveCallResult(item: ServerResponseList<TaxEntity>?) {
                item?.let {
                    it.data?.let { data -> productDao.saveTax(data) }
                    lastSyncDao.saveLastSync(LastSyncEntity(TaxEntity::class.java.simpleName, it.millis))
                }
            }

            override fun loadFromDb() = productDao.getTax()
            override fun createCall(lastSync: Long) = productService.getTax(outletId, lastSync)
            override fun getLastSync() = lastSyncDao.getLastSync(TaxEntity::class.java.simpleName)?.lastSync
        }.asLiveData
    }

    fun loadCategories(): LiveData<Resource<List<SubCategoryEntity>>> {
        return object : NetworkBoundResource<List<SubCategoryEntity>, ServerResponseList<SubCategoryEntity>>() {
            override fun saveCallResult(item: ServerResponseList<SubCategoryEntity>?) {
                item?.data?.apply {
                    productDao.saveSubCategory(item.data)
                    lastSyncDao.saveLastSync(LastSyncEntity(BankEntity::class.java.simpleName, item.millis))
                }
            }

            override fun loadFromDb() = productDao.loadCategories()
            override fun createCall(lastSync: Long) = productService.getSubCategory()
            override fun getLastSync() = lastSyncDao.getLastSync(BankEntity::class.java.simpleName)?.lastSync
        }.asLiveData
    }

    fun getBank(outletId: Int?): LiveData<Resource<List<BankEntity>>> {
        return object : NetworkBoundResource<List<BankEntity>, ServerResponseList<BankEntity>>() {
            override fun saveCallResult(item: ServerResponseList<BankEntity>?) {
                item?.data?.let {
                    outletDao.saveBanks(it)
                    lastSyncDao.saveLastSync(LastSyncEntity(BankEntity::class.java.simpleName, item.millis))
                }
            }
            override fun loadFromDb() = outletDao.getBanks()
            override fun createCall(lastSync: Long) = outletService.getBank(outletId)
            override fun getLastSync() = lastSyncDao.getLastSync(BankEntity::class.java.simpleName)?.lastSync
        }.asLiveData
    }

    fun loadMultiplePrice(outletId: Int?): LiveData<Resource<List<MultiplePriceEntity>>> {
        return object : NetworkBoundResource<List<MultiplePriceEntity>, ServerResponseList<MultiplePriceEntity>>(){
            override fun saveCallResult(item: ServerResponseList<MultiplePriceEntity>?) {
                item?.data?.let {
                    productDao.saveMultiplePrice(it)
                    lastSyncDao.saveLastSync(LastSyncEntity(MultiplePriceEntity::class.java.simpleName, item.millis))
                }
            }
            override fun loadFromDb() = productDao.getMultiplePriceLive()
            override fun createCall(lastSync: Long) = productService.getMultiplePriceProduct(outletId, lastSync)
            override fun getLastSync() = lastSyncDao.getLastSync(MultiplePriceEntity::class.java.simpleName)?.lastSync
        }.asLiveData
    }

    fun getLinkMenu(outletId: Int): LiveData<Resource<List<LinkMenuEntity>>> {
        return object : NetworkBoundResource<List<LinkMenuEntity>, ServerResponseList<LinkMenuEntity>>() {
            override fun saveCallResult(item: ServerResponseList<LinkMenuEntity>?) {
                item?.data?.let { data ->
                    productDao.saveLinkMenus(data)
                    lastSyncDao.saveLastSync(LastSyncEntity(LinkMenuEntity::class.java.simpleName, item.millis))
                }
            }
            override fun loadFromDb() = productDao.getLinkMenuLive()
            override fun createCall(lastSync: Long) = productService.linkMenu(outletId, lastSync)
            override fun getLastSync() = lastSyncDao.getLastSync(LinkMenuEntity::class.java.simpleName)?.lastSync

        }.asLiveData
    }

    fun loadProductType(): LiveData<Resource<List<ProductTypeEntity>>>{
        return object : NetworkBoundResource<List<ProductTypeEntity>, ServerResponseList<ProductTypeEntity>>(){
            override fun saveCallResult(item: ServerResponseList<ProductTypeEntity>?) {
                item?.data?.let { data ->
                    productDao.saveProductTypes(data)
                    lastSyncDao.saveLastSync(LastSyncEntity(ProductTypeEntity::class.java.simpleName, item.millis))
                }
            }
            override fun loadFromDb() = productDao.getProductTypeLive()
            override fun createCall(lastSync: Long) = productService.getProductType(lastSync)
            override fun getLastSync() = lastSyncDao.getLastSync(ProductTypeEntity::class.java.simpleName)?.lastSync

        }.asLiveData
    }

    fun loadCategory(): LiveData<Resource<List<CategoryEntity>>> {
        return object : NetworkBoundResource<List<CategoryEntity>, ServerResponseList<CategoryEntity>>(){
            override fun saveCallResult(item: ServerResponseList<CategoryEntity>?) {
                item?.data?.let { data ->
                    productDao.saveCategories(data)
                    lastSyncDao.saveLastSync(LastSyncEntity(CategoryEntity::class.java.simpleName, item.millis))
                }
            }
            override fun loadFromDb() = productDao.getCategoriesLive()
            override fun createCall(lastSync: Long) = productService.getCategory()
            override fun getLastSync() = lastSyncDao.getLastSync(CategoryEntity::class.java.simpleName)?.lastSync

        }.asLiveData
    }

    fun loadPurchaseReportCategory(): LiveData<Resource<List<PurchaseReportCategoryEntity>>> {
        return object : NetworkBoundResource<List<PurchaseReportCategoryEntity>, ServerResponseList<PurchaseReportCategoryEntity>>(){
            override fun saveCallResult(item: ServerResponseList<PurchaseReportCategoryEntity>?) {
                item?.data?.let { data ->
                    productDao.savePurchaseReportCategories(data)
                    lastSyncDao.saveLastSync(LastSyncEntity(PurchaseReportCategoryEntity::class.java.simpleName, item.millis))
                }
            }
            override fun loadFromDb() = productDao.getPurchaseReportCategoriesLive()
            override fun createCall(lastSync: Long) = productService.getPurchaseReportCategory(lastSync)
            override fun getLastSync() = lastSyncDao.getLastSync(PurchaseReportCategoryEntity::class.java.simpleName)?.lastSync
        }.asLiveData
    }

    fun loadUnit(): LiveData<Resource<List<UnitEntity>>> {
        return object : NetworkBoundResource<List<UnitEntity>, ServerResponseList<UnitEntity>>() {
            override fun saveCallResult(item: ServerResponseList<UnitEntity>?) {
                item?.data?.let { data ->
                    productDao.saveUnits(data)
                    lastSyncDao.saveLastSync(LastSyncEntity(UnitEntity::class.java.simpleName, item.millis))
                }
            }
            override fun loadFromDb() = productDao.getUnitLive()
            override fun createCall(lastSync: Long) = productService.getUnit()
            override fun getLastSync() = lastSyncDao.getLastSync(UnitEntity::class.java.simpleName)?.lastSync

        }.asLiveData
    }

    fun loadVariant(): LiveData<Resource<List<ProductVariantEntity>>> {
        return object : NetworkBoundResource<List<ProductVariantEntity>, ServerResponseList<ProductVariantEntity>>(){
            override fun saveCallResult(item: ServerResponseList<ProductVariantEntity>?) {
                item?.let { response ->
                    response.data?.let { productDao.saveProductVariants(it) }
                    lastSyncDao.saveLastSync(LastSyncEntity(ProductVariantEntity::class.java.simpleName, response.millis))
                }
            }
            override fun loadFromDb() = productDao.getAllProductVariantLive()
            override fun createCall(lastSync: Long) = productService.getProductVariant(lastSync)
            override fun getLastSync() = lastSyncDao.getLastSync(ProductVariantEntity::class.java.simpleName)?.lastSync
        }.asLiveData
    }

    suspend fun updateProduct(productEntity: ProductEntity, sendToServerDirectly: Boolean = true){
         withContext(Dispatchers.IO){
             productDao.updateProduct(productEntity.copy(sync = false))
             if(sendToServerDirectly){
                 productService.updateProduct(productEntity).awaitBase { response ->
                     response?.takeIf { it.status }?.let {
                         productDao.updateProduct(productEntity.copy(sync = true))
                     }
                 }
             }
         }
    }

    suspend fun updateProductPhoto(productId: Int, photoFile: File): String? {
        return suspendCoroutine { continuation ->
            BACKGROUND.submit {
                try {
                    val requestFile = RequestBody.create(MediaType.parse("image/jpeg"), photoFile)
                    val photoPart = MultipartBody.Part.createFormData("file", photoFile.name, requestFile)

                    productService.updateProductPhoto(productId, photoPart).awaitBase { response ->
                        if (response == null) {
                            Timber.e("Update product photo failed - response is null")
                            continuation.resumeWithException(Exception("Failed to update product photo - no response from server"))
                        }

                        if (response?.status == false) {
                            Timber.e("Update product photo failed - status is false")
                            continuation.resumeWithException(Exception("Failed to update product photo - ${response.message}"))
                        }

                        Timber.i("Update product photo success... url ${response?.data?.imageUrl}")
                        response?.data?.let { result ->
                            // Update local product with new photo URL
                            val products = productDao.getProductListById(productId.toLong())
                            products.forEach { it.photo = result.imageUrl }
                            productDao.saveProducts(products)
                        }
                        continuation.resume(response?.data?.imageUrl)
                    }
                } catch (e: Exception) {
                    continuation.resumeWithException(e)
                }
            }
        }
    }

//    fun addProductAsync(product: ProductEntity, listener: (Boolean, String?) -> Unit){
//        productService.addProduct(product)
//                .awaitAsync({
//                    if(it.isSuccessful){
//                        it.body()?.data?.let {
//                            async(UI){
//                                bg { productDao.saveProducts(listOf(it)) }.await()
//                                listener(true, "Success")
//                            }
//                        }
//                    }else{
//                        listener(false, "Add Failed!")
//                    }
//                },{ err -> listener(false, err.message)})
//    }

    suspend fun addProductMultipartAsync(data: HashMap<String, RequestBody>, image: MultipartBody.Part?){
        withContext(Dispatchers.IO){
            try {
                val products = productService.addProductMultipart(data, image).await()
                products.data?.let { productEntity ->
                    productDao.saveProducts(listOf(productEntity))
                }
            } catch (e: Exception) {
                throw e
            }
        }
    }

    suspend fun saveProductExtraction(productList: ArrayList<ProductEntity>, outletId: Int) {
        withContext(Dispatchers.IO){
            try {
                val products = productService.saveProductExtraction(
                    hashMapOf(
                    Pair("outlet_id", outletId),
                    Pair("products", productList)
                )
                ).await()
                syncProduct(outletId)
            } catch (e: Exception) {
                throw e
            }
        }
    }

//    fun getSubCategories() = productDao.getSubCategories()

    suspend fun getSubCategories(): List<SubCategoryEntity> {
        return suspendCoroutine { continuation ->
            BACKGROUND.submit {
                val result = productDao.getSubCategories()
                continuation.resume(result)
            }
        }
    }

    fun getProductByCatalogue(catalogue: String) = productDao.getProductByCatalogueType(catalogue)

    fun getProduct() = productDao.getProducts()

    fun getGratuityList() = productDao.getGratuityList()

    fun getProductsByIds(ids: List<Int>) = productDao.getProductsByIds(ids)

    fun getProductById(id: Int) = productDao.getProductById(id)

    fun getAllVariant() = productDao.getAllVariant()

    fun getProductVariantListById(id: Long) = productDao.getVariantListById(id.toInt())

    fun getProductByVariantId(id: Int) = productDao.getProductByVariantId(id)

    fun getProductListById(id: Long) = productDao.getProductListById(id)

    fun extractMenu(data: String) = productService.extractMenu(data)

    fun extractMenuFile(data: MultipartBody.Part) = productService.extractMenuFile(data)

    suspend fun getProductByVariantIdSuspend(id: Int): ProductEntity? {
       return suspendCoroutine { continuation ->
           BACKGROUND.submit {
               continuation.resume(productDao.getProductVariant(id))
           }
       }
    }

    suspend fun generateImage(name: String, description: String?): String {
        return withContext(Dispatchers.IO) {
            try {
                val request = GenerateImageRequest(name, description)
                val response = productService.generateImage(request).await()
                Timber.i("image generate resp: $response")
                response.data?.imageUrl ?: throw Exception("Failed to generate image")
            } catch (e: Exception) {
                Timber.e(e)
                throw e
            }
        }
    }

    fun saveSubCategory(categories: List<SubCategoryEntity>) {
        productDao.saveSubCategory(categories)
    }

}
