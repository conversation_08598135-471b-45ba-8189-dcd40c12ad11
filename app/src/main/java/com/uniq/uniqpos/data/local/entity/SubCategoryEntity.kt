package com.uniq.uniqpos.data.local.entity

import android.os.Parcelable
import androidx.room.Entity
import androidx.room.PrimaryKey
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize
import java.io.Serializable

/**
 * Created by ANNASBlackHat on 10/10/2017.
 */

@Entity(tableName = "subcategory")
@Parcelize
data class SubCategoryEntity(
        @PrimaryKey
        @field:SerializedName("product_subcategory_id")
        var productCategoryId: Int,

        @field:SerializedName("name")
        var name: String? = null,

        @field:SerializedName("data_created")
        var dataCreated: String? = null,

        @field:SerializedName("data_status")
        var dataStatus: String? = null,

        @field:SerializedName("admin_fkid")
        var adminFkid: Int? = null,

        @field:SerializedName("position")
        var position: Int? = null // Nullable Int, defaults to null
) : Parcelable
