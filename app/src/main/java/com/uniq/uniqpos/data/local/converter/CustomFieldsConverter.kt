package com.uniq.uniqpos.data.local.converter

import androidx.room.TypeConverter
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.uniq.uniqpos.model.CustomFields

class CustomFieldsConverter {
    @TypeConverter
    fun fromCustomFieldsList(value: ArrayList<CustomFields>?): String? {
        return if (value == null) null else Gson().toJson(value)
    }

    @TypeConverter
    fun toCustomFieldsList(value: String?): ArrayList<CustomFields>? {
        return if (value == null) null else {
            val listType = object : TypeToken<ArrayList<CustomFields>>() {}.type
            Gson().fromJson(value, listType)
        }
    }
}
