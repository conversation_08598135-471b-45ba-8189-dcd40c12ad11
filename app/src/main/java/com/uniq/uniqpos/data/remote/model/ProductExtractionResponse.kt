package com.uniq.uniqpos.data.remote.model

import com.uniq.uniqpos.data.local.entity.ProductEntity

data class ProductExtractionResponse(
    val category: String,
    val name: String,
    val price: Int
)

fun ProductExtractionResponse.toProductEntity(unitName: String, outletId: Int): ProductEntity {
    return ProductEntity(
        productId = 0, // Generate a random ID (adjust as needed)
        name = this.name,
        priceSell = this.price, // Assuming 'price' is the selling price
        productcategoryName = this.category, // Assuming 'category' is the product type
        unitName = unitName,
        productDetailId = 0,
        outletFkid = outletId,
    )
}