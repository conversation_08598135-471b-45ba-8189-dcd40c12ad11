package com.uniq.uniqpos.data.local.converter

import androidx.room.TypeConverter
import com.google.gson.Gson
import com.uniq.uniqpos.data.local.entity.RefundEntity
import com.uniq.uniqpos.util.safe

class SalesRefundEntityConverter {
    @TypeConverter
    fun fromObjectToJson(data: RefundEntity?): String {
        return data?.let { Gson().toJson(data) } ?: ""
    }

    @TypeConverter
    fun fromJsonToObject(json: String?): RefundEntity? {
        return if(json.safe().isNotBlank()) Gson().fromJson(json, RefundEntity::class.java) else null
    }
}