package com.uniq.uniqpos.data.local.converter

import androidx.room.TypeConverter
import com.google.gson.Gson
import com.uniq.uniqpos.data.remote.model.Member
import com.uniq.uniqpos.util.safe

/**
 * Created by annasblackhat on 21/03/19
 */
class MemberDetailConverter {
    @TypeConverter
    fun fromObjectToJson(data: Member?): String {
        return data?.let { Gson().toJson(data) } ?: ""
    }

    @TypeConverter
    fun fromJsonToObject(json: String?): Member? {
        return if(json.safe().isNotBlank())Gson().fromJson(json, Member::class.java) else null
    }
}