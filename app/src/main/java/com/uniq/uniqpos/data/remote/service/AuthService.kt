package com.uniq.uniqpos.data.remote.service

import com.uniq.uniqpos.data.local.entity.EmployeeEntity
import com.uniq.uniqpos.data.remote.model.*
import com.uniq.uniqpos.model.EmployeePosition
import com.uniq.uniqpos.model.LoginAdmin
import retrofit2.Call
import retrofit2.http.*

/**
 * Created by ANNASBlackHat on 19/10/2017.
 */
interface AuthService {

    @FormUrlEncoded
    @POST("v2/auth/login")
    fun loginAdminCall(
        @Field("email") email: String,
        @Field("password") password: String,
        @Field("firebase_token") token: String? = ""
    ): Call<ServerResponse<LoginAdmin>>

    @GET("v1/outlet")
    fun outlet(): Call<ServerResponseList<Outlet>>

    @GET("v1/employee/{outletId}")
    fun getEmployee(@Path("outletId") outletId: Int): Call<ServerResponseList<EmployeeEntity>>

    @FormUrlEncoded
    @POST("v1/auth/login_employee")
    fun loginEmployee(@FieldMap data: Map<String, String?>): Call<ServerResponse<LoginEmployee>>

    @FormUrlEncoded
    @POST("join")
    fun joinUniq(
        @Field("name") name: String,
        @Field("phone") phone: String,
        @Field("email") email: String,
        @Field("company") company: String
    ): Call<ServerResponse<Any>>

    @GET("v1/employee-position")
    fun fetchEmployeePosition(): Call<ServerResponseList<EmployeePosition>>

    @FormUrlEncoded
    @POST("v1/multi-account")
    fun chooseMultiAccount(
        @Field("admin_id") adminId: Int,
        @Field("token") token: String
    ): Call<ServerResponse<LoginAdmin>>

    @FormUrlEncoded
    @POST("v1/auth/send-code")
    fun sendAuthCode(@Field("email") email: String, @Field("device") device: String = ""): Call<ServerResponse<AuthCodeResponse>>

    @FormUrlEncoded
    @POST("v1/auth/with-code")
    fun loginWithCode(@Field("token") token: String,
    @Field("code") code: String): Call<ServerResponse<LoginAdmin>>
}