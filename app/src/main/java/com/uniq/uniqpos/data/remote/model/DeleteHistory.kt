package com.uniq.uniqpos.data.remote.model


import com.google.gson.annotations.SerializedName

data class DeleteHistory(

	@field:SerializedName("date_time")
	val dateTime: String? = null,

	@field:SerializedName("id")
	val id: String? = null,

	@field:SerializedName("table_id")
	val tableId: String = "",

	@field:SerializedName("table_name")
	val tableName: String? = null,

	@field:SerializedName("admin_fkid")
	val adminFkid: String? = null
)