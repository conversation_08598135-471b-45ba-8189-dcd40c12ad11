package com.uniq.uniqpos.data.local.entity

import androidx.room.Entity
import androidx.room.PrimaryKey
import com.google.gson.annotations.SerializedName
import java.io.Serializable

@Entity (tableName = "member_type")
data class MemberTypeEntity(

        @PrimaryKey
        @field:SerializedName("type_id")
        val typeId: Int,

        @field:SerializedName("is_active")
        val isActive: Int? = null,

        @field:SerializedName("created")
        val created: Long? = null,

        @field:SerializedName("price")
        val price: Int? = null,

        @field:SerializedName("name")
        val name: String? = null,

        @field:SerializedName("modified")
        val modified: Long? = null,

        @field:SerializedName("admin_fkid")
        val adminFkid: Int? = null,

        @field:SerializedName("point")
        val point: Int? = null,

        @field:SerializedName("target")
        val target: String? = null
): Serializable