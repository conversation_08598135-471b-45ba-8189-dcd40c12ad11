package com.uniq.uniqpos.data.local.entity

import androidx.room.Entity
import androidx.room.PrimaryKey
import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Entity(tableName = "multiple_price")
@Parcelize
data class MultiplePriceEntity(

        @PrimaryKey
        @field:SerializedName("multipleprice_id")
        val multiplepriceId: Int,

        @field:SerializedName("data_modified")
        val dataModified: Long? = null,

        @field:SerializedName("price")
        val price: Int = 0,

        @field:SerializedName("qty")
        val qty: Int = 0,

        @field:SerializedName("data_created")
        val dataCreated: Long? = null,

        @field:SerializedName("data_status")
        val dataStatus: String? = null,

        @field:SerializedName("product_detail_fkid")
        val productDetailFkid: Int? = null
) : Parcelable