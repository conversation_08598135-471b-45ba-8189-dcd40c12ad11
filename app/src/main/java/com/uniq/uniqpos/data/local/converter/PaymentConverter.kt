package com.uniq.uniqpos.data.local.converter

import androidx.room.TypeConverter
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.uniq.uniqpos.data.remote.model.Payment

/**
 * Created by ANNASBlackHat on 11/01/18.
 */
class PaymentConverter {
    @TypeConverter
    fun fromObjectToArray(payments: ArrayList<Payment>): String {
        return Gson().toJson(payments)
    }

    @TypeConverter
    fun fromStringToObject(payment: String): ArrayList<Payment> {
        val type = object : TypeToken<ArrayList<Payment>>() {}.type
        return Gson().fromJson<ArrayList<Payment>>(payment, type)
    }
}