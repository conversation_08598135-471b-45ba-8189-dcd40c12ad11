package com.uniq.uniqpos.data.remote.auth

import android.content.Context
import android.content.Intent
import com.google.firebase.analytics.FirebaseAnalytics
import com.google.firebase.analytics.ktx.analytics
import com.google.firebase.ktx.Firebase
import com.google.gson.Gson
import com.uniq.uniqpos.BuildConfig
import com.uniq.uniqpos.R
import com.uniq.uniqpos.data.local.sharedpref.*
import com.uniq.uniqpos.model.Admin
import com.uniq.uniqpos.model.AuthToken
import com.uniq.uniqpos.util.*
import com.uniq.uniqpos.view.login.LoginAdminActivity
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import okhttp3.FormBody
import okhttp3.OkHttpClient
import okhttp3.Request
import timber.log.Timber
import java.util.concurrent.TimeUnit

/**
 * Created by annasblackhat on 15/02/18.
 */
class TokenManager(val context: Context) {

    private var isRequestOnProgress = false

    fun clearToken() {
        context.sharedPref().clearJson(SharedPref.TOKEN_DATA)
    }

    fun getToken(): AuthToken {
        return context.getJson(SharedPref.TOKEN_DATA, AuthToken::class.java) ?: AuthToken()
    }

    fun saveToken(authToken: AuthToken) {
        context.putJson(SharedPref.TOKEN_DATA, authToken)
    }

    fun isReauthorizeOnProcess() = isRequestOnProgress

    //only allow to request new token if user has refresh token in local db
    fun isAllowedToRefreshToken(): Boolean {
        //IF ALL USER ALREADY UPDATED APP
//        return getToken().refreshToken?.let {
//            !isRequestOnProgress
//        } ?: kotlin.run {
//            Timber.i("<AUTH> refresh token user is empty")
//            false
//        }

        return !isRequestOnProgress
    }

    fun refreshToken() {
        if (isRequestOnProgress) {
            return
        }
        isRequestOnProgress = true
        val authStart = System.currentTimeMillis()

        val request = getToken().refreshToken?.takeIf { it.isNotBlank() }?.let { refreshToken ->
            Timber.i("Reauthorize... - NEW")
            val outletId = context.outlet()?.outletId ?: "*"
            val formBody = FormBody.Builder()
                    .add("refresh_token", refreshToken)
                    .add("scope", outletId.toString())
                    .build()

            Request.Builder()
                    .url(BuildConfig.BASE_URL + "v2/auth/token")
                    .post(formBody)
                    .addHeader("Authorization", context.getString(R.string.authorization).decrypt())
                    .build()

        } ?: kotlin.run {
            Timber.i("Reauthorize... - LEGACY")

            context.getJson(SharedPref.ADMIN_DATA, Admin::class.java)?.let { admin ->
                val formBody = FormBody.Builder()
                        .add("admin_id", admin.email ?: "")
                        .add("admin_secret", admin.password ?: "")
                        .add("device", context.getLocalDataString(SharedPref.DEVICE_ID))
                        .build()

                Request.Builder()
                        .url(BuildConfig.BASE_URL + "v1/auth/token")
                        .post(formBody)
                        .addHeader("Authorization", context.getString(R.string.authorization).decrypt())
                        .build()
            }
        }

        val okHttpClient = OkHttpClient.Builder()
                .connectTimeout(50, TimeUnit.SECONDS)
                .readTimeout(50, TimeUnit.SECONDS)
                .build()

        try {
            val response = okHttpClient.newCall(request!!).execute()
            when (response.code()) {
                200 -> {
                    val jsonData = response.body()?.string()
                    saveToken(Gson().fromJson(jsonData, AuthToken::class.java))
                    Timber.i("Reauthorize Finish...")
                    GlobalScope.launch {
                        delay(500)
                        isRequestOnProgress = false
                    }
                }
                401 -> {
                    Timber.i("re-authorization return 401. Clear User Session & ReLogin")
                    isRequestOnProgress = false
                    context.putData(SharedPref.LOGIN_ADMIN_STATUS, false)
                    context.clearJson(SharedPref.ADMIN_DATA)
                    clearToken()

                    val intentBroadcast = Intent(Intent.ACTION_VIEW)
                    intentBroadcast.putExtra("action", Constant.ACTION_FINISH_ALL_ACTIVITIES)
                    context.sendBroadcast(intentBroadcast)

                    val intent = Intent(context, LoginAdminActivity::class.java)
                    intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                    context.startActivity(intent)
                }
                else -> {
                    isRequestOnProgress = false
                    Timber.i("reauthorize return unexpected code. ${response.code()} -> \n${Gson().toJson(response)}")
                    val outlet = context.outlet()
                    Firebase.analytics
                            .logEvent("warning",
                                    bundleOf("Outlet" to "RF:${outlet?.outletId}:${outlet?.name}",
                                            "Message" to "Reauthorize Failed",
                                            "Detail" to "RF:${response.code()}"))
                }
            }
        } catch (e: Exception) {
            isRequestOnProgress = false
            Timber.i("reauthorize error. $e")
            val outlet = context.outlet()
            Firebase.analytics
                    .logEvent("warning",
                            bundleOf("Outlet" to "RE:${outlet?.outletId}:${outlet?.name}",
                                    "Message" to "Reauthorize Error",
                                    "Detail" to "RE:${e.message}"))
        }

        //calculate time diff
        val diffSecond = (System.currentTimeMillis()..authStart).diffSecond().forcePositive()
        Timber.i("sync start $authStart | end ${System.currentTimeMillis()} | diff : $diffSecond second")
        if (diffSecond > 15) {
            val outlet = context.outlet()
            FirebaseAnalytics.getInstance(context)
                    .logEvent("warning",
                            bundleOf("Outlet" to "RTL:${outlet?.outletId}:${outlet?.name}",
                                    "Message" to "Reauthorize Error",
                                    "Detail" to "RTL:$diffSecond seconds"))
        }
    }
}