package com.uniq.uniqpos.data.local.entity

import android.annotation.SuppressLint
import androidx.room.Entity
import androidx.room.PrimaryKey
import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import com.uniq.uniqpos.data.remote.model.Member
import com.uniq.uniqpos.util.safe
import kotlinx.android.parcel.Parcelize
import java.io.Serializable

@Entity(tableName = "member")
@Parcelize
@SuppressLint("ParcelCreator")
data class MemberEntity(

        @PrimaryKey
        @field:SerializedName("member_id")
        val memberId: String,

        @field:SerializedName("reason")
        val reason: String? = null,

        @field:SerializedName("address")
        val address: String? = null,

        @field:SerializedName("gender")
        val gender: Int? = null,

        @field:SerializedName("date_of_birth")
        val dateOfBirth: Long? = null,

        @field:SerializedName("register_date")
        val registerDate: Long? = null,

        @field:SerializedName("admin_fkid")
        val adminFkid: Int? = null,

        @field:SerializedName("expired_date")
        val expiredDate: Long? = null,

        @field:SerializedName("password")
        val password: String? = null,

        @field:SerializedName("phone")
        val phone: String? = null,

        @field:SerializedName("member_from")
        val memberFrom: String? = null,

        @field:SerializedName("name")
        val name: String? = null,

        @field:SerializedName("customer_id")
        val customerId: Int? = null,

        @field:SerializedName("postal_code")
        val postalCode: Int? = null,

        @field:SerializedName("product_fkid")
        val productFkid: Int? = null,

        @field:SerializedName("email")
        val email: String? = null,

        @field:SerializedName("status")
        val status: Int? = null,

        @field:SerializedName("type_name")
        var typeName: String? = null,

        @field:SerializedName("type_id")
        var typeId: Int = 0

) : Parcelable