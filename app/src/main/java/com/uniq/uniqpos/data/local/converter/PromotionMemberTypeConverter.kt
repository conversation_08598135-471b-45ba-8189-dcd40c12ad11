package com.uniq.uniqpos.data.local.converter

import androidx.room.TypeConverter
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.uniq.uniqpos.data.local.entity.MemberTypeEntity


/**
 * Created by annasblackhat on 28/07/20.
 */

class PromotionMemberTypeConverter {
    @TypeConverter
    fun fromArrayToJson(array: ArrayList<MemberTypeEntity>): String {
        return Gson().toJson(array)
    }

    @TypeConverter
    fun fromJsonToArray(json: String?): ArrayList<MemberTypeEntity> {
        val type = object : TypeToken<ArrayList<MemberTypeEntity>>() {}.type
        return json?.takeIf { it.isNotEmpty() }?.let {
            Gson().fromJson<ArrayList<MemberTypeEntity>>(it, type)
        } ?: ArrayList()
    }
}