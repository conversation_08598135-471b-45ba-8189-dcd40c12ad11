package com.uniq.uniqpos.data.local.sharedpref

import android.content.Context
import android.content.SharedPreferences
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.uniq.uniqpos.util.decrypt
import com.uniq.uniqpos.util.encrypt
import com.uniq.uniqpos.util.safe
import com.uniq.uniqpos.util.safeToInt
import timber.log.Timber


/**
 * Created by ANNASBlackHat on 16/09/2017.
 */

class SharedPref(context: Context) {

    private val sp: SharedPreferences = context.getSharedPreferences("uniq", Context.MODE_PRIVATE)
    fun getSharedPreference() = sp

    companion object {
        const val EMPLOYEE_POSITION_DATA: String = "employee_position"
        const val AUTOSHOW_PROMO_FREE: String = "auto_show_promo_free"
        const val PENDING_VOUCHER_REMOVE: String = "pending_voucher_should_be_removed"
        const val LOGIN_ADMIN_STATUS = "isadminlogin"
        const val LOGIN_EMPLOYEE_STATUS = "isemployeelogin"
        const val ADMIN_DATA = "data_admin"
        const val EMPLOYEE_DATA = "data_employee"
        const val OUTLET_DATA = "data_outlet"
        const val SHIFT_DATA = "shift_data"
        const val SHIFT_NAME = "shift_name"
        const val SETTING_LAYOUT_TRANSACTION = "layout_transaction"
        const val SETTING_HIDE_EXTRA = "hide_extra"
        const val DEVICE_ID = "device_id"
        const val PRINTER_SERVER_STATUS = "printer_server_status"
        const val IP_SERVER = "ip_server"
        const val TOKEN_DATA = "token"
        const val EMPLOYEE_LIST = "employeelist"
        const val PRINTER_DEACTIVE = "list_deactive_printer"
        const val SUBSCRIPTION_STATUS = "subscription_status"
        const val TEMP_VOUCHER = "temporary_voucher"
        const val PREV_CLOSE_SHIFT_STATUS = "previous_close_shift_status_"
        const val LAST_SYNC = "last_sync_process"
        const val FREE_INFO_HISTORY = "free_item_info_history"
        const val PRINTER_WIFI_SUPPORT_STATUS =
            "support_status_of_print_wifi" //hold information, whether the device supported or not
        const val LOG_KEYS = "log_keys"
        const val HELP_DATA = "help_data"
        const val WARNING_APP = "app_warning"
        const val PRINTER_PORT = "sp_printer_port"
        const val PRINT_WIFI_V2 = "use_print_wifi_v2"
        const val TRANSACTION_CONFIG = "transaction_config"
        const val NOTIFICATION_PATH = "notification_sound_path"
        const val SUBCATEGORY_DATA = "subcategory_data"
    }

    fun putData(key: String, value: Any?) {
        if(value == null) return
        val spe = sp.edit()
        spe.putString(key.encrypt(), value.toString().encrypt())
        spe.apply()
    }

    fun getBoolean(key: String, default: Boolean = false): Boolean {
        var result = default
        try {
            val value = sp.getString(key.encrypt(), default.toString()).decrypt()
            result = value.toBoolean()
        } catch (e: Exception) {
            Timber.i("Get Boolean error: $e")
        }
        return result
    }

    fun getString(key: String, default: String? = null): String? {
        val result = sp.getString(key.encrypt(), default)
        return result?.decrypt() ?: default
    }

    fun <T : Any> getJsonList(key: String): List<T> {
        this.getString(key)?.let { data ->
            try {
                val type = object : TypeToken<List<T>>() {}.type
                val json = Gson().fromJson<List<T>>(data.trim(), type)
                return json
            } catch (e: Exception) {
                Timber.i("Get Json List error: $e\n>>data: $data")
            }
        }
        return emptyList()
    }

    fun removeAllData() {
        val spe = sp.edit()
        spe.clear()
        spe.apply()
    }

    fun clearJson(jsonKey: String) {
//        val fields = sp.getString(jsonKey, null)?.split(",")
//        fields?.forEach { spe.remove(it) }
        val spe = sp.edit()
        spe.remove(jsonKey.encrypt())
        spe.apply()
    }

    fun <T : Any> saveJson(jsonKey: String, cls: T) {
        val data = Gson().toJson(cls)
        putData(jsonKey, data)
//        var values = ""
//        val map = Gson().fromJson(Gson().toJsonTree(cls), Map::class.java)
//        for ((key, value) in map) {
//            putData("${jsonKey}_${key as String}", value)
//            values +="${jsonKey}_${key as String},"
//            if( value is Int) {
//                Timber.i("Yes value is int " + value.toString())
//            }
//        }
//        spe.putString(jsonKey.encrypt(), values.encrypt())
//        spe.commit()
    }

    fun <T> getJson(key: String, cls: Class<T>): T? {
        val data = getString(key)
        return Gson().fromJson(data, cls)
//        val fields = getString(key)?.split(",")
//        val mapLogin = HashMap<String, Any>()
//        fields?.let { for(s in fields) mapLogin.put(s.replace(key+"_",""), getString(s))}
//        return Gson().fromJson(Gson().toJsonTree(mapLogin), cls)
    }

}

fun Context.sharedPref() = SharedPref(this)

fun Context.putData(key: String, value: Any) {
    val sp = SharedPref(this)
    sp.putData(key, value)
}

fun Context.getLocalDataString(key: String, default: String? = null): String {
    val sp = SharedPref(this)
    return sp.getString(key, default) ?: default.safe()
}

fun Context.getLocalDataBoolean(key: String, default: Boolean = false): Boolean {
    val sp = SharedPref(this)
    return sp.getBoolean(key, default)
}

fun Context.getLocalDataInt(key: String, default: Int = 0): Int {
    return SharedPref(this).getString(key, default.toString()).safeToInt()
}

fun <T : Any> Context.putJson(key: String, cls: T) {
    val sp = SharedPref(this)
    sp.saveJson(key, cls)
}

fun <T : Any> Context.getJson(key: String, cls: Class<T>): T? {
    val sp = SharedPref(this)
    return sp.getJson(key, cls)
}

fun <T : Any> Context.getJsonList(key: String): List<T> {
    val sp = SharedPref(this)
    return sp.getJsonList(key)
}

fun Context.clearJson(key: String) {
    val sp = SharedPref(this)
    sp.clearJson(key)
}

fun Context.clearAllData() {
    val sp = SharedPref(this)
    sp.removeAllData()
}
