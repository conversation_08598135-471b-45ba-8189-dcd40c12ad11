package com.uniq.uniqpos.data.remote.model


import com.google.gson.annotations.SerializedName

data class SelfOrder(
    @SerializedName("id_order")
    val idOrder: String?,
    @SerializedName("order_list")
    val orderList: List<SelfOrderDetail?>?,
    @SerializedName("outlet_id")
    val outletId: Int?,
    val contact: String?,
    @SerializedName("order_item")
    val orderItem: String? = null,
    @SerializedName("expired_at")
    val expiredAt: Long? = null,
    @SerializedName("order_code")
    val orderCode: String? = null,
    @SerializedName("time_created")
    val timeCreated: Long? = null,
    @SerializedName("customer_name")
    val customerName: String? = null,
    @SerializedName("dining_table")
    val diningTable: String? = null,
    var itemNames: String? = null,
    @SerializedName("applied_at")
    var appliedAt: Long? = 0,
    var member: Member? = null
)