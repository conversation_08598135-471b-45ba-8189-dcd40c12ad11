package com.uniq.uniqpos.data.local.entity

import androidx.room.Entity
import androidx.room.PrimaryKey
import androidx.room.TypeConverters
import com.google.gson.annotations.SerializedName
import com.uniq.uniqpos.data.local.converter.LinkMenuConverter
import com.uniq.uniqpos.model.LinkMenuDetail


/**
 * Created by annasblackhat on 22/03/18.
 */

@Entity (tableName = "link_menu")
@TypeConverters(LinkMenuConverter::class)
data class LinkMenuEntity (

        @PrimaryKey
        @field:SerializedName("linkmenu_id")
        val linkmenuId: Int,

        @field:SerializedName("order_no")
        val orderNo: Int = 0,

        @field:SerializedName("product_fkid")
        val productFkid: Int = 0,

        @field:SerializedName("product_detail_fkid")
        val productDetailFkid: Int = 0,

        @field:SerializedName("data_modified")
        val dataModified: Long = System.currentTimeMillis(),

        @field:SerializedName("name")
        val name: String = "",

        @field:SerializedName("description")
        val description: String? = null,

        @field:SerializedName("detail")
        val linkMenuDetail: ArrayList<LinkMenuDetail> = ArrayList(),

        @field:SerializedName("data_created")
        val dataCreated: Long = System.currentTimeMillis(),

        @field:SerializedName("data_status")
        val dataStatus: String? = null,

        @field:SerializedName("admin_fkid")
        val adminFkid: Int = 0,

        @field:SerializedName("outlet_fkid")
        val outletFkid: Int = 0,

        @field:SerializedName("is_multiplechoice")
        val multiplechoice: Int = 0
)