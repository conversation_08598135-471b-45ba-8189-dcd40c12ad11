package com.uniq.uniqpos.data.remote.repository

import androidx.lifecycle.LiveData
import com.uniq.uniqpos.data.local.dao.LastSyncDao
import com.uniq.uniqpos.data.local.dao.OutletDao
import com.uniq.uniqpos.data.local.dao.ProductDao
import com.uniq.uniqpos.data.local.dao.PurchaseDao
import com.uniq.uniqpos.data.local.entity.*
import com.uniq.uniqpos.data.remote.NetworkBoundResource
import com.uniq.uniqpos.data.remote.Resource
import com.uniq.uniqpos.data.remote.model.ServerResponseList
import com.uniq.uniqpos.data.remote.service.OutletService
import com.uniq.uniqpos.data.remote.service.ProductService
import com.uniq.uniqpos.data.remote.service.PurchaseService
import com.uniq.uniqpos.util.await
import com.uniq.uniqpos.util.awaitAsync
import com.uniq.uniqpos.util.awaitListBase
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import retrofit2.await
import timber.log.Timber
import javax.inject.Inject
import kotlin.LazyThreadSafetyMode.NONE

/**
 * Created by annasblackhat on 14/02/19
 */
class PurchaseRepository @Inject constructor(
    val purchaseDao: PurchaseDao,
    val lastSyncDao: LastSyncDao,
    val outletService: OutletService,
    val outletDao: OutletDao,
    val productDao: ProductDao,
    val productService: ProductService,
    val purchaseService: PurchaseService
) {

    val uniqueOpCostName: LiveData<List<String>> by lazy(NONE) {
        purchaseDao.getUniqueOpCostName()
    }

    suspend fun getHistoryOperationalCost() = purchaseDao.getHistoryOperationalCost()
    suspend fun getUniqueOpCostNameOnce() = purchaseDao.getUniqueOpCostNameOnce()

    fun syncPurchaseData(outletId: Int?) {
        var sync = lastSyncDao.getLastSync(OperationalCostEntity::class.java.simpleName)
        purchaseService.getOperationalCost(outletId, sync?.lastSync ?: 0)
            .awaitListBase { response ->
                response.data?.let { listData ->
                    listData.forEach { it.synced = true }
                    purchaseDao.saveOperationalCosts(listData)
                }
                lastSyncDao.saveLastSync(
                    LastSyncEntity(
                        OperationalCostEntity::class.java.simpleName,
                        response.millis
                    )
                )
            }

        purchaseDao.getUnSyncOperationalCost().forEach { operationalCostEntity ->
            purchaseService.saveOperationalCost(operationalCostEntity).await { data ->
                data?.let {
                    Timber.i("saving operational cost: ${it.operationalcostId}")
                    purchaseDao.saveOperationalCost(it.copy(synced = true))
                    Timber.i("deleting operational cost: ${operationalCostEntity.operationalcostId}")
                    purchaseDao.deleteOperationalCost(operationalCostEntity)
                }
            }
        }
    }

    fun getSupplierLive(): LiveData<Resource<List<SupplierEntity>>> {
        return object :
            NetworkBoundResource<List<SupplierEntity>, ServerResponseList<SupplierEntity>>() {
            override fun saveCallResult(item: ServerResponseList<SupplierEntity>?) {
                item?.data?.let { items ->
                    items.forEach { it.synced = true }
                    purchaseDao.saveSuppliers(items)
                    lastSyncDao.saveLastSync(
                        LastSyncEntity(
                            SupplierEntity::class.java.simpleName,
                            item.millis
                        )
                    )
                }
            }

            override fun loadFromDb() = purchaseDao.getSuppliersLive()
            override fun createCall(lastSync: Long) = purchaseService.getSupplier(lastSync)
            override fun getLastSync() =
                lastSyncDao.getLastSync(SupplierEntity::class.java.simpleName)?.lastSync

        }.asLiveData
    }

    fun getBankLive(outletId: Int?): LiveData<Resource<List<BankEntity>>> {
        return object : NetworkBoundResource<List<BankEntity>, ServerResponseList<BankEntity>>() {
            override fun saveCallResult(item: ServerResponseList<BankEntity>?) {
                item?.data?.let { data ->
                    outletDao.saveBanks(data)
                    lastSyncDao.saveLastSync(
                        LastSyncEntity(
                            BankEntity::class.java.simpleName,
                            item.millis
                        )
                    )
                }
            }

            override fun loadFromDb() = outletDao.getBanks()
            override fun createCall(lastSync: Long) = outletService.getBank(outletId)
            override fun getLastSync() =
                lastSyncDao.getLastSync(BankEntity::class.java.simpleName)?.lastSync

        }.asLiveData
    }

    fun getPurchaseReportCategoryLive(): LiveData<Resource<List<PurchaseReportCategoryEntity>>> {
        return object :
            NetworkBoundResource<List<PurchaseReportCategoryEntity>, ServerResponseList<PurchaseReportCategoryEntity>>() {
            override fun saveCallResult(item: ServerResponseList<PurchaseReportCategoryEntity>?) {
                item?.data?.let { data ->
                    productDao.savePurchaseReportCategories(data)
                    lastSyncDao.saveLastSync(
                        LastSyncEntity(
                            PurchaseReportCategoryEntity::class.java.simpleName,
                            item.millis
                        )
                    )
                }
            }

            override fun loadFromDb() = productDao.getPurchaseReportCategoriesLive()
            override fun createCall(lastSync: Long) =
                productService.getPurchaseReportCategory(lastSync)

            override fun getLastSync() =
                lastSyncDao.getLastSync(PurchaseReportCategoryEntity::class.java.simpleName)?.lastSync
        }.asLiveData
    }

    fun getOperationalCostLive(outeltId: Int?): LiveData<Resource<List<OperationalCostEntity>>> {
        return object :
            NetworkBoundResource<List<OperationalCostEntity>, ServerResponseList<OperationalCostEntity>>() {
            override fun saveCallResult(item: ServerResponseList<OperationalCostEntity>?) {
                item?.data?.let { data ->
                    data.forEach { it.synced = true }
                    purchaseDao.saveOperationalCosts(data)
                    lastSyncDao.saveLastSync(
                        LastSyncEntity(
                            OperationalCostEntity::class.java.simpleName,
                            item.millis
                        )
                    )
                }
            }

            override fun loadFromDb() = purchaseDao.getOperationalCostStartAtLive()
            override fun createCall(lastSync: Long) =
                purchaseService.getOperationalCost(outeltId, lastSync)

            override fun getLastSync() =
                lastSyncDao.getLastSync(OperationalCostEntity::class.java.simpleName)?.lastSync
        }.asLiveData
    }

    suspend fun saveOperationalCost(operationalCostEntity: OperationalCostEntity) {
        withContext(Dispatchers.IO) {
            try {
                val response = purchaseService.saveOperationalCost(operationalCostEntity).await()
                response.data?.let { operationalCost ->
                    purchaseDao.saveOperationalCost(operationalCost.copy(synced = true))
                }
            } catch (e: Exception) {
                //save to local
                Timber.i("saving operational cost to local only, cause: $e")
                purchaseDao.saveOperationalCost(
                    operationalCostEntity.copy(
                        synced = false,
                        operationalcostId = "tmp_${System.currentTimeMillis()}"
                    )
                )
//                throw e
            }
        }
    }

    suspend fun saveSupplier(supplier: SupplierEntity) {
        withContext(Dispatchers.IO) {
            try {
                val resp = purchaseService.addSupplier(supplier).await()
                if (resp.status) {
                    resp.data?.let { id ->
                        supplier.supplierId = id
                        purchaseDao.saveSuppliers(listOf(supplier))
                    }
                } else {
                    throw Exception(resp.message)
                }
            } catch (e: Exception) {
                throw e
            }
        }
    }

    suspend fun savePurchaseReportCategory(value: String) {
        withContext(Dispatchers.IO) {
            try {
                val resp = purchaseService.addPurchaseReportCategory(value).await()
                if (resp.status) {
                    resp.data?.let { id ->
                        val purcRepCat = PurchaseReportCategoryEntity(id, value, dataStatus = "on")
                        productDao.savePurchaseReportCategories(listOf(purcRepCat))
                    }
                } else {
                    throw Exception(resp.message)
                }
            } catch (e: Exception) {
                throw e
            }
        }
    }
}