package com.uniq.uniqpos.data.remote.model

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class PromotionDetail(

        @field:SerializedName("max_redeem")
        val maxRedeem: Int = 0,

        @field:SerializedName("promo_type")
        val promoType: String = "",

        @field:SerializedName("products_free")
        val productsFree: List<PromotionProduct> = ArrayList(),

        @field:SerializedName("used")
        val used: Int = 0,

        @field:SerializedName("products")
        val products: List<PromotionProduct> = ArrayList(),

        @field:SerializedName("min_order")
        val minOrder: Int = 0

) : Parcelable

fun PromotionDetail.simplify(promo: Promotion, productDetailIds: List<Int> = listOf()): PromotionDetail {
        val products = promo.promotionDetail?.let { detail ->
                detail.products.filter { product -> productDetailIds.any { id -> id == product.productDetailFkid } }
        } ?: listOf()
        return this.copy(products = products, productsFree = listOf())
}