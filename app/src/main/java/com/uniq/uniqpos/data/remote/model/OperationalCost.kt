package com.uniq.uniqpos.data.remote.model


import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class OperationalCost(

	@field:SerializedName("operationalcost_id")
	val operationalcostId: String? = null,

	@field:SerializedName("date")
	val date: String? = null,

	@field:SerializedName("total")
	val total: String = "0",

	@field:SerializedName("keterangan")
	val keterangan: String? = null,

	@field:SerializedName("user_fkid")
	val userFkid: String? = null,

	@field:SerializedName("harga")
	val harga: Int = 0,

	@field:SerializedName("qty")
	val qty: Int = 1,

	@field:SerializedName("data_created")
	val dataCreated: String? = null,

	@field:SerializedName("data_status")
	val dataStatus: String? = null,

	@field:SerializedName("opcost_name")
	val opcostName: String? = null,

	@field:SerializedName("supplier_fkid")
	val supplierFkid: String? = null,

	@field:SerializedName("outlet_fkid")
	val outletFkid: String? = null
) : Parcelable