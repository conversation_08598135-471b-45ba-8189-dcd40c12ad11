package com.uniq.uniqpos.data.remote.service

import com.uniq.uniqpos.data.local.entity.*
import com.uniq.uniqpos.data.remote.model.*
import com.uniq.uniqpos.model.Device
import com.uniq.uniqpos.model.SalesPaymentCreate
import com.uniq.uniqpos.model.SalesPaymentStatus
import retrofit2.Call
import retrofit2.http.*

/**
 * Created by ANNASBlackHat on 22/10/2017.
 */
interface SalesService {

    @POST("v1/sales")
    fun postSales(@Body salesEntity: SalesEntity): Call<ServerResponse<Ids>>

    @GET("v1/sales/{outletId}/{page}/{lastSync}")
    fun getSalesToday(@Path("outletId") outletId: Int,
                      @Path("page") page: Int,
                      @Path("lastSync") lastSync: Long = 0): Call<ServerResponseList<Sales>>

    @POST("v1/refund")
    fun postRefund(@Body refundEntity: RefundEntity): Call<ServerResponse<Any>>

    @GET("v1/sales/index/{outletId}/{lastSync}")
    fun getSales(@Path("outletId") outletId: String, @Path("lastSync") lastSync: Long = 0): Call<ServerResponseList<Sales>>

    @GET("v1/shift_available/{outlet_id}/{timeOffset}")
    fun getAvailableShift(@Path("outlet_id") outletId: Int, @Path("timeOffset") timeOffset: Int): Call<ServerResponseList<ShiftEntity>>

    @GET("/v1/outlet/shift/active/{outletId}")
    fun getActiveShift(@Path("outletId") outletId: Int): Call<ServerResponse<OpenShiftEntity>>

    @POST("v1/open_shift")
    fun addOpenShift(@Body shiftOpen: HashMap<String, Any?>): Call<ServerResponse<OpenShiftEntity>>

//    @FormUrlEncoded
//    @POST("v1/auth/logout_employee")
//    fun logoutEmployee(@Field("device_id") deviceId: String): Observable<ServerResponse<Any>>

    @FormUrlEncoded
    @POST("v1/auth/logout_employee")
    fun logoutEmployeeCall(@Field("device_id") deviceId: String): Call<ServerResponse<Any>>

//    @GET("v1/active_device/{open_shift_id}/{device_id}")
//    fun getActiveDevice(@Path("open_shift_id")openShiftId: Long, @Path("device_id")deviceId: String): Observable<ServerResponseList<Device>>

    @GET("v1/active_device/{open_shift_id}/{device_id}")
    fun getActiveDeviceCall(@Path("open_shift_id") openShiftId: Long, @Path("device_id") deviceId: String): Call<ServerResponseList<Device>>

    @POST("v1/sales_cart")
    fun postTmpSales(@Body tmpSalesEntity: TmpSalesEntity): Call<ServerResponse<Any>>

    @PUT("v1/sales_cart")
    fun updateTmpSales(@Body tmpSalesEntity: TmpSalesEntity): Call<ServerResponse<Any>>

    @GET("v1/sales_cart/{outletId}/{lastSync}")
    fun getTmpSales(@Path("outletId") outletId: Int?, @Path("lastSync") lastSync: Long): Call<ServerResponseList<TmpSalesEntity>>

    @GET("v1/reservation/outlet/{outletId}/{lastSync}")
    fun getReservationByOutlet(@Path("outletId") outletId: Int?,
                               @Path("lastSync") lastSync: Long = 0): Call<ServerResponseList<ReservationEntity>>

    @POST("v1/reservation")
    fun postReservation(@Body reservationEntity: ReservationEntity): Call<ServerResponse<Any>>

    @GET("v1/transaction/piutang/{outletId}/{lastSync}")
    fun getPiutang(@Path("outletId") outletId: Int?,
                   @Path("lastSync") lastSync: Long = 0): Call<ServerResponseList<PiutangEntity>>

    @POST("v1/transaction/piutang_history")
    fun postPiutangHistory(@Body piutangHistoryEntity: PiutangHistoryEntity): Call<ServerResponse<Ids>>

    @GET("v1/transaction/piutang_history/{outletId}/{lastSync}")
    fun getPiutangHistory(@Path("outletId") outletId: Int?,
                          @Path("lastSync") lastSync: Long = 0): Call<ServerResponseList<PiutangHistoryEntity>>

    @GET("v1/transaction/sales_cart/status/{salesId}")
    fun getSalesCartEditableStatus(@Path("salesId") salesId: String): Call<ServerResponse<TmpSalesEntity>>

    @GET("v1/outlet/device/on/{outletId}")
    fun getAvailableDevice(@Path("outletId") outletId: Int?): Call<ServerResponseList<Device>>

    @GET("v1/transaction/member/check/{outletId}")
    fun checkMember(@Path("outletId") outletId: Int?,
                    @Query("barcode") barcode: String): Call<ServerResponse<Member>>

    @GET("v1/transaction/promotion/check/{outletId}")
    fun checkPromotionCode(@Path("outletId") outletId: Int?,
                           @Query("qr_code") barcode: String): Call<ServerResponse<Promotion>>

    @FormUrlEncoded
    @POST("/v1/transaction/promotion/void")
    fun voidVoucher(@Field("outlet_id") outletId: Int?,
                    @Field("voucher_code") voucherCode: String?): Call<ServerResponse<Any>>

    @GET("v1/transaction/online/{outletId}/{lastSync}")
    fun getOrderSales(@Path("outletId") outletId: Int?,
                      @Path("lastSync") lastSync: Long = 0): Call<ServerResponseList<OrderSalesEntity>>

    @PUT("v1/transaction/online")
    fun updateOrderSale(@Body order: OrderSalesEntity): Call<ServerResponse<Any>>

    @FormUrlEncoded
    @PATCH("v2/order-sales/{id}")
    fun updateOrderSalesStatus(
        @Path("id") id: String,
        @Field("status") status: String,
        @Field("employee_id") employeeId: Int,
        @Field("message") message: String?
    ): Call<ServerResponse<Any>>

    @GET("v1/transaction/member/secret_code")
    fun checkSecretCode(@Query("code") code: String): Call<ServerResponse<Member>>

    @GET("v1/transaction/self_order/{code}")
    fun getSelfOrder(@Path("code") code: String): Call<ServerResponse<SelfOrder>>

    @FormUrlEncoded
    @POST("/v1/sales/validate")
    fun validateSalesSyncWithServer(@Field("open_shift_id") openShiftId: Long,
                                    @Field("sales_ids") salesIds: String): Call<ServerResponse<Map<String, List<String>>>>

    @GET("v1/transaction/notes")
    fun getSalesNotes(): Call<ServerResponseList<SalesNotes>>

    @FormUrlEncoded
    @POST("v1/sales-payment")
    fun createInstantPayment(@Field("id") salesId: String): Call<ServerResponse<SalesPaymentCreate>>

    @GET("v1/sales-payment/{id}")
    fun checkPaymentStatus(@Path("id")salesId: String): Call<ServerResponse<SalesPaymentStatus>>

    @GET("v1/sales")
    fun getSalesByIds(@Query("sales_ids") salesIds: String): Call<ServerResponseList<Sales>>

    @GET("v1/sales-tag")
    fun getSalesTag(@Query("outlet_id")outletId: Int): Call<ServerResponseList<SalesTagEntity>>
}