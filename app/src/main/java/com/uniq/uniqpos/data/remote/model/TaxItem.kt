package com.uniq.uniqpos.data.remote.model


import com.google.gson.annotations.SerializedName

data class TaxItem(

	@field:SerializedName("tax_fkid")
	val taxFkid: Int? = null,

	@field:SerializedName("tax_status")
	val taxStatus: String? = null,

	@field:SerializedName("tax_category")
	val taxCategory: String? = null,

	@field:SerializedName("sales_tax_id")
	val salesTaxId: Int? = null,

	@field:SerializedName("gratuity_id")
	val gratuityId: Int? = null,

	@field:SerializedName("data_created")
	val dataCreated: String? = null,

	@field:SerializedName("admin_fkid")
	val adminFkid: Int? = null,

	@field:SerializedName("total")
	val total: Int? = null,

	@field:SerializedName("jumlah")
	val jumlah: Int? = null,

	@field:SerializedName("tax_type")
	val taxType: String? = null,

	@field:SerializedName("data_modified")
	val dataModified: String? = null,

	@field:SerializedName("name")
	val name: String? = null,

	@field:SerializedName("sales_fkid")
	val salesFkid: String? = null,

	@field:SerializedName("data_status")
	val dataStatus: String? = null
)