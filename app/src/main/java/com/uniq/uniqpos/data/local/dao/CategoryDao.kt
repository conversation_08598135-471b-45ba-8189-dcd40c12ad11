package com.uniq.uniqpos.data.local.dao

import androidx.lifecycle.LiveData
import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import com.uniq.uniqpos.data.local.entity.SubCategoryEntity

/**
 * Created by ANNASBlackHat on 10/10/2017.
 */

@Dao
interface CategoryDao {
    @Query("SELECT * From subcategory ORDER BY name")
    fun loadCategories(): LiveData<List<SubCategoryEntity>>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun saveCategory(subCategories: List<SubCategoryEntity>)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun addCategory(subCategory: SubCategoryEntity)
}