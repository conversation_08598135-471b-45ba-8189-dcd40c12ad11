package com.uniq.uniqpos.data.local.entity

import android.annotation.SuppressLint
import androidx.room.Entity
import androidx.room.PrimaryKey
import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize
import java.io.Serializable

@Parcelize
@SuppressLint("ParcelCreator")
@Entity(tableName = "tax")
data class TaxEntity(

        @PrimaryKey
        @field:SerializedName("taxdetail_id")
        var taxdetailId: Int,

        @field:SerializedName("tax_fkid")
        var taxFkid: Int? = null,

        @field:SerializedName("data_created")
        var dataCreated: String? = null,

        @field:SerializedName("data_status")
        var dataStatus: String? = null,

        @field:SerializedName("product_detail_fkid")
        var productDetailFkid: Int? = null
) : Parcelable