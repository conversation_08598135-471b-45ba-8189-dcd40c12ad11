package com.uniq.uniqpos.data.local.entity

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class PromotionProduct(

        @field:SerializedName("promotion_product_id")
        val promotionProductId: Int? = null,

        @field:SerializedName("is_percent")
        val isPercent: Int = 0,

        @field:SerializedName("created")
        val created: String? = null,

        @field:SerializedName("price")
        val price: Int? = null,

        @field:SerializedName("product_detail_fkid")
        val productDetailId: Int? = null,

        @field:SerializedName("qty")
        val qty: Int = 0,

        @field:SerializedName("promotion_id")
        val promotionId: Int? = null,

        @field:SerializedName("product_price")
        val productPrice: Int? = null,

        @field:SerializedName("type")
        val type: String? = null,

        @field:SerializedName("ammount")
        val ammount: Int = 0,

        @field:SerializedName("point")
        val point: Int? = null
) : Parcelable