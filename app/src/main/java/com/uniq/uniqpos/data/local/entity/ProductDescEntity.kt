package com.uniq.uniqpos.data.local.entity

import androidx.room.Entity
import androidx.room.PrimaryKey
import com.google.gson.annotations.SerializedName

/**
 * Created by ANNASBlackHat on 20/12/17.
 */
@Entity(tableName = "product_desc")
data class ProductDescEntity (

        @PrimaryKey
        @field:SerializedName("product_description_id")
        var productDescriptionId: Int,

        @field:SerializedName("name")
        var name: String? = null,

        @field:SerializedName("price_add")
        var priceAdd: Int? = null,

        @field:SerializedName("data_created")
        var dataCreated: String? = null,

        @field:SerializedName("data_status")
        var dataStatus: String? = null,

        @field:SerializedName("outlet_fkid")
        var outletFkid: Int? = null
)