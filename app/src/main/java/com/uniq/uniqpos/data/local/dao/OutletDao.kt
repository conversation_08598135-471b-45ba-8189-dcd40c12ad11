package com.uniq.uniqpos.data.local.dao

import androidx.lifecycle.LiveData
import androidx.room.*
import com.uniq.uniqpos.data.local.entity.*

/**
 * Created by ANNASBlackHat on 08/01/18.
 */

@Dao
interface OutletDao {

    @Query("SELECT * FROM shift")
    fun getShiftsLive(): LiveData<List<ShiftEntity>>

    @Query("SELECT * FROM shift")
    suspend fun getShifts(): List<ShiftEntity>

    @Query("SELECT * FROM shift WHERE shiftId NOT IN (SELECT shiftFkid FROM open_shift WHERE timeOpen >= :dayStart AND timeOpen <= :dayEnd)")
    fun getAvailableShifts(dayStart: Long, dayEnd: Long): List<ShiftEntity>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun addShifts(shifts: List<ShiftEntity>)

    @Query("SELECT * FROM open_shift")
    fun getOpenShiftsLive(): LiveData<List<OpenShiftEntity>>

    @Query("SELECT * FROM open_shift WHERE synced = 0")
    fun getUnsyncedOpenShifts(): List<OpenShiftEntity>

    @Query("SELECT * FROM open_shift WHERE openShiftId = :id")
    fun getOpenShiftById(id: Int): OpenShiftEntity

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun addOpenShifts(openShifts: List<OpenShiftEntity>)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun addOpenShift(openShift: OpenShiftEntity)

    @Query("SELECT * FROM dining_table ORDER BY tableName")
    fun getTablesLive(): LiveData<List<DiningTableEntity>>

    @Query("SELECT * FROM dining_table WHERE diningTableId = :id LIMIT 1")
    fun getTableById(id: Long): DiningTableEntity

    @Query("SELECT * FROM dining_table WHERE tableName = :name LIMIT 1")
    fun getTableByName(name: String): DiningTableEntity?

    @Query("SELECT * FROM dining_table WHERE synced=0")
    fun getUnsyncedTable(): List<DiningTableEntity>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun saveTables(tables: List<DiningTableEntity>)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun saveTable(tableEntity: DiningTableEntity)

    @Query("UPDATE dining_table set status= :status, timeModified = :modifyTime, synced=0 WHERE tableName= :tableName")
    fun updateTableStatusByName(status: String, tableName: String, modifyTime: Long)

    @Update
    fun updateTable(diningTableEntity: DiningTableEntity)

    @Delete
    fun deleteTable(diningTableEntity: DiningTableEntity)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun addEmployee(employee: List<EmployeeEntity>)

    @Query("delete from employee")
    fun deleteAllEmployee()

    @Query("SELECT * FROM employee where dataStatus='on' ")
    fun getEmployees(): List<EmployeeEntity>

    @Query("SELECT * FROM employee where dataStatus='on'")
    fun getEmployeesLive(): LiveData<List<EmployeeEntity>>

    @Query("SELECT * FROM bank WHERE dataStatus = 'on'")
    fun getBanks(): LiveData<List<BankEntity>>

    @Query("SELECT * FROM bank WHERE dataStatus = 'on'")
    fun getBanksMedia(): List<BankEntity>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun saveBanks(banks: List<BankEntity>)

    @Query("delete from bank where bankId = :id")
    fun deleteBankById(id: Int)

}