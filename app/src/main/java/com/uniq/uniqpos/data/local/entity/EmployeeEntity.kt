package com.uniq.uniqpos.data.local.entity

import androidx.room.Entity
import androidx.room.PrimaryKey
import com.google.gson.annotations.SerializedName

/**
 * Created by annasblackhat on 19/03/18.
 */

@Entity (tableName = "employee")
data class EmployeeEntity (

        @PrimaryKey
        @field:SerializedName("employee_id")
        val employeeId: Int,

        @field:SerializedName("address")
        val address: String? = null,

        @field:SerializedName("role")
        val role: String? = null,

        @field:SerializedName("level")
        val level: Int? = null,

        @field:SerializedName("last_login")
        val lastLogin: Long? = null,

        @field:SerializedName("access_status_mobile")
        val accessStatusMobile: String? = null,

        @field:SerializedName("photo")
        val photo: String? = null,

        @field:SerializedName("data_created")
        val dataCreated: Long? = null,

        @field:SerializedName("access_mode")
        val accessMode: String? = null,

        @field:SerializedName("admin_fkid")
        val adminFkid: Int? = null,

        @field:SerializedName("jabatan_fkid")
        val jabatanFkid: Int? = null,

        @field:SerializedName("user_activation_expired")
        val userActivationExpired: String? = null,

        @field:SerializedName("role_mobile")
        val roleMobile: String? = null,

        @field:SerializedName("phone")
        val phone: String? = null,

        @field:SerializedName("data_modified")
        val dataModified: Long? = null,

        @field:SerializedName("access_status_web")
        val accessStatusWeb: String? = null,

        @field:SerializedName("name")
        var name: String? = null,

        @field:SerializedName("data_status")
        val dataStatus: String? = null,

        @field:SerializedName("date_join")
        val dateJoin: Long? = null,

        @field:SerializedName("email")
        val email: String? = null,

        val pin: String? = null
)