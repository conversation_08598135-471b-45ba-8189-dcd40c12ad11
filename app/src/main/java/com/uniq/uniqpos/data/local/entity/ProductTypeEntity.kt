package com.uniq.uniqpos.data.local.entity


import androidx.room.Entity
import androidx.room.PrimaryKey
import com.google.gson.annotations.SerializedName

@Entity(tableName = "product_type")
data class ProductTypeEntity(

        @PrimaryKey
        @field:SerializedName("products_type_id")
        val productsTypeId: Int,

        @field:SerializedName("data_modified")
        val dataModified: Long? = null,

        @field:SerializedName("name")
        val name: String? = null,


        @field:SerializedName("data_created")
        val dataCreated: Long? = null,

        @field:SerializedName("data_status")
        val dataStatus: String? = null,

        @field:SerializedName("admin_fkid")
        val adminFkid: Int? = null
)