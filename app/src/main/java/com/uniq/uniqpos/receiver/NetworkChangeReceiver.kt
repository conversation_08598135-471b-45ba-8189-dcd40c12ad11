package com.uniq.uniqpos.receiver

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import com.uniq.uniqpos.util.Constant
import com.uniq.uniqpos.util.NetworkUtil

/**
 * Created by ANNASBlackHat on 09/11/17.
 */
class NetworkChangeReceiver: BroadcastReceiver() {
    override fun onReceive(context: Context?, intent: Intent) {
        val status = NetworkUtil.getConnectivityStatusString(context)
        val intentBroadcast = Intent(Intent.ACTION_VIEW)
        intentBroadcast.putExtra("network_status", status != NetworkUtil.NETWORK_STATUS_NOT_CONNECTED)
        intentBroadcast.putExtra("action", Constant.STATE_NETWORK_STATUS)
//        intentBroadcast.putExtra("bt_status", BluetoothDevice.ACTION_ACL_CONNECTED == intent.action)
        context?.sendBroadcast(intentBroadcast)
    }
}