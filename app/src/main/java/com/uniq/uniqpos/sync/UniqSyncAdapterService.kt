package com.uniq.uniqpos.sync

import android.app.Service
import android.content.Intent
import android.os.IBinder
import dagger.android.AndroidInjection
import javax.inject.Inject

/**
 * Created by AnnasBlackHat on 11/10/2017.
 */
class UniqSyncAdapterService : Service() {

    @Inject
    lateinit var syncAdapter: UniqSyncAdapter

    override fun onCreate() {
        AndroidInjection.inject(this)
    }

    override fun onBind(p0: Intent?): IBinder {
        return syncAdapter.syncAdapterBinder
    }
}