package com.uniq.uniqpos.sync

import android.accounts.Account
import android.accounts.AccountManager
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.*
import android.os.Build
import android.os.Bundle
import androidx.core.app.NotificationCompat
import com.bugsnag.android.Bugsnag
import com.google.firebase.analytics.FirebaseAnalytics
import com.google.firebase.analytics.ktx.analytics
import com.google.firebase.ktx.Firebase
import com.uniq.uniqpos.BuildConfig
import com.uniq.uniqpos.R
import com.uniq.uniqpos.data.local.sharedpref.*
import com.uniq.uniqpos.data.remote.model.Outlet
import com.uniq.uniqpos.data.remote.model.ShiftOpen
import com.uniq.uniqpos.data.remote.repository.*
import com.uniq.uniqpos.model.Admin
import com.uniq.uniqpos.util.*
import com.uniq.uniqpos.view.chooseoperator.ChooseOperatorActivity
import timber.log.Timber
import java.io.File
import java.util.*
import javax.inject.Inject
import kotlin.math.absoluteValue


/**
 * Created by ANNASBlackHat on 11/10/2017.
 */
class UniqSyncAdapter @Inject
constructor(
    context: Context?,
    autoInitialize: Boolean,
    private val salesRepository: SalesRepository,
    private val productRepository: ProductRepository,
    private val settingRepository: SettingRepository,
    private val systemRepository: SystemRepository,
    private val outletRepository: OutletRepository,
    private val purchaseRepository: PurchaseRepository,
    private val promotionRepository: PromotionRepository
) : AbstractThreadedSyncAdapter(context, autoInitialize) {

    override fun onPerformSync(
        account: Account?,
        extra: Bundle?,
        authority: String?,
        provider: ContentProviderClient?,
        syncResult: SyncResult?
    ) {
        Timber.i("sync... ${extra?.getString("action").safe()}")
        sendNotification("Synchronizing data...", context.getString(R.string.app_name))

        val deviceId = context.getLocalDataString(SharedPref.DEVICE_ID)
        val deviceStatus =
            systemRepository.getGetDeviceStatus(deviceId) //this will also update last-sync of device
        val timeNowFromServer = deviceStatus?.millis ?: System.currentTimeMillis()

        val diffMinute =
            (System.currentTimeMillis()..timeNowFromServer).diffMinutes().absoluteValue
        if (diffMinute > 60) {
            Timber.i(">> TIME FROM SERVER : ${deviceStatus?.millis} | Diff Minutes : $diffMinute")
            Firebase.analytics
                .logEvent(
                    "fraud_detect",
                    bundleOf(
                        "type" to "Date Changed",
                        "info" to "Diff Minute : $diffMinute",
                        "outlet" to context.outlet()?.name,
                        "device" to "${Build.BRAND} ${Build.MODEL} - ${Build.SERIAL}"
                    )
                )

            val intentBroadcast = Intent(Intent.ACTION_VIEW)
            intentBroadcast.putExtra("action", Constant.ACTION_CLOSE_AND_OPEN_SETTING)
            intentBroadcast.putExtra("setting", android.provider.Settings.ACTION_DATE_SETTINGS)
            context.sendBroadcast(intentBroadcast)
            return
        }

        if (deviceStatus?.data?.deviceStatus == "off") {
            if (context.sharedPref()
                    .getBoolean(SharedPref.LOGIN_EMPLOYEE_STATUS) && context.role().pembayaran
            ) {
                val shift = context.getJson(SharedPref.SHIFT_DATA, ShiftOpen::class.java)
                shift?.openShiftId?.takeIf { it > 0 }?.let {
                    Timber.i("Device Status : ${deviceStatus?.data}")
                    context.putData(SharedPref.LOGIN_EMPLOYEE_STATUS, false)
                    context.sharedPref().clearJson(SharedPref.SHIFT_DATA)
                    context.sharedPref().clearJson(SharedPref.EMPLOYEE_DATA)

                    val intentBroadcast = Intent(Intent.ACTION_VIEW)
                    intentBroadcast.putExtra("action", Constant.ACTION_FINISH_ALL_ACTIVITIES)
                    context.sendBroadcast(intentBroadcast)

                    val intent = Intent(context, ChooseOperatorActivity::class.java)
                    intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                    context.startActivity(intent)

                    context.toast("You are logged out from Back-end System")
                }
            }
        }

        if (extra?.getString("action") == "clear_old_sales") {
            salesRepository.clearOldSales()
        }

        if (context.sharedPref().getBoolean(SharedPref.LOGIN_ADMIN_STATUS)) {
            val outlet = context.getJson(SharedPref.OUTLET_DATA, Outlet::class.java)
            val admin = context.getJson(SharedPref.ADMIN_DATA, Admin::class.java)
            val employee = context.employee()
            systemRepository.syncSetting(outlet?.outletId)
            outlet?.outletId?.let { outletId ->
                productRepository.syncProduct(outletId)
                salesRepository.syncSales(outletId, deviceId)
                settingRepository.syncSetting(outletId, context)
                outletRepository.syncOutlet(outletId, admin?.adminId, context)
                purchaseRepository.syncPurchaseData(outletId)
                promotionRepository.syncPromotion(outletId)

                uploadLogFiles(outletId, extra?.getString("action") == "action_upload_log")
            } ?: run {
                Timber.i("sync failed, outletId is null")
            }

            employee?.employeeId?.let { employeeId ->
                outletRepository.getEmployeeRole(employeeId).await {
                    if (context.employee()?.employeeId == employeeId && it != null && it.safe()
                            .isNotBlank()
                    ) {
                        context.putJson(SharedPref.EMPLOYEE_DATA, employee.copy(roleMobile = it))
                    }
                }
            }

            systemRepository.getSubscriptionStatus(deviceId)?.let { subscription ->
                context.putJson(SharedPref.SUBSCRIPTION_STATUS, subscription)
                if (subscription.timeExpiredCount < 0) {
                    Timber.i("[SUBSCRIPTION] $subscription")
                    Timber.i("subscription expired... logging out")
                    context.putData(SharedPref.LOGIN_EMPLOYEE_STATUS, false)
                }
            }

            salesRepository.processPendingRemovedVoucher(context.sharedPref(), outlet?.outletId)
        } else {
            salesRepository.syncSales(deviceId = deviceId)
            Timber.i("sync skipped... admin not logged-in")
        }

        val (unSynced, lastSynced) = systemRepository.countUnsynced()
//        val diff = System.currentTimeMillis() - lastSynced
//        val diffMinutes = diff / (60 * 1000) % 60

        val title =
            context.getString(R.string.app_name) + if (unSynced > 0) " - $unSynced Unsynced" else ""
//        val message = if (lastSynced == 0.toLong()) "synchronization pending" else if (diffMinutes > 15) "Last synced ${lastSynced.dateTimeFormat()}" else "Synced at ${Date().dateTimeFormat("dd MMM HH:mm")}"
        val message =
            if (lastSynced == 0.toLong()) "synchronization may in progress" else "Last synced ${lastSynced.dateTimeFormat()}"
        sendNotification(message, title, false)

        val intentBroadcast = Intent(Intent.ACTION_VIEW)
        intentBroadcast.putExtra("action", Constant.STATE_FINISH_SYNCED)
        context.sendBroadcast(intentBroadcast)

        context.putData(SharedPref.LAST_SYNC, timeNowFromServer)
        Timber.i("Sync finish, lastSync: $lastSynced | timeFromServer: $timeNowFromServer")
    }

    //isForceUpload : if yes, it will upload all available log at given folder
    private fun uploadLogFiles(outletId: Int, isForceUpload: Boolean = false) {
//        if (BuildConfig.DEBUG) {
//            val keys = context.getLocalDataString(SharedPref.LOG_KEYS)
//            val currentKey = Date().dateFormat("ddMMyyyyHH")
//            (keys.split(",")).filter { it.isNotEmpty() && it != currentKey }.forEach { key ->
//                val logText = context.getLocalDataString(key)
//                val folderParent = File(context.filesDir, "logcat")
//                if (!folderParent.exists()) {
//                    folderParent.mkdirs()
//                }
//                val file = File(folderParent, "$key.txt")
//                file.printWriter().use { out -> out.println(logText) }
//            }
//            return
//        }

        val folderParent = File(context.filesDir, "logcat")
        val currentFile = Date().dateFormat("yyyy_MM_dd HH")
        folderParent.list()?.forEach { file ->
            try {
                if (isForceUpload || !file.startsWith(currentFile)) {
                    systemRepository.uploadLogFile(File(folderParent, file), outletId, context)
                }
            } catch (e: Exception) {
                Timber.d("upload log file error - $e")
            }
        }
    }

    companion object {
        private val SYNC_INTERVAL: Long = 60  // 60 seconds
        private val SYNC_FLEXTIME: Long = SYNC_INTERVAL / 3

        fun initializeSyncAdapter(context: Context) {
            getSyncAccount(context)
        }

        private fun getSyncAccount(context: Context): Account? {
            try {
                val accountManager =
                    context.getSystemService(Context.ACCOUNT_SERVICE) as AccountManager
                val newAccount = Account(
                    context.getString(R.string.app_name),
                    context.getString(R.string.account_type)
                )
                if (null == accountManager.getPassword(newAccount)) {
                    if (!accountManager.addAccountExplicitly(newAccount, "", null)) {
                        return null
                    }
                    onAccountCreated(newAccount, context)
                }
                return newAccount
            } catch (e: Exception) {
                Timber.i("get sync account err: $e")
            }
            return null
        }

        private fun onAccountCreated(newAccount: Account, context: Context) {
            UniqSyncAdapter.configurePeriodicSync(context, SYNC_INTERVAL, SYNC_FLEXTIME)
            ContentResolver.setSyncAutomatically(
                newAccount,
                context.getString(R.string.authority),
                true
            )
            syncImmediately(context)
        }

        private fun configurePeriodicSync(
            context: Context,
            synC_INTERVAL: Long,
            synC_FLEXTIME: Long
        ) {
            val account = getSyncAccount(context)
            val authority = context.getString(R.string.authority)
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
                val request = SyncRequest.Builder()
                    .syncPeriodic(synC_INTERVAL, synC_FLEXTIME)
                    .setSyncAdapter(account, authority)
                    .setExtras(Bundle())
                    .build()
                ContentResolver.requestSync(request)
            } else {
                ContentResolver.addPeriodicSync(account, authority, Bundle.EMPTY, synC_INTERVAL)
            }
        }

        fun syncImmediately(context: Context, action: String? = null) {
            val bundle = Bundle()
            bundle.putBoolean(ContentResolver.SYNC_EXTRAS_EXPEDITED, true)
            bundle.putBoolean(ContentResolver.SYNC_EXTRAS_MANUAL, true)
            action?.let { bundle.putString("action", action) }
            ContentResolver.requestSync(
                getSyncAccount(context),
                context.getString(R.string.authority),
                bundle
            )
        }
    }

    private fun sendNotification(
        messageBody: String,
        title: String = context.getString(R.string.app_name),
        isOngoing: Boolean = true
    ) {
        val pendingIntent = if(android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.S){
            PendingIntent.getActivity(
            context, 0 /* Request code */, Intent(),
                PendingIntent.FLAG_IMMUTABLE
        )
        }else{
            PendingIntent.getActivity(
            context, 0 /* Request code */, Intent(),
                PendingIntent.FLAG_ONE_SHOT or PendingIntent.FLAG_IMMUTABLE
        )
        } 

        val channelId = "last_sync_notif"
//        val defaultSoundUri = RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION)
        val notificationBuilder = NotificationCompat.Builder(context, channelId)
            .setSmallIcon(R.mipmap.ic_launcher)
            .setContentTitle(title)
            .setContentText(messageBody)
            .setAutoCancel(true)
//                .setSound(defaultSoundUri)
            .setContentIntent(pendingIntent)
            .setOngoing(isOngoing)

        val notificationManager =
            context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        notificationBuilder.priority = NotificationManager.IMPORTANCE_LOW
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                channelId,
                "Last Sync",
                NotificationManager.IMPORTANCE_LOW
            )
            channel.setSound(null, null)
            notificationManager.createNotificationChannel(channel)
        }

        notificationManager.notify(0 /* ID of notification */, notificationBuilder.build())
    }
}
