package com.uniq.uniqpos.sync

import android.app.job.JobInfo
import android.app.job.JobScheduler
import android.content.ComponentName
import android.content.Context
import android.os.Build

/**
 * Created by an<PERSON><PERSON><PERSON><PERSON> on 20/07/18
 */

fun Context.scheduleJob() {
    if(Build.VERSION.SDK_INT >= Build.VERSION_CODES.M){
        val serviceComponent = ComponentName(this, UniqJobService::class.java)
        val builder = JobInfo.Builder(0, serviceComponent)
        builder.setMinimumLatency(60 * 1000)
        builder.setOverrideDeadline(120 * 1000) //maximum delay
        builder.setRequiredNetworkType(JobInfo.NETWORK_TYPE_ANY)

        val jobScheduler = getSystemService(JobScheduler::class.java)
        jobScheduler.schedule(builder.build())
    }
}