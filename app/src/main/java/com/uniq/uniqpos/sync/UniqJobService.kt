package com.uniq.uniqpos.sync

import android.app.job.JobParameters
import android.app.job.JobService
import android.os.Build
import androidx.annotation.RequiresApi


/**
 * Created by an<PERSON><PERSON>ck<PERSON> on 20/07/18
 */

@RequiresApi(Build.VERSION_CODES.LOLLIPOP)
class UniqJobService: JobService() {
    override fun onStopJob(params: JobParameters?): <PERSON><PERSON><PERSON> {
        return true
    }

    override fun onStartJob(params: JobParameters?): <PERSON><PERSON><PERSON> {
        UniqSyncAdapter.syncImmediately(applicationContext)
        applicationContext.scheduleJob()
        return true
    }

}