package com.uniq.uniqpos.sync

import android.app.Service
import android.content.Intent
import android.os.IBinder
import com.uniq.uniqpos.sync.UniqAuthenticator

/**
 * Created by ANNASBlackHat on 11/10/2017.
 */
class UniqAuthenticatorService: Service() {

    private lateinit var mAuthenticator: UniqAuthenticator

    override fun onCreate() {
        mAuthenticator = UniqAuthenticator(this)
    }

    override fun onBind(p0: Intent?): IBinder {
       return mAuthenticator.iBinder
    }
}