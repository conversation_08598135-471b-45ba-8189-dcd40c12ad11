package com.uniq.uniqpos.model

import com.google.gson.Gson
import com.uniq.uniqpos.data.local.entity.SalesEntity
import org.junit.Test
import org.junit.Assert.*

class CustomFieldsIntegrationTest {

    @Test
    fun `CustomFields should serialize to JSON correctly`() {
        // Given
        val customFields = arrayListOf(
            CustomFields(
                settingTransactionFkid = 1,
                label = "Customer Type",
                value = "Member Baru"
            ),
            CustomFields(
                settingTransactionFkid = 2,
                label = "Notes",
                value = "Special request"
            )
        )

        // When
        val json = Gson().toJson(customFields)

        // Then
        assertTrue(json.contains("setting_transaction_fkid"))
        assertTrue(json.contains("Customer Type"))
        assertTrue(json.contains("Member Baru"))
        assertTrue(json.contains("Special request"))
    }

    @Test
    fun `SalesEntity should include customFields in JSON serialization`() {
        // Given
        val customFields = arrayListOf(
            CustomFields(
                settingTransactionFkid = 1,
                label = "Customer Type",
                value = "Member Baru"
            )
        )
        
        val salesEntity = SalesEntity(
            noNota = "TEST001",
            grandTotal = 50000,
            customer = "John Doe",
            customFields = customFields
        )

        // When
        val json = Gson().toJson(salesEntity)

        // Then
        assertTrue(json.contains("custom_fields"))
        assertTrue(json.contains("setting_transaction_fkid"))
        assertTrue(json.contains("Customer Type"))
        assertTrue(json.contains("Member Baru"))
    }

    @Test
    fun `SalesEntity should handle null customFields`() {
        // Given
        val salesEntity = SalesEntity(
            noNota = "TEST002",
            grandTotal = 25000,
            customer = "Jane Doe",
            customFields = null
        )

        // When
        val json = Gson().toJson(salesEntity)

        // Then
        // Should not crash and should serialize properly
        assertTrue(json.contains("TEST002"))
        assertTrue(json.contains("Jane Doe"))
        // custom_fields should be null or not present
        assertTrue(!json.contains("custom_fields") || json.contains("\"custom_fields\":null"))
    }

    @Test
    fun `CustomFields should deserialize from JSON correctly`() {
        // Given
        val json = """[
            {
                "setting_transaction_fkid": 1,
                "label": "Customer Type",
                "value": "Member Baru"
            },
            {
                "setting_transaction_fkid": 2,
                "label": "Notes", 
                "value": "Special request"
            }
        ]"""

        // When
        val customFields = Gson().fromJson(json, Array<CustomFields>::class.java)

        // Then
        assertEquals(2, customFields.size)
        assertEquals(1, customFields[0].settingTransactionFkid)
        assertEquals("Customer Type", customFields[0].label)
        assertEquals("Member Baru", customFields[0].value)
        assertEquals(2, customFields[1].settingTransactionFkid)
        assertEquals("Notes", customFields[1].label)
        assertEquals("Special request", customFields[1].value)
    }
}
