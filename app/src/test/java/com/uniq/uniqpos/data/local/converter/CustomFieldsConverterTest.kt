package com.uniq.uniqpos.data.local.converter

import com.uniq.uniqpos.model.CustomFields
import org.junit.Test
import org.junit.Assert.*

class CustomFieldsConverterTest {

    private val converter = CustomFieldsConverter()

    @Test
    fun `fromCustomFieldsList should convert list to JSON string`() {
        // Given
        val customFields = arrayListOf(
            CustomFields(
                settingTransactionFkid = 1,
                label = "Customer Type",
                value = "Member Baru"
            ),
            CustomFields(
                settingTransactionFkid = 2,
                label = "Notes",
                value = "Special request"
            )
        )

        // When
        val json = converter.fromCustomFieldsList(customFields)

        // Then
        assertNotNull(json)
        assertTrue(json!!.contains("setting_transaction_fkid"))
        assertTrue(json.contains("Customer Type"))
        assertTrue(json.contains("Member Baru"))
    }

    @Test
    fun `fromCustomFieldsList should return null for null input`() {
        // When
        val result = converter.fromCustomFieldsList(null)

        // Then
        assertNull(result)
    }

    @Test
    fun `toCustomFieldsList should convert JSON string to list`() {
        // Given
        val json = """[
            {
                "settingTransactionFkid": 1,
                "label": "Customer Type",
                "value": "Member Baru"
            }
        ]"""

        // When
        val customFields = converter.toCustomFieldsList(json)

        // Then
        assertNotNull(customFields)
        assertEquals(1, customFields!!.size)
        assertEquals(1, customFields[0].settingTransactionFkid)
        assertEquals("Customer Type", customFields[0].label)
        assertEquals("Member Baru", customFields[0].value)
    }

    @Test
    fun `toCustomFieldsList should return null for null input`() {
        // When
        val result = converter.toCustomFieldsList(null)

        // Then
        assertNull(result)
    }

    @Test
    fun `toCustomFieldsList should handle empty JSON array`() {
        // Given
        val json = "[]"

        // When
        val customFields = converter.toCustomFieldsList(json)

        // Then
        assertNotNull(customFields)
        assertEquals(0, customFields!!.size)
    }

    @Test
    fun `round trip conversion should preserve data`() {
        // Given
        val originalFields = arrayListOf(
            CustomFields(
                settingTransactionFkid = 1,
                label = "Customer Type",
                value = "Member Baru"
            ),
            CustomFields(
                settingTransactionFkid = 2,
                label = "Notes",
                value = "Special request"
            )
        )

        // When
        val json = converter.fromCustomFieldsList(originalFields)
        val convertedFields = converter.toCustomFieldsList(json)

        // Then
        assertNotNull(convertedFields)
        assertEquals(originalFields.size, convertedFields!!.size)
        assertEquals(originalFields[0].settingTransactionFkid, convertedFields[0].settingTransactionFkid)
        assertEquals(originalFields[0].label, convertedFields[0].label)
        assertEquals(originalFields[0].value, convertedFields[0].value)
        assertEquals(originalFields[1].settingTransactionFkid, convertedFields[1].settingTransactionFkid)
        assertEquals(originalFields[1].label, convertedFields[1].label)
        assertEquals(originalFields[1].value, convertedFields[1].value)
    }
}
