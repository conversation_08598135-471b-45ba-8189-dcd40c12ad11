package com.uniq.uniqpos.ui.form

import android.content.Context
import androidx.test.core.app.ApplicationProvider
import org.junit.Test
import org.junit.Assert.*
import org.junit.Before
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner

@RunWith(RobolectricTestRunner::class)
class SingleChoiceComponentTest {

    private lateinit var context: Context
    private lateinit var config: FormFieldConfig

    @Before
    fun setup() {
        context = ApplicationProvider.getApplicationContext()
        config = FormFieldConfig(
            id = "1",
            label = "Customer Type",
            type = FormFieldConfig.TYPE_SINGLE_CHOICE,
            isRequired = true,
            displayOrder = 1,
            optionValues = listOf("Member Baru", "Member Lama", "Non Member")
        )
    }

    @Test
    fun `component should initialize with no selection`() {
        // Given
        val component = SingleChoiceComponent(config)

        // When
        val view = component.createView(context)
        val value = component.getValue()

        // Then
        assertNotNull(view)
        assertNull(value)
    }

    @Test
    fun `component should validate as error when required and no selection`() {
        // Given
        val component = SingleChoiceComponent(config)
        component.createView(context)

        // When
        val result = component.validate()

        // Then
        assertTrue(result is ValidationResult.Error)
        assertEquals("Please select Customer Type", (result as ValidationResult.Error).message)
    }

    @Test
    fun `component should validate as success when not required and no selection`() {
        // Given
        val nonRequiredConfig = config.copy(isRequired = false)
        val component = SingleChoiceComponent(nonRequiredConfig)
        component.createView(context)

        // When
        val result = component.validate()

        // Then
        assertTrue(result is ValidationResult.Success)
    }

    @Test
    fun `component should reset selection`() {
        // Given
        val component = SingleChoiceComponent(config)
        component.createView(context)

        // When
        component.reset()
        val value = component.getValue()

        // Then
        assertNull(value)
    }

    @Test
    fun `component should handle empty options list`() {
        // Given
        val emptyConfig = config.copy(optionValues = emptyList())
        val component = SingleChoiceComponent(emptyConfig)

        // When
        val view = component.createView(context)
        val value = component.getValue()

        // Then
        assertNotNull(view)
        assertNull(value)
    }

    @Test
    fun `component should handle null options list`() {
        // Given
        val nullConfig = config.copy(optionValues = null)
        val component = SingleChoiceComponent(nullConfig)

        // When
        val view = component.createView(context)
        val value = component.getValue()

        // Then
        assertNotNull(view)
        assertNull(value)
    }
}
