package com.uniq.uniqpos.ui.form

import android.content.Context
import androidx.test.core.app.ApplicationProvider
import org.junit.Test
import org.junit.Assert.*
import org.junit.Before
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner

@RunWith(RobolectricTestRunner::class)
class RequiredFieldValidationTest {

    private lateinit var context: Context

    @Before
    fun setup() {
        context = ApplicationProvider.getApplicationContext()
    }

    @Test
    fun `required text field should fail validation when empty`() {
        // Given
        val config = FormFieldConfig(
            id = "1",
            label = "Customer Name",
            type = FormFieldConfig.TYPE_TEXT,
            isRequired = true,
            displayOrder = 1
        )
        val component = TextFieldComponent(config)
        component.createView(context)

        // When
        val result = component.validate()

        // Then
        assertTrue("Required text field should fail validation when empty", result is ValidationResult.Error)
        assertEquals("Please enter Customer Name", (result as ValidationResult.Error).message)
    }

    @Test
    fun `required text field should pass validation when filled`() {
        // Given
        val config = FormFieldConfig(
            id = "1",
            label = "Customer Name",
            type = FormFieldConfig.TYPE_TEXT,
            isRequired = true,
            displayOrder = 1
        )
        val component = TextFieldComponent(config)
        val view = component.createView(context)
        
        // Simulate user input
        component.setValue("John Doe")

        // When
        val result = component.validate()

        // Then
        assertTrue("Required text field should pass validation when filled", result is ValidationResult.Success)
    }

    @Test
    fun `required single choice field should fail validation when no selection`() {
        // Given
        val config = FormFieldConfig(
            id = "2",
            label = "Customer Type",
            type = FormFieldConfig.TYPE_SINGLE_CHOICE,
            isRequired = true,
            displayOrder = 2,
            optionValues = listOf("Member Baru", "Member Lama", "Non Member")
        )
        val component = SingleChoiceComponent(config)
        component.createView(context)

        // When
        val result = component.validate()

        // Then
        assertTrue("Required single choice field should fail validation when no selection", result is ValidationResult.Error)
        assertEquals("Please select Customer Type", (result as ValidationResult.Error).message)
    }

    @Test
    fun `non-required text field should pass validation when empty`() {
        // Given
        val config = FormFieldConfig(
            id = "3",
            label = "Notes",
            type = FormFieldConfig.TYPE_TEXT,
            isRequired = false,
            displayOrder = 3
        )
        val component = TextFieldComponent(config)
        component.createView(context)

        // When
        val result = component.validate()

        // Then
        assertTrue("Non-required text field should pass validation when empty", result is ValidationResult.Success)
    }

    @Test
    fun `non-required single choice field should pass validation when no selection`() {
        // Given
        val config = FormFieldConfig(
            id = "4",
            label = "Preferences",
            type = FormFieldConfig.TYPE_SINGLE_CHOICE,
            isRequired = false,
            displayOrder = 4,
            optionValues = listOf("Option A", "Option B", "Option C")
        )
        val component = SingleChoiceComponent(config)
        component.createView(context)

        // When
        val result = component.validate()

        // Then
        assertTrue("Non-required single choice field should pass validation when no selection", result is ValidationResult.Success)
    }

    @Test
    fun `dynamic form manager should validate all required fields`() {
        // Given
        val configs = listOf(
            FormFieldConfig(
                id = "1",
                label = "Customer Name",
                type = FormFieldConfig.TYPE_TEXT,
                isRequired = true,
                displayOrder = 1
            ),
            FormFieldConfig(
                id = "2",
                label = "Customer Type",
                type = FormFieldConfig.TYPE_SINGLE_CHOICE,
                isRequired = true,
                displayOrder = 2,
                optionValues = listOf("Member Baru", "Member Lama")
            ),
            FormFieldConfig(
                id = "3",
                label = "Notes",
                type = FormFieldConfig.TYPE_TEXT,
                isRequired = false,
                displayOrder = 3
            )
        )

        val formManager = DynamicFormManager()
        formManager.buildForm(context, configs)

        // When - validate without filling required fields
        val result = formManager.validateForm()

        // Then
        assertTrue("Form should fail validation when required fields are empty", result is ValidationResult.Error)
        assertTrue("Error message should mention first required field", 
            (result as ValidationResult.Error).message.contains("Customer Name"))
    }

    @Test
    fun `form validation should show specific field name in error message`() {
        // Given
        val config = FormFieldConfig(
            id = "1",
            label = "Phone Number",
            type = FormFieldConfig.TYPE_TEXT,
            isRequired = true,
            displayOrder = 1
        )
        val component = TextFieldComponent(config)
        component.createView(context)

        // When
        val result = component.validate()

        // Then
        assertTrue(result is ValidationResult.Error)
        val errorMessage = (result as ValidationResult.Error).message
        assertTrue("Error message should contain field name", errorMessage.contains("Phone Number"))
        assertEquals("Please enter Phone Number", errorMessage)
    }
}
