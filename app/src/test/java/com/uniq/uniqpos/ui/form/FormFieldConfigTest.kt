package com.uniq.uniqpos.ui.form

import com.uniq.uniqpos.data.local.entity.TransactionSettingEntity
import org.junit.Test
import org.junit.Assert.*

class FormFieldConfigTest {

    @Test
    fun `fromEntity should create FormFieldConfig from TransactionSettingEntity`() {
        // Given
        val entity = TransactionSettingEntity(
            id = 1,
            outletId = 100,
            label = "Customer Type",
            type = "SINGLE_CHOICE",
            optionValues = "[\"Member Baru\", \"Member Lama\"]",
            isRequired = true,
            displayOrder = 1,
            createdAt = System.currentTimeMillis(),
            updatedAt = System.currentTimeMillis(),
            synced = true
        )

        // When
        val config = FormFieldConfig.fromEntity(entity)

        // Then
        assertEquals("1", config.id)
        assertEquals("Customer Type", config.label)
        assertEquals("SINGLE_CHOICE", config.type)
        assertTrue(config.isRequired)
        assertEquals(1, config.displayOrder)
        assertNotNull(config.optionValues)
        assertEquals(2, config.optionValues?.size)
        assertEquals("Member Baru", config.optionValues?.get(0))
        assertEquals("Member Lama", config.optionValues?.get(1))
    }

    @Test
    fun `fromEntity should handle null optionValues`() {
        // Given
        val entity = TransactionSettingEntity(
            id = 2,
            outletId = 100,
            label = "Notes",
            type = "TEXT",
            optionValues = null,
            isRequired = false,
            displayOrder = 2,
            createdAt = System.currentTimeMillis(),
            updatedAt = System.currentTimeMillis(),
            synced = true
        )

        // When
        val config = FormFieldConfig.fromEntity(entity)

        // Then
        assertEquals("2", config.id)
        assertEquals("Notes", config.label)
        assertEquals("TEXT", config.type)
        assertFalse(config.isRequired)
        assertEquals(2, config.displayOrder)
        assertNull(config.optionValues)
    }

    @Test
    fun `fromEntity should handle invalid JSON in optionValues`() {
        // Given
        val entity = TransactionSettingEntity(
            id = 3,
            outletId = 100,
            label = "Invalid Options",
            type = "SINGLE_CHOICE",
            optionValues = "invalid json",
            isRequired = true,
            displayOrder = 3,
            createdAt = System.currentTimeMillis(),
            updatedAt = System.currentTimeMillis(),
            synced = true
        )

        // When
        val config = FormFieldConfig.fromEntity(entity)

        // Then
        assertEquals("3", config.id)
        assertEquals("Invalid Options", config.label)
        assertEquals("SINGLE_CHOICE", config.type)
        assertTrue(config.isRequired)
        assertEquals(3, config.displayOrder)
        assertNull(config.optionValues) // Should be null due to JSON parsing error
    }
}
