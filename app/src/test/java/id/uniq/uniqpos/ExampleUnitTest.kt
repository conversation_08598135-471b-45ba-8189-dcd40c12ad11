package id.uniq.uniqpos

import com.uniq.uniqpos.util.force
import kotlinx.coroutines.*
import org.junit.Test
import java.net.URL

/**
 * Example local unit test, which will execute on the development machine (host).
 *
 * @see [Testing documentation](http://d.android.com/tools/testing)
 */
class ExampleUnitTest {
    @Test
    @Throws(Exception::class)
    fun addition_isCorrect() {
        val sb = StringBuilder()
        val res = sb.substring(0, 0)
        test()
        println("res $res")
    }

    private fun test() = GlobalScope.launch {
        GlobalScope.launch(Dispatchers.IO) {
            repeat(1000){}
            println("loop finish...")
        }
        println("after await!!")
    }

    @Test
    fun testForce(){
        val logo = "https://i.stack.imgur.com/sjOJv.jpg?s=32&g=1"
        val path = URL(logo).path
        println(path.substring(path.lastIndexOf("/") + 1))
    }

    @Test
    fun testSplit(){
        val text = "thisIsExample"
        text.split(",").takeIf { text.isNotBlank() }?.forEachIndexed { index, s ->
            println("index $index, s $s")
        }
    }
}