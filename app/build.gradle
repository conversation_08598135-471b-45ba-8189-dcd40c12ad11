plugins {
    id 'com.android.application'
    id 'kotlin-parcelize'
}
apply plugin: 'kotlin-android'
apply plugin: 'kotlin-kapt'
//apply plugin: 'kotlin-android-extensions'
apply plugin: 'com.google.firebase.crashlytics'
apply from: "$project.rootDir/tools/script-git-version.gradle"

def keystorePropertiesFile = rootProject.file("keystore.properties")
def keystoreProperties = new Properties()
keystoreProperties.load(new FileInputStream(keystorePropertiesFile))

def getVersionCode = { ->
    try {
        def code = new ByteArrayOutputStream()
        exec {
            commandLine 'git', 'tag', '--list'
            standardOutput = code
        }
        return code.toString().split("\n").size()
    }
    catch (ignored) {
        return -1;
    }
}

def getVersionName = { ->
    try {
        def stdout = new ByteArrayOutputStream()
        exec {
            commandLine 'git', 'describe', '--tags', '--dirty'
            standardOutput = stdout
        }
        return stdout.toString().trim()
    }
    catch (ignored) {
        return null;
    }
}

android {
    compileSdk 35
    namespace = "com.uniq.uniqpos"

    defaultConfig {
        applicationId "id.uniq.uniqpos"
        minSdkVersion 24
        targetSdkVersion 35
        versionCode gitVersionCodeTime
        versionName gitVersionName
        multiDexEnabled true
        vectorDrawables.useSupportLibrary = true
//        playAccountConfig = playAccountConfigs.defaultAccountConfig
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
//        ndk {
//            abiFilters 'x86', 'x86_64', 'armeabi', 'armeabi-v7a', 'arm64-v8a'
//            abiFilters "arm64-v8a","armeabi", "armeabi-v7a", "x86","x86_64","mips","mips64"
//        }

        kapt {
            arguments {
                arg("room.schemaLocation", "$projectDir/schemas".toString())
            }
        }
    }

//    splits {
//        abi {
//            enable true
//            reset()
//            include 'x86', 'armeabi-v7a'
//            universalApk true
//        }
//    }

    signingConfigs {
        release {
            storeFile file(keystoreProperties['storeFile'])
            storePassword keystoreProperties['storePassword']
            keyAlias keystoreProperties['keyAlias']
            keyPassword keystoreProperties['keyPassword']
        }
    }

    buildTypes {
        debug {
            versionNameSuffix ".debug"
            resValue("string", "PORT_NUMBER", "8081")

//          ext.betaDistributionApkFilePath = "app/build/outputs/apk/alive/debug/app-alive-universal-debug.apk"
            ext.betaDistributionReleaseNotesFilePath = "release_note.txt"
            ext.betaDistributionGroupAliases = "uniq-beta-tester"
            ext.betaDistributionNotifications = true

            applicationVariants.all { variant ->
                variant.outputs.all { output ->
//                    outputFileName = "${output.outputFile.name.replace(".apk","")}_${defaultConfig.versionName}.apk"
                    def abiName = output.getFilter(output.ABI)
                    if (abiName == null) {
                        abiName = "universal"
                    }
                    outputFileName = "uniq_pos_${variant.getFlavorName()}_${abiName}_${defaultConfig.versionName}.apk"
                }
            }

            //disable crashlytic: to speedup build
//            extra["enableCrashlytics"] = false
        }
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.release

            ext.betaDistributionReleaseNotesFilePath = "release_note.txt"
            ext.betaDistributionGroupAliases = "uniq-dev,uniq-beta-tester"
            //ext.betaDistributionEmails="<EMAIL>"
            ext.betaDistributionNotifications = true
        }
    }

//    dexOptions {
//        jumboMode true
//        javaMaxHeapSize "4g"
//    }

//    kotlinOptions {
//     //    jvmTarget = '1.8'
//         jvmTarget = '17'
//    }

    buildFeatures {
        dataBinding = true
        viewBinding = true
        buildConfig = true
    }


    compileOptions {
        // sourceCompatibility JavaVersion.VERSION_1_8
        // targetCompatibility JavaVersion.VERSION_1_8
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }

    flavorDimensions "default"
    productFlavors {
        production {
            buildConfigField "String", "BASE_URL", '"https://api.uniq.id/"'
            buildConfigField "String", "WEB_URL", '"https://client.uniq.id/"'
            buildConfigField "String", "IV_SPEC", '"BvbIvMGrCmpYOyKK"'
            buildConfigField "String", "KEY_SPEC", '"LhY7wTc62ymXt6Sw"'
        }
        development {
            applicationIdSuffix ".development"
            buildConfigField "String", "BASE_URL", '"https://api.uniqdev.web.id/"'
            buildConfigField "String", "WEB_URL", '"https://uniqdev.web.id/"'
            buildConfigField "String", "IV_SPEC", '"MzzgjsZy1gFZ22aP"'
            buildConfigField "String", "KEY_SPEC", '"Dg0pAITYNufb68z0"'
        }
        alive {
            applicationIdSuffix ".beta"
            buildConfigField "String", "BASE_URL", '"https://api-staging.uniq.id/"'
            buildConfigField "String", "WEB_URL", '"https://staging.uniq.id/"'
            buildConfigField "String", "IV_SPEC", '"BvbIvMGrCmpYOyKK"'
            buildConfigField "String", "KEY_SPEC", '"LhY7wTc62ymXt6Sw"'
        }
    }
}

dependencies {
    implementation fileTree(include: ['*.jar'], dir: 'libs')

    implementation 'androidx.legacy:legacy-support-v4:1.0.0'
    implementation 'androidx.navigation:navigation-fragment-ktx:2.4.2' // 2.4.2 | 2.5.0 | 2.6.0 | 2.7.7
    implementation 'androidx.navigation:navigation-ui-ktx:2.4.2' // 2.4.2 | 2.5.0 | 2.6.0 | 2.7.7

    // Kotlin
//    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.5.10"
    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-core:1.6.4' //1.5.2
    implementation "org.jetbrains.kotlinx:kotlinx-coroutines-android:1.6.4" // 1.5.2 | 1.6.4

    // AndroidX
    // Import the BoM for the AndroidX core library
//    implementation platform('androidx.core:core-bom:1.12.0')
    implementation(platform("androidx.compose:compose-bom:2025.05.00"))
    implementation ("androidx.core:core-ktx") // 1.8.0 | 1.9.0 | 1.10.0 | 1.12.0 | 1.13.1
    implementation 'androidx.appcompat:appcompat' // 1.4.2 | 1.7.0

    // UI 
     implementation 'androidx.activity:activity:1.10.0'
    implementation 'androidx.cardview:cardview:1.0.0'
    implementation 'androidx.constraintlayout:constraintlayout'
    implementation 'com.google.android.material:material:1.6.1' // 1.6.1 | 1.12.0
    implementation 'de.hdodenhof:circleimageview:3.1.0'
    // implementation 'com.amulyakhare:com.amulyakhare.textdrawable:1.0.1'
    implementation 'com.github.alvinhkh:TextDrawable:c1c2b5b'
    implementation('com.weiwangcn.betterspinner:library-material:1.1.0') {
        exclude group: 'com.android.support', module: 'appcompat-v7'
    }
    implementation('com.wdullaer:materialdatetimepicker:4.2.3') {
        exclude group: 'com.android.support'
    }

    // Image Caching
    implementation "com.github.bumptech.glide:glide:4.13.2"
//    implementation 'androidx.media3:media3-common-ktx:1.5.0'
    kapt "com.github.bumptech.glide:compiler:$glideVersion"

    //image ui utils
    implementation 'com.github.chrisbanes:PhotoView:2.3.0'

    implementation 'androidx.webkit:webkit:1.4.0'
    implementation('androidx.browser:browser:1.3.0')
//    {
//        exclude group: 'com.android.support', module: 'appcompat-v7'
//    }
        

    //architecture component
    implementation "androidx.lifecycle:lifecycle-extensions:2.2.0"
    implementation "androidx.lifecycle:lifecycle-viewmodel-ktx" // 2.4.1 | 2.8.4

    //networking
    // implementation "io.ktor:ktor-client-websockets:1.6.4" 
    implementation "io.ktor:ktor-client-cio:1.6.4"
//    implementation "io.ktor:ktor-server-websockets:1.6.4"

    // Database 
    implementation "androidx.room:room-runtime:$room_version"
    implementation "androidx.room:room-ktx:$room_version"
    kapt "androidx.room:room-compiler:$room_version"

    //debuging
    // debugImplementation 'com.amitshekhar.android:debug-db:1.0.1'

    //Google play service API
    implementation "com.google.android.gms:play-services-vision:20.1.3"
    // implementation 'com.google.android.play:core:1.10.3'
    implementation("com.google.android.play:app-update:2.1.0")
    implementation("com.google.android.play:app-update-ktx:2.1.0")

    //mlkit
//    implementation 'com.google.mlkit:barcode-scanning:17.0.2'

    //Firebase
    implementation platform('com.google.firebase:firebase-bom:31.1.0') // 31.1.0 | 32.1.0 | 33.1.0
//    implementation "com.google.firebase:firebase-core:$firebaseVersion"
    implementation 'com.google.firebase:firebase-messaging-ktx'
    implementation 'com.google.firebase:firebase-crashlytics-ktx'
    implementation 'com.google.firebase:firebase-analytics-ktx'
    implementation 'com.google.firebase:firebase-config-ktx'

    //analytic tool
    implementation "com.bugsnag:bugsnag-android:6.17.0"

    // Logging
    implementation 'com.jakewharton.timber:timber:5.0.1'  

    // memory leak detection
    debugImplementation 'com.squareup.leakcanary:leakcanary-android:2.11'  

    //anko
//   implementation "org.jetbrains.anko:anko-coroutines:0.10.7"

    // Dependency Injection
    implementation "com.google.dagger:dagger:$daggerVersion"
    implementation "com.google.dagger:dagger-android:$daggerVersion"
    implementation "com.google.dagger:dagger-android-support:$daggerVersion"
    kapt "com.google.dagger:dagger-compiler:$daggerVersion"
    kapt "com.google.dagger:dagger-android-processor:$daggerVersion"

    // Networking
    implementation "com.squareup.retrofit2:retrofit:$retrofit_version"
    implementation "com.squareup.retrofit2:converter-gson:$retrofit_version"
    implementation "com.squareup.okhttp3:logging-interceptor:$okhttp_interceptor_version"

    //printer
    implementation 'com.github.DantSu:ESCPOS-ThermalPrinter-Android:3.2.1'

    implementation 'com.gu.android:toolargetool:0.3.0'
    implementation 'androidx.multidex:multidex:2.0.1'
    implementation 'com.github.mreram:showcaseview:1.4.1'
    implementation project(path: ':materialanimation')

    //testing
    testImplementation 'org.junit.jupiter:junit-jupiter'
    testImplementation 'org.junit.jupiter:junit-jupiter'
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation('androidx.test.espresso:espresso-core:3.1.0', {
        exclude group: 'com.android.support', module: 'support-annotations'
    })

    implementation 'com.github.chrisbanes:PhotoView:2.3.0'
}
apply plugin: 'com.google.gms.google-services'
