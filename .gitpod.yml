image: uniqdev/android-fastlane:android-jdk17-gitpod #annasblackhat/android-vnc:latest
  # file: .gitpod.Dockerfile

tasks:
  - name: permissoons
    command: |
      chown -R gitpod /sdk/
      export PATH="$PATH:/home/<USER>/android-sdk/platform-tools/"
      #cp /home/<USER>/.android/debug.keystore /workspace/debug.keystore
      #mkdir /home/<USER>/.android/ &&  cp /workspace/debug.keystore /home/<USER>/.android/debug.keystore
      # sudo apt-get update && sudo apt-get install tailscale -q -y
      # 
#      export GRADLE_USER_HOME=${PWD}/.gradle

  - name: tailscaled
    command: |
      if [ -n "${TAILSCALE_STATE_MYPROJECT}" ]; then
        # restore the tailscale state from gitpod user's env vars
        sudo mkdir -p /var/lib/tailscale
        echo "${TAILSCALE_STATE_MYPROJECT}" | sudo tee /var/lib/tailscale/tailscaled.state > /dev/null
      fi
      sudo tailscaled
  - name: tailscale
    command: |
      if [ -n "${TAILSCALE_STATE_MYPROJECT}" ]; then
        sudo -E tailscale up
      else
        sudo -E tailscale up --hostname "gitpod-${GITPOD_GIT_USER_NAME// /-}-$(echo ${GITPOD_WORKSPACE_CONTEXT} | jq -r .repository.name)"
        # store the tailscale state into gitpod user
        gp env TAILSCALE_STATE_MYPROJECT="$(sudo cat /var/lib/tailscale/tailscaled.state)"
        # export TAILSCALE_STATE_MYPROJECT="$(sudo cat /var/lib/tailscale/tailscaled.state)"
        # sudo cp /var/lib/tailscale/tailscaled.state tailscaled.state 
        # sudo cp tailscaled.state /var/lib/tailscale/tailscaled.state
        #gp env TAILSCALE_STATE_MYPROJECT="$(sudo cat tailscaled.state)"
        #export TAILSCALE_STATE_MYPROJECT="$(sudo cat tailscaled.state)"
      fi
      
ports:
  - port: 6080
