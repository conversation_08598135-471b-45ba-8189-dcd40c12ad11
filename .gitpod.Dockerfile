FROM uniqdev/android-fastlane:jdk-11

RUN apt install -q iputils-ping --yes
RUN apt-get install telnetd -y

## installing tailscale
#USER root
RUN curl -fsSL https://pkgs.tailscale.com/stable/ubuntu/focal.gpg | sudo apt-key add - \
     && curl -fsSL https://pkgs.tailscale.com/stable/ubuntu/focal.list | sudo tee /etc/apt/sources.list.d/tailscale.list \
     && apt-get update \
     && apt-get install -y tailscale


#WORKDIR /app
#COPY . .
#ENV GRADLE_USER_HOME=${PWD}/.gradle
#RUN chmod +x ./gradlew
ENV PATH="${PATH}:/sdk/platform-tools"

