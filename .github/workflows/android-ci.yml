name: Android CI/CD

on:
  push:
    branches: [ main, master, staging, testing ]
    # tags:
    #   - 'v*'
  pull_request:
    branches: [ main, master, staging ]
  workflow_dispatch:

jobs:
  build:
    runs-on: ubuntu-latest
    env:
      BUILD_DATE: ${{ format('YYYY-MM-DD HH:mm:ss') }}
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0  # Needed for git describe

      - name: Set up Ruby
        uses: ruby/setup-ruby@v1
        with:
          ruby-version: '3.0'
          bundler-cache: true

      - name: Set Up JDK
        uses: actions/setup-java@v4
        with:
          distribution: 'zulu' # See 'Supported distributions' for available options
          java-version: '17'
          cache: 'gradle'

    #   - name: Setup Android SDK
    #     uses: android-actions/setup-android@v3

      - name: Cache Gradle packages
        uses: actions/cache@v4
        with:
          path: |
            ~/.gradle/caches
            ~/.gradle/wrapper
          key: ${{ runner.os }}-gradle-${{ hashFiles('**/*.gradle*', '**/gradle-wrapper.properties') }}
          restore-keys: ${{ runner.os }}-gradle-

      - name: Setup Fastlane
        run: |
          bundle install
          echo "${{ secrets.GOOGLE_PLAY_KEY }}" | base64 -d > app/keys.json
          echo "${{ secrets.KEYSTORE_FILE }}" | base64 -d > app/uniqkeystore.jks
          echo "${{ secrets.KEYSTORE_PROPERTIES }}" | base64 -d > keystore.properties
          chmod +x ./gradlew

      # Testing branch workflow
      - name: Build and Deploy to Slack (Testing)
        if: github.ref == 'refs/heads/testing'
        env:
          SLACK_ACCESS_TOKEN: ${{ secrets.SLACK_ACCESS_TOKEN }}
          GOFILE_TOKEN: ${{ secrets.GOFILE_TOKEN }}
          GOFILE_FOLDER_ID: ${{ secrets.GOFILE_FOLDER_ID }}
          TELEGRAM_BOT_TOKEN: ${{ secrets.TELEGRAM_BOT_TOKEN }}
          TELEGRAM_CHAT_ID: ${{ secrets.TELEGRAM_CHAT_ID }}
          BUILD_DATE: ${{ env.BUILD_DATE }}
        run: bundle exec fastlane developmentDeploy

      # Staging branch workflow
      - name: Deploy to Play Store Internal (Staging)
        if: github.ref == 'refs/heads/staging'
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
        run: bundle exec fastlane stagingDeploy

      # Production deployment (master/main branch)
      - name: Deploy to Play Store Beta (Production)
        if: github.ref == 'refs/heads/master' || github.ref == 'refs/heads/main'
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
        run: bundle exec fastlane betaDeploy

      # Notify on completion
      - name: Notify Slack on Success
        if: success()
        uses: slackapi/slack-github-action@v1.24.0
        with:
          payload: |
            {
              "text": "✅ Pipeline succeeded in ${{ github.repository }}"
            }
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

      - name: Notify Slack on Failure
        if: failure()
        uses: slackapi/slack-github-action@v1.24.0
        with:
          payload: |
            {
              "text": "❌ Pipeline failed in : ${{ github.repository }}"
            }
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
