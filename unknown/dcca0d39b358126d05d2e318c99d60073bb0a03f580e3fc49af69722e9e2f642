package com.uniq.uniqpos.view.cashdrawer


import android.content.DialogInterface
import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.provider.Settings
import android.view.View
import android.widget.ArrayAdapter
import androidx.fragment.app.Fragment
import com.bugsnag.android.Bugsnag
import com.google.firebase.analytics.ktx.analytics
import com.google.firebase.ktx.Firebase
import com.google.gson.Gson
import com.uniq.uniqpos.BuildConfig
import com.uniq.uniqpos.R
import com.uniq.uniqpos.data.local.sharedpref.SharedPref
import com.uniq.uniqpos.data.local.sharedpref.getLocalDataString
import com.uniq.uniqpos.data.local.sharedpref.putData
import com.uniq.uniqpos.data.local.sharedpref.putJson
import com.uniq.uniqpos.databinding.FragmentInputCashDrawerBinding
import com.uniq.uniqpos.util.*
import com.uniq.uniqpos.view.global.BaseFragment
import com.uniq.uniqpos.view.main.MainActivity
import timber.log.Timber
import java.util.*
import kotlin.collections.ArrayList
import kotlin.collections.HashMap

/**
 * A simple [Fragment] subclass.
 */
class InputCashDrawerFragment : BaseFragment<InputCashDrawerViewModel, FragmentInputCashDrawerBinding>() {

    private lateinit var shiftAdapter: ArrayAdapter<String>
    private var addNewShiftText = "-- ADD NEW SHIFT --"
    private var lastSelectedShift: String? = null

    override val layoutRes: Int = R.layout.fragment_input_cash_drawer
    override fun getViewModel() = InputCashDrawerViewModel::class.java

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        Bugsnag.leaveBreadcrumb(this::class.simpleName.safe())

        binding.employeeName.text = employee.name
        binding.btnSubmit.setOnClickListener {
            saveOpenShift()
        }

        Utils.registerToCurrency(binding.edtCashAwal)

        shiftAdapter = ArrayAdapter(requireContext(), android.R.layout.simple_dropdown_item_1line, ArrayList<String>())
        binding.spinShift.setAdapter(shiftAdapter)
        binding.spinShift.setOnItemClickListener { _, _, position, _ ->
            if(shiftAdapter.getItem(position) == addNewShiftText){
                binding.spinShift.setText(if(viewModel.shiftList.size == 1) viewModel.shiftList.first().name else lastSelectedShift)
                context?.launchUrl(BuildConfig.WEB_URL+"outlets/shift")
            }
            lastSelectedShift = binding.spinShift.text.toString()
            binding.edtCashAwal.requestFocus()
        }

        binding.imgReloadShift.setOnClickListener { loadAvailableShift() }
        binding.txtAddShift.setOnClickListener { context?.launchUrl(BuildConfig.WEB_URL+"outlets/shift") }
        addNewShiftText = getString(R.string.add_new_shift)

        viewModel.loadPreviousOpenShift()
        loadAvailableShift()
        observeTask()
    }

    private fun observeTask() {
        viewModel.getSubscriptionStatus(context?.getLocalDataString(SharedPref.DEVICE_ID, "") ?: "")
        viewModel.subscriptionTask.observe(viewLifecycleOwner, { subscription ->
            subscription?.let { subscription ->
                subscription.warningMessage?.let { binding.txtWarning.text = it }
                context?.putJson(SharedPref.SUBSCRIPTION_STATUS, subscription)
            }
            Timber.i("Subscription Status: ${Gson().toJson(subscription)}")
        })

        viewModel.pDialogTask.observe(viewLifecycleOwner) {
            it.takeIf { it.isNotBlank() }?.let { msg -> showDialog(true, msg) } ?: run {
                showDialog(
                    false
                )
            }
        }

        viewModel.dialogMessageTask.observe(viewLifecycleOwner, { msg ->
            context?.showMessage(msg, "WARNING")
        })

        viewModel.openShiftTask.observe(viewLifecycleOwner, {
            it?.let { res ->
                val diffMinute = (System.currentTimeMillis()..res.millis).diffMinutes().forcePositive()
                if (diffMinute >= 60) {
                    Firebase.analytics
                            .logEvent("froud_detection",
                                    bundleOf("type" to "Date Changed",
                                            "info" to "Diff Minute : $diffMinute",
                                            "outlet" to context?.outlet()?.name,
                                            "device" to "${Build.BRAND} ${Build.MODEL} - ${Build.SERIAL}"))

                    context?.showMessage("Jam/Tanggal pada device anda sepertinya belum di setting dengan benar. \n" +
                            "Silahkah buka Pengaturan dan ganti tanggal terlebih dahulu!", "JAM/TANGAL BERBEDA", onDismissListener = DialogInterface.OnDismissListener {
                        startActivity(Intent(Settings.ACTION_DATE_SETTINGS))
                    })
                } else {
                    if (res.status || res.code == "1") {
                        if (res.code == "1") {
                            showMsg("Sistem mendeteksi adanya shift lain yang aktif, \nanda akan secara otomatis masuk ke shift tersebut", "INFO")
                        }
                        res.data?.let {
                            context?.putJson(SharedPref.SHIFT_DATA, res.data)
                            context?.putData(SharedPref.SHIFT_NAME, binding.spinShift.editableText.toString())
                        }
                        Timber.i("login with shift : ${res.data}")
                        (context as MainActivity).reInitNavigationMenu(0)
                    } else {
                        showMsg(res.message)
                    }
                }
            }
        })

        viewModel.refreshShiftList.observe(viewLifecycleOwner) {
            it?.takeIf { it }?.let {
                if (viewModel.shiftList.isNotEmpty()) {
                    viewModel.shiftList.forEach { shiftAdapter.add(it.name) }
                    if (viewModel.shiftList.size == 1) {
                        binding.spinShift.setText(viewModel.shiftList.first().name)
                    }
                    shiftAdapter.add(addNewShiftText)
                    binding.txtAddShift.setVisible(false)
                    initLoadShiftView(true)
                } else {
                    initLoadShiftView(
                        false,
                        isError = true,
                        errMsg = resources.getString(R.string.no_available_shift)
                    )
                    binding.txtAddShift.setVisible(true)
                    toast(resources.getString(R.string.no_available_shift))
                }
            } ?: run { initLoadShiftView(isSuccess = false, isError = true) }
        }

        viewModel.suggestionShiftTask.observe(viewLifecycleOwner) { shiftName ->
            binding.spinShift.setText(shiftName)
            toast("suggested: $shiftName")
        }
    }

    private fun loadAvailableShift() {
        initLoadShiftView(false)
        val offset = TimeZone.getDefault().getOffset(System.currentTimeMillis()) / 1000
        viewModel.loadAvailableShift(outlet.outletId ?: 0, offset)
        viewModel.loadActiveShift(outlet.outletId ?: 0)
    }

    private fun initLoadShiftView(isSuccess: Boolean = false, isError: Boolean = false, errMsg: String? = null) {
        try {
            binding.layoutProgressShift.visibility = if (isSuccess) View.GONE else View.VISIBLE
            binding.spinShift.visibility = if (isSuccess) View.VISIBLE else View.GONE
            if (isError) {
                var msg = errMsg
                if (msg == null) {
                    msg = mContext?.resources?.getString(R.string.failed_get_shift)
                            ?: "Failed to get shift."
                }

                binding.txtInfoShift.text = msg
                binding.progressShift.visibility = View.GONE
                binding.imgReloadShift.visibility = View.VISIBLE //usually this line caused error. from crashlytic
            } else {
                binding.progressShift.visibility = View.VISIBLE
                binding.imgReloadShift.visibility = View.GONE
                binding.txtInfoShift.text = resources.getString(R.string.load_shift)
            }
        } catch (e: Exception) {
            Timber.i("update shift ui error $e")
        }
        Timber.i("update shift ui. success? $isSuccess")
    }

    private fun saveOpenShift(skipSuggestionCheck: Boolean = false) {
        if (!role().inputkasmasuk) {
            showMsg(getString(R.string.no_permission))
            return
        }

        if (!Utils.isValidField(binding.edtCashAwal, binding.spinShift)) return

        viewModel.suggestedShift?.takeIf { !skipSuggestionCheck && it != binding.spinShift.text.toString() }?.let { suggested ->
            context?.showMessage(getString(R.string.shift_choosen_confirmation, binding.spinShift.text, suggested), "WARNING", DialogInterface.OnClickListener { _, _ ->
                saveOpenShift(true)
            })
            return
        }

        viewModel.suggestedShift?.let { suggestion ->
            Firebase.analytics
                    .logEvent("shift_suggestion", bundleOf("follow" to if (suggestion == binding.spinShift.text.toString()) "yes" else "no",
                            "outlet_id" to outlet.outletId.toString(),
                            "outlet" to outlet.name.safe()))
        }

        viewModel.shiftList.firstOrNull { it.name == binding.spinShift.editableText.toString() }?.let { selectedShift ->
            val map = HashMap<String, Any?>()
            map["device_id"] = context?.getLocalDataString(SharedPref.DEVICE_ID)
            map["device_name"] = "${Build.BRAND} ${Build.MODEL}"
            map["outlet_fkid"] = outlet.outletId
            map["employee_fkid"] = employee.employeeId
            map["shift_fkid"] = selectedShift.shiftId
            map["time_open"] = System.currentTimeMillis()
            map["early_cash"] = binding.edtCashAwal.text.toString().fromCurrency()
            map["open_shift_id"] = System.currentTimeMillis()

            Timber.i("open new shift, shift: '${binding.spinShift.text}' (${selectedShift.shiftId}) | early cash: ${binding.edtCashAwal.text}")
            viewModel.createOpenShift(map, context?.outletFeature()?.offlineMode)

        } ?: kotlin.run { toast("Can not find selected shift! Try to relaunch app") }

    }
}
