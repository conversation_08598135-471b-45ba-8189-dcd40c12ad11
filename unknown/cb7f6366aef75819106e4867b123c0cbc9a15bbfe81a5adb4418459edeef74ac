package com.uniq.uniqpos.view.cashdrawer

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.google.gson.Gson
import com.uniq.uniqpos.data.local.entity.OpenShiftEntity
import com.uniq.uniqpos.data.local.entity.ShiftEntity
import com.uniq.uniqpos.data.remote.model.ServerResponse
import com.uniq.uniqpos.data.remote.repository.OutletRepository
import com.uniq.uniqpos.data.remote.repository.SalesRepository
import com.uniq.uniqpos.data.remote.repository.SettingRepository
import com.uniq.uniqpos.model.SubscriptionStatus
import com.uniq.uniqpos.util.*
import com.uniq.uniqpos.util.lifecycle.SingleLiveEvent
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import timber.log.Timber
import javax.inject.Inject

class InputCashDrawerViewModel @Inject constructor(
        private var settingRepository: SettingRepository,
        private var salesRepository: SalesRepository,
        private var outletRepository: OutletRepository
) : ViewModel() {

    val shiftList = ArrayList<ShiftEntity>()
    private val allShiftList = ArrayList<ShiftEntity>()
    private val openShiftHistory = ArrayList<OpenShiftEntity>()
    var suggestedShift: String? = null

    val openShiftTask = SingleLiveEvent<ServerResponse<OpenShiftEntity>>()
    val refreshShiftList = SingleLiveEvent<Boolean>()
    val subscriptionTask = SingleLiveEvent<SubscriptionStatus>()
    val pDialogTask = SingleLiveEvent<String>()
    val dialogMessageTask = SingleLiveEvent<String>()
    val suggestionShiftTask = SingleLiveEvent<String>()

    fun createOpenShift(shift: HashMap<String, Any?>, offlineMode: Boolean?) {
        viewModelScope.launch {
            pDialogTask.postValue("Saving...")
            try {
                val resp = salesRepository.createOpenShift(shift)
                openShiftTask.postValue(resp)
            } catch (e: Exception) {
                dialogMessageTask.postValue(e.readableError())
            } finally {
                pDialogTask.call<String>()
            }
        }
    }

    //getting all shift available today
    fun loadAvailableShift(outletId: Int, timeOffset: Int) {
        viewModelScope.launch {
            try {
                Timber.i("start load available shift...")
                val resp = salesRepository.getAvailableShiftOld(outletId, timeOffset).await()
                resp.data?.let { items ->
                    shiftList.clear()
                    shiftList.addAll(items)
                    refreshShiftList.postValue(true)
                } ?: run { refreshShiftList.postValue(true) }
            } catch (e: Exception) {
                refreshShiftList.postValue(false)
                dialogMessageTask.postValue(e.readableError())
            }

            if (shiftList.isNotEmpty() && shiftList.size > 1)
                makeShiftSuggestion()
            Timber.i("load available shift finish: ${Gson().toJson(shiftList)}")
        }
    }

    //getting current active shift, if any
    fun loadActiveShift(outletId: Int) {
        viewModelScope.launch {
            try {
                val resp = salesRepository.getActiveShift(outletId).await()
                resp.takeIf { it.status }?.data?.takeIf { it.openShiftId > 0 }?.let {
                    Timber.i("there is current shift active : ${Gson().toJson(it)}")
                    resp.code = "1"
                    openShiftTask.postValue(resp)
                }
            } catch (e: Exception) {
                dialogMessageTask.postValue(e.readableError())
            }
        }
    }

    fun getSubscriptionStatus(deviceId: String) {
        viewModelScope.launch {
            try {
                val subscription = settingRepository.getSubscriptionStatus(deviceId)
                subscriptionTask.postValue(subscription)
            } catch (e: Exception) {
                Timber.i("Getting subscription error - $e")
            }
        }
    }

    fun loadPreviousOpenShift() {
        viewModelScope.launch {
            if (allShiftList.isEmpty()) {
                allShiftList.addAll(outletRepository.getAllShift())
            }

            //get previous history
            if (openShiftHistory.isEmpty()) {
                openShiftHistory.addAll(salesRepository.getOpenShiftHistory())
            }

            Timber.i("finish load previous data. openShiftHistory ${openShiftHistory.size} | allShift ${allShiftList.size}")
//            if (suggestedShift == null) makeShiftSuggestion()
        }
    }

    private fun makeShiftSuggestion() {
        viewModelScope.launch {
            if (allShiftList.isEmpty() || openShiftHistory.isEmpty()) {
                withContext(Dispatchers.Default) { loadPreviousOpenShift() }
            }

            if (openShiftHistory.isEmpty()) {
                Timber.i("can not give suggestion, user has no history")
                return@launch
            }

            if (shiftList.isEmpty() || shiftList.size == 1) {
                Timber.i("can not give suggestion, available shift is not loaded, empty or only one. size is ${shiftList.size}")
                return@launch
            }

            Timber.i("shift : ${Gson().toJson(allShiftList)}")

            //first way: based on time
            val hourNow = System.currentTimeMillis().dateFormat("HH").safeToInt()
            val previousShifts = openShiftHistory.filter { it.timeOpen.dateFormat("HH").safeToInt() in (hourNow - 3) until (hourNow + 3) }
            Timber.i("previousShifts match criteria : ${previousShifts.size}")
            if (previousShifts.isNotEmpty()) {
                val shiftGroupCount = previousShifts.groupingBy { it.shiftFkid }.eachCount().toList().sortedByDescending { it.second }

                Timber.i("shift group : ${previousShifts.groupingBy { it.shiftFkid }.eachCount()}")
                Timber.i("should suggest : ${shiftGroupCount[0]}")

                if (shiftList.any { it.shiftId == shiftGroupCount[0].first }) {
                    allShiftList.firstOrNull { it.shiftId == shiftGroupCount[0].first }?.let { shift ->
                        Timber.i("which is '${shift.name}'")
                        suggestedShift = shift.name
                        suggestionShiftTask.postValue(shift.name)
                        return@launch
                    }
                } else {
                    Timber.i("suggested shift is not in available shift")
                }
            }

            //second way, check the shift order (from previous)
            Timber.i("use second way to give suggestion")
            val result = HashMap<Int, Int>() //key: shiftId, value: count
            val lastShiftId = openShiftHistory[0].shiftFkid
            Timber.i("last used shift : $lastShiftId")
            openShiftHistory.sortedBy { it.timeOpen }.forEachIndexed { index, openshift ->
                if (index > 0) {
                    if (openShiftHistory[index - 1].shiftFkid == lastShiftId && openshift.shiftFkid != lastShiftId) {
                        result[openshift.shiftFkid] = result[openshift.shiftFkid].safe() + 1
                    }
                }
            }

            Timber.i("result : ${Gson().toJson(result)}")
            val resultSorted = result.toList().sortedByDescending { it.second }
            if (result.isNotEmpty() && shiftList.any { it.shiftId == resultSorted[0].first }) {
                allShiftList.firstOrNull { it.shiftId == resultSorted[0].first }?.let { shift ->
                    Timber.i("shift suggested is '${shift.name}'")
                    suggestedShift = shift.name
                    suggestionShiftTask.postValue(shift.name)
                }
            } else {
                Timber.i("suggested shift is not in available shift")
            }
        }

        //the old way, the first.... the simplest one
//        val hourNow = System.currentTimeMillis().dateFormat("HH").safeToInt()
//        openShiftHistory.firstOrNull { it.timeOpen.dateFormat("HH").safeToInt() in (hourNow - 3) until (hourNow + 3) }?.let { openShift ->
//            Timber.i("suggestion shift is ${openShift.shiftFkid}")
//            if (shiftList.any { it.shiftId == openShift.shiftFkid }) {
//                allShiftList.firstOrNull { it.shiftId == openShift.shiftFkid }?.let { shift ->
//                    Timber.i("which is '${shift.name}'")
//                    suggestedShift = shift.name
//                    suggestionShiftTask.postValue(shift.name)
//                }
//            } else {
//                Timber.i("current available shift has no suggested shift")
//            }
//        }
    }

}