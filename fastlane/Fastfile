# This file contains the fastlane.tools configuration
# You can find the documentation at https://docs.fastlane.tools
#
# For a list of all available actions, check out
#
#     https://docs.fastlane.tools/actions
#
# For a list of all available plugins, check out
#
#     https://docs.fastlane.tools/plugins/available-plugins
#

# Uncomment the line if you want fastlane to automatically update itself
# update_fastlane

default_platform(:android)

platform :android do
  desc "Runs all the tests"
  lane :test do
    gradle(task: "test")
  end

  desc "build apks"
  lane :buildApk do
    gradle(task: "assembleDebug")
  end

  lane :testBuild do
    gradle(task: 'bundleAlive', build_type: 'Release')
  end

  desc "upload apk (p.o.s) to google play"
  lane :stagingDeploy do
        gradle(
            task: 'bundleAlive',
            build_type: 'Release'
        )
        upload_to_gofile()
        upload_to_play_store(
            track: 'internal',
            skip_upload_apk: true,
            skip_upload_metadata: true,
            skip_upload_images: true,
            skip_upload_screenshots: true
        )
        version = sh("git", "describe", "--tags")
        slack(
             message: "New Staging APK (P.O.S) v"+version+" Published to Google Play",
             slack_url: "*****************************************************************************",
             default_payloads: [:last_git_commit_message, :last_git_commit_hash , :git_author]
            )
        #promote_release()
  end


  desc "upload apk production to google play"
  lane :buildApkRelease do
          gradle(
               task: "assemble",
               build_type: 'Release'
          )
  end

  
  desc "upload apk production to google play"
  lane :betaDeploy do
          gradle(
               task: "bundleProduction",
               build_type: 'Release'
          )
          upload_to_gofile()
          upload_to_play_store(
                      track: 'beta',
                      skip_upload_apk: true,
                      skip_upload_metadata: true,
                      skip_upload_images: true,
                      skip_upload_screenshots: true
                  )
          slack(
               message: "New Production APK Published to Google Play",
               slack_url: "*****************************************************************************",
               default_payloads: [:last_git_commit_message, :last_git_commit_hash , :git_author]
              )
  end

  desc "upload apk production release to google play"
    lane :productionDeploy do
            gradle(
                 task: "bundleProduction",
                 build_type: 'Release'
            )
            upload_to_play_store(
                        track: 'production',
                        skip_upload_apk: true,
                        skip_upload_metadata: true,
                        skip_upload_images: true,
                        skip_upload_screenshots: true
                    )
            slack(
                 message: "New Production APK Published to Google Play",
                 slack_url: "*****************************************************************************",
                 default_payloads: [:last_git_commit_message, :last_git_commit_hash , :git_author]
                )
    end

  desc "Upload apk to Slack"
  lane :developmentDeploy do
      gradle(task: "assembleDevelopmentDebug", flags: "-x lint")
      # distribute_app()
      upload_to_telegram()
      # upload_all_to_slack()
      # slack_notification()
      updateApkVersion()
      # upload_to_gofile()      
    end

    lane :updateApkVersion do
        branch = sh("git", "rev-parse","--abbrev-ref", "HEAD")
        baseUrl = 'https://api.uniqdev.web.id'
        variantName = 'development'
        if branch == 'staging'
            baseUrl = 'https://api-staging.uniq.id'
            puts 'staging branch'
        elsif branch == 'master'
            baseUrl = 'https://api.uniq.id'
            puts 'master branch'
        end

        filePath = ''
        lane_context[SharedValues::GRADLE_ALL_APK_OUTPUT_PATHS].each do | full_file_path |
          puts "variant : " + full_file_path
          if full_file_path.include? variantName
            filePath = full_file_path
            puts 'use this file to upload....'
          end
        end

        versionName = sh("git", "describe", "--tags")
        versionCode = sh("git", "log", "-1", "--format=%ct")
        releaseNote = File.read("../release_note.txt")
        sh "curl --location --request POST "+ baseUrl +"/v1/system/app_release -F version_code=\""+ versionCode +"\" -F version_name=\""+ versionName +"\" -F release_note=\""+ releaseNote +"\" -F track=\"internal\" -F \"file=@"+filePath+"\" "
    end

    lane :diawiUpload do
      
    end 

    desc "Send notification to Slack Channel"
    private_lane :slack_notification do
        version = sh("git", "describe", "--tags")
        changelog = File.read("../release_note.txt")
        slack(
              default_payloads: [:git_author],
              message: "New APK v"+ version +" Ready To QA \nCHANGE LOG : \n" + changelog,
              slack_url: "*****************************************************************************",
              )
    end

    desc "Upload the APK to Slack channel"
    private_lane :upload_to_slack do |options|
      full_file_path = lane_context[SharedValues::GRADLE_APK_OUTPUT_PATH]
      sh "echo filepath : "+ full_file_path
      file_name = full_file_path.gsub(/\/.*\//,"")
      sh "echo Uploading " + file_name + " to #mobile-team Slack"
      sh "curl https://slack.com/api/files.upload -F token=\""+ ENV['SLACK_ACCESS_TOKEN'] +"\" -F channels=\"prod-pos\" -F title=\"" + file_name + "\" -F filename=\"" + file_name + "\" -F file=@" + full_file_path
    end


    desc "upload apk all variant to Slack Channel"
      private_lane :upload_all_to_slack do |options|
        lane_context[SharedValues::GRADLE_ALL_APK_OUTPUT_PATHS].each do | full_file_path |
          puts "upload apk this variant : " + full_file_path
          file_name = full_file_path.gsub(/\/.*\//,"")
          sh "curl https://slack.com/api/files.upload -F token=\""+ ENV['SLACK_ACCESS_TOKEN'] +"\" -F channels=\"prod-pos\" -F title=\"" + file_name + "\" -F filename=\"" + file_name + "\" -F file=@" + full_file_path
        end
      end


  desc "Upload APKs and AABs to Gofile"
  lane :upload_to_gofile do
    # Fetch Gofile server
    server = sh("curl -s 'https://api.gofile.io/servers' | jq -r '.data.servers[0] | .name'").strip
    puts "Using Gofile server: '#{server}'"

    lane_context[SharedValues::GRADLE_ALL_APK_OUTPUT_PATHS].each do | full_file_path |
      apk_name = File.basename(full_file_path)
      puts "uploading [#{apk_name}] #{full_file_path}"

      # Upload to Gofile
      response = sh("curl -s -X POST 'https://#{server}.gofile.io/uploadfile' \
        -H 'Authorization: Bearer #{ENV['GOFILE_TOKEN']}' \
        -F 'file=@#{full_file_path}' \
        | jq -r '.data.downloadPage'")

      puts "Download page for #{apk_name}: #{response}"
    end

    lane_context[SharedValues::GRADLE_ALL_AAB_OUTPUT_PATHS].each do | full_file_path |
      apk_name = File.basename(full_file_path)
      puts "uploading [#{apk_name}] #{full_file_path}"

      # Upload to Gofile
      response = sh("curl -s -X POST 'https://#{server}.gofile.io/uploadfile' \
        -H 'Authorization: Bearer #{ENV['GOFILE_TOKEN']}' \
        -F 'file=@#{full_file_path}' \
        | jq -r '.data.downloadPage'")

      puts "Download page for #{apk_name}: #{response}"
    end

    # Combine APK and AAB paths into a single array for processing
    files_to_upload = []
    files_to_upload += lane_context[SharedValues::GRADLE_ALL_APK_OUTPUT_PATHS] if lane_context[SharedValues::GRADLE_ALL_APK_OUTPUT_PATHS]
    files_to_upload += lane_context[SharedValues::GRADLE_ALL_AAB_OUTPUT_PATHS] if lane_context[SharedValues::GRADLE_ALL_AAB_OUTPUT_PATHS]

    # Upload each file to Gofile
    files_to_upload.each do |file_path|
      file_name = File.basename(file_path)
      puts "Uploading #{file_name} to Gofile..."
    end

  end

  desc "Submit a new Beta Build to Crashlytics Beta"
  lane :beta do
    gradle(task: "clean assembleRelease")
    crashlytics

    # sh "your_script.sh"
    # You can also use other beta testing services here
  end

  desc "Deploy a new version to the Google Play"
  lane :deploy do
    gradle(task: "clean assembleRelease")
    upload_to_play_store
  end

  lane :version do
    versi = sh("git", "describe", "--tags")
    sh "echo version --> " + versi
  end

  lane :distribute_app do |options|    
    artifact_path = ''   
    lane_context[SharedValues::GRADLE_ALL_APK_OUTPUT_PATHS].each do | full_file_path |      
      file_name = full_file_path.gsub(/\/.*\//,"")
      puts "variant : " + full_file_path
      if file_name.include?("development")
        artifact_path = full_file_path
      end      
    end

    puts 'use artifact: '+artifact_path
    firebase_app_distribution(
        app: ENV['APP_ID'],
        groups: "uniq-qa-team",
        release_notes_file: "release_note.txt",
        firebase_cli_token: ENV['FIREBASE_CLI_TOKEN'],
        android_artifact_type: "APK",
        android_artifact_path: artifact_path
    )

    # notify_to_slack(options)
  end

  lane :distribute_app_manually do |options|    
    artifact_path = '/workspace/android-pos/app/build/outputs/apk/development/debug/uniq_pos_development_universal_1.16-174-g0eb7dcc.apk'
    firebase_app_distribution(
        app: ENV['APP_ID'],
        groups: "uniq-qa-team",
        # release_notes_file: "release_note.txt",
        firebase_cli_token: ENV['FIREBASE_CLI_TOKEN'],
        android_artifact_type: "APK",
        android_artifact_path: artifact_path,
        debug: true
    )
    # notify_to_slack(options)
  end

  desc 'promote internal to alpha'
  lane :promote_release do 
    upload_to_play_store(
      track: 'internal',
      track_promote_to: 'alpha'
    )
  end

  lane :testGofile do 
    gradle(task: "assembleDevelopmentDebug", flags: "-x lint")
    upload_to_gofile()
  end 

  desc "Upload apk to Telegram"
  private_lane :upload_to_telegram do |options|
    version = sh("git", "describe", "--tags")
    versionCode = sh("git", "log", "-1", "--format=%ct")
    branch = sh("git", "rev-parse", "--abbrev-ref", "HEAD")

    lane_context[SharedValues::GRADLE_ALL_APK_OUTPUT_PATHS].each do | full_file_path |
      message = "🚀 New Mobile POS Dev Build\n" \
                "📱 Version: #{version} (#{versionCode})\n" \
                "📅 Build Date: #{ENV['BUILD_DATE']}\n" \
                "🔧 Branch: #{branch}\n\n" \
                "Please test the latest build and report any issues.\n\n" \
                "#android #pos"

      sh "curl -F document=@\"#{full_file_path}\" \
          -F caption=\"#{message}\" \
          \"https://api.telegram.org/bot#{ENV['TELEGRAM_BOT_TOKEN']}/sendDocument?chat_id=#{ENV['TELEGRAM_CHAT_ID']}\""
    end
  end

end
