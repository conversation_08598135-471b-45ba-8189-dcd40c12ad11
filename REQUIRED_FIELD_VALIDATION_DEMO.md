# Required Field Validation Demo

## Overview
This document demonstrates how the required field validation works in the dynamic form system.

## Visual Indicators

### Required Fields
- **Label**: Shows field name with red asterisk (*) 
- **Example**: "Customer Name *" (where * is red)
- **Styling**: Gray label text with red asterisk using SpannableString

### Optional Fields  
- **Label**: Shows field name without asterisk
- **Example**: "Notes"
- **Styling**: Gray label text only

## Validation Behavior

### Text Fields (TextFieldComponent)

#### Required Text Field - Empty
```
Field: "Customer Name *"
Input: [empty]
Validation: ❌ FAIL
Error: "Please enter Customer Name"
```

#### Required Text Field - Filled
```
Field: "Customer Name *"
Input: "John Doe"
Validation: ✅ PASS
```

#### Optional Text Field - Empty
```
Field: "Notes"
Input: [empty]
Validation: ✅ PASS
```

### Single Choice Fields (SingleChoiceComponent)

#### Required Choice Field - No Selection
```
Field: "Customer Type *"
Options: [Member Baru] [Member Lama] [Non Member]
Selection: none
Validation: ❌ FAIL
Error: "Please select Customer Type"
```

#### Required Choice Field - Selected
```
Field: "Customer Type *"
Options: [Member Baru] [Member Lama] [Non Member]
Selection: "Member Baru" (green chip)
Validation: ✅ PASS
```

#### Optional Choice Field - No Selection
```
Field: "Preferences"
Options: [Option A] [Option B] [Option C]
Selection: none
Validation: ✅ PASS
```

## Integration with SaveOrderDialog

### Validation Flow
1. User clicks "Pay" or "Save" button
2. System validates all dynamic form fields
3. If any required field is empty:
   - Shows error toast with specific field name
   - Prevents proceeding to payment/save
   - User must fill required fields to continue
4. If all validations pass:
   - Collects form data into CustomFields objects
   - Adds to SalesEntity for API transmission
   - Proceeds with payment/save flow

### Error Messages
- **Text Field**: "Please enter [Field Name]"
- **Choice Field**: "Please select [Field Name]"
- **Toast Display**: Error shown as red toast message
- **User Action**: Must correct the error to proceed

## Code Examples

### Creating Required Field Config
```kotlin
val requiredConfig = FormFieldConfig(
    id = "1",
    label = "Customer Name",
    type = FormFieldConfig.TYPE_TEXT,
    isRequired = true,  // This makes it required
    displayOrder = 1
)
```

### Creating Optional Field Config
```kotlin
val optionalConfig = FormFieldConfig(
    id = "2", 
    label = "Notes",
    type = FormFieldConfig.TYPE_TEXT,
    isRequired = false,  // This makes it optional
    displayOrder = 2
)
```

### Validation in Action
```kotlin
// In SaveOrderDialog.validateInput()
if (::dynamicFormManager.isInitialized && dynamicFormManager.hasFields()) {
    when (val validationResult = dynamicFormManager.validateForm()) {
        is ValidationResult.Error -> {
            // Show error toast and stop processing
            context.toast(validationResult.message, level = Level.ERROR)
            return
        }
        is ValidationResult.Success -> {
            // Continue with payment/save
            Timber.d("Dynamic form validation passed")
        }
    }
}
```

## Testing

### Unit Tests
- ✅ Required fields fail validation when empty
- ✅ Required fields pass validation when filled
- ✅ Optional fields always pass validation
- ✅ Error messages contain specific field names
- ✅ Form manager validates all fields correctly

### Manual Testing Steps
1. Configure transaction settings with mix of required/optional fields
2. Open SaveOrderDialog
3. Try to pay/save without filling required fields
4. Verify error toast appears with correct message
5. Fill required fields and verify validation passes
6. Confirm optional fields don't block submission

## Benefits

1. **Clear Visual Cues**: Red asterisk makes required fields obvious
2. **Specific Error Messages**: Users know exactly which field to fill
3. **Prevents Data Loss**: Validation stops submission until complete
4. **Consistent UX**: Same validation pattern across all field types
5. **Flexible Configuration**: Backend can control which fields are required
